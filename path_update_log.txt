PATH UPDATE LOG
Started: 2025-07-04 01:49:44

[01:49:44] Starting path usage update...
[01:49:44] Found 653 files to process
[01:49:44] Backed up: system\api\data_table.api.php
[01:49:44] Updated 2 patterns in: system\api\data_table.api.php
[01:49:44] Backed up: system\api\enhanced_data.api.php
[01:49:44] Backed up: system\api\handler.api.php
[01:49:44] Updated 1 patterns in: system\api\handler.api.php
[01:49:44] Backed up: system\api\hilt_settings.api.php
[01:49:44] Backed up: system\api\logout.php
[01:49:44] Updated 1 patterns in: system\api\logout.php
[01:49:44] Backed up: system\api\logs.api.php
[01:49:44] Updated 1 patterns in: system\api\logs.api.php
[01:49:44] Backed up: system\api\nav_tree.api.php
[01:49:44] Updated 8 patterns in: system\api\nav_tree.api.php
[01:49:44] Backed up: system\api\notifications.api.php
[01:49:44] Updated 3 patterns in: system\api\notifications.api.php
[01:49:44] Backed up: system\api\system.api.php
[01:49:44] Updated 4 patterns in: system\api\system.api.php
[01:49:44] Backed up: system\api\test_notifications.api.php
[01:49:44] Updated 5 patterns in: system\api\test_notifications.api.php
[01:49:44] Backed up: system\api\users.api.php
[01:49:44] Backed up: system\autoloader.php
[01:49:44] Updated 13 patterns in: system\autoloader.php
[01:49:44] Backed up: system\classes\database.class.php
[01:49:44] Updated 2 patterns in: system\classes\database.class.php
[01:49:44] Backed up: system\classes\data_importer.class.php
[01:49:44] Backed up: system\classes\data_table_generator.class.php
[01:49:44] Backed up: system\classes\edge\edge.class.php
[01:49:44] Updated 1 patterns in: system\classes\edge\edge.class.php
[01:49:44] Backed up: system\classes\edge\edge_loop.class.php
[01:49:44] Backed up: system\classes\edge\edge_pagination.class.php
[01:49:44] Backed up: system\classes\edge\loop.class.php
[01:49:44] Backed up: system\classes\edge\pagination.class.php
[01:49:44] Backed up: system\classes\hilt.class.php
[01:49:44] Updated 1 patterns in: system\classes\hilt.class.php
[01:49:44] Backed up: system\classes\notifications.class.php
[01:49:44] Updated 1 patterns in: system\classes\notifications.class.php
[01:49:44] Backed up: system\classes\router.class.php
[01:49:44] Backed up: system\classes\startup_sequence.class.php
[01:49:44] Updated 5 patterns in: system\classes\startup_sequence.class.php
[01:49:44] Backed up: system\classes\table_config_manager.class.php
[01:49:44] Backed up: system\classes\users.class.php
[01:49:44] Backed up: system\components\edges\card-data-table.blade.php
[01:49:44] Backed up: system\components\edges\card-data-table.edge.php
[01:49:44] Backed up: system\components\edges\component-activity-feed.edge.php
[01:49:44] Backed up: system\components\edges\component-add-flag.edge.php
[01:49:44] Backed up: system\components\edges\component-forms-basic-info.edge.php
[01:49:44] Backed up: system\components\edges\component-forms-csv-upload.edge.php
[01:49:44] Backed up: system\components\edges\component-forms-file-upload.edge.php
[01:49:44] Backed up: system\components\edges\component-forms-html-editor.edge.php
[01:49:44] Backed up: system\components\edges\component-listbox-date.edge.php
[01:49:44] Backed up: system\components\edges\component-listbox-label.edge.php
[01:49:44] Backed up: system\components\edges\component-listbox.edge.php
[01:49:44] Backed up: system\components\edges\component-modal.edge.php
[01:49:44] Backed up: system\components\edges\component-mood-selector.edge.php
[01:49:44] Backed up: system\components\edges\component-multi-select.edge.php
[01:49:44] Backed up: system\components\edges\component-popover-box.edge.php
[01:49:44] Backed up: system\components\edges\data-table-filter.edge.php
[01:49:44] Updated 3 patterns in: system\components\edges\data-table-filter.edge.php
[01:49:44] Backed up: system\components\edges\data-table-simple.blade.php
[01:49:44] Backed up: system\components\edges\data-table-simple.edge.php
[01:49:44] Backed up: system\components\edges\data-table.blade.php
[01:49:44] Backed up: system\components\edges\data-table.edge.php
[01:49:44] Backed up: system\components\edges\data-table.fn.php
[01:49:44] Backed up: system\components\edges\data-table.group.blade.php
[01:49:44] Backed up: system\components\edges\data-table.group.edge.php
[01:49:44] Backed up: system\components\edges\email-send-rules-widget.edge.php
[01:49:44] Updated 2 patterns in: system\components\edges\email-send-rules-widget.edge.php
[01:49:44] Backed up: system\components\edges\forms-button.edge.php
[01:49:44] Backed up: system\components\edges\forms-checkbox-toggle.edge.php
[01:49:44] Backed up: system\components\edges\forms-description-toggle.edge.php
[01:49:44] Backed up: system\components\edges\forms-field-with-description.edge.php
[01:49:44] Backed up: system\components\edges\forms-form.blade.php
[01:49:44] Backed up: system\components\edges\forms-form.edge.php
[01:49:44] Backed up: system\components\edges\forms-input-button.edge.php
[01:49:44] Backed up: system\components\edges\forms-input-select-button.blade.php
[01:49:44] Backed up: system\components\edges\forms-input-select-button.edge.php
[01:49:44] Backed up: system\components\edges\forms-input-select-insertbutton.blade.php
[01:49:44] Backed up: system\components\edges\forms-input-select-insertbutton.edge.php
[01:49:44] Backed up: system\components\edges\forms-input-select.blade.php
[01:49:44] Backed up: system\components\edges\forms-input-select.edge.php
[01:49:44] Backed up: system\components\edges\forms-input.edge.php
[01:49:44] Backed up: system\components\edges\forms-radio-group.edge.php
[01:49:44] Backed up: system\components\edges\forms-radio.edge.php
[01:49:44] Backed up: system\components\edges\forms-select-invisible.blade.php
[01:49:44] Backed up: system\components\edges\forms-select-invisible.edge.php
[01:49:44] Backed up: system\components\edges\forms-select-with-icons.edge.php
[01:49:44] Backed up: system\components\edges\forms-select.blade.php
[01:49:44] Backed up: system\components\edges\forms-select.edge.php
[01:49:44] Backed up: system\components\edges\forms-template-selector.edge.php
[01:49:44] Updated 1 patterns in: system\components\edges\forms-template-selector.edge.php
[01:49:44] Backed up: system\components\edges\forms-textarea.edge.php
[01:49:44] Backed up: system\components\edges\hilt-settings.edge.php
[01:49:44] Updated 3 patterns in: system\components\edges\hilt-settings.edge.php
[01:49:44] Backed up: system\components\edges\html-div.blade.php
[01:49:44] Backed up: system\components\edges\html-div.edge.php
[01:49:44] Backed up: system\components\edges\htmx-progress-bar.edge.php
[01:49:44] Backed up: system\components\edges\json-editor.edge.php
[01:49:44] Backed up: system\components\edges\json-viewer.edge.php
[01:49:44] Backed up: system\components\edges\layout-api.edge.php
[01:49:44] Backed up: system\components\edges\layout-blank.edge.php
[01:49:44] Backed up: system\components\edges\layout-box.edge.php
[01:49:44] Backed up: system\components\edges\layout-card.edge.php
[01:49:44] Backed up: system\components\edges\layout-footer.edge.php
[01:49:44] Backed up: system\components\edges\layout-head.edge.php
[01:49:44] Updated 1 patterns in: system\components\edges\layout-head.edge.php
[01:49:44] Backed up: system\components\edges\layout-main.edge.php
[01:49:44] Backed up: system\components\edges\layout-modal_data_display.edge.php
[01:49:44] Backed up: system\components\edges\layout-modal_data_display.php
[01:49:44] Backed up: system\components\edges\layout-modal_data_edit.edge.php
[01:49:44] Backed up: system\components\edges\layout-sidebar.edge.php
[01:49:44] Backed up: system\components\edges\layout-view.edge.php
[01:49:44] Backed up: system\components\edges\layout-white_box_rounded.edge.php
[01:49:44] Backed up: system\components\edges\nav-entry-form.bak.php
[01:49:44] Updated 2 patterns in: system\components\edges\nav-entry-form.bak.php
[01:49:44] Backed up: system\components\edges\nav-entry-form.edge.php
[01:49:44] Updated 3 patterns in: system\components\edges\nav-entry-form.edge.php
[01:49:44] Backed up: system\components\edges\nav-tree.edge.php
[01:49:44] Updated 2 patterns in: system\components\edges\nav-tree.edge.php
[01:49:44] Backed up: system\components\edges\navbar.edge.php
[01:49:44] Updated 13 patterns in: system\components\edges\navbar.edge.php
[01:49:44] Backed up: system\components\edges\notification-dropdown.edge.php
[01:49:44] Updated 5 patterns in: system\components\edges\notification-dropdown.edge.php
[01:49:44] Backed up: system\components\edges\notification-handler.blade.php
[01:49:44] Backed up: system\components\edges\notification-handler.edge.php
[01:49:44] Backed up: system\components\edges\pagination-strip.blade.php
[01:49:44] Backed up: system\components\edges\pagination-strip.edge.php
[01:49:44] Backed up: system\components\edges\popover-box.edge.php
[01:49:44] Backed up: system\components\edges\select-with-icons.edge.php
[01:49:44] Backed up: system\components\edges\table-editable.blade.php
[01:49:44] Backed up: system\components\edges\table-editable.edge.php
[01:49:44] Backed up: system\components\edges\template-container.edge.php
[01:49:44] Backed up: system\components\edges\template-selector.edge.php
[01:49:44] Updated 1 patterns in: system\components\edges\template-selector.edge.php
[01:49:44] Backed up: system\components\edges\template-viewer.edge.php
[01:49:44] Backed up: system\components\edges\test.php
[01:49:44] Backed up: system\components\edges\user-modal.blade.php
[01:49:44] Backed up: system\components\edges\user-modal.edge.php
[01:49:44] Backed up: system\components\icons.php
[01:49:44] Backed up: system\components\icons_raw.php
[01:49:44] Backed up: system\config\config.php
[01:49:44] Updated 7 patterns in: system\config\config.php
[01:49:44] Backed up: system\config\db_config.php
[01:49:44] Backed up: system\config\db_config_local.php
[01:49:44] Backed up: system\config\path_definitions.php
[01:49:44] Backed up: system\config\path_schema.php
[01:49:44] Backed up: system\config\routes.php
[01:49:44] Backed up: system\config\webhook_config.php
[01:49:44] Backed up: system\config.php
[01:49:44] Updated 7 patterns in: system\config.php
[01:49:44] Backed up: system\csv-uploader.edge.php
[01:49:44] Backed up: system\db_config.php
[01:49:44] Backed up: system\db_config_local.php
[01:49:44] Backed up: system\definitions\data_type_definitions.php
[01:49:44] Backed up: system\functions\components.fn.php
[01:49:44] Backed up: system\functions\database.php
[01:49:44] Backed up: system\functions\enhanced_data_callbacks.php
[01:49:44] Updated 1 patterns in: system\functions\enhanced_data_callbacks.php
[01:49:44] Backed up: system\functions\functions.php
[01:49:44] Updated 1 patterns in: system\functions\functions.php
[01:49:44] Backed up: system\functions\notifications.fn.php
[01:49:44] Backed up: system\functions\path_utils.php
[01:49:44] Backed up: system\functions\tcs_components_v2.php
[01:49:44] Backed up: system\functions\users.fn.php
[01:49:44] Updated 2 patterns in: system\functions\users.fn.php
[01:49:44] Backed up: system\generate_ide_helper.php
[01:49:44] Updated 4 patterns in: system\generate_ide_helper.php
[01:49:44] Backed up: system\paths.php
[01:49:44] Updated 2 patterns in: system\paths.php
[01:49:44] Backed up: system\routes.php
[01:49:44] Backed up: system\route_permissions.php
[01:49:44] Backed up: system\startup_sequence.php
[01:49:44] Updated 12 patterns in: system\startup_sequence.php
[01:49:44] Backed up: system\startup_sequence_minimal.php
[01:49:44] Updated 5 patterns in: system\startup_sequence_minimal.php
[01:49:44] Backed up: system\templates\custom_html_template.hilt.php
[01:49:44] Backed up: system\templates\data_table_template.api.php
[01:49:44] Backed up: system\templates\data_table_template.hilt.php
[01:49:44] Updated 1 patterns in: system\templates\data_table_template.hilt.php
[01:49:44] Backed up: system\templates\default_template.hilt.php
[01:49:44] Updated 1 patterns in: system\templates\default_template.hilt.php
[01:49:44] Backed up: system\templates\default_template.temp.php
[01:49:44] Backed up: system\templates\file_upload_template.hilt.php
[01:49:44] Updated 1 patterns in: system\templates\file_upload_template.hilt.php
[01:49:44] Backed up: system\views\admin\update_autodesk_hashes.view.php
[01:49:44] Backed up: system\views\api\update_autodesk_hashes.php
[01:49:44] Backed up: system\views\login\login.view.php
[01:49:44] Backed up: system\views\login\reset-password.view.php
[01:49:44] Backed up: system\views\logs\logs.api.php
[01:49:44] Backed up: system\views\logs\logs.fn.php
[01:49:44] Updated 4 patterns in: system\views\logs\logs.fn.php
[01:49:44] Backed up: system\views\logs\logs.view.php
[01:49:44] Backed up: system\views\logs\view.view.php
[01:49:44] Backed up: system\views\notifications\notification-badge.php
[01:49:44] Backed up: system\views\notifications\notification-list.php
[01:49:44] Backed up: system\views\notifications\notification-preferences.php
[01:49:44] Backed up: system\views\notifications\notifications.view.php
[01:49:44] Backed up: system\views\settings.api.php
[01:49:44] Updated 1 patterns in: system\views\settings.api.php
[01:49:44] Backed up: system\views\system\logs\logs.api.php
[01:49:44] Backed up: system\views\system\logs\logs.fn.php
[01:49:44] Updated 4 patterns in: system\views\system\logs\logs.fn.php
[01:49:44] Backed up: system\views\system\logs\logs.view.php
[01:49:44] Backed up: system\views\system\logs\view.view.php
[01:49:44] Backed up: system\views\system\system.api.php
[01:49:44] Updated 4 patterns in: system\views\system\system.api.php
[01:49:44] Backed up: system\views\system\system.edge.php
[01:49:44] Backed up: system\views\system.api.php
[01:49:44] Updated 4 patterns in: system\views\system.api.php
[01:49:44] Backed up: system\views\system.edge.php
[01:49:44] Backed up: system\views\template.view.php
[01:49:44] Backed up: system\views\test.edge.php
[01:49:44] Backed up: system\views\update_autodesk_hashes.api.php
[01:49:44] Backed up: system\views\update_autodesk_hashes.view.php
[01:49:44] Backed up: system\views\users\users.api.php
[01:49:44] Backed up: system\views\users\users.view.php
[01:49:44] Updated 1 patterns in: system\views\users\users.view.php
[01:49:44] Backed up: system\webhook_config.php
[01:49:44] Backed up: resources\api\admin\toggle-form.php
[01:49:44] Backed up: resources\api\autodesk.api.php
[01:49:44] Backed up: resources\api\csv.api.php
[01:49:44] Backed up: resources\api\customer\existing-form.php
[01:49:44] Backed up: resources\api\customer\new-form.php
[01:49:44] Backed up: resources\api\line-items\add.php
[01:49:44] Backed up: resources\api\line-items\remove.php
[01:49:44] Backed up: resources\api\quote-v3\add-line-item.php
[01:49:44] Backed up: resources\api\quote-v3\remove-line-item.php
[01:49:44] Backed up: resources\api\quote-v3\toggle-description.php
[01:49:44] Backed up: resources\api\quote-v3.api.php
[01:49:44] Backed up: resources\api\quotes\submit.php
[01:49:44] Backed up: resources\api\quotes\validate.php
[01:49:44] Backed up: resources\api\quotes.api.php
[01:49:44] Updated 3 patterns in: resources\api\quotes.api.php
[01:49:44] Backed up: resources\api\recipients\add.php
[01:49:44] Backed up: resources\api\recipients\remove.php
[01:49:44] Backed up: resources\api\subscriptions.api - Copy.php
[01:49:44] Updated 3 patterns in: resources\api\subscriptions.api - Copy.php
[01:49:44] Backed up: resources\api\subscriptions.api.php
[01:49:44] Updated 3 patterns in: resources\api\subscriptions.api.php
[01:49:44] Backed up: resources\api\view.api.php
[01:49:44] Backed up: resources\classes\attributes.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_api.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_api_interface.class.php
[01:49:44] Updated 1 patterns in: resources\classes\autodesk_api\autodesk_api_interface.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_authenticator.class.php
[01:49:44] Updated 2 patterns in: resources\classes\autodesk_api\autodesk_authenticator.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_customer.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_customers.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_customer_success.class.php
[01:49:44] Updated 1 patterns in: resources\classes\autodesk_api\autodesk_customer_success.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_order.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_orders.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_product.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_products.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_quote.class.php
[01:49:44] Updated 1 patterns in: resources\classes\autodesk_api\autodesk_quote.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_quotes.class.php
[01:49:44] Updated 2 patterns in: resources\classes\autodesk_api\autodesk_quotes.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_subscription.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_subscriptions.class.php
[01:49:44] Updated 5 patterns in: resources\classes\autodesk_api\autodesk_subscriptions.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\autodesk_website_quote.class.php
[01:49:44] Backed up: resources\classes\autodesk_api\config.php
[01:49:44] Backed up: resources\classes\data_table.class.php
[01:49:44] Backed up: resources\components\edges\quote-v3-contact-check-result.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-customer-form.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-customer-search-results.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-editor.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-form.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-line-item.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-new-customer-form.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-offering-search-results.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-success.edge.php
[01:49:44] Backed up: resources\components\edges\quote-v3-ui-form.edge.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config-column.blade.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config-column.edge.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config-filter-row.blade.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config-filter-row.edge.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config-replacement-row.blade.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config-replacement-row.edge.php
[01:49:44] Backed up: resources\components\edges\subscription-table-config.edge.php
[01:49:44] Updated 3 patterns in: resources\components\edges\subscription-table-config.edge.php
[01:49:44] Backed up: resources\components\edges\subscriptions-email-settings.edge.php
[01:49:44] Updated 3 patterns in: resources\components\edges\subscriptions-email-settings.edge.php
[01:49:44] Backed up: resources\components\edges\view-subscription_display.edge.php
[01:49:44] Backed up: resources\components\icons.php
[01:49:44] Backed up: resources\components\icons_raw.php
[01:49:44] Backed up: resources\functions\customers.fn.php
[01:49:44] Backed up: resources\functions\data_table.fn.php
[01:49:44] Backed up: resources\functions\email_history.fn.php
[01:49:44] Backed up: resources\functions\email_settings.fn.php
[01:49:44] Updated 2 patterns in: resources\functions\email_settings.fn.php
[01:49:44] Backed up: resources\functions\navigation.fn.php
[01:49:44] Backed up: resources\functions\orders.fn.php
[01:49:44] Backed up: resources\functions\products.fn.php
[01:49:44] Backed up: resources\functions\quotes.fn.php
[01:49:44] Backed up: resources\functions\subscriptions.fn.php
[01:49:44] Backed up: resources\scheduled_scripts\send_email.sched.php
[01:49:44] Updated 2 patterns in: resources\scheduled_scripts\send_email.sched.php
[01:49:44] Backed up: resources\scripts\update_autodesk_hashes.php
[01:49:44] Backed up: resources\subscriptions.api.php
[01:49:44] Updated 3 patterns in: resources\subscriptions.api.php
[01:49:44] Backed up: resources\views\.compiled\007154649e5208d700381ffc32e25ce7.comp.php
[01:49:44] Backed up: resources\views\.compiled\02818524f6ccd39cb474701a926c31a2.comp.php
[01:49:44] Backed up: resources\views\.compiled\031825233552ac096e3ad18d400c2148.comp.php
[01:49:44] Backed up: resources\views\.compiled\06893c213912111a969383c46bc62a5c.comp.php
[01:49:44] Backed up: resources\views\.compiled\0bf09ba04741f55371d6b2beaec92b5f.comp.php
[01:49:44] Backed up: resources\views\.compiled\15242a3255a6c109f9ac1d9c671d804e.comp.php
[01:49:44] Backed up: resources\views\.compiled\1903423eac53223db19138bc1ddba25d.comp.php
[01:49:44] Backed up: resources\views\.compiled\26facf066eba3d045eed111a20d5ebbc.comp.php
[01:49:44] Backed up: resources\views\.compiled\276aa4dec5c3e17fb93f27ccd7add6f9.comp.php
[01:49:44] Backed up: resources\views\.compiled\3544f09415da96287cdc59dab0d87f58.comp.php
[01:49:44] Backed up: resources\views\.compiled\378c6e07908355087558259ed7ed58fb.comp.php
[01:49:44] Backed up: resources\views\.compiled\38a5912746ea5ac6140a1105fab37ce6.comp.php
[01:49:44] Backed up: resources\views\.compiled\38db1d8bf3cc1c6faf2ccd7712481ae4.comp.php
[01:49:44] Backed up: resources\views\.compiled\418e08de2259a7ed024dad17a6034bac.comp.php
[01:49:44] Backed up: resources\views\.compiled\45a11e820b5fc8c02e5d9071e30c3a22.comp.php
[01:49:44] Backed up: resources\views\.compiled\4901995a065ee6c74c781ea5850eaa1e.comp.php
[01:49:44] Backed up: resources\views\.compiled\4c63f6a1d5629fd3e9938698726077f4.comp.php
[01:49:44] Backed up: resources\views\.compiled\4e335caaf7156e576be243d928fdeefb.comp.php
[01:49:44] Backed up: resources\views\.compiled\52240ca90073a5722bfb64ab0cde31d8.comp.php
[01:49:44] Backed up: resources\views\.compiled\5e77436447c54aa58bff9fb128981f21.comp.php
[01:49:44] Backed up: resources\views\.compiled\7f593e9a78dc36d22ee002fae0af801e.comp.php
[01:49:44] Backed up: resources\views\.compiled\81d12fd5a641d90a0c8ccdd4b5c4f1df.comp.php
[01:49:44] Backed up: resources\views\.compiled\845ef9f0270c769fc9e3311c3c2b4afc.comp.php
[01:49:44] Backed up: resources\views\.compiled\89e364be3ebd81c1f9f1c5ea319b7292.comp.php
[01:49:44] Backed up: resources\views\.compiled\9669891c8d60f32a6d3c334284732223.comp.php
[01:49:44] Backed up: resources\views\.compiled\99fbdcbec1d887f3a9d2bb99f99270fe.comp.php
[01:49:44] Backed up: resources\views\.compiled\a249ad26dc081a699e508e0455b7a0aa.comp.php
[01:49:44] Backed up: resources\views\.compiled\a42b011c88c1c87b68dd616029da2aaf.comp.php
[01:49:44] Backed up: resources\views\.compiled\a57bfad158e8dba8efd4f63662286cfe.comp.php
[01:49:44] Backed up: resources\views\.compiled\a8cceb9531dbfb3dbc4dc555c6574877.comp.php
[01:49:44] Backed up: resources\views\.compiled\adea2c11ea40d74f65927a78011efbb8.comp.php
[01:49:44] Backed up: resources\views\.compiled\afc3a356e12b4b2fe989a91cf7155965.comp.php
[01:49:44] Backed up: resources\views\.compiled\b0bad89182bf2cecc280f16e1f5fd128.comp.php
[01:49:44] Backed up: resources\views\.compiled\c2266980a9c46e0179e7b76bb5f90cca.comp.php
[01:49:44] Backed up: resources\views\.compiled\c52c691233e1accaa397c62aad65d3dc.comp.php
[01:49:44] Backed up: resources\views\.compiled\c86e5f2a4e8d671216a0703d719d889f.comp.php
[01:49:44] Backed up: resources\views\.compiled\d7ac63c0c8832f2d406d89cfcbe62536.comp.php
[01:49:44] Backed up: resources\views\.compiled\da3fc3f0ce3557f0108f573887258518.comp.php
[01:49:44] Backed up: resources\views\.compiled\e0b66ee8de22ca707f4640387d415dbb.comp.php
[01:49:44] Backed up: resources\views\.compiled\eba08744a7f459cfb1163ec23b852ab9.comp.php
[01:49:44] Backed up: resources\views\.compiled\f2a153f631644854753c0f609ede4821.comp.php
[01:49:44] Backed up: resources\views\.compiled\f52b9cb096f43ca14dea9c889bf5a1f8.comp.php
[01:49:44] Backed up: resources\views\customers\customers.api.php
[01:49:44] Updated 3 patterns in: resources\views\customers\customers.api.php
[01:49:44] Backed up: resources\views\customers\customers.view.php
[01:49:44] Backed up: resources\views\dashboard\dashboard.view.php
[01:49:44] Backed up: resources\views\orders\orders.api.php
[01:49:44] Backed up: resources\views\orders\orders.view.php
[01:49:44] Backed up: resources\views\products\products.api.php
[01:49:44] Updated 1 patterns in: resources\views\products\products.api.php
[01:49:44] Backed up: resources\views\products\products.view.php
[01:49:44] Backed up: resources\views\quotes\quote-v3-ui-alpine-htmx-form.view.php
[01:49:44] Backed up: resources\views\quotes\quotes.api.php
[01:49:44] Backed up: resources\views\quotes\quotes.view.php
[01:49:44] Backed up: resources\views\subscriptions\config.api.php
[01:49:44] Backed up: resources\views\subscriptions\email_history\email_history.api.php
[01:49:44] Updated 2 patterns in: resources\views\subscriptions\email_history\email_history.api.php
[01:49:44] Backed up: resources\views\subscriptions\email_history\email_history.view.php
[01:49:44] Backed up: resources\views\subscriptions\email_history\reminder_email.view.php
[01:49:44] Backed up: resources\views\subscriptions\email_history\settings.view.php
[01:49:44] Updated 3 patterns in: resources\views\subscriptions\email_history\settings.view.php
[01:49:44] Backed up: resources\views\subscriptions\settings.api.php
[01:49:44] Backed up: resources\views\subscriptions\settings.view.php
[01:49:44] Backed up: resources\views\subscriptions\subscriptions.settings.php
[01:49:44] Backed up: resources\views\subscriptions\subscriptions.view.php
[01:49:44] Backed up: resources\views\system\poo.view.php
[01:49:44] Backed up: resources\views\system\test2.view.php
[01:49:44] Backed up: resources\viewspoop.edge.php
[01:49:44] Backed up: .\adwsapi_v2.php
[01:49:44] Updated 2 patterns in: .\adwsapi_v2.php
[01:49:44] Backed up: .\adws_api_subscription_export.php
[01:49:44] Updated 2 patterns in: .\adws_api_subscription_export.php
[01:49:44] Backed up: .\analyze_path_usage.php
[01:49:44] Backed up: .\cache\constants_171c93c5607c38f533774a5103172685.php
[01:49:44] Backed up: .\cache\constants_b2b300990ad363893437b904e7edeecb.php
[01:49:44] Backed up: .\check_logs_permissions.php
[01:49:44] Backed up: .\create_admin.php
[01:49:44] Backed up: .\db_sync.php
[01:49:44] Backed up: .\debug_app_path.php
[01:49:44] Updated 2 patterns in: .\debug_app_path.php
[01:49:44] Backed up: .\generate_constants_helper.php
[01:49:44] Updated 2 patterns in: .\generate_constants_helper.php
[01:49:44] Backed up: .\index.php
[01:49:44] Updated 1 patterns in: .\index.php
[01:49:44] Backed up: .\local_db_setup.php
[01:49:44] Backed up: .\local_dev_helper.php
[01:49:44] Backed up: .\login.php
[01:49:44] Backed up: .\minimal_test.php
[01:49:44] Updated 1 patterns in: .\minimal_test.php
[01:49:44] Backed up: .\old\setup_xampp_vhost.php
[01:49:44] Backed up: .\old\simple_test.php
[01:49:44] Updated 2 patterns in: .\old\simple_test.php
[01:49:44] Backed up: .\old\test_csv_import.php
[01:49:44] Updated 1 patterns in: .\old\test_csv_import.php
[01:49:44] Backed up: .\old\test_database_error_handling.php
[01:49:44] Updated 4 patterns in: .\old\test_database_error_handling.php
[01:49:44] Backed up: .\old\test_import.php
[01:49:44] Backed up: .\old\test_import_web.php
[01:49:44] Updated 1 patterns in: .\old\test_import_web.php
[01:49:44] Backed up: .\old\test_named_parameters.php
[01:49:44] Backed up: .\old\test_paths_fix.php
[01:49:44] Updated 2 patterns in: .\old\test_paths_fix.php
[01:49:44] Backed up: .\old\update_htmx_links.php
[01:49:44] Backed up: .\old\web_test_db_config.php
[01:49:44] Backed up: .\reset-password.php
[01:49:44] Backed up: .\resources\api\admin\toggle-form.php
[01:49:44] Backed up: .\resources\api\autodesk.api.php
[01:49:44] Backed up: .\resources\api\csv.api.php
[01:49:44] Backed up: .\resources\api\customer\existing-form.php
[01:49:44] Backed up: .\resources\api\customer\new-form.php
[01:49:44] Backed up: .\resources\api\line-items\add.php
[01:49:44] Backed up: .\resources\api\line-items\remove.php
[01:49:44] Backed up: .\resources\api\quote-v3\add-line-item.php
[01:49:44] Backed up: .\resources\api\quote-v3\remove-line-item.php
[01:49:44] Backed up: .\resources\api\quote-v3\toggle-description.php
[01:49:44] Backed up: .\resources\api\quote-v3.api.php
[01:49:44] Backed up: .\resources\api\quotes\submit.php
[01:49:44] Backed up: .\resources\api\quotes\validate.php
[01:49:44] Backed up: .\resources\api\quotes.api.php
[01:49:44] Backed up: .\resources\api\recipients\add.php
[01:49:44] Backed up: .\resources\api\recipients\remove.php
[01:49:44] Backed up: .\resources\api\subscriptions.api - Copy.php
[01:49:44] Backed up: .\resources\api\subscriptions.api.php
[01:49:44] Backed up: .\resources\api\view.api.php
[01:49:44] Backed up: .\resources\classes\attributes.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_api.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_api_interface.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_authenticator.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_customer.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_customers.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_customer_success.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_order.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_orders.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_product.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_products.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_quote.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_quotes.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_subscription.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_subscriptions.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\autodesk_website_quote.class.php
[01:49:44] Backed up: .\resources\classes\autodesk_api\config.php
[01:49:44] Backed up: .\resources\classes\data_table.class.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-contact-check-result.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-customer-form.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-customer-search-results.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-editor.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-form.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-line-item.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-new-customer-form.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-offering-search-results.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-success.edge.php
[01:49:44] Backed up: .\resources\components\edges\quote-v3-ui-form.edge.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config-column.blade.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config-column.edge.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config-filter-row.blade.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config-filter-row.edge.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config-replacement-row.blade.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config-replacement-row.edge.php
[01:49:44] Backed up: .\resources\components\edges\subscription-table-config.edge.php
[01:49:44] Backed up: .\resources\components\edges\subscriptions-email-settings.edge.php
[01:49:44] Backed up: .\resources\components\edges\view-subscription_display.edge.php
[01:49:44] Backed up: .\resources\components\icons.php
[01:49:44] Backed up: .\resources\components\icons_raw.php
[01:49:44] Backed up: .\resources\functions\customers.fn.php
[01:49:44] Backed up: .\resources\functions\data_table.fn.php
[01:49:44] Backed up: .\resources\functions\email_history.fn.php
[01:49:44] Backed up: .\resources\functions\email_settings.fn.php
[01:49:44] Backed up: .\resources\functions\navigation.fn.php
[01:49:44] Backed up: .\resources\functions\orders.fn.php
[01:49:44] Backed up: .\resources\functions\products.fn.php
[01:49:44] Backed up: .\resources\functions\quotes.fn.php
[01:49:44] Backed up: .\resources\functions\subscriptions.fn.php
[01:49:44] Backed up: .\resources\scheduled_scripts\send_email.sched.php
[01:49:44] Backed up: .\resources\scripts\update_autodesk_hashes.php
[01:49:44] Backed up: .\resources\subscriptions.api.php
[01:49:44] Backed up: .\resources\views\.compiled\007154649e5208d700381ffc32e25ce7.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\02818524f6ccd39cb474701a926c31a2.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\031825233552ac096e3ad18d400c2148.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\06893c213912111a969383c46bc62a5c.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\0bf09ba04741f55371d6b2beaec92b5f.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\15242a3255a6c109f9ac1d9c671d804e.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\1903423eac53223db19138bc1ddba25d.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\26facf066eba3d045eed111a20d5ebbc.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\276aa4dec5c3e17fb93f27ccd7add6f9.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\3544f09415da96287cdc59dab0d87f58.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\378c6e07908355087558259ed7ed58fb.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\38a5912746ea5ac6140a1105fab37ce6.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\38db1d8bf3cc1c6faf2ccd7712481ae4.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\418e08de2259a7ed024dad17a6034bac.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\45a11e820b5fc8c02e5d9071e30c3a22.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\4901995a065ee6c74c781ea5850eaa1e.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\4c63f6a1d5629fd3e9938698726077f4.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\4e335caaf7156e576be243d928fdeefb.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\52240ca90073a5722bfb64ab0cde31d8.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\5e77436447c54aa58bff9fb128981f21.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\7f593e9a78dc36d22ee002fae0af801e.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\81d12fd5a641d90a0c8ccdd4b5c4f1df.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\845ef9f0270c769fc9e3311c3c2b4afc.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\89e364be3ebd81c1f9f1c5ea319b7292.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\9669891c8d60f32a6d3c334284732223.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\99fbdcbec1d887f3a9d2bb99f99270fe.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\a249ad26dc081a699e508e0455b7a0aa.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\a42b011c88c1c87b68dd616029da2aaf.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\a57bfad158e8dba8efd4f63662286cfe.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\a8cceb9531dbfb3dbc4dc555c6574877.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\adea2c11ea40d74f65927a78011efbb8.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\afc3a356e12b4b2fe989a91cf7155965.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\b0bad89182bf2cecc280f16e1f5fd128.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\c2266980a9c46e0179e7b76bb5f90cca.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\c52c691233e1accaa397c62aad65d3dc.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\c86e5f2a4e8d671216a0703d719d889f.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\d7ac63c0c8832f2d406d89cfcbe62536.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\da3fc3f0ce3557f0108f573887258518.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\e0b66ee8de22ca707f4640387d415dbb.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\eba08744a7f459cfb1163ec23b852ab9.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\f2a153f631644854753c0f609ede4821.comp.php
[01:49:44] Backed up: .\resources\views\.compiled\f52b9cb096f43ca14dea9c889bf5a1f8.comp.php
[01:49:44] Backed up: .\resources\views\customers\customers.api.php
[01:49:44] Backed up: .\resources\views\customers\customers.view.php
[01:49:44] Backed up: .\resources\views\dashboard\dashboard.view.php
[01:49:44] Backed up: .\resources\views\orders\orders.api.php
[01:49:44] Backed up: .\resources\views\orders\orders.view.php
[01:49:44] Backed up: .\resources\views\products\products.api.php
[01:49:44] Backed up: .\resources\views\products\products.view.php
[01:49:44] Backed up: .\resources\views\quotes\quote-v3-ui-alpine-htmx-form.view.php
[01:49:44] Backed up: .\resources\views\quotes\quotes.api.php
[01:49:44] Backed up: .\resources\views\quotes\quotes.view.php
[01:49:44] Backed up: .\resources\views\subscriptions\config.api.php
[01:49:44] Backed up: .\resources\views\subscriptions\email_history\email_history.api.php
[01:49:44] Backed up: .\resources\views\subscriptions\email_history\email_history.view.php
[01:49:44] Backed up: .\resources\views\subscriptions\email_history\reminder_email.view.php
[01:49:44] Backed up: .\resources\views\subscriptions\email_history\settings.view.php
[01:49:44] Backed up: .\resources\views\subscriptions\settings.api.php
[01:49:44] Backed up: .\resources\views\subscriptions\settings.view.php
[01:49:44] Backed up: .\resources\views\subscriptions\subscriptions.settings.php
[01:49:44] Backed up: .\resources\views\subscriptions\subscriptions.view.php
[01:49:44] Backed up: .\resources\views\system\poo.view.php
[01:49:44] Backed up: .\resources\views\system\test2.view.php
[01:49:44] Backed up: .\resources\viewspoop.edge.php
[01:49:44] Backed up: .\send_email.php
[01:49:44] Updated 1 patterns in: .\send_email.php
[01:49:44] Backed up: .\system\api\data_table.api.php
[01:49:44] Backed up: .\system\api\enhanced_data.api.php
[01:49:44] Backed up: .\system\api\handler.api.php
[01:49:44] Backed up: .\system\api\hilt_settings.api.php
[01:49:44] Backed up: .\system\api\logout.php
[01:49:44] Backed up: .\system\api\logs.api.php
[01:49:44] Backed up: .\system\api\nav_tree.api.php
[01:49:44] Backed up: .\system\api\notifications.api.php
[01:49:44] Backed up: .\system\api\system.api.php
[01:49:44] Backed up: .\system\api\test_notifications.api.php
[01:49:44] Backed up: .\system\api\users.api.php
[01:49:44] Backed up: .\system\autoloader.php
[01:49:44] Backed up: .\system\classes\database.class.php
[01:49:44] Backed up: .\system\classes\data_importer.class.php
[01:49:44] Backed up: .\system\classes\data_table_generator.class.php
[01:49:44] Backed up: .\system\classes\edge\edge.class.php
[01:49:44] Backed up: .\system\classes\edge\edge_loop.class.php
[01:49:44] Backed up: .\system\classes\edge\edge_pagination.class.php
[01:49:44] Backed up: .\system\classes\edge\loop.class.php
[01:49:44] Backed up: .\system\classes\edge\pagination.class.php
[01:49:44] Backed up: .\system\classes\hilt.class.php
[01:49:44] Backed up: .\system\classes\notifications.class.php
[01:49:44] Backed up: .\system\classes\router.class.php
[01:49:44] Backed up: .\system\classes\startup_sequence.class.php
[01:49:44] Backed up: .\system\classes\table_config_manager.class.php
[01:49:44] Backed up: .\system\classes\users.class.php
[01:49:44] Backed up: .\system\components\edges\card-data-table.blade.php
[01:49:44] Backed up: .\system\components\edges\card-data-table.edge.php
[01:49:44] Backed up: .\system\components\edges\component-activity-feed.edge.php
[01:49:44] Backed up: .\system\components\edges\component-add-flag.edge.php
[01:49:44] Backed up: .\system\components\edges\component-forms-basic-info.edge.php
[01:49:44] Backed up: .\system\components\edges\component-forms-csv-upload.edge.php
[01:49:44] Backed up: .\system\components\edges\component-forms-file-upload.edge.php
[01:49:44] Backed up: .\system\components\edges\component-forms-html-editor.edge.php
[01:49:44] Backed up: .\system\components\edges\component-listbox-date.edge.php
[01:49:44] Backed up: .\system\components\edges\component-listbox-label.edge.php
[01:49:44] Backed up: .\system\components\edges\component-listbox.edge.php
[01:49:44] Backed up: .\system\components\edges\component-modal.edge.php
[01:49:44] Backed up: .\system\components\edges\component-mood-selector.edge.php
[01:49:44] Backed up: .\system\components\edges\component-multi-select.edge.php
[01:49:44] Backed up: .\system\components\edges\component-popover-box.edge.php
[01:49:44] Backed up: .\system\components\edges\data-table-filter.edge.php
[01:49:44] Backed up: .\system\components\edges\data-table-simple.blade.php
[01:49:44] Backed up: .\system\components\edges\data-table-simple.edge.php
[01:49:44] Backed up: .\system\components\edges\data-table.blade.php
[01:49:44] Backed up: .\system\components\edges\data-table.edge.php
[01:49:44] Backed up: .\system\components\edges\data-table.fn.php
[01:49:44] Backed up: .\system\components\edges\data-table.group.blade.php
[01:49:44] Backed up: .\system\components\edges\data-table.group.edge.php
[01:49:44] Backed up: .\system\components\edges\email-send-rules-widget.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-button.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-checkbox-toggle.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-description-toggle.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-field-with-description.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-form.blade.php
[01:49:44] Backed up: .\system\components\edges\forms-form.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-input-button.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-input-select-button.blade.php
[01:49:44] Backed up: .\system\components\edges\forms-input-select-button.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-input-select-insertbutton.blade.php
[01:49:44] Backed up: .\system\components\edges\forms-input-select-insertbutton.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-input-select.blade.php
[01:49:44] Backed up: .\system\components\edges\forms-input-select.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-input.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-radio-group.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-radio.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-select-invisible.blade.php
[01:49:44] Backed up: .\system\components\edges\forms-select-invisible.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-select-with-icons.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-select.blade.php
[01:49:44] Backed up: .\system\components\edges\forms-select.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-template-selector.edge.php
[01:49:44] Backed up: .\system\components\edges\forms-textarea.edge.php
[01:49:44] Backed up: .\system\components\edges\hilt-settings.edge.php
[01:49:44] Backed up: .\system\components\edges\html-div.blade.php
[01:49:44] Backed up: .\system\components\edges\html-div.edge.php
[01:49:44] Backed up: .\system\components\edges\htmx-progress-bar.edge.php
[01:49:44] Backed up: .\system\components\edges\json-editor.edge.php
[01:49:44] Backed up: .\system\components\edges\json-viewer.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-api.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-blank.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-box.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-card.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-footer.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-head.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-main.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-modal_data_display.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-modal_data_display.php
[01:49:44] Backed up: .\system\components\edges\layout-modal_data_edit.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-sidebar.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-view.edge.php
[01:49:44] Backed up: .\system\components\edges\layout-white_box_rounded.edge.php
[01:49:44] Backed up: .\system\components\edges\nav-entry-form.bak.php
[01:49:44] Backed up: .\system\components\edges\nav-entry-form.edge.php
[01:49:44] Backed up: .\system\components\edges\nav-tree.edge.php
[01:49:44] Backed up: .\system\components\edges\navbar.edge.php
[01:49:44] Backed up: .\system\components\edges\notification-dropdown.edge.php
[01:49:44] Backed up: .\system\components\edges\notification-handler.blade.php
[01:49:44] Backed up: .\system\components\edges\notification-handler.edge.php
[01:49:44] Backed up: .\system\components\edges\pagination-strip.blade.php
[01:49:44] Backed up: .\system\components\edges\pagination-strip.edge.php
[01:49:44] Backed up: .\system\components\edges\popover-box.edge.php
[01:49:44] Backed up: .\system\components\edges\select-with-icons.edge.php
[01:49:44] Backed up: .\system\components\edges\table-editable.blade.php
[01:49:44] Backed up: .\system\components\edges\table-editable.edge.php
[01:49:44] Backed up: .\system\components\edges\template-container.edge.php
[01:49:44] Backed up: .\system\components\edges\template-selector.edge.php
[01:49:44] Backed up: .\system\components\edges\template-viewer.edge.php
[01:49:44] Backed up: .\system\components\edges\test.php
[01:49:44] Backed up: .\system\components\edges\user-modal.blade.php
[01:49:44] Backed up: .\system\components\edges\user-modal.edge.php
[01:49:44] Backed up: .\system\components\icons.php
[01:49:44] Backed up: .\system\components\icons_raw.php
[01:49:44] Backed up: .\system\config\config.php
[01:49:44] Backed up: .\system\config\db_config.php
[01:49:44] Backed up: .\system\config\db_config_local.php
[01:49:44] Backed up: .\system\config\path_definitions.php
[01:49:44] Backed up: .\system\config\path_schema.php
[01:49:44] Backed up: .\system\config\routes.php
[01:49:44] Backed up: .\system\config\webhook_config.php
[01:49:44] Backed up: .\system\config.php
[01:49:44] Backed up: .\system\csv-uploader.edge.php
[01:49:44] Backed up: .\system\db_config.php
[01:49:44] Backed up: .\system\db_config_local.php
[01:49:44] Backed up: .\system\definitions\data_type_definitions.php
[01:49:44] Backed up: .\system\functions\components.fn.php
[01:49:44] Backed up: .\system\functions\database.php
[01:49:44] Backed up: .\system\functions\enhanced_data_callbacks.php
[01:49:44] Backed up: .\system\functions\functions.php
[01:49:44] Backed up: .\system\functions\notifications.fn.php
[01:49:44] Backed up: .\system\functions\path_utils.php
[01:49:44] Backed up: .\system\functions\tcs_components_v2.php
[01:49:44] Backed up: .\system\functions\users.fn.php
[01:49:44] Backed up: .\system\generate_ide_helper.php
[01:49:44] Backed up: .\system\paths.php
[01:49:44] Backed up: .\system\routes.php
[01:49:44] Backed up: .\system\route_permissions.php
[01:49:44] Backed up: .\system\startup_sequence.php
[01:49:44] Backed up: .\system\startup_sequence_minimal.php
[01:49:44] Backed up: .\system\templates\custom_html_template.hilt.php
[01:49:44] Backed up: .\system\templates\data_table_template.api.php
[01:49:44] Backed up: .\system\templates\data_table_template.hilt.php
[01:49:44] Backed up: .\system\templates\default_template.hilt.php
[01:49:44] Backed up: .\system\templates\default_template.temp.php
[01:49:44] Backed up: .\system\templates\file_upload_template.hilt.php
[01:49:44] Backed up: .\system\views\admin\update_autodesk_hashes.view.php
[01:49:44] Backed up: .\system\views\api\update_autodesk_hashes.php
[01:49:44] Backed up: .\system\views\login\login.view.php
[01:49:44] Backed up: .\system\views\login\reset-password.view.php
[01:49:44] Backed up: .\system\views\logs\logs.api.php
[01:49:44] Backed up: .\system\views\logs\logs.fn.php
[01:49:44] Backed up: .\system\views\logs\logs.view.php
[01:49:44] Backed up: .\system\views\logs\view.view.php
[01:49:44] Backed up: .\system\views\notifications\notification-badge.php
[01:49:44] Backed up: .\system\views\notifications\notification-list.php
[01:49:44] Backed up: .\system\views\notifications\notification-preferences.php
[01:49:44] Backed up: .\system\views\notifications\notifications.view.php
[01:49:44] Backed up: .\system\views\settings.api.php
[01:49:44] Backed up: .\system\views\system\logs\logs.api.php
[01:49:44] Backed up: .\system\views\system\logs\logs.fn.php
[01:49:44] Backed up: .\system\views\system\logs\logs.view.php
[01:49:44] Backed up: .\system\views\system\logs\view.view.php
[01:49:44] Backed up: .\system\views\system\system.api.php
[01:49:44] Backed up: .\system\views\system\system.edge.php
[01:49:44] Backed up: .\system\views\system.api.php
[01:49:44] Backed up: .\system\views\system.edge.php
[01:49:44] Backed up: .\system\views\template.view.php
[01:49:44] Backed up: .\system\views\test.edge.php
[01:49:44] Backed up: .\system\views\update_autodesk_hashes.api.php
[01:49:44] Backed up: .\system\views\update_autodesk_hashes.view.php
[01:49:44] Backed up: .\system\views\users\users.api.php
[01:49:44] Backed up: .\system\views\users\users.view.php
[01:49:44] Backed up: .\system\webhook_config.php
[01:49:44] Backed up: .\test_clean_enhanced_import.php
[01:49:44] Backed up: .\test_database_methods.php
[01:49:44] Backed up: .\test_enhanced_database_logging.php
[01:49:44] Backed up: .\test_enhanced_import.php
[01:49:44] Backed up: .\test_hilt_system.php
[01:49:44] Backed up: .\test_sql_generation.php
[01:49:44] Backed up: .\test_table_creation.php
[01:49:44] Backed up: .\test_table_existence.php
[01:49:44] Backed up: .\test_template_fix.php
[01:49:44] Backed up: .\update_path_usage.php
[01:49:44] Updated 4 patterns in: .\update_path_usage.php
[01:49:44] Backed up: .\webhook_autodesk_subscription.php
[01:49:44] Updated 2 patterns in: .\webhook_autodesk_subscription.php
[01:49:44] Backed up: .\_ide_helper_constants.php
[01:49:44] 
Update complete!
[01:49:44] Files processed: 653
[01:49:44] Files updated: 653
[01:49:44] Errors: 0
