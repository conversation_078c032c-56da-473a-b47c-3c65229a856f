<?php
/**
 * Simple database constraint fix for data_table_storage
 * Access via browser: http://localhost/autobooks/simple_database_fix.php
 */

header('Content-Type: text/plain');

echo "=== Simple Database Constraint Fix ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    echo "2. Fixing database constraint...\n";
    
    // Step 1: Drop the problematic unique constraint
    try {
        system\database::rawQuery("ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name");
        echo "✅ Dropped old unique constraint on table_name\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "check that column/key exists") !== false) {
            echo "ℹ️ Old constraint already removed\n";
        } else {
            echo "⚠️ Error dropping old constraint: " . $e->getMessage() . "\n";
        }
    }
    
    // Step 2: Clean up any duplicate data first
    echo "\n3. Cleaning up duplicate data...\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    // Count existing configurations
    $existing_configs = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->get();
    
    echo "Found " . count($existing_configs) . " existing configurations for '{$table_name}'\n";
    
    if (count($existing_configs) > 0) {
        // Delete all existing configurations for this table
        system\database::table('autobooks_data_table_storage')
            ->where('table_name', $table_name)
            ->delete();
        
        echo "✅ Cleaned up all existing configurations for '{$table_name}'\n";
    }
    
    // Step 3: Add the new composite unique constraint
    try {
        system\database::rawQuery("ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)");
        echo "✅ Added new composite unique constraint on (table_name, user_id)\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "Duplicate key name") !== false) {
            echo "ℹ️ New constraint already exists\n";
        } else {
            echo "❌ Error adding new constraint: " . $e->getMessage() . "\n";
        }
    }
    
    // Step 4: Verify the fix
    echo "\n4. Verifying the fix...\n";
    
    // Test creating configurations
    $test_config = [
        'structure' => [
            ['id' => 'test_col', 'label' => 'Test', 'field' => 'test', 'filter' => true]
        ],
        'columns' => [
            ['id' => 'test_col', 'label' => 'Test', 'field' => 'test', 'filter' => true]
        ],
        'data_source_type' => 'data_source',
        'data_source_id' => 75,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Test global configuration (user_id = null)
    try {
        $global_result = system\data_table_storage::save_configuration($table_name, $test_config, null, 75);
        if ($global_result) {
            echo "✅ Global configuration saved successfully\n";
        } else {
            echo "❌ Failed to save global configuration\n";
        }
    } catch (Exception $e) {
        echo "❌ Error saving global configuration: " . $e->getMessage() . "\n";
    }
    
    // Test user-specific configuration (user_id = 2)
    try {
        $user_result = system\data_table_storage::save_configuration($table_name, $test_config, 2, 75);
        if ($user_result) {
            echo "✅ User-specific configuration saved successfully\n";
        } else {
            echo "❌ Failed to save user-specific configuration\n";
        }
    } catch (Exception $e) {
        echo "❌ Error saving user-specific configuration: " . $e->getMessage() . "\n";
    }
    
    // Verify both configurations exist
    $final_configs = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->get();
    
    echo "\nFinal verification:\n";
    echo "Found " . count($final_configs) . " configurations for '{$table_name}':\n";
    
    foreach ($final_configs as $config) {
        echo "- ID: {$config['id']}, User ID: " . ($config['user_id'] ?? 'null') . ", Data Source ID: {$config['data_source_id']}\n";
    }
    
    // Clean up test configurations
    system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->delete();
    
    echo "✅ Test configurations cleaned up\n";
    
    echo "\n=== Fix Complete ===\n";
    
    if (count($final_configs) == 2) {
        echo "✅ Database constraint fix successful!\n";
        echo "✅ Both global and user-specific configurations can now coexist\n";
        echo "✅ The duplicate entry error should be resolved\n";
        
        echo "\nNext steps:\n";
        echo "1. Try importing a CSV file - should complete successfully\n";
        echo "2. Open the SketchUp view - should display without duplicate entry errors\n";
        echo "3. Run the configuration fix script to create proper column data\n";
    } else {
        echo "⚠️ Fix may not be complete - expected 2 test configurations, found " . count($final_configs) . "\n";
        echo "Check the error messages above for details\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Fix ===\n";
?>
