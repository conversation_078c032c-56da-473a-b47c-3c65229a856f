-- Manual SQL Script to Fix Auto-Increment Issue on autobooks_navigation Table
-- Run these commands in your MySQL/phpMyAdmin interface

-- Step 1: Check if table exists and show current structure
SELECT 'Checking table structure...' as status;
SHOW CREATE TABLE autobooks_navigation;

-- Step 2: Create backup table with timestamp
SET @backup_table = CONCAT('autobooks_navigation_backup_', DATE_FORMAT(NOW(), '%Y_%m_%d_%H_%i_%s'));
SET @sql = CONCAT('CREATE TABLE ', @backup_table, ' AS SELECT * FROM autobooks_navigation');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CONCAT('Backup created as: ', @backup_table) as status;

-- Step 3: Check for duplicate IDs and data integrity
SELECT 'Checking data integrity...' as status;
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT id) as unique_ids,
    COUNT(*) - COUNT(DISTINCT id) as duplicates
FROM autobooks_navigation;

-- Show any duplicate entries
SELECT 'Duplicate entries (if any):' as status;
SELECT parent_path, route_key, COUNT(*) as count
FROM autobooks_navigation 
GROUP BY parent_path, route_key 
HAVING COUNT(*) > 1;

-- Step 4: Show current data before modification
SELECT 'Current data sample:' as status;
SELECT id, parent_path, route_key, name, icon 
FROM autobooks_navigation 
ORDER BY COALESCE(id, 0), parent_path, route_key 
LIMIT 10;

-- Step 5: Drop the existing table and recreate with proper structure
DROP TABLE autobooks_navigation;

-- Step 6: Create new table with proper auto-increment structure
CREATE TABLE `autobooks_navigation` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_path` varchar(255) NOT NULL DEFAULT '' COMMENT 'Parent path for hierarchical navigation',
  `route_key` varchar(100) NOT NULL COMMENT 'Unique route identifier within parent path',
  `name` varchar(100) NOT NULL COMMENT 'Display name for the navigation item',
  `icon` varchar(50) DEFAULT NULL COMMENT 'Icon identifier for the navigation item',
  `required_roles` json DEFAULT NULL COMMENT 'JSON array of roles required to access this route',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Sort order for navigation items within the same parent',
  `show_navbar` tinyint(1) DEFAULT 1 COMMENT 'Whether to show this item in the navigation bar',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_route` (`parent_path`, `route_key`) COMMENT 'Ensure unique route within parent path',
  KEY `idx_parent_path` (`parent_path`),
  KEY `idx_route_key` (`route_key`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Navigation menu structure and routing';

SELECT 'New table created with auto-increment enabled.' as status;

-- Step 7: Restore data from backup, removing duplicates and assigning new sequential IDs
-- This uses a complex query to handle duplicates by keeping only the first occurrence
SET @sql = CONCAT('
INSERT INTO autobooks_navigation 
(parent_path, route_key, name, icon, required_roles, sort_order, show_navbar, created_at, updated_at)
SELECT DISTINCT
    COALESCE(parent_path, \'\') as parent_path,
    route_key,
    name,
    icon,
    required_roles,
    COALESCE(sort_order, 0) as sort_order,
    COALESCE(show_navbar, 1) as show_navbar,
    COALESCE(created_at, NOW()) as created_at,
    COALESCE(updated_at, NOW()) as updated_at
FROM ', @backup_table, '
GROUP BY parent_path, route_key
ORDER BY COALESCE(id, 0), parent_path, route_key
');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 8: Verify the restoration
SELECT 'Data restoration completed. Verification:' as status;

SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM autobooks_navigation;

-- Show sample of restored data
SELECT 'Sample of restored data:' as status;
SELECT id, parent_path, route_key, name, icon 
FROM autobooks_navigation 
ORDER BY id 
LIMIT 10;

-- Step 9: Test auto-increment functionality
INSERT INTO autobooks_navigation (parent_path, route_key, name, icon, required_roles) 
VALUES ('test', CONCAT('test_route_', UNIX_TIMESTAMP()), 'Test Entry', 'test', '[]');

SELECT 'Test insert completed. Last inserted ID:' as status, LAST_INSERT_ID() as new_id;

-- Clean up test entry
DELETE FROM autobooks_navigation WHERE route_key LIKE 'test_route_%' AND parent_path = 'test';

SELECT 'Test entry cleaned up.' as status;

-- Step 10: Final verification
SELECT 'Final verification - table structure:' as status;
SHOW CREATE TABLE autobooks_navigation;

SELECT 'Final record count:' as status, COUNT(*) as total_records FROM autobooks_navigation;

-- Show backup table name for reference
SET @backup_info = CONCAT('Backup table created: ', @backup_table, ' - You can drop it later if everything works correctly.');
SELECT @backup_info as backup_info;

SELECT '✅ AUTO_INCREMENT fix completed successfully!' as final_status;
