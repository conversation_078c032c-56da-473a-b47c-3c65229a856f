<?php
/**
 * Simple web script to fix data sources for existing CSV tables
 */

// Include the main application
require_once 'index.php';

use system\data_source_manager;
use system\unified_field_mapper;
use system\database;

$table_name = $_GET['table'] ?? 'autobooks_import_bluebeam_data';
$action = $_GET['action'] ?? 'preview';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Data Source</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Fix Data Source for <?= htmlspecialchars($table_name) ?></h1>
        
        <?php
        try {
            // Get table information
            $table_info = data_source_manager::get_table_info($table_name);
            
            if (!$table_info) {
                throw new Exception("Table {$table_name} not found");
            }
            
            // Get column names
            $column_names = [];
            foreach ($table_info['columns'] as $column) {
                if (!in_array($column['Field'], ['id', 'created_at', 'updated_at'])) {
                    $column_names[] = $column['Field'];
                }
            }
            
            // Generate field mapping suggestions
            $suggestions = unified_field_mapper::suggest_field_mappings($column_names);
            
            echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
            echo "<h2 class='text-xl font-semibold mb-4'>Table Analysis</h2>";
            echo "<p><strong>Table:</strong> {$table_name}</p>";
            echo "<p><strong>Rows:</strong> {$table_info['row_count']}</p>";
            echo "<p><strong>Columns:</strong> " . count($column_names) . "</p>";
            echo "</div>";
            
            echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
            echo "<h2 class='text-xl font-semibold mb-4'>Field Mapping Suggestions</h2>";
            
            $applied_count = 0;
            foreach ($suggestions as $column_name => $suggestion) {
                $confidence = $suggestion['confidence'];
                $will_apply = $confidence >= 75;
                if ($will_apply) $applied_count++;
                
                $bg_color = $will_apply ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200';
                $text_color = $will_apply ? 'text-green-800' : 'text-yellow-800';
                
                echo "<div class='p-3 rounded border {$bg_color} mb-2'>";
                echo "<div class='flex justify-between items-center'>";
                echo "<div>";
                echo "<strong>{$column_name}</strong> → {$suggestion['field_name']} ";
                echo "<span class='text-sm {$text_color}'>(confidence: {$confidence}%, score: {$suggestion['final_score']})</span>";
                echo "</div>";
                echo "<div class='text-xs " . ($will_apply ? 'text-green-600' : 'text-yellow-600') . "'>";
                echo $will_apply ? '✓ Will Apply' : '⚠ Below Threshold';
                echo "</div>";
                echo "</div>";
                echo "<div class='text-xs text-gray-600 mt-1'>Maps to: " . implode(', ', $suggestion['normalized_fields']) . "</div>";
                echo "</div>";
            }
            
            echo "<p class='mt-4'><strong>Applied mappings:</strong> {$applied_count} out of " . count($suggestions) . "</p>";
            echo "</div>";
            
            if ($action === 'fix') {
                echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6'>";
                echo "<h2 class='text-xl font-semibold mb-4'>Fixing Data Source...</h2>";
                
                // Build unified mappings
                $unified_mappings = [
                    'min_confidence' => 75,
                    'applied' => [],
                    'overrides' => []
                ];
                
                foreach ($suggestions as $column_name => $suggestion) {
                    if ($suggestion['confidence'] >= 75) {
                        $unified_mappings['applied'][$column_name] = [
                            'category' => $suggestion['field_name'],
                            'field_name' => $suggestion['field_name'],
                            'confidence' => $suggestion['confidence'],
                            'final_score' => $suggestion['final_score'],
                            'normalized_fields' => $suggestion['normalized_fields']
                        ];
                    }
                }
                
                // Delete old data sources for this table
                $old_sources = database::table('autobooks_data_sources')
                    ->where('table_name', $table_name)
                    ->get();
                
                foreach ($old_sources as $old_source) {
                    echo "<p>Deleting old data source ID {$old_source['id']}: {$old_source['name']}</p>";
                    database::table('autobooks_data_sources')
                        ->where('id', $old_source['id'])
                        ->delete();
                }
                
                // Create new data source
                $data_source_config = [
                    'name' => 'CSV Import: Bluebeam Data (Enhanced)',
                    'table_name' => $table_name,
                    'description' => "Enhanced data source with unified field mapping ({$table_info['row_count']} rows, " . count($column_names) . " columns)",
                    'category' => 'csv_import',
                    'data_source_type' => 'multi_table_merger',
                    'mapping_method' => 'unified_field_mapper',
                    'resolved_tables' => [$table_name],
                    'unified_mappings' => $unified_mappings,
                    'tables' => [$table_name],
                    'status' => 'active'
                ];
                
                $data_source_id = data_source_manager::create_data_source($data_source_config);
                
                echo "<p class='text-green-600 font-semibold'>✅ Created new data source ID: {$data_source_id}</p>";
                
                // Test column aliases
                $aliases = data_source_manager::generate_unified_field_aliases($table_name, $unified_mappings);
                
                echo "<h3 class='text-lg font-semibold mt-4 mb-2'>Generated Column Aliases:</h3>";
                if (!empty($aliases)) {
                    echo "<ul class='list-disc list-inside'>";
                    foreach ($aliases as $original => $selected) {
                        echo "<li><strong>{$original}</strong> AS <em>{$original}_AS_{$selected}</em></li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p>No aliases generated</p>";
                }
                
                echo "</div>";
                
                echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>";
                echo "<h2 class='text-xl font-semibold text-green-800 mb-2'>✅ Fix Complete!</h2>";
                echo "<p class='text-green-700'>The data source has been updated with unified field mapping and column aliases.</p>";
                echo "<p class='text-green-700 mt-2'>Refresh your data table to see the enhanced field mappings!</p>";
                echo "</div>";
                
            } else {
                echo "<div class='bg-white rounded-lg shadow p-6'>";
                echo "<h2 class='text-xl font-semibold mb-4'>Actions</h2>";
                echo "<a href='?table={$table_name}&action=fix' class='bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 inline-block'>Fix Data Source</a>";
                echo "<p class='text-sm text-gray-600 mt-2'>This will replace the existing basic data source with an enhanced one that includes unified field mapping and column aliases.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>";
            echo "<h2 class='text-xl font-semibold text-red-800 mb-2'>❌ Error</h2>";
            echo "<p class='text-red-700'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
