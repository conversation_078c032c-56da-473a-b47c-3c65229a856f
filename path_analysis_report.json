{"system\\classes\\router.class.php": [{"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", "line": 235, "line_content": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\",", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}, {"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.edge.php\"", "line": 236, "line_content": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.edge.php\",", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}, {"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}.view.php\"", "line": 237, "line_content": "FS_SYS_VIEWS . \"/{$system_view}.view.php\",", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}, {"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}.edge.php\"", "line": 238, "line_content": "FS_SYS_VIEWS . \"/{$system_view}.edge.php\"", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}], "system\\components\\edges\\email-send-rules-widget.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", "line": 30, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/email_history\"", "full_matches": [["{{ APP_ROOT }}/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1308], ["APP_ROOT", 1311], ["/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1322]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/email_history\"\n        hx-include=\"#new_rule\"\n        hx-target=\"#rules_buttons\"\n        hx-swap=\"outerHTML\"\n        hx-vals='", "line": 51, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/email_history\"", "full_matches": [["{{ APP_ROOT }}/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1308], ["APP_ROOT", 1311], ["/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1322]]}], "system\\components\\edges\\hilt-settings.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", "line": 46, "line_content": "<form hx-post=\"{{ APP_ROOT }}/api/hilt_settings/upload_csv\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/hilt_settings/clear_data\"\n                                    hx-target=\"#table-summary\"\n                                    hx-vals='", "line": 93, "line_content": "hx-post=\"{{ APP_ROOT }}/api/hilt_settings/clear_data\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}api/hilt_settings/export_csv?route_key=", "line": 111, "line_content": "<a href=\"{{ APP_ROOT }}api/hilt_settings/export_csv?route_key={{ $route_key }}\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/navigation\" \n                           class=\"inline-block text-blue-600 hover:text-blue-800 text-sm\">\n                            → Back to Navigation\n                        </a>\n                    </div>\n                </div>\n            </div>\n\n            ", "line": 188, "line_content": "<a href=\"{{ APP_ROOT }}/navigation\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}], "system\\components\\edges\\layout-head.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/system/img/favicon.ico\" />\n<script src=\"https://cdn.tailwindcss.com\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\n\n<script>\n    // Define APP_ROOT for JavaScript files\n    var APP_ROOT = '<?= APP_ROOT ?>';\n\n    // Navigation tree initialization function\n    function initNavTree() ", "line": 6, "line_content": "<link rel=\"shortcut icon\" href=\"{{ APP_ROOT }}/system/img/favicon.ico\" />", "full_matches": [["{{ APP_ROOT }}/system/img/favicon.ico\" />\n<script src=\"https://cdn.tailwindcss.com\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\n\n<script>\n    // Define APP_ROOT for JavaScript files\n    var APP_ROOT = '<?= APP_ROOT ?>';\n\n    // Navigation tree initialization function\n    function initNavTree() ", 201], ["APP_ROOT", 204], ["/system/img/favicon.ico\" />\n<script src=\"https://cdn.tailwindcss.com\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\n\n<script>\n    // Define APP_ROOT for JavaScript files\n    var APP_ROOT = '<?= APP_ROOT ?>';\n\n    // Navigation tree initialization function\n    function initNavTree() ", 215]]}], "system\\components\\edges\\nav-entry-form.bak.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", "line": 94, "line_content": "<link rel=\"stylesheet\" href=\"{{ APP_ROOT }}/resources/css/htmx-indicators.css\">", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2443], ["APP_ROOT", 2446], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2457]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"\n            hx-target=\"#nav-entry-progress-container\"\n            hx-swap=\"innerHTML\"\n            hx-on::before-request=\"document.getElementById('nav-entry-progress-container').style.display = 'block'; this.style.display = 'none';\"\n            @submit=\"$dispatch('hide-modal')\"\n            class=\"space-y-4\"\n            x-data=\"", "line": 105, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2443], ["APP_ROOT", 2446], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2457]]}], "system\\components\\edges\\nav-entry-form.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", "line": 86, "line_content": "<link rel=\"stylesheet\" href=\"{{ APP_ROOT }}/resources/css/htmx-indicators.css\">", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2555], ["APP_ROOT", 2558], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2569]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"\n        hx-target=\"#nav-entry-progress-container\"\n        hx-swap=\"innerHTML\"\n        hx-trigger=\"submit\"\n        hx-on::before-request=\"if(event.detail.elt.tagName === 'FORM') ", "line": 97, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2555], ["APP_ROOT", 2558], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2569]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/get_template_fields\"\n                    hx-target=\"#template-specific-fields\"\n                    hx-swap=\"innerHTML\"\n                    hx-trigger=\"change\"\n                    hx-include=\"this\"\n                    onchange=\"document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type\"\n                    class=\"block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6\"\n            >\n                @foreach($templates as $key => $template)\n                    @php\n                        $isDefault = $key === 'default_template';\n                    @endphp\n                    <option value=\"", "line": 113, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/get_template_fields\"", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2555], ["APP_ROOT", 2558], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2569]]}], "system\\components\\edges\\nav-tree.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", "line": 44, "line_content": "hx-delete=\"{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2650], ["APP_ROOT", 2653], ["/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2664]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/add_nav_entry\"\n                hx-vals='", "line": 65, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/add_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2650], ["APP_ROOT", 2653], ["/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2664]]}], "system\\components\\edges\\navbar.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", "line": 42, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/toggle_debug_mode\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/settings\"\n                    hx-replace-url=\"", "line": 68, "line_content": "hx-post=\"{{ APP_ROOT }}/settings\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/profile\"\n                       class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                       role=\"menuitem\"\n                       tabindex=\"-1\"\n                       hx-get=\"", "line": 107, "line_content": "<a href=\"{{ APP_ROOT }}/profile\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/profile\"\n                       hx-target=\"#content_wrapper\"\n                       hx-push-url=\"true\">\n                        Your Profile (", "line": 111, "line_content": "hx-get=\"{{ APP_ROOT }}/profile\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/users\"\n                           class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                           role=\"menuitem\"\n                           tabindex=\"-1\"\n                           hx-get=\"", "line": 119, "line_content": "<a href=\"{{ APP_ROOT }}/users\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/users\"\n                           hx-target=\"#content_wrapper\"\n                           hx-replace-url=\"", "line": 123, "line_content": "hx-get=\"{{ APP_ROOT }}/users\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/users\"\n                           hx-push-url=\"true\">\n                            User Management\n                        </a>\n                    @endif\n\n                    <div class=\"border-t border-gray-200 my-1\"></div>\n                    <a href=\"", "line": 125, "line_content": "hx-replace-url=\"{{ APP_ROOT }}/users\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/settings\"\n                       class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                       role=\"menuitem\"\n                       tabindex=\"-1\"\n                       hx-get=\"", "line": 132, "line_content": "<a href=\"{{ APP_ROOT }}/settings\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/settings\"\n                       hx-target=\"#content_wrapper\"\n                       hx-push-url=\"true\">\n                        Settings\n                    </a>\n                    <a href=\"", "line": 136, "line_content": "hx-get=\"{{ APP_ROOT }}/settings\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/logout\"\n                       class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                       role=\"menuitem\"\n                       tabindex=\"-1\"\n                       hx-post=\"", "line": 141, "line_content": "<a href=\"{{ APP_ROOT }}/logout\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/logout\"\n                       hx-target=\"body\"\n                       hx-push-url=\"true\"\n                       hx-redirect=\"", "line": 145, "line_content": "hx-post=\"{{ APP_ROOT }}/logout\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/login\">\n                        Sign out\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>", "line": 148, "line_content": "hx-redirect=\"{{ APP_ROOT }}/login\">", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}], "system\\components\\edges\\notification-dropdown.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", "line": 23, "line_content": "hx-get=\"{{ APP_ROOT }}/api/system/notifications/get_unread_count\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/notifications\"\n                   class=\"text-xs text-blue-600 hover:text-blue-800\"\n                   hx-get=\"", "line": 47, "line_content": "<a href=\"{{ APP_ROOT }}/notifications\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/notifications\"\n                   hx-target=\"#content_wrapper\"\n                   hx-push-url=\"true\">\n                    View all\n                </a>\n            </div>\n        </div>\n\n        <!-- Notification list -->\n        <div id=\"notification-list-container\"\n             hx-get=\"", "line": 49, "line_content": "hx-get=\"{{ APP_ROOT }}/notifications\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/notifications/get_notifications\"\n             hx-trigger=\"load, notification-updated from:body\"\n             hx-swap=\"innerHTML\">\n            <!-- Notification list will be loaded here -->\n        </div>\n    </div>\n</div>\n\n<!-- SSE connection for real-time notifications -->\n<div hx-ext=\"sse\"\n     sse-connect=\"", "line": 59, "line_content": "hx-get=\"{{ APP_ROOT }}/api/system/notifications/get_notifications\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/notifications/sse\"\n     sse-swap=\"none\"\n     class=\"hidden\">\n    <div sse-swap=\"count\"\n         hx-trigger=\"sse:count\"\n         @sse-message=\"\n            const data = JSON.parse(event.detail.data);\n            if (data.count > 0) ", "line": 69, "line_content": "sse-connect=\"{{ APP_ROOT }}/api/system/notifications/sse\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}], "system\\components\\edges\\template-container.edge.php": [{"pattern": "fs_constant_concat", "match": "FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", "line": 11, "line_content": "$template_exists = file_exists(FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\");", "full_matches": [["FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", 338], ["FS_APP_ROOT", 338], ["resources/components/templates/{$template}.edge.php", 353]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", "line": 11, "line_content": "$template_exists = file_exists(FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\");", "full_matches": [["APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", 341], ["APP_ROOT", 341], ["resources/components/templates/{$template}.edge.php", 353]]}], "system\\functions\\components.fn.php": [{"pattern": "fs_constant_concat", "match": "FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'", "line": 72, "line_content": "//echo \"<br>cached version doesn't exist, compiling... from '\" . FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'<br>\\n\";", "full_matches": [["FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'", 2845], ["FS_APP_ROOT", 2845], ["resources/components/{$component_name}.blade.php", 2860]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"resources/components/{$component_name}.blade.php'", "line": 72, "line_content": "//echo \"<br>cached version doesn't exist, compiling... from '\" . FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'<br>\\n\";", "full_matches": [["APP_ROOT . \"resources/components/{$component_name}.blade.php'", 2848], ["APP_ROOT", 2848], ["resources/components/{$component_name}.blade.php", 2860]]}], "system\\templates\\default_template.hilt.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/navigation\" \n                           class=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Navigation\n                        </a>\n                        <a href=\"", "line": 109, "line_content": "<a href=\"{{ APP_ROOT }}/navigation\"", "full_matches": [["{{ APP_ROOT }}/navigation\" \n                           class=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Navigation\n                        </a>\n                        <a href=\"", 6375], ["APP_ROOT", 6378], ["/navigation\" \n                           class=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Navigation\n                        </a>\n                        <a href=\"", 6389]]}], "system\\templates\\file_upload_template.hilt.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Templates\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"template-info mt-6\">\n        <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">File Upload Template Information</h3>\n            <div class=\"text-sm text-blue-700\">\n                <p><strong>Route:</strong> ", "line": 31, "line_content": "<a href=\"{{ APP_ROOT }}/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">", "full_matches": [["{{ APP_ROOT }}/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Templates\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"template-info mt-6\">\n        <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">File Upload Template Information</h3>\n            <div class=\"text-sm text-blue-700\">\n                <p><strong>Route:</strong> ", 1541], ["APP_ROOT", 1544], ["/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Templates\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"template-info mt-6\">\n        <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">File Upload Template Information</h3>\n            <div class=\"text-sm text-blue-700\">\n                <p><strong>Route:</strong> ", 1555]]}], "resources\\components\\edges\\subscription-table-config.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", "line": 9, "line_content": "<form hx-post=\"{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">", "full_matches": [["{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 248], ["APP_ROOT", 251], ["/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 262]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/add_replacement_row\"\n                        hx-target=\"#replacements_container\"\n                        hx-swap=\"beforeend\">\n                    Add Replacement\n                </button>\n            </div>\n        </div>\n\n        <!-- Columns Configuration -->\n        <div x-data=\"", "line": 30, "line_content": "hx-post=\"{{ APP_ROOT }}/api/add_replacement_row\"", "full_matches": [["{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 248], ["APP_ROOT", 251], ["/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 262]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/add_column_row\"\n                        hx-target=\"#columns_container\"\n                        hx-swap=\"beforeend\"\n                        hx-vals='", "line": 63, "line_content": "hx-post=\"{{ APP_ROOT }}/api/add_column_row\"", "full_matches": [["{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 248], ["APP_ROOT", 251], ["/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 262]]}], "resources\\components\\edges\\subscriptions-email-settings.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", "line": 85, "line_content": "hx-post=\"{{ APP_ROOT }}/api/update_days_to_send\"", "full_matches": [["{{ APP_ROOT }}/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2817], ["APP_ROOT", 2820], ["/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2831]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/update_time_to_send\">\n                    <span class=\"ml-2 text-lg font-bold text-neutral-900 text-black\" x-text=\"currentVal < 10 ? '0' + currentVal : currentVal\">00</span>\n                    <span class=\"text-lg font-bold text-neutral-900 text-black\">:00</span>\n                </div>\n            </div>\n        </div>\n        @php\n            $input = json_encode([\n                    'id' => 'test_email_to',\n                    'name' => 'test_email_to',\n                    'icon' => 'envelope'\n            ]);\n            $button = json_encode([\n                    'id' => 'test_email_send',\n                    'name' => 'test_email_send',\n                    'label' => 'Send',\n                    'icon' => 'envelope',\n                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/send_test_email',\n                    'hx-swap' => 'this',\n                    'hx-target' => 'this',\n                    'hx-include' => '#test_email_to'\n            ])\n        @endphp\n        <div class=\"col-span-4\">\n            <x-forms-input-button-group\n                label=\"Send test email\"\n                :input=\"$input\"\n                :button=\"$button\"\n            />\n        </div>\n    </div>\n    <script>\n        Jodit.defaultOptions.controls.insertPlaceholder = ", "line": 117, "line_content": "hx-post=\"{{ APP_ROOT }}/api/update_time_to_send\">", "full_matches": [["{{ APP_ROOT }}/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2817], ["APP_ROOT", 2820], ["/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2831]]}], "resources\\views\\subscriptions\\email_history\\settings.view.php": [{"pattern": "app_root_concat", "match": "APP_ROOT . \"/api/update_days_to_send'", "line": 47, "line_content": "$footer .= \"<div><label for='days_to_send' class='block text-sm/6 font-medium text-gray-900'>Days To Send: </label></div><div class='isolate inline-flex rounded-md shadow-sm' hx-post='\" . APP_ROOT . \"/api/update_days_to_send' hx-include='#email_send_days' hx-trigger='click delay:2000ms' hx-swap='none'>", "full_matches": [["APP_ROOT . \"/api/update_days_to_send'", 2430], ["APP_ROOT", 2430], ["/api/update_days_to_send", 2442]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"api/update_time_to_send'", "line": 70, "line_content": "<DIV class='flex items-center'><input class='block text-sm/6 font-medium text-gray-900' x-model='currentVal' id='hour_of_day' name='time_to_send' type='range' class='' min='0' max='23' step='1' hx-trigger='change delay 1000ms' hx-post='\" . APP_ROOT . \"api/update_time_to_send'>", "full_matches": [["APP_ROOT . \"/api/update_days_to_send'", 2430], ["APP_ROOT", 2430], ["/api/update_days_to_send", 2442]]}], ".\\index.php": [{"pattern": "fs_app_root_interpolation", "match": "{$path['fs_app_root']}{$schema['system']['root']", "line": 11, "line_content": "$path['fs_system'] = \"{$path['fs_app_root']}{$schema['system']['root']}\" ;", "full_matches": [["{$path['fs_app_root']}{$schema['system']['root']", 493], ["{$schema['system']['root']", 515]]}], ".\\resources\\components\\edges\\subscription-table-config.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", "line": 9, "line_content": "<form hx-post=\"{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">", "full_matches": [["{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 248], ["APP_ROOT", 251], ["/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 262]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/add_replacement_row\"\n                        hx-target=\"#replacements_container\"\n                        hx-swap=\"beforeend\">\n                    Add Replacement\n                </button>\n            </div>\n        </div>\n\n        <!-- Columns Configuration -->\n        <div x-data=\"", "line": 30, "line_content": "hx-post=\"{{ APP_ROOT }}/api/add_replacement_row\"", "full_matches": [["{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 248], ["APP_ROOT", 251], ["/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 262]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/add_column_row\"\n                        hx-target=\"#columns_container\"\n                        hx-swap=\"beforeend\"\n                        hx-vals='", "line": 63, "line_content": "hx-post=\"{{ APP_ROOT }}/api/add_column_row\"", "full_matches": [["{{ APP_ROOT }}/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 248], ["APP_ROOT", 251], ["/api/save_subscription_config\" hx-target=\"#config_status\" class=\"space-y-4\">\n        <!-- Replacements Section -->\n        <div x-data=\"", 262]]}], ".\\resources\\components\\edges\\subscriptions-email-settings.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", "line": 85, "line_content": "hx-post=\"{{ APP_ROOT }}/api/update_days_to_send\"", "full_matches": [["{{ APP_ROOT }}/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2817], ["APP_ROOT", 2820], ["/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2831]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/update_time_to_send\">\n                    <span class=\"ml-2 text-lg font-bold text-neutral-900 text-black\" x-text=\"currentVal < 10 ? '0' + currentVal : currentVal\">00</span>\n                    <span class=\"text-lg font-bold text-neutral-900 text-black\">:00</span>\n                </div>\n            </div>\n        </div>\n        @php\n            $input = json_encode([\n                    'id' => 'test_email_to',\n                    'name' => 'test_email_to',\n                    'icon' => 'envelope'\n            ]);\n            $button = json_encode([\n                    'id' => 'test_email_send',\n                    'name' => 'test_email_send',\n                    'label' => 'Send',\n                    'icon' => 'envelope',\n                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/send_test_email',\n                    'hx-swap' => 'this',\n                    'hx-target' => 'this',\n                    'hx-include' => '#test_email_to'\n            ])\n        @endphp\n        <div class=\"col-span-4\">\n            <x-forms-input-button-group\n                label=\"Send test email\"\n                :input=\"$input\"\n                :button=\"$button\"\n            />\n        </div>\n    </div>\n    <script>\n        Jodit.defaultOptions.controls.insertPlaceholder = ", "line": 117, "line_content": "hx-post=\"{{ APP_ROOT }}/api/update_time_to_send\">", "full_matches": [["{{ APP_ROOT }}/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2817], ["APP_ROOT", 2820], ["/api/update_days_to_send\" \n                 hx-include=\"#email_send_days\" \n                 hx-trigger=\"click delay:2000ms\" \n                 hx-swap=\"none\">\n                <input type=\"hidden\" x-model=\"selectedDays\" id=\"email_send_days\" name=\"days_to_send\" value=\"\">\n                @php\n                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];\n                @endphp\n                @foreach($days as $key => $day)\n                    <button type=\"button\"\n                            name=\"day_", 2831]]}], ".\\resources\\views\\subscriptions\\email_history\\settings.view.php": [{"pattern": "app_root_concat", "match": "APP_ROOT . \"/api/update_days_to_send'", "line": 47, "line_content": "$footer .= \"<div><label for='days_to_send' class='block text-sm/6 font-medium text-gray-900'>Days To Send: </label></div><div class='isolate inline-flex rounded-md shadow-sm' hx-post='\" . APP_ROOT . \"/api/update_days_to_send' hx-include='#email_send_days' hx-trigger='click delay:2000ms' hx-swap='none'>", "full_matches": [["APP_ROOT . \"/api/update_days_to_send'", 2430], ["APP_ROOT", 2430], ["/api/update_days_to_send", 2442]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"api/update_time_to_send'", "line": 70, "line_content": "<DIV class='flex items-center'><input class='block text-sm/6 font-medium text-gray-900' x-model='currentVal' id='hour_of_day' name='time_to_send' type='range' class='' min='0' max='23' step='1' hx-trigger='change delay 1000ms' hx-post='\" . APP_ROOT . \"api/update_time_to_send'>", "full_matches": [["APP_ROOT . \"/api/update_days_to_send'", 2430], ["APP_ROOT", 2430], ["/api/update_days_to_send", 2442]]}], ".\\system\\classes\\router.class.php": [{"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", "line": 235, "line_content": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\",", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}, {"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.edge.php\"", "line": 236, "line_content": "FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.edge.php\",", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}, {"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}.view.php\"", "line": 237, "line_content": "FS_SYS_VIEWS . \"/{$system_view}.view.php\",", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}, {"pattern": "fs_constant_concat", "match": "FS_SYS_VIEWS . \"/{$system_view}.edge.php\"", "line": 238, "line_content": "FS_SYS_VIEWS . \"/{$system_view}.edge.php\"", "full_matches": [["FS_SYS_VIEWS . \"/{$system_view}/{$current_page}.view.php\"", 9875], ["FS_SYS_VIEWS", 9875], ["/{$system_view}/{$current_page}.view.php", 9891]]}], ".\\system\\components\\edges\\email-send-rules-widget.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", "line": 30, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/email_history\"", "full_matches": [["{{ APP_ROOT }}/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1308], ["APP_ROOT", 1311], ["/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1322]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/email_history\"\n        hx-include=\"#new_rule\"\n        hx-target=\"#rules_buttons\"\n        hx-swap=\"outerHTML\"\n        hx-vals='", "line": 51, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/email_history\"", "full_matches": [["{{ APP_ROOT }}/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1308], ["APP_ROOT", 1311], ["/api/system/email_history\"\n                hx-target=\"#rules_buttons\"\n                hx-vals='", 1322]]}], ".\\system\\components\\edges\\hilt-settings.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", "line": 46, "line_content": "<form hx-post=\"{{ APP_ROOT }}/api/hilt_settings/upload_csv\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/hilt_settings/clear_data\"\n                                    hx-target=\"#table-summary\"\n                                    hx-vals='", "line": 93, "line_content": "hx-post=\"{{ APP_ROOT }}/api/hilt_settings/clear_data\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}api/hilt_settings/export_csv?route_key=", "line": 111, "line_content": "<a href=\"{{ APP_ROOT }}api/hilt_settings/export_csv?route_key={{ $route_key }}\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/navigation\" \n                           class=\"inline-block text-blue-600 hover:text-blue-800 text-sm\">\n                            → Back to Navigation\n                        </a>\n                    </div>\n                </div>\n            </div>\n\n            ", "line": 188, "line_content": "<a href=\"{{ APP_ROOT }}/navigation\"", "full_matches": [["{{ APP_ROOT }}/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2111], ["APP_ROOT", 2114], ["/api/hilt_settings/upload_csv\" \n                      hx-target=\"#table-summary\"\n                      hx-encoding=\"multipart/form-data\"\n                      class=\"space-y-4\">\n                    \n                    <input type=\"hidden\" name=\"route_key\" value=\"", 2125]]}], ".\\system\\components\\edges\\layout-head.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/system/img/favicon.ico\" />\n<script src=\"https://cdn.tailwindcss.com\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\n\n<script>\n    // Define APP_ROOT for JavaScript files\n    var APP_ROOT = '<?= APP_ROOT ?>';\n\n    // Navigation tree initialization function\n    function initNavTree() ", "line": 6, "line_content": "<link rel=\"shortcut icon\" href=\"{{ APP_ROOT }}/system/img/favicon.ico\" />", "full_matches": [["{{ APP_ROOT }}/system/img/favicon.ico\" />\n<script src=\"https://cdn.tailwindcss.com\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\n\n<script>\n    // Define APP_ROOT for JavaScript files\n    var APP_ROOT = '<?= APP_ROOT ?>';\n\n    // Navigation tree initialization function\n    function initNavTree() ", 201], ["APP_ROOT", 204], ["/system/img/favicon.ico\" />\n<script src=\"https://cdn.tailwindcss.com\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\n\n<script>\n    // Define APP_ROOT for JavaScript files\n    var APP_ROOT = '<?= APP_ROOT ?>';\n\n    // Navigation tree initialization function\n    function initNavTree() ", 215]]}], ".\\system\\components\\edges\\nav-entry-form.bak.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", "line": 94, "line_content": "<link rel=\"stylesheet\" href=\"{{ APP_ROOT }}/resources/css/htmx-indicators.css\">", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2443], ["APP_ROOT", 2446], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2457]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"\n            hx-target=\"#nav-entry-progress-container\"\n            hx-swap=\"innerHTML\"\n            hx-on::before-request=\"document.getElementById('nav-entry-progress-container').style.display = 'block'; this.style.display = 'none';\"\n            @submit=\"$dispatch('hide-modal')\"\n            class=\"space-y-4\"\n            x-data=\"", "line": 105, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2443], ["APP_ROOT", 2446], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n            hx-post=\"", 2457]]}], ".\\system\\components\\edges\\nav-entry-form.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", "line": 86, "line_content": "<link rel=\"stylesheet\" href=\"{{ APP_ROOT }}/resources/css/htmx-indicators.css\">", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2555], ["APP_ROOT", 2558], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2569]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"\n        hx-target=\"#nav-entry-progress-container\"\n        hx-swap=\"innerHTML\"\n        hx-trigger=\"submit\"\n        hx-on::before-request=\"if(event.detail.elt.tagName === 'FORM') ", "line": 97, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2555], ["APP_ROOT", 2558], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2569]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/get_template_fields\"\n                    hx-target=\"#template-specific-fields\"\n                    hx-swap=\"innerHTML\"\n                    hx-trigger=\"change\"\n                    hx-include=\"this\"\n                    onchange=\"document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type\"\n                    class=\"block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6\"\n            >\n                @foreach($templates as $key => $template)\n                    @php\n                        $isDefault = $key === 'default_template';\n                    @endphp\n                    <option value=\"", "line": 113, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/get_template_fields\"", "full_matches": [["{{ APP_ROOT }}/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2555], ["APP_ROOT", 2558], ["/resources/css/htmx-indicators.css\">\n\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium leading-6 text-gray-900 mb-4\">Add Navigation Entry</h3>\n\n    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->\n    <div id=\"nav-entry-progress-container\" style=\"display: none;\">\n        <!-- Progress bar will be loaded here -->\n    </div>\n\n    <form\n        hx-post=\"", 2569]]}], ".\\system\\components\\edges\\nav-tree.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", "line": 44, "line_content": "hx-delete=\"{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2650], ["APP_ROOT", 2653], ["/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2664]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/nav_tree/add_nav_entry\"\n                hx-vals='", "line": 65, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/nav_tree/add_nav_entry\"", "full_matches": [["{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2650], ["APP_ROOT", 2653], ["/api/system/nav_tree/delete_nav_entry\"\n                    hx-vals='", 2664]]}], ".\\system\\components\\edges\\navbar.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", "line": 42, "line_content": "hx-post=\"{{ APP_ROOT }}/api/system/toggle_debug_mode\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/settings\"\n                    hx-replace-url=\"", "line": 68, "line_content": "hx-post=\"{{ APP_ROOT }}/settings\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/profile\"\n                       class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                       role=\"menuitem\"\n                       tabindex=\"-1\"\n                       hx-get=\"", "line": 107, "line_content": "<a href=\"{{ APP_ROOT }}/profile\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/profile\"\n                       hx-target=\"#content_wrapper\"\n                       hx-push-url=\"true\">\n                        Your Profile (", "line": 111, "line_content": "hx-get=\"{{ APP_ROOT }}/profile\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/users\"\n                           class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                           role=\"menuitem\"\n                           tabindex=\"-1\"\n                           hx-get=\"", "line": 119, "line_content": "<a href=\"{{ APP_ROOT }}/users\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/users\"\n                           hx-target=\"#content_wrapper\"\n                           hx-replace-url=\"", "line": 123, "line_content": "hx-get=\"{{ APP_ROOT }}/users\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/users\"\n                           hx-push-url=\"true\">\n                            User Management\n                        </a>\n                    @endif\n\n                    <div class=\"border-t border-gray-200 my-1\"></div>\n                    <a href=\"", "line": 125, "line_content": "hx-replace-url=\"{{ APP_ROOT }}/users\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/settings\"\n                       class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                       role=\"menuitem\"\n                       tabindex=\"-1\"\n                       hx-get=\"", "line": 132, "line_content": "<a href=\"{{ APP_ROOT }}/settings\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/settings\"\n                       hx-target=\"#content_wrapper\"\n                       hx-push-url=\"true\">\n                        Settings\n                    </a>\n                    <a href=\"", "line": 136, "line_content": "hx-get=\"{{ APP_ROOT }}/settings\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/logout\"\n                       class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                       role=\"menuitem\"\n                       tabindex=\"-1\"\n                       hx-post=\"", "line": 141, "line_content": "<a href=\"{{ APP_ROOT }}/logout\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/logout\"\n                       hx-target=\"body\"\n                       hx-push-url=\"true\"\n                       hx-redirect=\"", "line": 145, "line_content": "hx-post=\"{{ APP_ROOT }}/logout\"", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/login\">\n                        Sign out\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>", "line": 148, "line_content": "hx-redirect=\"{{ APP_ROOT }}/login\">", "full_matches": [["{{ APP_ROOT }}/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2044], ["APP_ROOT", 2047], ["/api/system/toggle_debug_mode\"\n                               hx-trigger=\"change\"\n                               hx-swap=\"none\"\n                               hx-indicator=\"#debug-mode-indicator\"\n                        >\n                        <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        <span class=\"ms-3 text-sm font-medium text-gray-900\">Debug Mode</span>\n                        <!-- Small loading indicator -->\n                        <span id=\"debug-mode-indicator\" class=\"htmx-indicator ml-2\">\n                            <svg class=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\"\n                                 fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\"\n                                        stroke-width=\"4\"></circle>\n                                <path class=\"opacity-75\" fill=\"currentColor\"\n                                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                        </span>\n                    </label>\n                </div>\n            @endif\n\n            <div>\n\n                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )\n                <button type=\"button\" class=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\n                    hx-target=\"#content_wrapper\"\n                    hx-post=\"", 2058]]}], ".\\system\\components\\edges\\notification-dropdown.edge.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", "line": 23, "line_content": "hx-get=\"{{ APP_ROOT }}/api/system/notifications/get_unread_count\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/notifications\"\n                   class=\"text-xs text-blue-600 hover:text-blue-800\"\n                   hx-get=\"", "line": 47, "line_content": "<a href=\"{{ APP_ROOT }}/notifications\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/notifications\"\n                   hx-target=\"#content_wrapper\"\n                   hx-push-url=\"true\">\n                    View all\n                </a>\n            </div>\n        </div>\n\n        <!-- Notification list -->\n        <div id=\"notification-list-container\"\n             hx-get=\"", "line": 49, "line_content": "hx-get=\"{{ APP_ROOT }}/notifications\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/notifications/get_notifications\"\n             hx-trigger=\"load, notification-updated from:body\"\n             hx-swap=\"innerHTML\">\n            <!-- Notification list will be loaded here -->\n        </div>\n    </div>\n</div>\n\n<!-- SSE connection for real-time notifications -->\n<div hx-ext=\"sse\"\n     sse-connect=\"", "line": 59, "line_content": "hx-get=\"{{ APP_ROOT }}/api/system/notifications/get_notifications\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/api/system/notifications/sse\"\n     sse-swap=\"none\"\n     class=\"hidden\">\n    <div sse-swap=\"count\"\n         hx-trigger=\"sse:count\"\n         @sse-message=\"\n            const data = JSON.parse(event.detail.data);\n            if (data.count > 0) ", "line": 69, "line_content": "sse-connect=\"{{ APP_ROOT }}/api/system/notifications/sse\"", "full_matches": [["{{ APP_ROOT }}/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 705], ["APP_ROOT", 708], ["/api/system/notifications/get_unread_count\"\n             hx-trigger=\"load, every 30s, notification-updated from:body\"\n             hx-swap=\"outerHTML\">\n            <!-- Badge will be loaded here -->\n        </div>\n    </button>\n\n    <!-- Notification dropdown -->\n    <div x-show=\"open\"\n         x-transition:enter=\"transition ease-out duration-100\"\n         x-transition:enter-start=\"transform opacity-0 scale-95\"\n         x-transition:enter-end=\"transform opacity-100 scale-100\"\n         x-transition:leave=\"transition ease-in duration-75\"\n         x-transition:leave-start=\"transform opacity-100 scale-100\"\n         x-transition:leave-end=\"transform opacity-0 scale-95\"\n         class=\"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\n         role=\"menu\"\n         aria-orientation=\"vertical\"\n         tabindex=\"-1\">\n\n        <!-- Notification header -->\n        <div class=\"px-4 py-2 border-b border-gray-200\">\n            <div class=\"flex justify-between items-center\">\n                <h3 class=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                <a href=\"", 719]]}], ".\\system\\components\\edges\\template-container.edge.php": [{"pattern": "fs_constant_concat", "match": "FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", "line": 11, "line_content": "$template_exists = file_exists(FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\");", "full_matches": [["FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", 338], ["FS_APP_ROOT", 338], ["resources/components/templates/{$template}.edge.php", 353]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", "line": 11, "line_content": "$template_exists = file_exists(FS_APP_ROOT . \"resources/components/templates/{$template}.edge.php\");", "full_matches": [["APP_ROOT . \"resources/components/templates/{$template}.edge.php\"", 341], ["APP_ROOT", 341], ["resources/components/templates/{$template}.edge.php", 353]]}], ".\\system\\functions\\components.fn.php": [{"pattern": "fs_constant_concat", "match": "FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'", "line": 72, "line_content": "//echo \"<br>cached version doesn't exist, compiling... from '\" . FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'<br>\\n\";", "full_matches": [["FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'", 2845], ["FS_APP_ROOT", 2845], ["resources/components/{$component_name}.blade.php", 2860]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"resources/components/{$component_name}.blade.php'", "line": 72, "line_content": "//echo \"<br>cached version doesn't exist, compiling... from '\" . FS_APP_ROOT . \"resources/components/{$component_name}.blade.php'<br>\\n\";", "full_matches": [["APP_ROOT . \"resources/components/{$component_name}.blade.php'", 2848], ["APP_ROOT", 2848], ["resources/components/{$component_name}.blade.php", 2860]]}], ".\\system\\templates\\default_template.hilt.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/navigation\" \n                           class=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Navigation\n                        </a>\n                        <a href=\"", "line": 109, "line_content": "<a href=\"{{ APP_ROOT }}/navigation\"", "full_matches": [["{{ APP_ROOT }}/navigation\" \n                           class=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Navigation\n                        </a>\n                        <a href=\"", 6375], ["APP_ROOT", 6378], ["/navigation\" \n                           class=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Navigation\n                        </a>\n                        <a href=\"", 6389]]}], ".\\system\\templates\\file_upload_template.hilt.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Templates\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"template-info mt-6\">\n        <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">File Upload Template Information</h3>\n            <div class=\"text-sm text-blue-700\">\n                <p><strong>Route:</strong> ", "line": 31, "line_content": "<a href=\"{{ APP_ROOT }}/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">", "full_matches": [["{{ APP_ROOT }}/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Templates\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"template-info mt-6\">\n        <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">File Upload Template Information</h3>\n            <div class=\"text-sm text-blue-700\">\n                <p><strong>Route:</strong> ", 1541], ["APP_ROOT", 1544], ["/navigation\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n                            Manage Templates\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"template-info mt-6\">\n        <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">File Upload Template Information</h3>\n            <div class=\"text-sm text-blue-700\">\n                <p><strong>Route:</strong> ", 1555]]}], ".\\update_path_usage.php": [{"pattern": "template_app_root", "match": "{{ APP_ROOT }}/path -> ", "line": 115, "line_content": "// Pattern 4: Template patterns {{ APP_ROOT }}/path -> {{ APP_ROOT }}/path", "full_matches": [["{{ APP_ROOT }}/path -> ", 3900], ["APP_ROOT", 3903], ["/path -> ", 3914]]}, {"pattern": "template_app_root", "match": "{{ APP_ROOT }}/path\n    $pattern4 = '/\\", "line": 115, "line_content": "// Pattern 4: Template patterns {{ APP_ROOT }}/path -> {{ APP_ROOT }}/path", "full_matches": [["{{ APP_ROOT }}/path -> ", 3900], ["APP_ROOT", 3903], ["/path -> ", 3914]]}], ".\\update_remaining_paths.php": [{"pattern": "fs_constant_concat", "match": "FS_CONSTANT . \"{$variable}.extension\"", "line": 64, "line_content": "// Pattern 1: FS_CONSTANT . \"{$variable}.extension\" -> FS_CONSTANT . DIRECTORY_SEPARATOR . \"{$variable}.extension\"", "full_matches": [["FS_CONSTANT . \"{$variable}.extension\"", 1651], ["FS_CONSTANT", 1651], ["{$variable}.extension", 1666]]}, {"pattern": "app_root_concat", "match": "APP_ROOT . \"{$variable}\"", "line": 80, "line_content": "// Pattern 2: APP_ROOT . \"{$variable}\" -> APP_ROOT . DIRECTORY_SEPARATOR . \"{$variable}\"", "full_matches": [["APP_ROOT . \"{$variable}\"", 2356], ["APP_ROOT", 2356], ["{$variable}", 2368]]}]}