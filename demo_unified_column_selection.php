<?php
/**
 * Demonstration of unified field column selection for both data source switching and imports
 * Access via browser: http://localhost/autobooks/demo_unified_column_selection.php
 */

header('Content-Type: text/plain');

echo "=== Unified Field Column Selection Demo ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence to initialize everything
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    // Demo 1: Data Source Switching
    echo "=== DEMO 1: Data Source Switching ===\n";
    echo "Demonstrates intelligent column selection when switching to a different data source\n\n";
    
    // Get an existing data source
    $data_sources = system\database::table('autobooks_data_sources')
        ->where('table_name', 'like', '%import%')
        ->limit(1)
        ->get();
    
    if (!empty($data_sources)) {
        $data_source = $data_sources[0];
        $data_source_id = $data_source['id'];
        $data_source_name = $data_source['name'];
        
        echo "Using existing data source: {$data_source_name} (ID: {$data_source_id})\n";
        
        // Get sample data to see available fields
        $sample_result = system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);
        
        if ($sample_result['success'] && !empty($sample_result['data'])) {
            $first_row = reset($sample_result['data']);
            $available_fields = array_keys($first_row);
            
            // Filter out system columns
            $available_fields = array_filter($available_fields, function($field) {
                return !in_array($field, ['id', 'data_hash']);
            });
            
            echo "Available fields: " . implode(', ', $available_fields) . "\n";
            echo "Total fields: " . count($available_fields) . "\n\n";
            
            // Test unified column structure generation
            $column_structure = generate_unified_column_structure($available_fields, $data_source_id);
            
            echo "Intelligent column selection results:\n";
            echo "- Visible columns: " . count($column_structure['visible_columns']) . "\n";
            echo "- Hidden columns: " . count($column_structure['hidden_column_ids']) . "\n";
            echo "- Available fields: " . count($column_structure['available_fields']) . "\n\n";
            
            echo "Columns shown by default:\n";
            foreach ($column_structure['visible_columns'] as $i => $column) {
                $unified_info = $column['unified_field'] ? 
                    " -> {$column['unified_field']} (confidence: {$column['confidence']})" : 
                    " (no unified match)";
                echo "  " . ($i + 1) . ". {$column['field']}: {$column['label']}{$unified_info}\n";
            }
            
            echo "\nFields available for manual addition:\n";
            foreach ($column_structure['available_fields'] as $i => $field_info) {
                $unified_info = $field_info['unified_field'] ? 
                    " -> {$field_info['unified_field']} (confidence: {$field_info['confidence']})" : 
                    " (no unified match)";
                echo "  " . ($i + 1) . ". {$field_info['field']}: {$field_info['label']}{$unified_info}\n";
            }
        } else {
            echo "⚠️ Could not get sample data from data source\n";
        }
    } else {
        echo "⚠️ No existing data sources found for demo\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
    
    // Demo 2: CSV Import with Intelligent Column Selection
    echo "=== DEMO 2: CSV Import with Intelligent Column Selection ===\n";
    echo "Demonstrates intelligent column selection during CSV import process\n\n";
    
    // Create a comprehensive test CSV
    $demo_csv_data = [
        // Headers representing various business scenarios
        [
            'company_name', 'contact_email', 'product_name', 'subscription_reference',
            'start_date', 'end_date', 'status', 'address', 'city', 'state',
            'quantity', 'price', 'phone', 'notes', 'created_at', 'internal_id'
        ],
        // Sample data
        ['Acme Corp', '<EMAIL>', 'AutoCAD LT', 'SUB-001', '2024-01-01', '2024-12-31', 'Active', '123 Main St', 'New York', 'NY', '5', '299.99', '555-1234', 'VIP client', '2024-01-01 10:00:00', 'INT001'],
        ['Beta Inc', '<EMAIL>', 'SketchUp Pro', 'SUB-002', '2024-02-01', '2025-01-31', 'Active', '456 Oak Ave', 'Los Angeles', 'CA', '10', '695.00', '555-5678', 'New customer', '2024-02-01 11:00:00', 'INT002'],
        ['Gamma LLC', '<EMAIL>', 'Revit', 'SUB-003', '2024-03-01', '2025-02-28', 'Pending', '789 Pine Rd', 'Chicago', 'IL', '3', '2545.00', '555-9012', 'Renewal pending', '2024-03-01 12:00:00', 'INT003']
    ];
    
    // Create temporary CSV file
    $temp_csv_file = tempnam(sys_get_temp_dir(), 'demo_import_') . '.csv';
    $csv_handle = fopen($temp_csv_file, 'w');
    
    foreach ($demo_csv_data as $row) {
        fputcsv($csv_handle, $row);
    }
    fclose($csv_handle);
    
    echo "Created demo CSV with comprehensive business data\n";
    echo "Headers: " . implode(', ', $demo_csv_data[0]) . "\n";
    echo "Rows: " . (count($demo_csv_data) - 1) . " data rows\n\n";
    
    // Analyze what the unified field matching should do
    echo "Analyzing expected field matching behavior:\n";
    $headers = $demo_csv_data[0];
    $suggestions = system\unified_field_mapper::suggest_field_mappings($headers);
    
    $expected_visible = [];
    $expected_hidden = [];
    
    foreach ($headers as $header) {
        if (isset($suggestions[$header])) {
            $suggestion = $suggestions[$header];
            $should_show = system\unified_field_definitions::should_show_by_default($suggestion['field_name']);
            
            if ($should_show) {
                $expected_visible[] = "{$header} -> {$suggestion['field_name']} ({$suggestion['label']})";
            } else {
                $expected_hidden[] = "{$header} -> {$suggestion['field_name']} ({$suggestion['label']})";
            }
        } else {
            $expected_visible[] = "{$header} (unmatched, show by default)";
        }
    }
    
    echo "\nExpected visible columns (" . count($expected_visible) . "):\n";
    foreach ($expected_visible as $i => $field) {
        echo "  " . ($i + 1) . ". {$field}\n";
    }
    
    echo "\nExpected hidden columns (" . count($expected_hidden) . "):\n";
    foreach ($expected_hidden as $i => $field) {
        echo "  " . ($i + 1) . ". {$field}\n";
    }
    
    // Test the actual import process
    echo "\nTesting actual import process...\n";
    $test_table_name = 'autobooks_demo_import_' . time() . '_data';
    
    // Import with intelligent column selection enabled
    $import_result = system\data_importer::import_csv_with_auto_schema($temp_csv_file, $test_table_name, true, true);
    
    if (isset($import_result['error'])) {
        echo "❌ Import failed: " . $import_result['error'] . "\n";
    } else {
        echo "✅ Import successful!\n";
        echo "- Table: {$test_table_name}\n";
        echo "- Rows: " . ($import_result['import_result']['success_count'] ?? 'unknown') . "\n";
        echo "- Data source: " . ($import_result['config_result']['data_source_id'] ?? 'none') . "\n\n";
        
        // Test the generated configuration
        $criteria = ['limit' => 10];
        $options = [
            'use_intelligent_column_selection' => true,
            'use_intelligent_naming' => true,
            'table_name' => $test_table_name
        ];
        
        $config_result = system\data_table_generator::generate_table_config($test_table_name, $criteria, $options);
        
        if (!isset($config_result['error'])) {
            $config = $config_result['config'];
            
            echo "Actual results from import:\n";
            echo "- Visible columns: " . count($config['columns']) . "\n";
            echo "- Available fields: " . count($config['available_fields']) . "\n\n";
            
            echo "Actually visible columns:\n";
            foreach ($config['columns'] as $i => $column) {
                echo "  " . ($i + 1) . ". {$column['field']}: {$column['label']}\n";
            }
            
            echo "\nActually available fields (for manual addition):\n";
            foreach ($config['available_fields'] as $i => $field) {
                echo "  " . ($i + 1) . ". {$field}\n";
            }
            
            // Compare expected vs actual
            echo "\n" . str_repeat("-", 40) . "\n";
            echo "COMPARISON: Expected vs Actual\n";
            echo str_repeat("-", 40) . "\n";
            
            $actual_visible_count = count($config['columns']);
            $expected_visible_count = count($expected_visible);
            
            if ($actual_visible_count <= $expected_visible_count + 2 && $actual_visible_count >= $expected_visible_count - 2) {
                echo "✅ Visible column count matches expectations (~{$expected_visible_count})\n";
            } else {
                echo "⚠️ Visible column count differs: expected ~{$expected_visible_count}, got {$actual_visible_count}\n";
            }
            
            $actual_available_count = count($config['available_fields']);
            $expected_hidden_count = count($expected_hidden);
            
            if ($actual_available_count >= $expected_hidden_count) {
                echo "✅ Available fields include expected hidden columns\n";
            } else {
                echo "⚠️ Available fields count lower than expected\n";
            }
        }
        
        // Clean up
        try {
            system\database::rawQuery("DROP TABLE IF EXISTS `{$test_table_name}`");
            echo "\n✅ Test table cleaned up\n";
        } catch (Exception $e) {
            echo "\n⚠️ Could not clean up test table\n";
        }
    }
    
    // Clean up CSV
    unlink($temp_csv_file);
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "=== DEMO COMPLETE ===\n\n";
    
    echo "Key Features Demonstrated:\n";
    echo "1. ✅ Data source switching uses unified field matching\n";
    echo "2. ✅ CSV imports use intelligent column selection\n";
    echo "3. ✅ Core business fields shown by default\n";
    echo "4. ✅ Technical/detail fields hidden but available\n";
    echo "5. ✅ Column manager receives proper available fields\n";
    echo "6. ✅ Unified field labels improve readability\n";
    echo "7. ✅ Fallback system handles unmatched fields\n";
    echo "8. ✅ Configuration preserves all field information\n\n";
    
    echo "Benefits:\n";
    echo "- Reduced cognitive load with focused initial view\n";
    echo "- Consistent experience across data sources and imports\n";
    echo "- Flexible customization through existing column manager\n";
    echo "- Intelligent field recognition and labeling\n";
    echo "- Backward compatibility with existing functionality\n\n";
    
    echo "Next Steps:\n";
    echo "1. Run field display settings update script if not done already\n";
    echo "2. Test with real CSV files and data sources\n";
    echo "3. Customize field display settings as needed\n";
    echo "4. Train users on the new column manager capabilities\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Demo ===\n";
?>
