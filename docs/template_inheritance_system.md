# Edge Template Inheritance System

## Overview

The Edge template inheritance system allows templates to inherit properties and initialization logic from parent templates, creating a hierarchical structure that eliminates code duplication and improves maintainability.

## Key Features

### 1. Property Inheritance
- Child templates inherit all properties from parent templates
- Child properties override parent properties (reverse merge)
- Multi-level inheritance supported (grandparent → parent → child)

### 2. @init Block Inheritance
- Special `@init` blocks define initialization code
- Init blocks are merged from the entire inheritance chain
- **Smart Execution**: Init blocks only run once in the hierarchy
- If parent executes init, children skip it; if child called directly, it executes all inherited init blocks

### 3. Compile-time Processing
- All inheritance resolution happens during template compilation
- No runtime performance impact
- Cached compiled templates include resolved inheritance

## Usage

### Declaring Inheritance

```php
@props([
    'parent' => 'parent-template-name', // Inherit from parent
    'child_specific_prop' => 'value',   // Child-specific properties
    // All parent properties are automatically inherited
])
```

### Using @init Blocks

```php
@init
    // Initialization code that runs once in hierarchy
    if ($some_condition) {
        // Prepare data, set up variables, etc.
        $prepared_data = prepare_data($raw_data);
        $_inheritance_execution_state['data_prepared'] = true;
    }
@endinit
```

## Template Hierarchy Example

```
data-table (master)
├── Properties: items, columns, table_name, callback, etc.
├── @init: Master data preparation logic
│
├── data-table-structure (child of data-table)
│   ├── Inherits: All data-table properties
│   ├── Adds: include_column_manager, oob-swap
│   ├── @init: Additional structure-specific logic
│   │
│   └── data-table-rows (child of data-table-structure)
│       ├── Inherits: All properties from data-table + data-table-structure
│       └── No additional properties or @init blocks
│
└── data-table-column-manager (child of data-table)
    ├── Inherits: All data-table properties
    ├── Adds: db_table, current_data_source_type, keep_open
    │
    └── data-table-column-manager-panel (child of data-table-column-manager)
        ├── Inherits: All properties from data-table + data-table-column-manager
        └── Adds: available_data_sources, processed_columns, hidden_columns
```

## Execution Logic

### Scenario 1: Child Called Directly
```php
// User calls: Edge::render('data-table-structure', $data)
// System executes:
// 1. data-table @init block (master initialization)
// 2. data-table-structure @init block (child-specific logic)
```

### Scenario 2: Parent Calls Child
```php
// Parent template includes: <x-data-table-structure />
// System executes:
// 1. Parent already executed data-table @init
// 2. Child skips data-table @init (already done)
// 3. Child executes only its own @init block
```

## Implementation Details

### Core Methods Added to Edge Class

1. **`processTemplateInheritance()`** - Main orchestrator
2. **`extractParentFromProps()`** - Finds parent template name
3. **`buildInheritanceChain()`** - Builds complete inheritance hierarchy
4. **`mergeInheritedProps()`** - Merges properties with child override
5. **`mergeInheritedInitBlocks()`** - Combines @init blocks with tracking
6. **`addInitBlocksToSource()`** - Adds conditional init execution logic

### Execution State Tracking

The system uses `$_inheritance_execution_state` array to track:
- Which @init blocks have been executed
- Prevents duplicate execution in parent-child scenarios
- Automatically passed between templates in the same render chain

## Benefits

1. **DRY Principle**: Eliminate property duplication across related templates
2. **Maintainability**: Changes to base templates automatically propagate
3. **Performance**: All processing happens at compile-time
4. **Flexibility**: Supports complex inheritance hierarchies
5. **Smart Execution**: Prevents duplicate initialization logic
6. **Backward Compatible**: Existing templates continue to work unchanged

## Migration Guide

### Converting Existing Templates

**Before:**
```php
@props([
    'items' => [],
    'columns' => [],
    'table_name' => '',
    'callback' => null,
    // ... 20+ repeated properties
])

@php
    if ($external_call) {
        // Duplicate initialization logic
        extract(data_table_storage::prepare_template_data([...]));
    }
@endphp
```

**After:**
```php
@props([
    'parent' => 'data-table', // Inherit base properties
    'child_specific_prop' => 'value' // Only child-specific properties
])

@init
    // Child-specific initialization only
    if ($external_call) {
        print_rr($debug_data, 'child_debug');
    }
@endinit
```

## Testing

Run `test_template_inheritance.php` to verify:
- Property inheritance works correctly
- @init blocks execute conditionally
- Multi-level inheritance functions properly
- No duplicate execution occurs

## Future Enhancements

1. **Circular Inheritance Detection**: Already implemented with warning
2. **Init Block Dependencies**: Could add dependency ordering
3. **Property Validation**: Could validate inherited properties
4. **Performance Caching**: Could cache inheritance chains
5. **IDE Support**: Could generate property hints for inherited templates
