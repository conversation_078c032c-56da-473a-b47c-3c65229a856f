# Universal Sortable Migration Guide

## Overview

The new Universal Sortable system replaces the complex, hardcoded sortable implementations with a simple, reusable script that works with any element having the `.sortable` class and HTMX inline attributes.

## What Changed

### Before (Complex Implementation)
- Multiple hardcoded JavaScript implementations in `layout-head.edge.php`
- Separate scripts for nav-sortable, column-sortable, field-container
- Complex event handling and HTMX ajax calls in JavaScript
- Difficult to maintain and extend

### After (Universal Implementation)
- Single `universal-sortable.js` script handles all sortable functionality
- Simple class-based configuration with data attributes
- HTMX inline attributes for server communication
- Easy to extend and maintain

## Migration Steps

### 1. Update Your Templates

Replace the old sortable structure:
```html
<!-- OLD WAY -->
<div class="nav-sortable">
    <!-- Items with complex JavaScript handling -->
</div>
```

With the new universal structure:
```html
<!-- NEW WAY -->
<div class="sortable nav-sortable"
     data-sortable-handle=".drag-handle"
     data-sortable-group="nav-tree"
     data-sortable-item-selector=".sortable-item"
     hx-post="{{ APP_ROOT }}/api/nav_tree/reorder_navigation"
     hx-trigger="sortableEnd"
     hx-swap="none"
     hx-include="this"
     hx-vals='{"parent_path": "{{ $parent_path }}"}'>
    
    <div class="sortable-item" data-sort-id="item-1">
        <div class="drag-handle">⋮⋮</div>
        <span>Item Content</span>
    </div>
</div>
```

### 2. Required Changes

1. **Add `sortable` class** to the container element
2. **Add `data-sort-id`** attributes to sortable items
3. **Configure HTMX attributes**:
   - `hx-trigger="sortableEnd"` (instead of "end")
   - Other HTMX attributes as needed
4. **Add data attributes** for sortable configuration (optional, has defaults)

### 3. Server-Side Changes

Your server endpoints now receive additional data automatically:
- `sortedIds`: JSON array of sorted item IDs
- `fromIndex`: Original index of moved item
- `toIndex`: New index of moved item
- `movedId`: ID of the moved item
- `fromContainer`: ID of source container (for cross-container moves)
- `toContainer`: ID of target container (for cross-container moves)

Update your server endpoints to use this new data format.

## Configuration Options

### Data Attributes (all optional)

- `data-sortable-handle`: CSS selector for drag handle (default: '.drag-handle')
- `data-sortable-group`: Group name for cross-container dragging
- `data-sortable-animation`: Animation duration in ms (default: 150)
- `data-sortable-ghost-class`: CSS class for ghost element (default: 'sortable-ghost')
- `data-sortable-chosen-class`: CSS class for chosen element (default: 'sortable-chosen')
- `data-sortable-drag-class`: CSS class for dragging element (default: 'sortable-drag')
- `data-sortable-filter`: CSS selector for elements to exclude (default: '.htmx-indicator')
- `data-sortable-item-selector`: CSS selector for valid sortable items

### HTMX Attributes (required)

- `hx-post`: URL to send the sort data to
- `hx-trigger="sortableEnd"`: Triggered when sorting ends
- `hx-target`: Target element for the response
- `hx-swap`: How to swap the response
- `hx-include`: What data to include with the request
- `hx-vals`: Additional static values to send

## Examples

### Navigation Tree
```html
<div class="sortable nav-sortable"
     data-sortable-handle=".drag-handle"
     data-sortable-group="nav-tree"
     data-sortable-item-selector=".sortable-item"
     hx-post="{{ APP_ROOT }}/api/nav_tree/reorder_navigation"
     hx-trigger="sortableEnd"
     hx-swap="none"
     hx-include="this"
     hx-vals='{"parent_path": "{{ $parent_path }}"}'>
    <!-- sortable items with data-sort-id -->
</div>
```

### Column Manager
```html
<div class="sortable column-sortable"
     data-sortable-handle=".column-drag-handle"
     hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/reorder_columns"
     hx-trigger="sortableEnd"
     hx-target=".data_table"
     hx-swap="outerHTML"
     hx-include="this"
     hx-vals='{"table_name": "{{ $table_name }}"}'>
    <!-- column items with data-sort-id and data-column-id -->
</div>
```

### Cross-Container Dragging
```html
<div class="sortable field-container"
     data-sortable-handle=".field-drag-handle"
     data-sortable-group="fieldsAndActions"
     hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/move_field"
     hx-trigger="sortableEnd"
     hx-target=".data_table"
     hx-swap="outerHTML"
     hx-include="closest .column-sortable">
    <!-- field items with data-sort-id -->
</div>
```

## Benefits

1. **Simplified Maintenance**: One script handles all sortable functionality
2. **Consistent Behavior**: All sortables work the same way
3. **Easy Extension**: Add new sortable types without JavaScript changes
4. **HTMX Integration**: Clean separation using HTMX inline attributes
5. **Flexible Configuration**: Data attributes allow customization per instance
6. **Automatic Cleanup**: Proper instance management and cleanup

## Files Modified

- `system/components/edges/layout-head.edge.php`: Removed complex sortable code, added universal script
- `resources/components/js/universal-sortable.js`: New universal sortable handler
- `system/components/edges/nav-tree.edge.php`: Updated to use new system
- `system/components/edges/sortable-examples.edge.php`: Examples and documentation

## Next Steps

1. Update remaining templates that use sortable functionality
2. Test all sortable interactions
3. Update server endpoints to handle new data format
4. Remove old sortable-specific JavaScript files if no longer needed
