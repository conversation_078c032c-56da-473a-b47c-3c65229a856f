<?php
/**
 * Test script for Edge template inheritance system
 * This demonstrates how the new inheritance system works
 */

require_once 'system/classes/edge/edge.class.php';

// Test data for demonstration
$test_data = [
    'items' => [
        ['id' => 1, 'name' => '<PERSON>', 'email' => '<EMAIL>'],
        ['id' => 2, 'name' => '<PERSON>', 'email' => '<EMAIL>']
    ],
    'columns' => [
        ['label' => 'ID', 'field' => 'id'],
        ['label' => 'Name', 'field' => 'name'],
        ['label' => 'Email', 'field' => 'email']
    ],
    'table_name' => 'test_users',
    'external_call' => true
];

echo "<h1>Template Inheritance Test</h1>\n";

// Test 1: Render data-table-structure (should inherit from data-table)
echo "<h2>Test 1: data-table-structure (inherits from data-table)</h2>\n";
echo "<p><strong>Scenario:</strong> Child template called directly - should execute parent @init blocks</p>\n";
try {
    $result = Edge::render('data-table-structure', $test_data);
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo $result;
    echo "</div>";
    echo "<p style='color: green;'>✓ data-table-structure rendered successfully with inheritance</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error rendering data-table-structure: " . $e->getMessage() . "</p>\n";
}

// Test 1b: Render parent first, then child (should not duplicate init)
echo "<h2>Test 1b: Parent → Child Execution</h2>\n";
echo "<p><strong>Scenario:</strong> Parent template calls child - should NOT duplicate @init execution</p>\n";
try {
    // First render parent (this will execute @init blocks)
    $parent_data = $test_data;
    $parent_data['_inheritance_execution_state'] = []; // Start fresh
    $parent_result = Edge::render('data-table', $parent_data);

    // Now render child with same execution state (should skip @init)
    $child_data = $test_data;
    $child_data['_inheritance_execution_state'] = $parent_data['_inheritance_execution_state'];
    $child_result = Edge::render('data-table-structure', $child_data);

    echo "<p style='color: green;'>✓ Parent-child execution completed without duplicate initialization</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error in parent-child execution: " . $e->getMessage() . "</p>\n";
}

// Test 2: Render data-table-rows (should inherit from data-table-structure -> data-table)
echo "<h2>Test 2: data-table-rows (inherits from data-table-structure -> data-table)</h2>\n";
try {
    $result = Edge::render('data-table-rows', $test_data);
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo $result;
    echo "</div>";
    echo "<p style='color: green;'>✓ data-table-rows rendered successfully with multi-level inheritance</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error rendering data-table-rows: " . $e->getMessage() . "</p>\n";
}

// Test 3: Render data-table-column-manager (should inherit from data-table)
echo "<h2>Test 3: data-table-column-manager (inherits from data-table)</h2>\n";
try {
    $result = Edge::render('data-table-column-manager', $test_data);
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo $result;
    echo "</div>";
    echo "<p style='color: green;'>✓ data-table-column-manager rendered successfully with inheritance</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error rendering data-table-column-manager: " . $e->getMessage() . "</p>\n";
}

// Test 4: Show inheritance chain analysis
echo "<h2>Test 4: Inheritance Chain Analysis</h2>\n";
echo "<h3>Template Hierarchy:</h3>\n";
echo "<ul>\n";
echo "<li><strong>data-table</strong> (master template)\n";
echo "  <ul>\n";
echo "    <li>Contains base properties: items, columns, table_name, etc.</li>\n";
echo "    <li>Contains @init block for data preparation</li>\n";
echo "  </ul>\n";
echo "</li>\n";
echo "<li><strong>data-table-structure</strong> (inherits from data-table)\n";
echo "  <ul>\n";
echo "    <li>Adds: include_column_manager, oob-swap</li>\n";
echo "    <li>Adds additional @init logic for debugging</li>\n";
echo "  </ul>\n";
echo "</li>\n";
echo "<li><strong>data-table-rows</strong> (inherits from data-table-structure)\n";
echo "  <ul>\n";
echo "    <li>Inherits all properties from data-table-structure and data-table</li>\n";
echo "    <li>No additional properties or init blocks</li>\n";
echo "  </ul>\n";
echo "</li>\n";
echo "<li><strong>data-table-column-manager</strong> (inherits from data-table)\n";
echo "  <ul>\n";
echo "    <li>Adds: db_table, current_data_source_type, keep_open</li>\n";
echo "  </ul>\n";
echo "</li>\n";
echo "<li><strong>data-table-column-manager-panel</strong> (inherits from data-table-column-manager)\n";
echo "  <ul>\n";
echo "    <li>Adds: available_data_sources, processed_columns, hidden_columns</li>\n";
echo "  </ul>\n";
echo "</li>\n";
echo "</ul>\n";

echo "<h3>Benefits of This System:</h3>\n";
echo "<ul>\n";
echo "<li><strong>DRY Principle:</strong> No need to repeat common properties across templates</li>\n";
echo "<li><strong>Maintainability:</strong> Changes to base properties automatically propagate to children</li>\n";
echo "<li><strong>Initialization Logic:</strong> @init blocks are merged and executed once in the hierarchy</li>\n";
echo "<li><strong>Compile-time Processing:</strong> All inheritance is resolved during template compilation</li>\n";
echo "<li><strong>Child Override:</strong> Child properties override parent properties as expected</li>\n";
echo "</ul>\n";

echo "<p><em>Test completed. Check the rendered output above to verify inheritance is working correctly.</em></p>\n";
?>
