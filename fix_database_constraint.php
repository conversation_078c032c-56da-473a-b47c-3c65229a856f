<?php
/**
 * Fix the database constraint issue for data_table_storage
 * Access via browser: http://localhost/autobooks/fix_database_constraint.php
 */

header('Content-Type: text/plain');

echo "=== Fixing Database Constraint Issue ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the startup sequence to initialize everything
    require_once 'system/startup_sequence_minimal.php';

    // PDO should be available through the database class
    
    echo "1. System initialized successfully\n\n";
    
    echo "2. Analyzing current database constraint...\n";
    
    // Check current table structure
    $table_info_stmt = system\database::rawQuery("SHOW CREATE TABLE autobooks_data_table_storage");
    $table_info = $table_info_stmt->fetchAll(\PDO::FETCH_ASSOC);
    $create_statement = $table_info[0]['Create Table'];
    
    echo "Current table structure:\n";
    if (strpos($create_statement, 'UNIQUE KEY `idx_table_name` (`table_name`)') !== false) {
        echo "❌ Found problematic constraint: UNIQUE KEY `idx_table_name` (`table_name`)\n";
        echo "This prevents multiple users from having configurations for the same table.\n\n";
    } else {
        echo "✅ Constraint appears to be correct or already fixed.\n\n";
    }
    
    echo "3. Fixing the database constraint...\n";
    
    // Drop the existing unique constraint
    try {
        system\database::rawQuery("ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name");
        echo "✅ Dropped old unique constraint on table_name only\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "check that column/key exists") !== false) {
            echo "ℹ️ Old constraint already removed\n";
        } else {
            echo "⚠️ Error dropping old constraint: " . $e->getMessage() . "\n";
        }
    }
    
    // Add new composite unique constraint
    try {
        system\database::rawQuery("ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)");
        echo "✅ Added new composite unique constraint on (table_name, user_id)\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "Duplicate key name") !== false) {
            echo "ℹ️ New constraint already exists\n";
        } else {
            echo "❌ Error adding new constraint: " . $e->getMessage() . "\n";
            echo "This might be due to existing duplicate data.\n";
            
            // Check for duplicates
            echo "\n4. Checking for duplicate data...\n";
            $duplicates_stmt = system\database::rawQuery("
                SELECT table_name, user_id, COUNT(*) as count
                FROM autobooks_data_table_storage
                GROUP BY table_name, user_id
                HAVING COUNT(*) > 1
            ");
            $duplicates = $duplicates_stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            if (!empty($duplicates)) {
                echo "Found duplicate records:\n";
                foreach ($duplicates as $dup) {
                    echo "- table_name: '{$dup['table_name']}', user_id: " . ($dup['user_id'] ?? 'null') . ", count: {$dup['count']}\n";
                }
                
                echo "\nCleaning up duplicates...\n";
                
                foreach ($duplicates as $dup) {
                    $table_name = $dup['table_name'];
                    $user_id = $dup['user_id'];
                    
                    // Keep only the most recent record for each table_name/user_id combination
                    if ($user_id === null) {
                        $delete_query = "
                            DELETE FROM autobooks_data_table_storage 
                            WHERE table_name = ? AND user_id IS NULL 
                            AND id NOT IN (
                                SELECT * FROM (
                                    SELECT MAX(id) FROM autobooks_data_table_storage 
                                    WHERE table_name = ? AND user_id IS NULL
                                ) as temp
                            )
                        ";
                        system\database::rawQuery($delete_query, [$table_name, $table_name]);
                    } else {
                        $delete_query = "
                            DELETE FROM autobooks_data_table_storage 
                            WHERE table_name = ? AND user_id = ? 
                            AND id NOT IN (
                                SELECT * FROM (
                                    SELECT MAX(id) FROM autobooks_data_table_storage 
                                    WHERE table_name = ? AND user_id = ?
                                ) as temp
                            )
                        ";
                        system\database::rawQuery($delete_query, [$table_name, $user_id, $table_name, $user_id]);
                    }
                    
                    echo "✅ Cleaned up duplicates for table_name: '{$table_name}', user_id: " . ($user_id ?? 'null') . "\n";
                }
                
                // Try adding the constraint again
                try {
                    system\database::rawQuery("ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)");
                    echo "✅ Added new composite unique constraint after cleanup\n";
                } catch (Exception $e2) {
                    echo "❌ Still failed to add constraint: " . $e2->getMessage() . "\n";
                }
            } else {
                echo "No duplicate records found. Constraint issue might be different.\n";
            }
        }
    }
    
    echo "\n5. Verifying the fix...\n";
    
    // Check the new table structure
    $new_table_info_stmt = system\database::rawQuery("SHOW CREATE TABLE autobooks_data_table_storage");
    $new_table_info = $new_table_info_stmt->fetchAll(\PDO::FETCH_ASSOC);
    $new_create_statement = $new_table_info[0]['Create Table'];
    
    if (strpos($new_create_statement, 'UNIQUE KEY `idx_table_name_user` (`table_name`,`user_id`)') !== false) {
        echo "✅ New composite constraint is in place\n";
    } else {
        echo "❌ New constraint not found in table structure\n";
    }
    
    echo "\n6. Testing the fix with SketchUp data...\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    // Clean up any existing SketchUp configurations
    $existing_count = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->count();

    $delete_result = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->delete();

    echo "Cleaned up {$existing_count} existing SketchUp configurations\n";
    
    // Test creating both global and user-specific configurations
    $test_config = [
        'structure' => [
            ['id' => 'test_col_1', 'label' => 'Test Column', 'field' => 'test_field', 'filter' => true]
        ],
        'columns' => [
            ['id' => 'test_col_1', 'label' => 'Test Column', 'field' => 'test_field', 'filter' => true]
        ],
        'data_source_type' => 'data_source',
        'data_source_id' => 75, // From the error message
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Test global configuration (user_id = null)
    try {
        $global_result = system\data_table_storage::save_configuration($table_name, $test_config, null, 75);
        if ($global_result) {
            echo "✅ Global configuration saved successfully\n";
        } else {
            echo "❌ Failed to save global configuration\n";
        }
    } catch (Exception $e) {
        echo "❌ Error saving global configuration: " . $e->getMessage() . "\n";
    }
    
    // Test user-specific configuration (user_id = 2)
    try {
        $user_result = system\data_table_storage::save_configuration($table_name, $test_config, 2, 75);
        if ($user_result) {
            echo "✅ User-specific configuration saved successfully\n";
        } else {
            echo "❌ Failed to save user-specific configuration\n";
        }
    } catch (Exception $e) {
        echo "❌ Error saving user-specific configuration: " . $e->getMessage() . "\n";
    }
    
    // Verify both configurations exist
    $configs = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->get();
    
    echo "Found " . count($configs) . " configurations for table '{$table_name}':\n";
    foreach ($configs as $config) {
        echo "- ID: {$config['id']}, User ID: " . ($config['user_id'] ?? 'null') . ", Data Source ID: {$config['data_source_id']}\n";
    }
    
    if (count($configs) == 2) {
        echo "✅ Both global and user-specific configurations exist - constraint fix successful!\n";
    } else {
        echo "⚠️ Expected 2 configurations, found " . count($configs) . "\n";
    }
    
    // Clean up test configurations
    system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->delete();
    
    echo "✅ Test configurations cleaned up\n";
    
    echo "\n=== Fix Complete ===\n";
    echo "✅ Database constraint updated to allow multiple users per table\n";
    echo "✅ Duplicate data cleaned up\n";
    echo "✅ Both global and user-specific configurations can now coexist\n";
    
    echo "\nThe SketchUp import and view should now work correctly.\n";
    echo "Next steps:\n";
    echo "1. Run the comprehensive fix script to create proper configurations\n";
    echo "2. Test the SketchUp view - it should open without duplicate entry errors\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Fix ===\n";
?>
