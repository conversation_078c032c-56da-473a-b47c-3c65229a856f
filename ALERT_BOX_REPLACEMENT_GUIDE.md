# Alert Box Replacement Guide

This guide shows how to replace hardcoded alert/message boxes throughout the codebase with the new unified `<x-alert-box>` component.

## New Component Usage

The new `system/components/edges/alert-box.edge.php` component provides a unified way to display all types of alert messages.

### Basic Usage

```php
<x-alert-box type="warning" title="No Columns Configured" message="This data table has no columns configured. Please configure columns to display data." />
```

### Component Properties

- `type`: 'error', 'warning', 'info', 'success' (default: 'info')
- `title`: Alert title (optional, defaults to type-based title)
- `message`: Alert message content (optional)
- `icon`: Custom SVG icon path (optional, defaults to type-based icon)
- `size`: 'small', 'default', 'large' (default: 'default')
- `layout`: 'default', 'compact', 'centered' (default: 'default')
- `dismissible`: true/false (default: false)
- `show_icon`: true/false (default: true)
- `class`: Additional CSS classes (optional)
- `id`: Element ID (optional)

## Hardcoded Alert Boxes Found

### 1. Data Table Structure - No Columns Warning
**File:** `system/components/edges/data-table-structure.edge.php` (lines 23-33)

**Current Code:**
```php
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center data_table">
    <div class="flex items-center justify-center mb-4">
        <svg class="w-12 h-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
    </div>
    <h3 class="text-lg font-medium text-yellow-800 mb-2">No Columns Configured</h3>
    <p class="text-yellow-700 mb-4">
        This data table has no columns configured. Please configure columns to display data.
    </p>
</div>
```

**Replacement:**
```php
<x-alert-box 
    type="warning" 
    title="No Columns Configured" 
    message="This data table has no columns configured. Please configure columns to display data."
    layout="centered"
    size="large"
    class="data_table" />
```

### 2. Bluebeam Error Display
**File:** `resources/views/bluebeam/bluebeam.edge.php` (lines 130-142)

**Current Code:**
```php
<div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-circle text-red-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-2 text-sm text-red-700">
                {{ $error_message }}
            </div>
        </div>
    </div>
</div>
```

**Replacement:**
```php
<x-alert-box 
    type="error" 
    title="Error" 
    message="{{ $error_message }}"
    class="mb-6" />
```

### 3. Quote Form API Errors
**File:** `resources/components/edges/quote-v3-ui-form.edge.php` (lines 33-44)

**Current Code:**
```php
<div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-red-700">{{ $errors['api'] }}</p>
        </div>
    </div>
</div>
```

**Replacement:**
```php
<x-alert-box 
    type="error" 
    message="{{ $errors['api'] }}"
    class="mb-4" />
```

### 4. Success Messages
**File:** `resources/components/edges/quote-v3-success.edge.php` (lines 6-19)

**Current Code:**
```php
<div class="rounded-md bg-green-50 p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">{{ $message }}</h3>
            @if($quote_id)
            <p class="mt-2 text-sm text-green-700">
                Your quote ID is: <span class="font-semibold">{{ $quote_id }}</span>
            </p>
            @endif
        </div>
    </div>
</div>
```

**Replacement:**
```php
<x-alert-box type="success" title="{{ $message }}">
    @if($quote_id)
        <p>Your quote ID is: <span class="font-semibold">{{ $quote_id }}</span></p>
    @endif
</x-alert-box>
```

### 5. Empty State Messages
**File:** `system/views/data_sources/data_sources.edge.php` (lines 137-142)

**Current Code:**
```php
<div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No data sources</h3>
    <p class="mt-1 text-sm text-gray-500">Get started by creating your first data source.</p>
</div>
```

**Replacement:**
```php
<x-alert-box 
    type="info" 
    title="No data sources" 
    message="Get started by creating your first data source."
    layout="centered"
    size="large"
    icon='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>'
    class="py-12" />
```

### 6. Reset Password Success
**File:** `system/views/login/reset-password.view.php` (lines 82-93)

**Current Code:**
```php
<div class="rounded-md bg-green-50 p-4 mb-6">
    <div class="flex">
        <div class="shrink-0">
            <svg class="size-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800"><?= $success ?></p>
        </div>
    </div>
</div>
```

**Replacement:**
```php
<x-alert-box 
    type="success" 
    message="<?= $success ?>"
    class="mb-6" />
```

## Database Dump Status Functions

The following functions in database dump files can be simplified:

**Files:** 
- `system/views/system/database_dump.api.php` (lines 304-322)
- `system/views/system/system/database_dump.api.php` (lines 298-316)
- `system/api/system/database_dump.api.php` (lines 298-316)

**Current Function:**
```php
function render_dump_status($status) {
    $type_classes = [
        'success' => 'bg-green-50 border-green-200 text-green-800',
        'error' => 'bg-red-50 border-red-200 text-red-800',
        'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-800'
    ];
    
    $icon_classes = [
        'success' => 'text-green-400',
        'error' => 'text-red-400',
        'warning' => 'text-yellow-400'
    ];
    
    $class = $type_classes[$status['type']] ?? $type_classes['error'];
    $icon_class = $icon_classes[$status['type']] ?? $icon_classes['error'];
    
    $html = "<div class=\"p-4 border rounded-md {$class}\">";
    // ... more hardcoded HTML
}
```

**Replacement:**
```php
function render_dump_status($status) {
    return view('components.alert-box', [
        'type' => $status['type'] ?? 'error',
        'title' => $status['title'] ?? null,
        'message' => $status['message'] ?? null
    ])->render();
}
```

## Migration Strategy

1. **Phase 1**: Replace the most common patterns first (data table warnings, error messages)
2. **Phase 2**: Replace empty state messages and success notifications
3. **Phase 3**: Update database dump functions and other utility functions
4. **Phase 4**: Remove the old `error-message.edge.php` component after migration

## Benefits

- **Consistency**: All alert boxes will have the same look and feel
- **Maintainability**: Changes to alert styling only need to be made in one place
- **Flexibility**: Easy to customize with different types, sizes, and layouts
- **Accessibility**: Built-in ARIA attributes and semantic HTML
- **Reduced Code**: Eliminates hundreds of lines of duplicated HTML/CSS
