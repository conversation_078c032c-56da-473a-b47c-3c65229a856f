<?php
/**
 * Fix the existing data source ID 48 to correct the issues
 */

// Include the main application
require_once 'index.php';

use system\database;
use system\data_source_manager;

$data_source_id = 48;
$table_name = 'autobooks_import_bluebeam_data';

echo "<h2>Fixing Data Source ID {$data_source_id}</h2>\n";

try {
    // Get the current data source
    $current_source = database::table('autobooks_data_sources')
        ->where('id', $data_source_id)
        ->first();
    
    if (!$current_source) {
        throw new Exception("Data source ID {$data_source_id} not found");
    }
    
    echo "<h3>Current Issues:</h3>\n";
    echo "<ul>\n";
    echo "<li>data_source_type: {$current_source['data_source_type']} (should be 'standard')</li>\n";
    echo "<li>selected_columns: " . ($current_source['selected_columns'] ? 'SET' : 'NULL') . " (should be set)</li>\n";
    echo "</ul>\n";
    
    // Get table information
    $table_info = data_source_manager::get_table_info($table_name);
    if (!$table_info) {
        throw new Exception("Table {$table_name} not found");
    }
    
    // Get column names (excluding system columns)
    $column_names = [];
    foreach ($table_info['columns'] as $column) {
        if (!in_array($column['Field'], ['id', 'created_at', 'updated_at'])) {
            $column_names[] = $column['Field'];
        }
    }
    
    echo "<h3>Table Columns:</h3>\n";
    echo "<p>" . implode(', ', $column_names) . "</p>\n";
    
    // Build the selected_columns configuration
    $selected_columns = [];
    $selected_columns[$table_name] = $column_names;
    
    echo "<h3>Applying Fixes:</h3>\n";
    
    // Update the data source
    $update_data = [
        'data_source_type' => 'standard',
        'selected_columns' => json_encode($selected_columns),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $updated = database::table('autobooks_data_sources')
        ->where('id', $data_source_id)
        ->update($update_data);
    
    if ($updated) {
        echo "<p>✅ Fixed data_source_type: 'multi_table_merger' → 'standard'</p>\n";
        echo "<p>✅ Fixed selected_columns: NULL → " . json_encode($selected_columns) . "</p>\n";
        
        // Verify the column aliases are still there
        $updated_source = database::table('autobooks_data_sources')
            ->where('id', $data_source_id)
            ->first();
        
        $column_aliases = json_decode($updated_source['column_aliases'], true);
        
        echo "<h3>Column Aliases (should work now):</h3>\n";
        if (!empty($column_aliases)) {
            echo "<ul>\n";
            foreach ($column_aliases as $column_key => $alias) {
                echo "<li><strong>{$column_key}</strong> → <em>{$alias}</em></li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>❌ No column aliases found</p>\n";
        }
        
        echo "<h3>✅ Fix Complete!</h3>\n";
        echo "<p>The data source should now work correctly with:</p>\n";
        echo "<ul>\n";
        echo "<li>✅ Standard data source type</li>\n";
        echo "<li>✅ Selected columns configured</li>\n";
        echo "<li>✅ Column aliases active</li>\n";
        echo "</ul>\n";
        echo "<p><strong>Refresh your data table to see the column aliases in action!</strong></p>\n";
        
    } else {
        echo "<p>❌ Failed to update data source</p>\n";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error</h3>\n";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Fix Data Source</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2, h3 { color: #333; }
        ul { margin: 10px 0; }
        li { margin: 5px 0; }
        p { margin: 10px 0; }
    </style>
</head>
<body>
    <!-- Content is echoed above -->
</body>
</html>
