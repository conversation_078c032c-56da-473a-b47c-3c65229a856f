<?php
/**
 * Fix script to repair SketchUp configuration issues
 * Access via browser: http://localhost/autobooks/fix_sketchup_config.php
 */

header('Content-Type: text/plain');

echo "=== Fixing SketchUp Configuration Issues ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the startup sequence to initialize everything
    require_once 'system/startup_sequence.php';
    
    echo "1. System initialized successfully\n\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    echo "2. Cleaning up malformed configurations...\n";
    
    // Delete all existing malformed configurations for this table
    $deleted_count = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->delete();
    
    echo "Deleted {$deleted_count} existing configurations\n";
    
    // Find the data source for this table
    echo "\n3. Finding data source...\n";
    
    $data_sources = system\database::table('autobooks_data_sources')
        ->where('table_name', $table_name)
        ->get();
    
    if (empty($data_sources)) {
        echo "❌ No data source found for table {$table_name}\n";
        exit(1);
    }
    
    $data_source = $data_sources[0];
    $data_source_id = $data_source['id'];
    
    echo "Found data source: ID {$data_source_id}, Name: {$data_source['name']}\n";
    
    // Get sample data to determine available fields
    echo "\n4. Getting available fields from data source...\n";
    
    $sample_result = system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);
    
    if (!$sample_result['success'] || empty($sample_result['data'])) {
        echo "❌ Failed to get sample data from data source\n";
        exit(1);
    }
    
    $first_row = reset($sample_result['data']);
    $available_fields = array_keys($first_row);
    
    // Filter out system columns
    $available_fields = array_filter($available_fields, function($field) {
        return !in_array($field, ['id', 'data_hash']);
    });
    
    echo "Available fields: " . count($available_fields) . "\n";
    echo "Sample fields: " . implode(', ', array_slice($available_fields, 0, 10)) . "...\n";
    
    // Generate proper column structure using unified field matching
    echo "\n5. Generating proper column structure...\n";
    
    $column_structure = generate_unified_column_structure($available_fields, $data_source_id);
    
    echo "Generated structure:\n";
    echo "- Visible columns: " . count($column_structure['visible_columns']) . "\n";
    echo "- Hidden columns: " . count($column_structure['hidden_column_ids']) . "\n";
    echo "- Available fields: " . count($column_structure['available_fields']) . "\n";
    
    // Create proper configuration
    echo "\n6. Creating proper configuration...\n";
    
    $proper_config = [
        'structure' => $column_structure['visible_columns'],
        'columns' => $column_structure['visible_columns'], // For backward compatibility
        'hidden' => $column_structure['hidden_column_ids'],
        'available_fields' => array_column($column_structure['available_fields'], 'field'),
        'data_source_type' => 'data_source',
        'data_source_id' => $data_source_id,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // Save the configuration (global - no user_id)
    $save_result = system\data_table_storage::save_configuration($table_name, $proper_config, null, $data_source_id);
    
    if ($save_result) {
        echo "✅ Global configuration saved successfully\n";
    } else {
        echo "❌ Failed to save global configuration\n";
    }
    
    // Also create a user-specific configuration for user ID 2 (from the error)
    $user_id = 2;
    $user_save_result = system\data_table_storage::save_configuration($table_name, $proper_config, $user_id, $data_source_id);
    
    if ($user_save_result) {
        echo "✅ User configuration saved successfully for user {$user_id}\n";
    } else {
        echo "❌ Failed to save user configuration\n";
    }
    
    // Verify the configuration
    echo "\n7. Verifying the fixed configuration...\n";
    
    $verified_config = system\data_table_storage::get_configuration($table_name, null);
    
    if ($verified_config) {
        $config = $verified_config['configuration'];
        echo "✅ Configuration verified:\n";
        echo "- Structure count: " . count($config['structure'] ?? []) . "\n";
        echo "- Columns count: " . count($config['columns'] ?? []) . "\n";
        echo "- Available fields count: " . count($config['available_fields'] ?? []) . "\n";
        echo "- Data source ID: " . ($config['data_source_id'] ?? 'none') . "\n";
        
        // Check if columns are properly formed
        if (!empty($config['columns'])) {
            $first_column = $config['columns'][0];
            echo "- First column field: " . ($first_column['field'] ?? 'null') . "\n";
            echo "- First column label: " . ($first_column['label'] ?? 'empty') . "\n";
            echo "- First column unified_field: " . ($first_column['unified_field'] ?? 'none') . "\n";
            
            $malformed_count = 0;
            foreach ($config['columns'] as $col) {
                if (empty($col['field']) || empty($col['label'])) {
                    $malformed_count++;
                }
            }
            
            if ($malformed_count == 0) {
                echo "✅ All columns are properly formed\n";
            } else {
                echo "❌ {$malformed_count} columns are still malformed\n";
            }
        }
        
        echo "\nVisible columns:\n";
        foreach (array_slice($config['columns'], 0, 10) as $i => $column) {
            $unified_info = !empty($column['unified_field']) ? " -> {$column['unified_field']}" : "";
            echo "  " . ($i + 1) . ". {$column['field']}: {$column['label']}{$unified_info}\n";
        }
        
        if (count($config['columns']) > 10) {
            echo "  ... and " . (count($config['columns']) - 10) . " more columns\n";
        }
        
        if (!empty($config['available_fields'])) {
            echo "\nAvailable fields: " . implode(', ', array_slice($config['available_fields'], 0, 10));
            if (count($config['available_fields']) > 10) {
                echo " ... and " . (count($config['available_fields']) - 10) . " more";
            }
            echo "\n";
        }
    } else {
        echo "❌ Configuration verification failed\n";
    }
    
    // Test data retrieval
    echo "\n8. Testing data retrieval...\n";
    
    try {
        $table_data = system\data_table_storage::get_table_data($table_name, [], [], null);
        
        if ($table_data['success']) {
            echo "✅ Data retrieval successful:\n";
            echo "- Records: " . $table_data['count'] . "\n";
            echo "- Source: " . $table_data['source'] . "\n";
            echo "- Available fields: " . count($table_data['available_fields'] ?? []) . "\n";
        } else {
            echo "❌ Data retrieval failed\n";
        }
    } catch (Exception $e) {
        echo "❌ Error testing data retrieval: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== Fix Complete ===\n";
    echo "✅ SketchUp configuration has been repaired\n";
    echo "✅ Proper column structure with unified field matching applied\n";
    echo "✅ Both global and user-specific configurations created\n";
    echo "✅ Malformed configurations removed\n";
    
    echo "\nThe SketchUp view should now work correctly.\n";
    echo "Expected results:\n";
    echo "- Only core business columns visible initially\n";
    echo "- Technical fields available in column manager\n";
    echo "- Unified field labels instead of raw column names\n";
    echo "- No duplicate entry errors\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Fix ===\n";
?>
