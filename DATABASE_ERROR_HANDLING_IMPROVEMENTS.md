# Database Error Handling Improvements

## Overview
This document outlines the improvements made to the database error handling system to provide more graceful failure and comprehensive logging with input data for debugging purposes.

## Problem Statement
The original error was:
```json
{"error":"Error executing API function api\\nav_tree\\save_nav_entry: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"}
```

The issues with the original system were:
- Database errors caused the application to die with minimal information
- No logging of SQL parameters that caused the error
- No graceful fallback or recovery mechanisms
- Poor error messages for end users

## Improvements Made

### 1. Enhanced Database Class (`system/classes/database.class.php`)

#### New DatabaseException Class
- Created a custom `DatabaseException` class that extends `Exception`
- Stores query and parameters for detailed error reporting
- Provides `getDetailedMessage()` method for comprehensive error information

#### Improved executeQuery() Method
- Added comprehensive error logging with `logDatabaseError()` method
- Different error handling for API vs web requests
- Graceful error display instead of `die()` statements

#### Enhanced Error Logging
The `logDatabaseError()` method now logs:
- Error code and message
- SQL state and driver error details
- Complete SQL query and parameters
- Table name being accessed
- User ID, request URI, user agent, IP address
- Full stack trace
- Timestamp

#### Context-Aware Error Handling
- **API Requests**: Throws `DatabaseException` for caller to handle
- **Web Requests**: Shows user-friendly error pages
- **Debug Mode**: Shows detailed error information
- **Production Mode**: Shows generic error messages with unique error IDs

### 2. Enhanced Legacy Database Functions (`system/functions/database.php`)

#### Improved tep_db_error() Function
- Enhanced logging with more context information
- Parameter logging for better debugging
- Context-aware error responses (API vs web)
- Backwards compatibility maintained

#### Updated tep_db_query() Function
- Now passes parameters to error handler
- Enhanced query logging includes parameters
- Better error context for debugging

### 3. Enhanced API Error Handling (`system/api/nav_tree.api.php`)

#### save_nav_entry() Function
- Wrapped database operations in try-catch blocks
- Specific handling for `DatabaseException` vs general exceptions
- Detailed error logging with input data
- Appropriate error responses for different contexts
- Success logging for audit trails

#### get_navigation_items() Function
- Added error handling to prevent navigation breaking
- Returns empty array on database errors
- Comprehensive error logging
- Graceful degradation

## New Logging Features

### Log Categories
1. **database_errors**: New database class errors
2. **legacy_database_errors**: Legacy database function errors  
3. **navigation_errors**: Navigation-specific errors
4. **navigation**: Navigation success events

### Log Information Includes
- Complete error details (code, message, SQL state)
- Full SQL query and parameters
- Input data that caused the error
- User context (ID, IP, user agent, request URI)
- Stack traces for debugging
- Timestamps for correlation

## Error Response Improvements

### For API Requests
```json
{
  "error": "Database error occurred while saving navigation entry",
  "details": "Detailed error message (in debug mode)",
  "error_id": "unique_error_id"
}
```

### For Web Requests
- Debug mode: Detailed error information with query and parameters
- Production mode: User-friendly error message with unique error ID
- Styled error displays with proper formatting

## Benefits

1. **Better Debugging**: Complete context including SQL queries and parameters
2. **Graceful Degradation**: Applications continue to function when possible
3. **Audit Trail**: Success and failure events are logged
4. **User Experience**: Friendly error messages instead of technical details
5. **Security**: Sensitive information hidden in production mode
6. **Monitoring**: Unique error IDs for tracking and correlation

## Testing

A test script `test_database_error_handling.php` has been created to verify:
- Invalid table name handling
- Invalid column handling  
- SQL syntax error handling
- Log file creation and content

## Usage Examples

### Handling Database Errors in API Functions
```php
try {
    $result = database::table('my_table')->insert($data);
    // Success logging
    tcs_log(['action' => 'record_created', 'data' => $data], 'my_module');
} catch (\system\DatabaseException $e) {
    // Log detailed error with input data
    tcs_log([
        'action' => 'record_creation_failed',
        'error' => $e->getMessage(),
        'query' => $e->getQuery(),
        'params' => $e->getParams(),
        'input_data' => $data
    ], 'my_module_errors');
    
    // Return appropriate response
    if (defined('API_RUN') && API_RUN) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Database error', 'error_id' => uniqid()]);
    }
}
```

## Configuration

The error handling respects existing configuration:
- `DEBUG_MODE`: Controls detail level in error messages
- `API_RUN`: Determines response format (JSON vs HTML)
- `STORE_DB_TRANSACTIONS`: Legacy logging compatibility
- `FS_LOGS`: Log file directory location

## Backwards Compatibility

All changes maintain backwards compatibility:
- Existing database function signatures unchanged
- Legacy logging still works
- Existing error handling patterns still function
- No breaking changes to public APIs
