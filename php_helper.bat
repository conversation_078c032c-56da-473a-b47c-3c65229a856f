@echo off
REM PHP Helper Script
REM Use this to run PHP commands with the correct PHP installation

if "%1"=="xampp" (
    echo Using XAMPP PHP for database operations...
    E:\tools\xampp\php\php.exe %2 %3 %4 %5 %6 %7 %8 %9
) else if "%1"=="standalone" (
    echo Using standalone PHP...
    E:\tools\php\php.exe %2 %3 %4 %5 %6 %7 %8 %9
) else if "%1"=="db" (
    echo Running database command with XAMPP PHP...
    E:\tools\xampp\php\php.exe %2 %3 %4 %5 %6 %7 %8 %9
) else if "%1"=="syntax" (
    echo Checking syntax with standalone PHP...
    E:\tools\php\php.exe -l %2
) else (
    echo PHP Helper Usage:
    echo   php_helper.bat xampp [script]     - Use XAMPP PHP (for database operations)
    echo   php_helper.bat standalone [script] - Use standalone PHP
    echo   php_helper.bat db [script]        - Use XAMPP PHP for database operations
    echo   php_helper.bat syntax [file]      - Check syntax with standalone PHP
    echo.
    echo Examples:
    echo   php_helper.bat db local_dev_helper.php test
    echo   php_helper.bat syntax system/paths.php
    echo   php_helper.bat xampp db_sync.php pull
)
