<!DOCTYPE html>
<html>
<head>
    <title>CSV Data Source Migration Test</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">CSV Data Source Migration Test</h1>
        
        <!-- Summary Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">CSV Tables Summary</h2>
            <div id="summary-content">
                <button hx-get="<?= APP_ROOT ?>/api/csv_data_source_migration/get_csv_table_summary"
                        hx-target="#summary-content"
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Load Summary
                </button>
            </div>
        </div>
        
        <!-- Migration Actions -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Migration Actions</h2>
            
            <div class="space-y-4">
                <!-- Dry Run -->
                <div>
                    <button hx-post="<?= APP_ROOT ?>/api/csv_data_source_migration/create_missing_data_sources"
                            hx-vals='{"dry_run": true}'
                            hx-target="#migration-results"
                            class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 mr-4">
                        🔍 Dry Run (Preview Changes)
                    </button>
                    <span class="text-sm text-gray-600">See what would be created without making changes</span>
                </div>
                
                <!-- Actual Migration -->
                <div>
                    <button hx-post="<?= APP_ROOT ?>/api/csv_data_source_migration/create_missing_data_sources"
                            hx-vals='{"dry_run": false}'
                            hx-target="#migration-results"
                            hx-confirm="Are you sure you want to create data sources for all CSV tables missing them?"
                            class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-4">
                        ✅ Create Missing Data Sources
                    </button>
                    <span class="text-sm text-gray-600">Actually create the data sources with unified field mapping</span>
                </div>
            </div>
            
            <div id="migration-results" class="mt-6"></div>
        </div>
        
        <!-- Test New CSV Import -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Test New CSV Import</h2>
            <p class="text-gray-600 mb-4">Upload a CSV file to test the new automatic data source creation with unified field mapping.</p>
            
            <form hx-post="<?= APP_ROOT ?>/api/enhanced_data/upload_csv"
                  hx-encoding="multipart/form-data"
                  hx-target="#upload-results"
                  class="space-y-4">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Table Name:</label>
                    <input type="text" 
                           name="table_name" 
                           placeholder="test_csv_import"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">CSV File:</label>
                    <input type="file" 
                           name="csv_file" 
                           accept=".csv"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="replace_table" value="1" class="mr-2">
                        <span class="text-sm text-gray-700">Replace table if it exists</span>
                    </label>
                </div>
                
                <button type="submit" 
                        class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    Upload and Import CSV
                </button>
            </form>
            
            <div id="upload-results" class="mt-6"></div>
        </div>
    </div>

    <script>
        // Add some basic styling for HTMX responses
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            if (evt.detail.xhr.status >= 400) {
                evt.detail.target.innerHTML = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">Error: ' + evt.detail.xhr.responseText + '</div>';
            }
        });
    </script>
</body>
</html>

<?php
// Initialize minimal environment for testing
define('FS_API', __DIR__);
define('APP_ROOT', '');

// Include necessary files
require_once 'system/autoloader.php';
?>
