<?php
/**
 * Fix the bluebeam data source to use unified field mapping
 */

// Initialize environment
define('FS_API', __DIR__);
define('APP_ROOT', '');

require_once 'system/autoloader.php';

use system\data_source_manager;
use system\unified_field_mapper;
use system\database;

echo "<h2>Fixing Bluebeam Data Source</h2>\n";

$table_name = 'autobooks_import_bluebeam_data';

try {
    // Step 1: Get table information
    echo "<h3>Step 1: Analyzing Table</h3>\n";
    $table_info = data_source_manager::get_table_info($table_name);
    
    if (!$table_info) {
        throw new Exception("Table {$table_name} not found");
    }
    
    echo "✓ Table found: {$table_name}<br>\n";
    echo "✓ Row count: {$table_info['row_count']}<br>\n";
    echo "✓ Column count: " . count($table_info['columns']) . "<br>\n";
    
    // Step 2: Get column names
    $column_names = [];
    foreach ($table_info['columns'] as $column) {
        if (!in_array($column['Field'], ['id', 'created_at', 'updated_at'])) {
            $column_names[] = $column['Field'];
        }
    }
    
    echo "✓ Columns for mapping: " . implode(', ', $column_names) . "<br>\n";
    
    // Step 3: Generate field mapping suggestions
    echo "<h3>Step 2: Generating Field Mappings</h3>\n";
    $suggestions = unified_field_mapper::suggest_field_mappings($column_names);
    
    echo "Field mapping suggestions:<br>\n";
    foreach ($suggestions as $column_name => $suggestion) {
        echo "- <strong>{$column_name}</strong> → {$suggestion['field_name']} ";
        echo "(confidence: {$suggestion['confidence']}%, score: {$suggestion['final_score']})<br>\n";
    }
    
    // Step 4: Build unified mappings
    $unified_mappings = [
        'min_confidence' => 75,
        'applied' => [],
        'overrides' => []
    ];
    
    $applied_count = 0;
    foreach ($suggestions as $column_name => $suggestion) {
        if ($suggestion['confidence'] >= 75) {
            $unified_mappings['applied'][$column_name] = [
                'category' => $suggestion['field_name'],
                'field_name' => $suggestion['field_name'],
                'confidence' => $suggestion['confidence'],
                'final_score' => $suggestion['final_score'],
                'normalized_fields' => $suggestion['normalized_fields']
            ];
            $applied_count++;
        }
    }
    
    echo "<br>✓ Applied mappings: {$applied_count} out of " . count($suggestions) . "<br>\n";
    
    // Step 5: Delete the old basic data source
    echo "<h3>Step 3: Replacing Data Source</h3>\n";
    
    // Find and delete the old data source
    $old_sources = database::table('autobooks_data_sources')
        ->where('table_name', $table_name)
        ->get();
    
    foreach ($old_sources as $old_source) {
        echo "Deleting old data source ID {$old_source['id']}: {$old_source['name']}<br>\n";
        database::table('autobooks_data_sources')
            ->where('id', $old_source['id'])
            ->delete();
    }
    
    // Step 6: Create new data source with unified field mapping
    $data_source_config = [
        'name' => 'CSV Import: Bluebeam Data (Enhanced)',
        'table_name' => $table_name,
        'description' => "Enhanced data source with unified field mapping ({$table_info['row_count']} rows, " . count($column_names) . " columns)",
        'category' => 'csv_import',
        'data_source_type' => 'multi_table_merger',
        'mapping_method' => 'unified_field_mapper',
        'resolved_tables' => [$table_name],
        'unified_mappings' => $unified_mappings,
        'tables' => [$table_name],
        'status' => 'active'
    ];
    
    echo "Creating new data source with config:<br>\n";
    echo "<pre>" . json_encode($data_source_config, JSON_PRETTY_PRINT) . "</pre>\n";
    
    $data_source_id = data_source_manager::create_data_source($data_source_config);
    
    echo "✅ <strong>Created new data source ID: {$data_source_id}</strong><br>\n";
    
    // Step 7: Test column alias generation
    echo "<h3>Step 4: Testing Column Aliases</h3>\n";
    $aliases = data_source_manager::generate_unified_field_aliases($table_name, $unified_mappings);
    
    echo "Generated column aliases:<br>\n";
    if (!empty($aliases)) {
        foreach ($aliases as $original => $selected) {
            echo "- <strong>{$original}</strong> AS <em>{$original}_AS_{$selected}</em><br>\n";
        }
    } else {
        echo "No aliases generated (no matches above confidence threshold)<br>\n";
    }
    
    // Step 8: Test data retrieval
    echo "<h3>Step 5: Testing Data Retrieval</h3>\n";
    $test_data = data_source_manager::get_data_source_data($data_source_id, ['limit' => 3]);
    
    if ($test_data['success']) {
        echo "✅ Successfully retrieved " . count($test_data['data']) . " test rows<br>\n";
        if (!empty($test_data['data'])) {
            echo "Sample columns: " . implode(', ', array_keys($test_data['data'][0])) . "<br>\n";
        }
    } else {
        echo "❌ Failed to retrieve data: " . ($test_data['error'] ?? 'Unknown error') . "<br>\n";
    }
    
    echo "<h3>✅ Fix Complete!</h3>\n";
    echo "The bluebeam data source now has unified field mapping with column aliases.<br>\n";
    echo "Refresh your data table to see the enhanced field mappings!<br>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Error</h3>\n";
    echo "Error: " . $e->getMessage() . "<br>\n";
}
?>
