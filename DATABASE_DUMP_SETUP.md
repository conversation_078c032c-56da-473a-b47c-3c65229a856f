# Database Dump Manager Setup

This system provides a web-based interface for dumping database tables directly to the `system/sql` folder, eliminating the need to manually download and extract files from phpMyAdmin.

## Features

- **Web-based interface** accessible at `/system/database_dump`
- **Multiple dump options**:
  - Dump all database tables
  - Dump only `autobooks_*` tables
  - Dump only `autodesk_*` tables
  - Select specific tables to dump
- **File management**: View and delete existing dump files
- **Direct server-side creation**: Files are created directly in `system/sql` folder
- **FTP-ready**: Download files easily with FileZilla sync

## Installation

### 1. Add Navigation Entry

Run this SQL script in phpMyAdmin or your MySQL client:

```sql
-- File: system/sql/add_database_dump_navigation.sql
```

This will add the "Database Dump Manager" to your System menu.

### 2. Verify System Views Configuration

The system views have been updated to include `database_dump`. The following files were modified:
- `system/config/path_schema.php` - Added to system_views array
- `_ide_helper_constants.php` - Updated SYSTEM_VIEWS constant

### 3. Test the Installation

Run the test script to verify everything works:

```bash
php test_database_dump.php
```

## Usage

### Accessing the Interface

1. Log in as an admin or dev user
2. Navigate to **System > Database Dump Manager**
3. Choose your dump option:

#### Quick Actions

- **Dump All Tables**: Creates individual SQL files for every table in the database
- **Dump Autobooks Tables Only**: Creates SQL files only for tables starting with `autobooks_`
- **Dump Autodesk Tables Only**: Creates SQL files only for tables starting with `autodesk_`

#### Custom Selection

- Select specific tables from the checkbox list
- Click "Dump Selected Tables" to create SQL files for only those tables

### File Naming Convention

Dump files are created with the naming pattern:
```
autobooks_table_{table_name}.sql
```

For example:
- `autobooks_table_autobooks_users.sql`
- `autobooks_table_autodesk_subscriptions.sql`

### File Management

The interface shows all existing SQL dump files with:
- File name
- File size
- Last modified date
- Delete action

### Downloading Files

Use FileZilla or your preferred FTP client to sync the `system/sql` folder:

1. Connect to your server via FTP
2. Navigate to the `system/sql` folder
3. Use FileZilla's sync feature to download new/updated files
4. Files are ready for local use or backup

## File Structure

```
system/
├── sql/                              # Dump files location
│   ├── autobooks_table_*.sql        # Individual table dumps
│   └── add_database_dump_navigation.sql
├── views/system/
│   ├── database_dump.view.php       # Main interface
│   └── database_dump.api.php        # API handlers
└── config/
    └── path_schema.php              # Updated with new system view
```

## Security

- **Role-based access**: Only admin and dev users can access the dump functionality
- **File validation**: Only .sql files with valid names are allowed
- **Directory traversal protection**: Filenames are validated to prevent security issues
- **Server-side processing**: All operations happen server-side for security

## Technical Details

### Database Connection

The system uses your existing database configuration from:
- `system/config/db_config.php`
- Automatically detects local vs remote environment

### SQL Generation

Each dump file includes:
- phpMyAdmin-compatible header
- `DROP TABLE IF EXISTS` statement
- Complete table structure (`CREATE TABLE`)
- All table data as `INSERT` statements
- Proper escaping and NULL handling

### Error Handling

- Comprehensive error reporting in the web interface
- Failed operations show detailed error messages
- Individual table failures don't stop batch operations

## Troubleshooting

### Database Connection Issues

If you get connection errors:
1. Verify your database credentials in `system/config/db_config.php`
2. Ensure MySQL/MariaDB is running
3. Check that PHP has PDO MySQL extension enabled

### Permission Issues

If files can't be created:
1. Check that `system/sql` folder exists and is writable
2. Verify web server has write permissions to the folder
3. Check PHP file permissions and ownership

### Missing Tables

If tables don't appear in the interface:
1. Verify database connection is working
2. Check that your user has SELECT privileges on the tables
3. Ensure the database name is correct in the configuration

## Benefits Over Manual Process

1. **No manual downloads**: Files are created directly on the server
2. **Batch operations**: Dump multiple tables at once
3. **Consistent naming**: Standardized file naming convention
4. **Version control friendly**: Files are ready for git commits
5. **FTP sync ready**: Easy download with FileZilla sync feature
6. **Time savings**: No more manual phpMyAdmin export/download/extract process

## Next Steps

After setup:
1. Test the functionality with a few tables
2. Set up FileZilla sync for the `system/sql` folder
3. Consider adding this to your regular backup workflow
4. Optionally create scheduled dumps for critical tables
