# Local Development Environment Setup

This guide helps you set up a local development environment with XAMPP and database synchronization.

## Prerequisites

✅ **PHP installed** at `E:\tools\php`  
✅ **XAMPP installed** at `E:\tools\xampp`  

## Quick Start

### 1. Start XAMPP Services

1. Open XAMPP Control Panel: `E:\tools\xampp\xampp-control.exe`
2. Start **Apache** and **MySQL** services
3. Verify they're running (green indicators)

### 2. Set Up Local Database

```bash
# Test your environment first
php local_dev_helper.php test

# Set up the local database
php local_dev_helper.php setup
```

### 3. Configure Your Application

Update your database configuration to use local settings when running locally.

**Option A: Use the smart config (recommended)**
Replace the include in your startup files:
```php
// Instead of: require_once('system/db_config.php');
require_once('system/db_config_local.php');
```

**Option B: Manual configuration**
Update `system/db_config.php` to detect local environment:
```php
$is_local = ($_SERVER['SERVER_NAME'] === 'localhost');
if ($is_local) {
    $db_server = '127.0.0.1';
    $db_username = 'root';
    $db_password = '';
    $db_database = 'autobooks_local';
}
```

### 4. Sync Data from Remote Server

```bash
# Pull data from your web server to local database
php db_sync.php pull
```

**Note:** You'll need to update the remote server details in `db_sync.php`:
- Replace `'your-remote-host.com'` with your actual server hostname
- Verify the username, password, and database name

## Available Commands

### Development Helper
```bash
php local_dev_helper.php [command]
```

**Commands:**
- `setup` - Set up local database
- `sync` - Sync data from remote
- `export` - Export local changes
- `test` - Test database connections
- `status` - Show XAMPP status
- `start` - Start XAMPP services
- `phpinfo` - Show PHP configuration

### Database Sync
```bash
php db_sync.php [action]
```

**Actions:**
- `pull` - Pull data from remote to local
- `export` - Export local changes to SQL files

## Local URLs

Once everything is running:

- **Your Application:** http://localhost/baffletrain/autocadlt/autobooks/
- **phpMyAdmin:** http://localhost/phpmyadmin/
- **XAMPP Dashboard:** http://localhost/

## Database Configuration

### Local Database
- **Host:** 127.0.0.1
- **Port:** 3306
- **Username:** root
- **Password:** (empty)
- **Database:** autobooks_local

### Remote Database (for sync)
- **Host:** your-remote-host.com
- **Username:** wwwcadservicescouk
- **Password:** S96#1kvYuCGE
- **Database:** wwwcadservicescouk

## Workflow

### Daily Development
1. Start XAMPP services
2. Pull latest data: `php db_sync.php pull`
3. Develop and test locally
4. Export changes: `php db_sync.php export`
5. Upload changes to remote server

### Testing PHP Scripts
```bash
# Syntax check
E:\tools\php\php.exe -l filename.php

# Run script
E:\tools\php\php.exe filename.php

# Start local development server
E:\tools\php\php.exe -S localhost:8000
```

## Troubleshooting

### MySQL Won't Start
- Check if port 3306 is in use: `netstat -an | findstr :3306`
- Stop any other MySQL services
- Check XAMPP error logs in `E:\tools\xampp\mysql\data\`

### Connection Errors
- Verify XAMPP MySQL is running
- Check firewall settings
- Test connection: `php local_dev_helper.php test`

### PHP Issues
- Verify PHP path: `E:\tools\php\php.exe --version`
- Check PHP extensions: `php local_dev_helper.php phpinfo`

### Sync Issues
- Update remote server details in `db_sync.php`
- Check network connectivity to remote server
- Verify remote database credentials

## File Structure

```
project/
├── local_db_setup.php          # Database setup script
├── db_sync.php                 # Database synchronization
├── local_dev_helper.php        # Development helper commands
├── system/
│   ├── db_config.php           # Original config
│   └── db_config_local.php     # Smart local/remote config
└── exports/                    # Exported SQL files
```

## Security Notes

- Local database has no password (XAMPP default)
- Don't commit database passwords to version control
- Use environment variables for sensitive data in production
- The local environment is for development only

## Next Steps

1. **Set up version control** for your database schema changes
2. **Create migration scripts** for database updates
3. **Set up automated backups** of your remote database
4. **Configure SSL** for local HTTPS testing if needed

## Support

If you encounter issues:
1. Run `php local_dev_helper.php test` to diagnose problems
2. Check XAMPP error logs
3. Verify all file paths and configurations
