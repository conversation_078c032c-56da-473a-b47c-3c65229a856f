<?php
/**
 * Clean up duplicate data table storage configurations
 */

// Include the main application
require_once 'index.php';

use system\data_table_storage;
use system\database;

echo "<h2>Cleaning Up Duplicate Data Table Configurations</h2>\n";

try {
    $table_name = 'autobooks_import_bluebeam_data';
    
    echo "<h3>Current Configurations for {$table_name}:</h3>\n";
    
    // Get all configurations for this table
    $all_configs = database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->get();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>ID</th><th>User ID</th><th>Data Source ID</th><th>Created At</th><th>Updated At</th></tr>\n";
    
    foreach ($all_configs as $config) {
        echo "<tr>";
        echo "<td>{$config['id']}</td>";
        echo "<td>" . ($config['user_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($config['data_source_id'] ?? 'NULL') . "</td>";
        echo "<td>{$config['created_at']}</td>";
        echo "<td>{$config['updated_at']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<p><strong>Total configurations found:</strong> " . count($all_configs) . "</p>\n";
    
    if (count($all_configs) > 1) {
        echo "<h3>Cleaning Up Duplicates...</h3>\n";
        
        // Clean up duplicates - keep user-specific config (user_id = 2)
        $cleanup_result = data_table_storage::cleanup_duplicates($table_name, 2);
        
        if ($cleanup_result['success']) {
            echo "<p>✅ <strong>{$cleanup_result['message']}</strong></p>\n";
            echo "<p>Removed: {$cleanup_result['removed_count']} configurations</p>\n";
            echo "<p>Kept: {$cleanup_result['kept_count']} configurations</p>\n";
            
            // Show remaining configurations
            echo "<h3>Remaining Configurations:</h3>\n";
            $remaining_configs = database::table('autobooks_data_table_storage')
                ->where('table_name', $table_name)
                ->get();
            
            if (!empty($remaining_configs)) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
                echo "<tr><th>ID</th><th>User ID</th><th>Data Source ID</th><th>Created At</th><th>Updated At</th></tr>\n";
                
                foreach ($remaining_configs as $config) {
                    echo "<tr>";
                    echo "<td>{$config['id']}</td>";
                    echo "<td>" . ($config['user_id'] ?? 'NULL') . "</td>";
                    echo "<td>" . ($config['data_source_id'] ?? 'NULL') . "</td>";
                    echo "<td>{$config['created_at']}</td>";
                    echo "<td>{$config['updated_at']}</td>";
                    echo "</tr>\n";
                }
                echo "</table>\n";
            } else {
                echo "<p>❌ No configurations remaining!</p>\n";
            }
            
        } else {
            echo "<p>❌ Cleanup failed: {$cleanup_result['error']}</p>\n";
        }
        
    } else {
        echo "<p>✅ No duplicates found - only one configuration exists.</p>\n";
    }
    
    echo "<h3>✅ Cleanup Complete!</h3>\n";
    echo "<p>The duplicate configuration issue has been resolved.</p>\n";
    echo "<p><strong>Future imports will be handled correctly by the updated template logic.</strong></p>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Error</h3>\n";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Cleanup Duplicate Configurations</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2, h3 { color: #333; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        p { margin: 10px 0; }
    </style>
</head>
<body>
    <!-- Content is echoed above -->
</body>
</html>
