# Path Definitions Configuration

This document explains the new centralized path definitions system that makes the IDE helper generation more maintainable.

## Overview

Previously, all path descriptions, categories, and templates were hardcoded in the `system/paths.php` file. This made it difficult to maintain and customize the IDE helper generation. Now, all these definitions are centralized in `system/config/path_definitions.php`.

## File Structure

### `system/config/path_definitions.php`

This file contains all the configuration for path descriptions and IDE helper generation:

```php
return [
    'ide_helper' => [...],      // IDE helper file templates
    'categories' => [...],      // Constant categorization rules
    'descriptions' => [...],    // Specific constant descriptions
    'patterns' => [...],        // Pattern-based description templates
    'default_description' => '...', // Fallback description template
    'additional_constants' => [...] // Extra constants for IDE helper
];
```

## Configuration Sections

### 1. IDE Helper Templates (`ide_helper`)

Controls the generated IDE helper file structure:

- **file_header**: Title, description, and warning messages
- **safety_check**: Condition and message for preventing direct execution
- **section_separator**: Character pattern for section dividers

### 2. Categories (`categories`)

Defines how constants are grouped in the IDE helper file. Order matters - more specific prefixes should come first:

```php
'categories' => [
    'FS_FULL_' => 'Combo File System Paths',
    'FULL_' => 'Combo Web Paths',
    'FS_SYS_' => 'System File Paths',
    'FS_' => 'File System Paths',
    // ... more categories
]
```

### 3. Specific Descriptions (`descriptions`)

Exact descriptions for specific constants. These override any pattern-based descriptions:

```php
'descriptions' => [
    'FS_APP_ROOT' => 'File system path to application root directory',
    'APP_ROOT' => 'Web path to application root',
    // ... more descriptions
]
```

### 4. Pattern Templates (`patterns`)

Templates for generating descriptions based on constant name patterns:

```php
'patterns' => [
    'FS_FULL_' => 'File system combo path for {part}',
    'FS_' => 'File system path to {part} directory',
    // ... more patterns
]
```

The `{part}` placeholder is replaced with the extracted part of the constant name.

### 5. Additional Constants (`additional_constants`)

Constants that might be defined elsewhere but should be included in the IDE helper:

```php
'additional_constants' => [
    'DEBUG_MODE' => [
        'value' => true,
        'type' => 'boolean',
        'source_key' => 'debug_mode',
        'description' => 'Debug mode flag for development environment'
    ]
]
```

## Benefits

### 1. Maintainability
- All descriptions and templates in one place
- Easy to add new constant types
- Clear separation of concerns

### 2. Customization
- Easy to modify descriptions without touching core logic
- Simple to add new categories or patterns
- Flexible template system

### 3. Consistency
- Standardized description formats
- Consistent categorization rules
- Unified configuration approach

## Usage

### Adding New Constant Types

1. **Add to categories** (if needed):
```php
'categories' => [
    'NEW_PREFIX_' => 'New Category Name',
    // ... existing categories
]
```

2. **Add specific descriptions** (if needed):
```php
'descriptions' => [
    'NEW_CONSTANT_NAME' => 'Specific description for this constant',
    // ... existing descriptions
]
```

3. **Add pattern template** (if needed):
```php
'patterns' => [
    'NEW_PREFIX_' => 'Template for {part} constants',
    // ... existing patterns
]
```

### Modifying Existing Descriptions

Simply edit the appropriate section in `system/config/path_definitions.php`:

```php
'descriptions' => [
    'FS_APP_ROOT' => 'Your new description here',
    // ... other descriptions
]
```

### Customizing IDE Helper Format

Modify the `ide_helper` section:

```php
'ide_helper' => [
    'file_header' => [
        'title' => 'Your Custom Title',
        'description' => 'Your custom description',
        // ... other header fields
    ],
    'section_separator' => str_repeat('-', 50) // Custom separator
]
```

## Migration Notes

The system is backward compatible. If the `path_definitions.php` file is missing, the system falls back to basic defaults. However, for full functionality and maintainability, the definitions file should be present.

## Files Modified

- **`system/config/path_definitions.php`** - New centralized configuration
- **`system/paths.php`** - Updated to use definitions, IDE helper functions moved out
- **`system/generate_ide_helper.php`** - Contains IDE helper functions and uses definitions
- **`generate_constants_helper.php`** - Contains IDE helper functions and works with new system

## Performance Optimization

The IDE helper functions (`generate_ide_helper_constants`, `generate_constant_definition`, `generate_constant_description`) have been moved from `system/paths.php` into the generator scripts themselves. This provides several benefits:

### Production Performance
- **Reduced memory footprint**: IDE helper functions are not loaded in production
- **Faster startup**: Less code to parse and load during normal operation
- **Cleaner separation**: Core path functionality separated from development tools

### Development Functionality
- **Full IDE support**: All IDE helper features remain available during development
- **Graceful degradation**: If IDE helper functions aren't available, the system continues to work
- **Maintainable code**: IDE helper logic is contained within the scripts that use it

## Testing

After making changes to the definitions file, regenerate the IDE helper:

```bash
php generate_constants_helper.php
# or
php system/generate_ide_helper.php --force
```

Then check the generated `_ide_helper_constants.php` file to ensure your changes are applied correctly.
