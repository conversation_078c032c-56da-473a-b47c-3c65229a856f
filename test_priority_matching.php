<?php
/**
 * Test script for the enhanced priority-based field matching system
 */

// Initialize minimal environment
define('FS_API', __DIR__);
define('APP_ROOT', '');

require_once 'system/autoloader.php';

use system\unified_field_mapper;
use system\unified_field_definitions;

echo "<h2>Testing Enhanced Priority-Based Field Matching</h2>\n";

// Test case 1: Ambiguous 'name' field with different contexts
echo "<h3>Test 1: Ambiguous 'name' field matching</h3>\n";

$test_cases = [
    [
        'description' => 'Dataset with company context',
        'columns' => ['name', 'email', 'company_address', 'business_phone'],
        'expected_for_name' => 'company_name'
    ],
    [
        'description' => 'Dataset with contact context', 
        'columns' => ['name', 'personal_email', 'contact_phone', 'first_name'],
        'expected_for_name' => 'contact_name'
    ],
    [
        'description' => 'Dataset with product context',
        'columns' => ['name', 'product_version', 'license_key', 'software_type'],
        'expected_for_name' => 'product_name'
    ],
    [
        'description' => 'Mixed context dataset',
        'columns' => ['name', 'company_name', 'email', 'product'],
        'expected_for_name' => 'contact_name' // Should prefer more specific company_name over generic name
    ]
];

foreach ($test_cases as $i => $test_case) {
    echo "<h4>Test Case " . ($i + 1) . ": {$test_case['description']}</h4>\n";
    echo "Columns: " . implode(', ', $test_case['columns']) . "<br>\n";
    
    $suggestions = unified_field_mapper::suggest_field_mappings($test_case['columns']);
    
    if (isset($suggestions['name'])) {
        $suggestion = $suggestions['name'];
        echo "✓ 'name' matched to: <strong>{$suggestion['field_name']}</strong><br>\n";
        echo "  - Confidence: {$suggestion['confidence']}%<br>\n";
        echo "  - Final Score: {$suggestion['final_score']}<br>\n";
        echo "  - Priority: {$suggestion['priority']}<br>\n";
        echo "  - Contextual Score: {$suggestion['contextual_score']}<br>\n";
        
        if (!empty($suggestion['alternatives'])) {
            echo "  - Alternatives: ";
            foreach ($suggestion['alternatives'] as $alt) {
                echo "{$alt['field_name']} (score: {$alt['final_score']}) ";
            }
            echo "<br>\n";
        }
        
        $is_expected = ($suggestion['field_name'] === $test_case['expected_for_name']);
        echo "  - Expected: {$test_case['expected_for_name']} | " . 
             ($is_expected ? "✅ PASS" : "❌ FAIL") . "<br>\n";
    } else {
        echo "❌ No match found for 'name'<br>\n";
    }
    echo "<br>\n";
}

// Test case 2: Priority ordering
echo "<h3>Test 2: Priority-based field ordering</h3>\n";

$priority_test_columns = ['subscription_reference', 'email', 'company_name', 'name'];
$suggestions = unified_field_mapper::suggest_field_mappings($priority_test_columns);

echo "Field matching results (should be ordered by priority):<br>\n";
foreach ($priority_test_columns as $column) {
    if (isset($suggestions[$column])) {
        $suggestion = $suggestions[$column];
        echo "- <strong>{$column}</strong> → {$suggestion['field_name']} (priority: {$suggestion['priority']}, score: {$suggestion['final_score']})<br>\n";
    } else {
        echo "- <strong>{$column}</strong> → No match<br>\n";
    }
}

// Test case 3: Field definitions statistics
echo "<h3>Test 3: Field Definitions Statistics</h3>\n";
$stats = unified_field_definitions::get_field_statistics();
echo "Total fields: {$stats['total_fields']}<br>\n";
echo "Default fields: {$stats['default_fields']}<br>\n";
echo "Custom fields: {$stats['custom_fields']}<br>\n";
echo "Matching enabled: {$stats['matching_enabled']}<br>\n";
echo "Categories: " . implode(', ', array_keys($stats['categories'])) . "<br>\n";

echo "<h3>Test 4: Enabled Matching Fields</h3>\n";
$matching_fields = unified_field_definitions::get_matching_fields();
echo "Fields enabled for matching:<br>\n";
foreach ($matching_fields as $field_name => $definition) {
    $priority = $definition['matching']['priority'] ?? 'N/A';
    echo "- <strong>{$field_name}</strong> (priority: {$priority})<br>\n";
}

echo "<h3>Tests Complete!</h3>\n";
echo "The enhanced priority-based matching system is now active. Key improvements:<br>\n";
echo "✓ Priority-based field selection<br>\n";
echo "✓ Contextual scoring based on other columns<br>\n";
echo "✓ Alternative match suggestions<br>\n";
echo "✓ Manual override capabilities<br>\n";
echo "✓ Enhanced UI with detailed scoring information<br>\n";
?>
