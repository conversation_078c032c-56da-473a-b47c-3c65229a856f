# Database Error Logging Expansion

## Overview

This document outlines the improvements made to expand database error logging throughout the codebase to include both query details and error messages for better debugging and monitoring.

## Changes Made

### 1. Enhanced Schema Error Handling (`system/classes/database.class.php`)

#### Schema Class Updates:
- **Enhanced `Schema::execute()` method** to catch PDOException and convert to DatabaseException
- **Added `Schema::handleSchemaError()` method** for comprehensive schema error logging
- **Updated `Blueprint::execute()` method** to use enhanced error handling

#### Before:
```php
private static function execute(string $query, array $params = []): PDOStatement {
    $pdo = self::getConnection();
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    return $stmt;
}
```

#### After:
```php
private static function execute(string $query, array $params = []): PDOStatement {
    try {
        $pdo = self::getConnection();
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        // Enhanced error handling for schema operations
        self::handleSchemaError($e, $query, $params);
    }
}
```

**Key Improvements:**
- Schema operations (CREATE TABLE, ALTER TABLE, etc.) now throw `DatabaseException`
- Comprehensive error logging includes the exact DDL query that failed
- Structured logging with operation type and context information

### 2. Enhanced Hilt Class Error Handling (`system/classes/hilt.class.php`)

#### Before:
```php
} catch (\Exception $e) {
    tcs_log("Error creating table {$table_name}: " . $e->getMessage(), self::$log_target);
    return false;
}
```

#### After:
```php
} catch (\system\DatabaseException $e) {
    // Enhanced logging for database-specific errors
    tcs_log([
        'action' => 'table_creation_failed',
        'table_name' => $table_name,
        'error_message' => $e->getMessage(),
        'query' => $e->getQuery(),
        'parameters' => $e->getParams(),
        'detailed_message' => $e->getDetailedMessage()
    ], self::$log_target, true);
    return false;
} catch (\Exception $e) {
    // Fallback for non-database errors
    tcs_log("Error creating table {$table_name}: " . $e->getMessage(), self::$log_target);
    return false;
}
```

**Improvements:**
- Now catches `DatabaseException` specifically to access query and parameter details
- Logs structured data including the actual SQL query and parameters
- Maintains fallback for non-database exceptions
- Uses verbose logging (`true` parameter) for detailed output

### 2. Enhanced Autodesk API Error Handling

#### Files Updated:
- `resources/classes/autodesk_api/autodesk_api.class.php`
- `resources/classes/autodesk_api/autodesk_subscriptions.class.php`
- `resources/classes/autodesk_api/autodesk_quotes.class.php`

#### Before:
```php
} catch (\Exception $e) {
    error_log("Failed to update storage for key '$key': " . $e->getMessage());
    return false;
}
```

#### After:
```php
} catch (\system\DatabaseException $e) {
    // Enhanced logging for database-specific errors
    tcs_log([
        'action' => 'autodesk_storage_update_failed',
        'storage_key' => $key,
        'error_message' => $e->getMessage(),
        'query' => $e->getQuery(),
        'parameters' => $e->getParams(),
        'detailed_message' => $e->getDetailedMessage()
    ], 'autodesk_api_errors', true);
    return false;
} catch (\Exception $e) {
    // Fallback for non-database errors
    tcs_log("Failed to update storage for key '$key': " . $e->getMessage(), 'autodesk_api_errors');
    return false;
}
```

**Improvements:**
- Replaced basic `error_log()` with structured `tcs_log()`
- Added specific log categories for different modules
- Included context-specific information (storage keys, data sizes)
- Maintained backward compatibility with fallback exception handling

## Benefits of Enhanced Logging

### 1. **Detailed Query Information**
- **Query**: The exact SQL statement that failed
- **Parameters**: The bound parameters that were used
- **Context**: Additional information like table names, storage keys, etc.

### 2. **Structured Logging**
- Consistent log format across all modules
- Easy to parse and analyze programmatically
- Better integration with log analysis tools

### 3. **Categorized Logs**
- `hilt` - Hilt template system errors
- `database_schema_errors` - Schema operation errors (CREATE TABLE, ALTER TABLE, etc.)
- `autodesk_api_errors` - General Autodesk API database errors
- `autodesk_subscriptions_errors` - Subscription-specific errors
- `autodesk_quotes_errors` - Quote-specific errors

### 4. **Improved Debugging**
- Developers can see the exact query that failed
- Parameter values help identify data-related issues
- Stack traces and detailed messages provide full context

## Log File Locations

Based on the `tcs_log()` function, logs are stored in:
- `{FS_SYS_LOGS}hilt_logfile.log`
- `{FS_SYS_LOGS}database_schema_errors_logfile.log`
- `{FS_SYS_LOGS}autodesk_api_errors_logfile.log`
- `{FS_SYS_LOGS}autodesk_subscriptions_errors_logfile.log`
- `{FS_SYS_LOGS}autodesk_quotes_errors_logfile.log`

## Example Log Output

### Before (Basic Logging):
```
[2025-06-30 10:30:15] Error creating table autobooks_test_data: SQLSTATE[42000]: Syntax error
```

### After (Enhanced Logging):
```json
{
    "action": "table_creation_failed",
    "table_name": "autobooks_test_data",
    "error_message": "SQLSTATE[42000]: Syntax error or access violation",
    "query": "CREATE TABLE autobooks_test_data (id INT AUTO_INCREMENT PRIMARY KEY, data_json JSON, data_hash VARCHAR(64), created_at TIMESTAMP, updated_at TIMESTAMP)",
    "parameters": [],
    "detailed_message": "SQLSTATE[42000]: Syntax error or access violation | Query: CREATE TABLE... | Params: []"
}
```

## Usage Guidelines

### For Developers:
1. **Always catch `DatabaseException` first** when handling database operations
2. **Use structured logging** with relevant context information
3. **Include action identifiers** for easy log filtering
4. **Maintain fallback exception handling** for non-database errors

### For System Administrators:
1. **Monitor specific log categories** based on system components
2. **Set up alerts** for database error patterns
3. **Use log analysis tools** to parse structured JSON logs
4. **Review query patterns** to identify performance issues

## Future Enhancements

1. **Add query execution time** to log entries
2. **Include user context** in all database operations
3. **Implement log rotation** for large log files
4. **Add database performance metrics** to logs
5. **Create dashboard** for real-time error monitoring
