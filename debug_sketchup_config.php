<?php
/**
 * Debug script to check SketchUp configuration issues
 * Access via browser: http://localhost/autobooks/debug_sketchup_config.php
 */

header('Content-Type: text/plain');

echo "=== Debugging SketchUp Configuration Issues ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the startup sequence to initialize everything
    require_once 'system/startup_sequence.php';
    
    echo "1. System initialized successfully\n\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    $user_id = 2; // From the error message
    
    echo "2. Checking existing configurations...\n";
    
    // Check if table exists
    $table_exists = system\database::tableExists($table_name);
    echo "Table exists: " . ($table_exists ? 'YES' : 'NO') . "\n";
    
    if ($table_exists) {
        // Get table structure
        $columns = system\database::getTableColumns($table_name);
        echo "Table columns: " . count($columns) . "\n";
        echo "Column names: " . implode(', ', array_keys($columns)) . "\n\n";
    }
    
    // Check data table storage configurations
    echo "3. Checking data table storage configurations...\n";
    
    // Check global config (user_id = null)
    $global_config = system\data_table_storage::get_configuration($table_name, null);
    echo "Global config exists: " . ($global_config ? 'YES' : 'NO') . "\n";
    
    if ($global_config) {
        $config = $global_config['configuration'];
        echo "Global config structure count: " . count($config['structure'] ?? []) . "\n";
        echo "Global config columns count: " . count($config['columns'] ?? []) . "\n";
        echo "Global config data_source_id: " . ($config['data_source_id'] ?? 'none') . "\n";
        
        // Check if columns have proper data
        if (!empty($config['columns'])) {
            $first_column = $config['columns'][0];
            echo "First column field: " . ($first_column['field'] ?? 'null') . "\n";
            echo "First column label: " . ($first_column['label'] ?? 'empty') . "\n";
        }
    }
    
    // Check user-specific config
    $user_config = system\data_table_storage::get_configuration($table_name, $user_id);
    echo "\nUser config exists: " . ($user_config ? 'YES' : 'NO') . "\n";
    
    if ($user_config) {
        $config = $user_config['configuration'];
        echo "User config structure count: " . count($config['structure'] ?? []) . "\n";
        echo "User config columns count: " . count($config['columns'] ?? []) . "\n";
        echo "User config data_source_id: " . ($config['data_source_id'] ?? 'none') . "\n";
        
        // Check if columns have proper data
        if (!empty($config['columns'])) {
            $first_column = $config['columns'][0];
            echo "First column field: " . ($first_column['field'] ?? 'null') . "\n";
            echo "First column label: " . ($first_column['label'] ?? 'empty') . "\n";
        }
    }
    
    // Check data sources
    echo "\n4. Checking data sources...\n";
    
    $data_sources = system\database::table('autobooks_data_sources')
        ->where('table_name', $table_name)
        ->get();
    
    echo "Data sources found: " . count($data_sources) . "\n";
    
    foreach ($data_sources as $ds) {
        echo "- Data source ID: {$ds['id']}, Name: {$ds['name']}, Status: {$ds['status']}\n";
        
        // Check if there's a config for this data source
        $ds_config = system\data_table_storage::get_configuration_by_data_source_id($ds['id'], null);
        echo "  Global config for DS {$ds['id']}: " . ($ds_config ? 'YES' : 'NO') . "\n";
        
        $ds_user_config = system\data_table_storage::get_configuration_by_data_source_id($ds['id'], $user_id);
        echo "  User config for DS {$ds['id']}: " . ($ds_user_config ? 'YES' : 'NO') . "\n";
    }
    
    // Check table config manager
    echo "\n5. Checking table config manager...\n";
    
    try {
        $legacy_config = system\table_config_manager::get_table_config($table_name);
        echo "Legacy config exists: " . ($legacy_config ? 'YES' : 'NO') . "\n";
        
        if ($legacy_config) {
            echo "Legacy config description: " . $legacy_config['description'] . "\n";
            echo "Legacy config data source: " . $legacy_config['data_source'] . "\n";
            
            $table_config = json_decode($legacy_config['table_config'], true);
            if ($table_config) {
                echo "Legacy config columns: " . count($table_config['columns'] ?? []) . "\n";
                echo "Legacy config available_fields: " . count($table_config['available_fields'] ?? []) . "\n";
            }
        }
    } catch (Exception $e) {
        echo "Error checking legacy config: " . $e->getMessage() . "\n";
    }
    
    // Test creating a proper configuration
    echo "\n6. Testing configuration creation...\n";
    
    if ($table_exists && !empty($data_sources)) {
        $data_source_id = $data_sources[0]['id'];
        echo "Using data source ID: {$data_source_id}\n";
        
        // Get sample data from the data source
        $sample_result = system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);
        
        if ($sample_result['success'] && !empty($sample_result['data'])) {
            $first_row = reset($sample_result['data']);
            $available_fields = array_keys($first_row);
            
            echo "Available fields from data source: " . count($available_fields) . "\n";
            echo "Sample fields: " . implode(', ', array_slice($available_fields, 0, 5)) . "...\n";
            
            // Test unified column structure generation
            $column_structure = generate_unified_column_structure($available_fields, $data_source_id);
            
            echo "Generated visible columns: " . count($column_structure['visible_columns']) . "\n";
            echo "Generated available fields: " . count($column_structure['available_fields']) . "\n";
            
            // Check first visible column
            if (!empty($column_structure['visible_columns'])) {
                $first_col = $column_structure['visible_columns'][0];
                echo "First visible column field: " . $first_col['field'] . "\n";
                echo "First visible column label: " . $first_col['label'] . "\n";
                echo "First visible column unified_field: " . ($first_col['unified_field'] ?? 'none') . "\n";
            }
        } else {
            echo "Failed to get sample data from data source\n";
        }
    }
    
    // Check all data table storage entries for this table
    echo "\n7. Checking all data table storage entries...\n";
    
    $all_entries = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->get();
    
    echo "Total entries for table: " . count($all_entries) . "\n";
    
    foreach ($all_entries as $entry) {
        echo "- Entry ID: {$entry['id']}, User ID: " . ($entry['user_id'] ?? 'null') . ", Data Source ID: " . ($entry['data_source_id'] ?? 'null') . "\n";
        
        $config = json_decode($entry['configuration'], true);
        if ($config) {
            echo "  Structure count: " . count($config['structure'] ?? []) . "\n";
            echo "  Columns count: " . count($config['columns'] ?? []) . "\n";
            
            // Check if columns are malformed
            if (!empty($config['columns'])) {
                $malformed_count = 0;
                foreach ($config['columns'] as $col) {
                    if (empty($col['field']) || empty($col['label'])) {
                        $malformed_count++;
                    }
                }
                echo "  Malformed columns: {$malformed_count}\n";
            }
        }
    }
    
    echo "\n=== Debug Complete ===\n";
    echo "This should help identify where the configuration issue is occurring.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Debug ===\n";
?>
