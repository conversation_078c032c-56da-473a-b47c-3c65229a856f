<?php
use system\users;
use edge\Edge;
// Ensure only admin/dev can access this page
users::requireRole('admin');
require_once 'logs.fn.php';
$logFiles = process_log_files($_GET);

// Define the column structure for the data table
$columns = [
    [
        'label' => 'File Name',
        'field' => 'name',
        'filter' => false
    ],
    [
        'label' => 'Size',
        'field' => 'size',
        'filter' => false,
        'content' => function($item) {
            return formatFileSize($item['size']);
        }
    ],
    [
        'label' => 'Last Modified',
        'field' => 'modified',
        'filter' => false,
        'content' => function($item) {
            return date('Y-m-d H:i:s', $item['modified']);
        }
    ],
    [
        'label' => 'Actions',
        'field' => 'actions',
        'filter' => false,
        'content' => function($item) {
            return '<a href="' . APP_ROOT . APP_PATH . '/view?file=' . urlencode($item['name']) . '" class="text-indigo-600 hover:text-indigo-900">View</a>' .
                   '<span class="mx-2 text-gray-300">|</span>' .
                   '<a href="' . APP_ROOT . APP_PATH . '?clear=' . urlencode($item['name']) . '" class="text-red-600 hover:text-red-900" onclick="return confirm(\'Are you sure you want to clear this log file?\');">Clear</a>';
        }
    ]
];

// Define the row parameters
$rows = [
    'id_prefix' => 'log_',
    'id_field' => 'name',
    'class_postfix' => '',
    'extra_parameters' => ''
];

// Message display for notifications
$messageDisplay = '';
if (isset($message)) {
    $messageDisplay = '<div class="mt-4 rounded-md bg-green-50 p-4">' .
                      '<div class="flex">' .
                      '<div class="flex-shrink-0">' .
                      '<svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">' .
                      '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />' .
                      '</svg>' .
                      '</div>' .
                      '<div class="ml-3">' .
                      '<p class="text-sm font-medium text-green-800">' . $message . '</p>' .
                      '</div>' .
                      '</div>' .
                      '</div>';
}
?>
<div class="p-10 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-base font-semibold leading-6 text-gray-900">Log Viewer</h1>
            <p class="mt-2 text-sm text-gray-700">Showing log files for the system located at: <?= htmlspecialchars(FS_LOGS); ?></p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <a href="<?= APP_ROOT . APP_PATH ?>/view?file=all" class="text-indigo-600 hover:text-indigo-900 mr-4">View All</a>
            <a href="<?= APP_ROOT . APP_PATH ?>?clear=all" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to clear all log files?');">Clear All</a>
        </div>
    </div>

    <?= $messageDisplay ?>

    <?= Edge::render('data-table', [
        'title' => 'Log Files',
        'description' => 'System log files',
        'items' => $logFiles,
        'columns' => $columns,
        'rows' => $rows,
        'class' => 'mt-8'
    ]) ?>
</div>
