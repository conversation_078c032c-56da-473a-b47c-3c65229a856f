<?php
/**
 * Fix Autodesk Catalog Hashes - System View
 *
 * This page provides a web interface to fix the unique hashes in the 
 * products_autodesk_catalog table by regenerating them from field values.
 */

// Check admin access using proper role hierarchy
use system\users;
use system\data_importer;
users::requireRole('admin');


/**
 * Generate hash string from field values
 */
function generate_hash_string($row) {
    $hash_fields = [
        'offeringId',
        'intendedUsage_code', 
        'accessModel_code',
        'servicePlan_code',
        'connectivity_code',
        'term_code',
        'orderAction',
        'specialProgramDiscount_code',
        'fromQty',
        'toQty'
    ];
    
    $hash_values = [];
    foreach ($hash_fields as $field) {
        $value = isset($row[$field]) ? $row[$field] : '';
        if ($value === null) {
            $value = '';
        }
        $hash_values[] = $value;
    }
    
    return implode('', $hash_values);
}

/**
 * Generate unique hash from hash string
 */
function generate_unique_hash($hash_string) {
    return hash('crc32', $hash_string);
}

// Process form submission
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_hashes'])) {
    $batch_size = isset($_POST['batch_size']) ? (int)$_POST['batch_size'] : 100;
    $dry_run = isset($_POST['dry_run']);
    
    $offset = 0;
    $total_records = 0;
    $updated_records = 0;
    $failed_records = 0;
    $duplicate_hashes = [];
    $changes = [];
    
    try {
        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM products_autodesk_catalog";
        $count_result = tep_db_query($count_query);
        $count_row = tep_db_fetch_array($count_result);
        $total_count = $count_row['total'];
        
        $hash_usage = [];
        
        // Process records in batches
        while (true) {
            $query = "SELECT id, unique_hash, hash_string, offeringId, intendedUsage_code, 
                             accessModel_code, servicePlan_code, connectivity_code, term_code, 
                             orderAction, specialProgramDiscount_code, fromQty, toQty 
                      FROM products_autodesk_catalog 
                      LIMIT {$batch_size} OFFSET {$offset}";
            
            $query_result = tep_db_query($query);
            $batch_count = 0;
            $batch_updates = [];

            while ($row = tep_db_fetch_array($query_result)) {
                $batch_count++;
                $total_records++;

                $new_hash_string = generate_hash_string($row);
                $new_unique_hash = generate_unique_hash($new_hash_string);
                
                $old_hash_string = $row['hash_string'];
                $old_unique_hash = $row['unique_hash'];

                // Track hash usage
                if (!isset($hash_usage[$new_unique_hash])) {
                    $hash_usage[$new_unique_hash] = [];
                }
                $hash_usage[$new_unique_hash][] = [
                    'id' => $row['id'],
                    'hash_string' => $new_hash_string
                ];

                // Check if update is needed
                if ($new_hash_string !== $old_hash_string || $new_unique_hash !== $old_unique_hash) {
                    $change = [
                        'id' => $row['id'],
                        'old_hash_string' => $old_hash_string,
                        'new_hash_string' => $new_hash_string,
                        'old_unique_hash' => $old_unique_hash,
                        'new_unique_hash' => $new_unique_hash
                    ];
                    
                    $batch_updates[] = $change;
                    $changes[] = $change;
                }
            }

            if ($batch_count === 0) {
                break;
            }

            // Update records if not dry run
            if (!$dry_run) {
                foreach ($batch_updates as $update) {
                    $update_query = "UPDATE products_autodesk_catalog 
                                   SET hash_string = :new_hash_string, unique_hash = :new_unique_hash 
                                   WHERE id = :id";
                    $params = [
                        ':new_hash_string' => $update['new_hash_string'],
                        ':new_unique_hash' => $update['new_unique_hash'],
                        ':id' => $update['id']
                    ];

                    try {
                        $update_result = tep_db_query($update_query, null, $params);
                        $affected = tep_db_affected_rows($update_result);
                        if ($affected > 0) {
                            $updated_records++;
                        }
                    } catch (Exception $e) {
                        $failed_records++;
                    }
                }
            }

            $offset += $batch_size;
        }

        // Check for duplicates
        foreach ($hash_usage as $hash => $records) {
            if (count($records) > 1) {
                $duplicate_hashes[$hash] = $records;
            }
        }

        $result = [
            'success' => true,
            'total_records' => $total_records,
            'changes_needed' => count($changes),
            'updated_records' => $updated_records,
            'failed_records' => $failed_records,
            'duplicate_hashes' => $duplicate_hashes,
            'changes' => array_slice($changes, 0, 50), // Show first 50 changes
            'dry_run' => $dry_run
        ];

    } catch (Exception $e) {
        $result = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Fix Autodesk Catalog Hashes</h1>
        <p class="mt-2 text-sm text-gray-600">Database hash repair tool</p>
    </div>
    
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <h5 class="text-lg font-medium text-gray-900 mb-4">About This Tool</h5>
            <p class="text-gray-600 mb-4">
                This tool fixes unique hash inconsistencies in the <code class="bg-gray-100 px-2 py-1 rounded">products_autodesk_catalog</code> table 
                by regenerating both the <code class="bg-gray-100 px-2 py-1 rounded">hash_string</code> and <code class="bg-gray-100 px-2 py-1 rounded">unique_hash</code> fields from 
                the individual field values using the current hash generation logic.
            </p>
            <p class="text-gray-600 mb-4">
                This is necessary when the hash generation algorithm has changed, causing duplicate records 
                with different hashes that should be identical.
            </p>
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Important</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>Always run in "Dry Run" mode first to preview changes before applying them.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <h5 class="text-lg font-medium text-gray-900 mb-4">Fix Hash Process</h5>
            <form method="post" class="space-y-4">
                <div>
                    <label for="batch_size" class="block text-sm font-medium text-gray-700">Batch Size</label>
                    <input type="number" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           id="batch_size" 
                           name="batch_size" 
                           value="100" 
                           min="1" 
                           max="1000">
                    <p class="mt-2 text-sm text-gray-500">Number of records to process in each batch (1-1000)</p>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" 
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                           id="dry_run" 
                           name="dry_run" 
                           checked>
                    <label for="dry_run" class="ml-2 block text-sm text-gray-900">
                        Dry Run (Preview changes without applying them)
                    </label>
                </div>
                
                <button type="submit" 
                        name="fix_hashes" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Fix Hashes
                </button>
            </form>
        </div>
    </div>

    <?php if ($result): ?>
        <div class="bg-white shadow rounded-lg">
            <div class="p-6">
                <h5 class="text-lg font-medium text-gray-900 mb-4">Results</h5>
                
                <?php if ($result['success']): ?>
                    <div class="mb-4 p-4 rounded-md <?php echo $result['dry_run'] ? 'bg-blue-50 border border-blue-200' : 'bg-green-50 border border-green-200'; ?>">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 <?php echo $result['dry_run'] ? 'text-blue-400' : 'text-green-400'; ?>" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium <?php echo $result['dry_run'] ? 'text-blue-800' : 'text-green-800'; ?>">
                                    <?php if ($result['dry_run']): ?>
                                        Dry Run Complete
                                    <?php else: ?>
                                        Hash Fix Complete
                                    <?php endif; ?>
                                </h3>
                                <?php if ($result['dry_run']): ?>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <p>No changes were made to the database.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="bg-gray-50 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-gray-900"><?php echo number_format($result['total_records']); ?></div>
                            <div class="text-sm text-gray-600">Total Records</div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-gray-900"><?php echo number_format($result['changes_needed']); ?></div>
                            <div class="text-sm text-gray-600">Changes Needed</div>
                        </div>
                        <?php if (!$result['dry_run']): ?>
                        <div class="bg-gray-50 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-green-600"><?php echo number_format($result['updated_records']); ?></div>
                            <div class="text-sm text-gray-600">Updated Records</div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-red-600"><?php echo number_format($result['failed_records']); ?></div>
                            <div class="text-sm text-gray-600">Failed Records</div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($result['duplicate_hashes'])): ?>
                        <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p><?php echo count($result['duplicate_hashes']); ?> duplicate hash(es) found. This may indicate truly duplicate data that needs manual review.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($result['changes'])): ?>
                        <h6 class="text-base font-medium text-gray-900 mb-3">
                            Sample Changes <?php echo count($result['changes']) < $result['changes_needed'] ? '(First 50)' : ''; ?>:
                        </h6>
                        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Old Hash</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Hash</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hash String Changed</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($result['changes'] as $change): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $change['id']; ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-500"><?php echo htmlspecialchars($change['old_unique_hash']); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900"><?php echo htmlspecialchars($change['new_unique_hash']); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo $change['old_hash_string'] !== $change['new_hash_string'] ? 'Yes' : 'No'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="p-4 bg-red-50 border border-red-200 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p><?php echo htmlspecialchars($result['error']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
