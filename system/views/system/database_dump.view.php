<?php
use system\users;
use system\database;

// Ensure only admin/dev can access this page
users::requireRole(['admin', 'dev']);

// Get list of tables in the database
try {
    $tables = database::getAllTables();
} catch (Exception $e) {
    $tables = [];
    $error_message = "Error fetching tables: " . $e->getMessage();
}

// Get existing dump files from sql folder
$sql_folder = FS_APP_ROOT . DS . 'system' . DS . 'sql';
$existing_dumps = [];
if (is_dir($sql_folder)) {
    $files = scandir($sql_folder);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql' && !in_array($file, ['.', '..'])) {
            $file_path = $sql_folder . DS . $file;
            $existing_dumps[] = [
                'name' => $file,
                'size' => filesize($file_path),
                'modified' => filemtime($file_path),
                'path' => $file_path
            ];
        }
    }
    // Sort by modification time, newest first
    usort($existing_dumps, function($a, $b) {
        return $b['modified'] - $a['modified'];
    });
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<div class="p-6 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center mb-6">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-bold leading-6 text-gray-900">Database Dump Manager</h1>
            <p class="mt-2 text-sm text-gray-700">Export database tables to SQL files in the system/sql folder for easy download via FTP.</p>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p class="text-red-800"><?= htmlspecialchars($error_message) ?></p>
        </div>
    <?php endif; ?>

    <!-- Dump Controls -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Create Database Dumps</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Quick Actions -->
                <div>
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Actions</h3>
                    <div class="space-y-2">
                        <button 
                            hx-post="<?= APP_ROOT ?>/api/dump_all_tables"
                            hx-target="#dump-status"
                            hx-indicator="#dump-loading"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"></path>
                            </svg>
                            Dump All Tables
                        </button>
                        
                        <button 
                            hx-post="<?= APP_ROOT ?>/api/dump_autobooks_tables"
                            hx-target="#dump-status"
                            hx-indicator="#dump-loading"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"></path>
                            </svg>
                            Dump Autobooks Tables Only
                        </button>
                        
                        <button 
                            hx-post="<?= APP_ROOT ?>/api/dump_autodesk_tables"
                            hx-target="#dump-status"
                            hx-indicator="#dump-loading"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"></path>
                            </svg>
                            Dump Autodesk Tables Only
                        </button>
                    </div>
                </div>

                <!-- Custom Table Selection -->
                <div>
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Custom Selection</h3>
                    <form hx-post="<?= APP_ROOT ?>/api/dump_selected_tables" hx-target="#dump-status" hx-indicator="#dump-loading">
                        <div class="mb-3">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select Tables:</label>
                            <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2 bg-gray-50">
                                <?php foreach ($tables as $table): ?>
                                    <label class="flex items-center py-1">
                                        <input type="checkbox" name="tables[]" value="<?= htmlspecialchars($table) ?>" class="mr-2">
                                        <span class="text-sm"><?= htmlspecialchars($table) ?></span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"></path>
                            </svg>
                            Dump Selected Tables
                        </button>
                    </form>
                </div>
            </div>

            <!-- Status Display -->
            <div id="dump-status" class="mt-6"></div>
            
            <!-- Loading Indicator -->
            <div id="dump-loading" class="htmx-indicator mt-4">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-sm text-gray-600">Processing dump...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Existing Dumps -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Existing SQL Dumps</h2>
            <p class="text-sm text-gray-600">Files in system/sql folder ready for download</p>
        </div>
        <div class="overflow-hidden">
            <?php if (empty($existing_dumps)): ?>
                <div class="p-6 text-center text-gray-500">
                    No SQL dump files found in system/sql folder.
                </div>
            <?php else: ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($existing_dumps as $dump): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <?= htmlspecialchars($dump['name']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= formatFileSize($dump['size']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= date('Y-m-d H:i:s', $dump['modified']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button 
                                        hx-delete="<?= APP_ROOT ?>/api/delete_dump"
                                        hx-vals='{"filename": "<?= htmlspecialchars($dump['name']) ?>"}'
                                        hx-target="closest tr"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to delete <?= htmlspecialchars($dump['name']) ?>?"
                                        class="text-red-600 hover:text-red-900">
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>
