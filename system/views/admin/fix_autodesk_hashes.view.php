<?php
/**
 * Fix Autodesk Catalog Hashes - Web Interface
 *
 * This page provides a web interface to fix the unique hashes in the
 * products_autodesk_catalog table by regenerating them from field values.
 */

// Check admin access using proper role hierarchy
use system\users;
users::requireRole('admin');

// Set page title
$page_title = 'Fix Autodesk Catalog Hashes';

// Include necessary files
require_once(DOC_ROOT . '/system/classes/data_importer.class.php');

/**
 * Generate hash string from field values
 */
function generate_hash_string($row) {
    $hash_fields = [
        'offeringId',
        'intendedUsage_code', 
        'accessModel_code',
        'servicePlan_code',
        'connectivity_code',
        'term_code',
        'orderAction',
        'specialProgramDiscount_code',
        'fromQty',
        'toQty'
    ];
    
    $hash_values = [];
    foreach ($hash_fields as $field) {
        $value = isset($row[$field]) ? $row[$field] : '';
        if ($value === null) {
            $value = '';
        }
        $hash_values[] = $value;
    }
    
    return implode('', $hash_values);
}

/**
 * Generate unique hash from hash string
 */
function generate_unique_hash($hash_string) {
    return hash('crc32', $hash_string);
}

// Process form submission
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_hashes'])) {
    $batch_size = isset($_POST['batch_size']) ? (int)$_POST['batch_size'] : 100;
    $dry_run = isset($_POST['dry_run']);
    
    $offset = 0;
    $total_records = 0;
    $updated_records = 0;
    $failed_records = 0;
    $duplicate_hashes = [];
    $changes = [];
    
    try {
        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM products_autodesk_catalog";
        $count_result = tep_db_query($count_query);
        $count_row = tep_db_fetch_array($count_result);
        $total_count = $count_row['total'];
        
        $hash_usage = [];
        
        // Process records in batches
        while (true) {
            $query = "SELECT id, unique_hash, hash_string, offeringId, intendedUsage_code, 
                             accessModel_code, servicePlan_code, connectivity_code, term_code, 
                             orderAction, specialProgramDiscount_code, fromQty, toQty 
                      FROM products_autodesk_catalog 
                      LIMIT {$batch_size} OFFSET {$offset}";
            
            $query_result = tep_db_query($query);
            $batch_count = 0;
            $batch_updates = [];

            while ($row = tep_db_fetch_array($query_result)) {
                $batch_count++;
                $total_records++;

                $new_hash_string = generate_hash_string($row);
                $new_unique_hash = generate_unique_hash($new_hash_string);
                
                $old_hash_string = $row['hash_string'];
                $old_unique_hash = $row['unique_hash'];

                // Track hash usage
                if (!isset($hash_usage[$new_unique_hash])) {
                    $hash_usage[$new_unique_hash] = [];
                }
                $hash_usage[$new_unique_hash][] = [
                    'id' => $row['id'],
                    'hash_string' => $new_hash_string
                ];

                // Check if update is needed
                if ($new_hash_string !== $old_hash_string || $new_unique_hash !== $old_unique_hash) {
                    $change = [
                        'id' => $row['id'],
                        'old_hash_string' => $old_hash_string,
                        'new_hash_string' => $new_hash_string,
                        'old_unique_hash' => $old_unique_hash,
                        'new_unique_hash' => $new_unique_hash
                    ];
                    
                    $batch_updates[] = $change;
                    $changes[] = $change;
                }
            }

            if ($batch_count === 0) {
                break;
            }

            // Update records if not dry run
            if (!$dry_run) {
                foreach ($batch_updates as $update) {
                    $update_query = "UPDATE products_autodesk_catalog 
                                   SET hash_string = :new_hash_string, unique_hash = :new_unique_hash 
                                   WHERE id = :id";
                    $params = [
                        ':new_hash_string' => $update['new_hash_string'],
                        ':new_unique_hash' => $update['new_unique_hash'],
                        ':id' => $update['id']
                    ];

                    try {
                        $update_result = tep_db_query($update_query, null, $params);
                        $affected = tep_db_affected_rows($update_result);
                        if ($affected > 0) {
                            $updated_records++;
                        }
                    } catch (Exception $e) {
                        $failed_records++;
                    }
                }
            }

            $offset += $batch_size;
        }

        // Check for duplicates
        foreach ($hash_usage as $hash => $records) {
            if (count($records) > 1) {
                $duplicate_hashes[$hash] = $records;
            }
        }

        $result = [
            'success' => true,
            'total_records' => $total_records,
            'changes_needed' => count($changes),
            'updated_records' => $updated_records,
            'failed_records' => $failed_records,
            'duplicate_hashes' => $duplicate_hashes,
            'changes' => array_slice($changes, 0, 50), // Show first 50 changes
            'dry_run' => $dry_run
        ];

    } catch (Exception $e) {
        $result = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><?php echo $page_title; ?></h1>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">About This Tool</h5>
                <p class="card-text">
                    This tool fixes unique hash inconsistencies in the <code>products_autodesk_catalog</code> table 
                    by regenerating both the <code>hash_string</code> and <code>unique_hash</code> fields from 
                    the individual field values using the current hash generation logic.
                </p>
                <p class="card-text">
                    This is necessary when the hash generation algorithm has changed, causing duplicate records 
                    with different hashes that should be identical.
                </p>
                <div class="alert alert-warning">
                    <strong>Important:</strong> Always run in "Dry Run" mode first to preview changes before applying them.
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Fix Hash Process</h5>
                <form method="post">
                    <div class="mb-3">
                        <label for="batch_size" class="form-label">Batch Size</label>
                        <input type="number" class="form-control" id="batch_size" name="batch_size" 
                               value="100" min="1" max="1000">
                        <div class="form-text">Number of records to process in each batch (1-1000)</div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="dry_run" name="dry_run" checked>
                        <label class="form-check-label" for="dry_run">
                            Dry Run (Preview changes without applying them)
                        </label>
                    </div>
                    
                    <button type="submit" name="fix_hashes" class="btn btn-primary">
                        Fix Hashes
                    </button>
                </form>
            </div>
        </div>

        <?php if ($result): ?>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Results</h5>
                    
                    <?php if ($result['success']): ?>
                        <div class="alert alert-<?php echo $result['dry_run'] ? 'info' : 'success'; ?>">
                            <?php if ($result['dry_run']): ?>
                                <strong>Dry Run Complete</strong> - No changes were made to the database.
                            <?php else: ?>
                                <strong>Hash Fix Complete</strong>
                            <?php endif; ?>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo number_format($result['total_records']); ?></h5>
                                        <p class="card-text">Total Records</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo number_format($result['changes_needed']); ?></h5>
                                        <p class="card-text">Changes Needed</p>
                                    </div>
                                </div>
                            </div>
                            <?php if (!$result['dry_run']): ?>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo number_format($result['updated_records']); ?></h5>
                                        <p class="card-text">Updated Records</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo number_format($result['failed_records']); ?></h5>
                                        <p class="card-text">Failed Records</p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($result['duplicate_hashes'])): ?>
                            <div class="alert alert-warning mt-3">
                                <strong>Warning:</strong> <?php echo count($result['duplicate_hashes']); ?> duplicate hash(es) found. 
                                This may indicate truly duplicate data that needs manual review.
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($result['changes'])): ?>
                            <h6 class="mt-3">Sample Changes <?php echo count($result['changes']) < $result['changes_needed'] ? '(First 50)' : ''; ?>:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Old Hash</th>
                                            <th>New Hash</th>
                                            <th>Hash String Changed</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result['changes'] as $change): ?>
                                            <tr>
                                                <td><?php echo $change['id']; ?></td>
                                                <td><code><?php echo htmlspecialchars($change['old_unique_hash']); ?></code></td>
                                                <td><code><?php echo htmlspecialchars($change['new_unique_hash']); ?></code></td>
                                                <td><?php echo $change['old_hash_string'] !== $change['new_hash_string'] ? 'Yes' : 'No'; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="alert alert-danger">
                            <strong>Error:</strong> <?php echo htmlspecialchars($result['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
