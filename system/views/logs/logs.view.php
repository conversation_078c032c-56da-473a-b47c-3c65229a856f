<?php
use system\users;
use edge\Edge;
use function system\logs\render_log_table;

// Ensure only admin/dev can access this page
users::requireRole('admin');
// Message display for notifications
$messageDisplay = '';
if (isset($message)) {
    $messageDisplay = '<div class="mt-4 rounded-md bg-green-50 p-4">' .
        '<div class="flex">' .
        '<div class="flex-shrink-0">' .
        '<svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">' .
        '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />' .
        '</svg>' .
        '</div>' .
        '<div class="ml-3">' .
        '<p class="text-sm font-medium text-green-800">' . $message . '</p>' .
        '</div>' .
        '</div>' .
        '</div>';
}
?>
<div class="p-10 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-base font-semibold leading-6 text-gray-900">Log Viewer</h1>
            <p class="mt-2 text-sm text-gray-700">Showing log files located
                at: <?= htmlspecialchars(FS_SYS_LOGS); ?></p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <a href="<?= APP_ROOT . '/' . APP_PATH ?>/view?file=all" class="text-indigo-600 hover:text-indigo-900 mr-4">View
                All</a>
            <a href="<?= APP_ROOT . '/' . APP_PATH ?>?clear=all" class="text-red-600 hover:text-red-900"
               onclick="return confirm('Are you sure you want to clear all log files?');">Clear All</a>
        </div>
    </div>
    <?= $messageDisplay . render_log_table() ?>;
</div>