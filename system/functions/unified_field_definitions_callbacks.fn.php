<?php
/**
 * Callback functions for unified field definitions data tables
 * These functions are accessible to the data table API system
 */

use edge\edge;
use system\unified_field_definitions;

// Ensure the unified_field_definitions class is available
if (!class_exists('system\unified_field_definitions')) {
    $class_file = dirname(__DIR__) . '/classes/unified_field_definitions.class.php';
    if (file_exists($class_file)) {
        require_once $class_file;
    }
}

/**
 * Get unified field definitions preview data (always returns data structure)
 * This is a pure data function - no HTML rendering
 */
if (!function_exists('get_unified_field_definitions_preview_data')) {
function get_unified_field_definitions_preview_data() {
    try {
        // Get fields that should show by default from unified field definitions
        $default_fields = unified_field_definitions::get_default_display_fields();

        if (empty($default_fields)) {
            return [
                'items' => [],
                'columns' => [],
                'total' => 0,
                'available_fields' => [],
                'message' => 'No fields are currently set to "Show by default". Edit field definitions above to mark fields as default display fields.'
            ];
        }

        // Sort fields by category priority (lower number = higher priority)
        uasort($default_fields, function($a, $b) {
            $a_priority = $a['display']['category_priority'] ?? 10;
            $b_priority = $b['display']['category_priority'] ?? 10;
            if ($a_priority === $b_priority) {
                // Secondary sort by field name for consistency
                return strcmp($a['label'] ?? '', $b['label'] ?? '');
            }
            return $a_priority <=> $b_priority;
        });

        // Build columns array for the data table
        $columns = [];
        $available_fields = [];

        foreach ($default_fields as $field_name => $field_def) {
            $columns[] = [
                'field' => $field_name,  // Use 'field' for data-table compatibility
                'label' => $field_def['label'] ?? ucwords(str_replace('_', ' ', $field_name)),
                'filter' => true,
                'sortable' => true,
                'category' => $field_def['category'] ?? 'other',
                'priority' => $field_def['display']['category_priority'] ?? 10,
                'field_type' => $field_def['type'] ?? 'string'
            ];
            $available_fields[] = $field_name;
        }

        // Generate realistic sample data based on the default fields
        $sample_data = generate_sample_data_for_preview($default_fields);

        // Convert sample data to the format expected by data-table component
        $items = [];
        foreach ($sample_data as $row_index => $row) {
            $item = [
                'id' => $row_index + 1,
                '_row_class' => $row_index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
            ];

            // Add each field value to the item
            foreach ($default_fields as $field_name => $field_def) {
                $item[$field_name] = $row[$field_name] ?? '';
            }

            $items[] = $item;
        }

        // Always return data structure - no HTML rendering here
        return [
            'items' => $items,
            'columns' => $columns,
            'total' => count($items),
            'available_fields' => $available_fields,
            'configuration_info' => [
                'name' => 'unified_field_definitions_default',
                'field_count' => count($default_fields),
                'categories' => array_unique(array_column($columns, 'category')),
                'description' => 'Default column configuration based on "Show by default" field settings'
            ]
        ];

    } catch (Exception $e) {
        error_log("Error in get_unified_field_definitions_preview_data: " . $e->getMessage());
        return [
            'items' => [],
            'columns' => [],
            'total' => 0,
            'available_fields' => [],
            'error' => 'Error loading default field preview: ' . $e->getMessage()
        ];
    }
}
}

/**
 * Callback function for HTMX requests - renders the preview table template
 * This is a pure rendering function
 */
if (!function_exists('render_unified_field_definitions_preview_table')) {
function render_unified_field_definitions_preview_table($criteria = []) {
    try {
        return edge::render('unified-field-definitions-preview-table');
    } catch (Exception $e) {
        error_log("Error rendering unified field definitions preview table: " . $e->getMessage());
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error loading preview table: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}
}

/**
 * Generate realistic sample data for preview table based on actual field definitions
 */
if (!function_exists('generate_sample_data_for_preview')) {
function generate_sample_data_for_preview($fields) {
    $sample_data = [];

    // Comprehensive sample data templates organized by field patterns and types
    $sample_templates = [
        // Email patterns
        'email' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],

        // Company/Organization patterns
        'company_name' => ['Acme Corporation', 'Tech Solutions LLC', 'Global Industries Inc.'],
        'end_customer_name' => ['Acme Corporation', 'Tech Solutions LLC', 'Global Industries Inc.'],
        'sold_to_name' => ['TechDistributor Inc.', 'Software Reseller Corp', 'Channel Partner LLC'],
        'vendor_name' => ['Autodesk', 'Trimble Inc.', 'Adobe Systems'],

        // Product patterns
        'product_name' => ['AutoCAD LT 2024', 'SketchUp Pro 2024', 'Revit Architecture 2024'],
        'product_family' => ['Design & Drafting', '3D Modeling', 'BIM Solutions'],
        'product_sku' => ['ACDLT-2024-001', 'SKUP-PRO-2024', 'RVIT-ARCH-2024'],

        // Contact patterns
        'contact_name' => ['John Doe', 'Sarah Wilson', 'Michael Chen'],
        'end_customer_contact_name' => ['John Doe', 'Sarah Wilson', 'Michael Chen'],
        'subscription_contact_name' => ['John Doe', 'Sarah Wilson', 'Michael Chen'],

        // Phone patterns
        'phone' => ['******-0123', '******-0456', '******-0789'],
        'contact_phone' => ['******-0123', '******-0456', '******-0789'],

        // Address patterns
        'address' => ['123 Main Street', '456 Oak Avenue', '789 Pine Road'],
        'end_customer_address_1' => ['123 Main Street', '456 Oak Avenue', '789 Pine Road'],
        'city' => ['New York', 'Los Angeles', 'Chicago'],
        'state' => ['NY', 'CA', 'IL'],
        'country' => ['United States', 'United States', 'United States'],

        // Status patterns
        'subscription_status' => ['Active', 'Expired', 'Pending Renewal'],
        'agreement_status' => ['Active', 'Expired', 'Under Review'],
        'status' => ['Active', 'Inactive', 'Pending'],

        // Date patterns
        'start_date' => ['2024-01-15', '2023-12-01', '2024-02-10'],
        'end_date' => ['2025-01-15', '2024-12-01', '2025-02-10'],
        'subscription_start_date' => ['2024-01-15', '2023-12-01', '2024-02-10'],
        'subscription_end_date' => ['2025-01-15', '2024-12-01', '2025-02-10'],
        'agreement_start_date' => ['2024-01-15', '2023-12-01', '2024-02-10'],
        'agreement_end_date' => ['2025-01-15', '2024-12-01', '2025-02-10'],

        // Financial patterns
        'price' => ['$299.00', '$695.00', '$1,200.00'],
        'list_price' => ['$299.00', '$695.00', '$1,200.00'],
        'product_list_price' => ['$299.00', '$695.00', '$1,200.00'],
        'currency' => ['USD', 'USD', 'USD'],

        // Quantity patterns
        'quantity' => ['5', '10', '25'],
        'subscription_quantity' => ['5', '10', '25'],

        // ID patterns
        'subscription_reference' => ['SUB-2024-001', 'SUB-2024-002', 'SUB-2024-003'],
        'agreement_number' => ['AGR-2024-001', 'AGR-2024-002', 'AGR-2024-003'],
        'sold_to_number' => ['ST-12345', 'ST-67890', 'ST-11111'],

        // Agreement patterns
        'agreement_terms' => ['Annual', 'Monthly', 'Multi-Year'],
        'agreement_type' => ['Subscription', 'Perpetual', 'Trial']
    ];

    // Generate 3 sample rows to show variety
    for ($i = 0; $i < 3; $i++) {
        $row = [];

        foreach ($fields as $field_name => $field_def) {
            $sample_value = '';

            // First, try exact field name match
            if (isset($sample_templates[$field_name])) {
                $sample_value = $sample_templates[$field_name][$i % 3];
            }
            // Then try field type match
            elseif (isset($sample_templates[$field_def['type'] ?? ''])) {
                $sample_value = $sample_templates[$field_def['type']][$i % 3];
            }
            // Then try partial field name matches
            else {
                foreach ($sample_templates as $template_key => $values) {
                    if (strpos($field_name, $template_key) !== false ||
                        strpos(strtolower($field_def['label'] ?? ''), str_replace('_', ' ', $template_key)) !== false) {
                        $sample_value = $values[$i % 3];
                        break;
                    }
                }
            }

            // Fallback to category-based samples
            if (empty($sample_value)) {
                $category = $field_def['category'] ?? 'other';
                switch ($category) {
                    case 'contact':
                        $sample_value = ['Contact Person ' . ($i + 1), 'Admin User ' . ($i + 1), 'Primary Contact ' . ($i + 1)][$i];
                        break;
                    case 'organization':
                        $sample_value = ['Organization ' . ($i + 1), 'Company ' . ($i + 1), 'Business ' . ($i + 1)][$i];
                        break;
                    case 'product':
                        $sample_value = ['Product ' . ($i + 1), 'Software ' . ($i + 1), 'License ' . ($i + 1)][$i];
                        break;
                    case 'financial':
                        $sample_value = '$' . number_format(100 + ($i * 150), 2);
                        break;
                    case 'dates':
                        $sample_value = date('Y-m-d', strtotime('+' . ($i * 30) . ' days'));
                        break;
                    case 'identification':
                        $sample_value = 'ID-' . str_pad($i + 1, 6, '0', STR_PAD_LEFT);
                        break;
                    case 'address':
                        $sample_value = [(100 + $i * 100) . ' Main St', (200 + $i * 100) . ' Oak Ave', (300 + $i * 100) . ' Pine Rd'][$i];
                        break;
                    case 'subscription':
                        $sample_value = ['Subscription ' . ($i + 1), 'License ' . ($i + 1), 'Service ' . ($i + 1)][$i];
                        break;
                    case 'agreement':
                        $sample_value = ['Agreement ' . ($i + 1), 'Contract ' . ($i + 1), 'License ' . ($i + 1)][$i];
                        break;
                    default:
                        $sample_value = 'Sample Data ' . ($i + 1);
                }
            }

            $row[$field_name] = $sample_value;
        }

        $sample_data[] = $row;
    }

    return $sample_data;
}
}
