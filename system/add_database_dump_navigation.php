<?php
/**
 * Add Database Dump Manager to Navigation System
 * Run this script once to add the database dump functionality to the system navigation
 */

// Include the necessary files
require_once __DIR__ . '/../system/startup_sequence.php';

try {
    echo "🗄️ Adding Database Dump Manager to navigation system...\n";

    // Check if 'system' parent exists, if not create it
    $system_parent_query = "SELECT id FROM autobooks_navigation WHERE parent_path = 'root' AND route_key = 'system'";
    $system_parent = tcs_db_query($system_parent_query);

    if (empty($system_parent)) {
        echo "📁 Creating 'System' parent navigation entry...\n";
        $insert_parent_query = "INSERT INTO autobooks_navigation
            (parent_path, route_key, name, icon, file_path, required_roles, sort_order, show_navbar, can_delete, is_system)
            VALUES
            ('root', 'system', 'System', 'cog', '', ?, 100, 1, 0, 1)";
        tep_db_query($insert_parent_query, [json_encode(['admin', 'dev'])]);
        echo "✅ System parent navigation entry created\n";
    } else {
        echo "📁 System parent navigation entry already exists\n";
    }

    // Check if database_dump entry already exists
    $existing_query = "SELECT id FROM autobooks_navigation WHERE parent_path = 'system' AND route_key = 'database_dump'";
    $existing = tcs_db_query($existing_query);

    if (!empty($existing)) {
        echo "⚠️ Database Dump Manager navigation entry already exists. Updating...\n";
        $update_query = "UPDATE autobooks_navigation SET
            name = 'Database Dump Manager',
            icon = 'database',
            file_path = 'system',
            required_roles = ?,
            sort_order = 10,
            show_navbar = 1,
            can_delete = 0,
            is_system = 1
            WHERE parent_path = 'system' AND route_key = 'database_dump'";
        tep_db_query($update_query, [json_encode(['admin', 'dev'])]);
        echo "✅ Database Dump Manager navigation entry updated\n";
    } else {
        // Add the database dump manager entry
        echo "🗄️ Adding Database Dump Manager navigation entry...\n";
        $insert_dump_query = "INSERT INTO autobooks_navigation
            (parent_path, route_key, name, icon, file_path, required_roles, sort_order, show_navbar, can_delete, is_system)
            VALUES
            ('system', 'database_dump', 'Database Dump Manager', 'database', 'system', ?, 10, 1, 0, 1)";
        tep_db_query($insert_dump_query, [json_encode(['admin', 'dev'])]);
        echo "✅ Database Dump Manager navigation entry added successfully!\n";
    }

    echo "🌐 You can now access it at: /system/database_dump\n";
    echo "👤 Required roles: admin, dev\n";
    echo "📁 SQL dumps will be saved to: system/sql/\n";
    echo "\n";
    echo "🎉 Database Dump Manager setup complete!\n";
    echo "\n";
    echo "Features available:\n";
    echo "• Dump all database tables\n";
    echo "• Dump only autobooks_* tables\n";
    echo "• Dump only autodesk_* tables\n";
    echo "• Select specific tables to dump\n";
    echo "• View and delete existing dump files\n";
    echo "• Files saved directly to system/sql for easy FTP download\n";

} catch (Exception $e) {
    echo "❌ Error adding navigation entry: " . $e->getMessage() . "\n";
    exit(1);
}
?>
