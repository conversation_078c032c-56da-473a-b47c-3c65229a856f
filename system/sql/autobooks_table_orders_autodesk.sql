-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 25, 2025 at 11:04 PM
-- Table: orders_autodesk
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `orders_autodesk`
--

DROP TABLE IF EXISTS `orders_autodesk`;
CREATE TABLE `orders_autodesk` (
  `orders_autodesk_id` int(11) NOT NULL AUTO_INCREMENT,
  `orders_id` int(11) NOT NULL,
  `customers_id` int(11) NOT NULL DEFAULT 0,
  `opportunityNumber` varchar(64) NOT NULL,
  `customers_name` varchar(255) NOT NULL,
  `customers_company` varchar(255) DEFAULT NULL,
  `customers_street_address` varchar(255) NOT NULL,
  `customers_suburb` varchar(255) DEFAULT NULL,
  `customers_city` varchar(255) NOT NULL,
  `customers_postcode` varchar(255) NOT NULL,
  `customers_state` varchar(255) DEFAULT NULL,
  `customers_country` varchar(255) NOT NULL,
  `customers_telephone` varchar(255) NOT NULL,
  `customers_email_address` varchar(255) NOT NULL,
  `customers_address_format_id` int(5) NOT NULL DEFAULT 0,
  `delivery_name` varchar(255) NOT NULL,
  `delivery_company` varchar(255) DEFAULT NULL,
  `delivery_street_address` varchar(255) NOT NULL,
  `delivery_suburb` varchar(255) DEFAULT NULL,
  `delivery_city` varchar(255) NOT NULL,
  `delivery_postcode` varchar(255) NOT NULL,
  `delivery_state` varchar(255) DEFAULT NULL,
  `delivery_country` varchar(255) NOT NULL,
  `delivery_address_format_id` int(5) NOT NULL DEFAULT 0,
  `billing_name` varchar(255) NOT NULL,
  `billing_company` varchar(255) DEFAULT NULL,
  `billing_street_address` varchar(255) NOT NULL,
  `billing_suburb` varchar(255) DEFAULT NULL,
  `billing_city` varchar(255) NOT NULL,
  `billing_postcode` varchar(255) NOT NULL,
  `billing_state` varchar(255) DEFAULT NULL,
  `billing_country` varchar(255) NOT NULL,
  `billing_address_format_id` int(5) NOT NULL DEFAULT 0,
  `payment_method` varchar(255) NOT NULL,
  `cc_type` varchar(20) DEFAULT NULL,
  `cc_owner` varchar(255) DEFAULT NULL,
  `cc_number` varchar(64) DEFAULT NULL,
  `cc_expires` varchar(4) DEFAULT NULL,
  `ip_order_no` varchar(30) DEFAULT NULL,
  `ip_requested_by` varchar(30) DEFAULT NULL,
  `ip_contact_person` varchar(30) DEFAULT NULL,
  `cc_start` varchar(4) DEFAULT NULL,
  `cc_issue` char(3) DEFAULT NULL,
  `cc_cvv` varchar(4) DEFAULT NULL,
  `cc_po_no` varchar(30) DEFAULT NULL,
  `cc_po_contact_name` varchar(30) DEFAULT NULL,
  `cc_po_department` varchar(30) DEFAULT NULL,
  `last_modified` datetime DEFAULT NULL,
  `date_purchased` datetime DEFAULT NULL,
  `orders_status` int(5) NOT NULL DEFAULT 0,
  `orders_date_finished` datetime DEFAULT NULL,
  `currency` char(3) DEFAULT NULL,
  `currency_value` decimal(14,6) DEFAULT NULL,
  `account_name` varchar(32) NOT NULL DEFAULT '',
  `account_number` varchar(20) DEFAULT NULL,
  `po_number` varchar(12) DEFAULT NULL,
  `customers_street_address2` varchar(64) DEFAULT NULL,
  `delivery_street_address2` varchar(64) DEFAULT NULL,
  `billing_street_address2` varchar(64) DEFAULT NULL,
  `block_review_request` int(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`orders_autodesk_id`),
  KEY `idx1_orders` (`customers_id`),
  KEY `idx_orders_customers_id` (`customers_id`),
  KEY `orders_id` (`orders_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

COMMIT;
