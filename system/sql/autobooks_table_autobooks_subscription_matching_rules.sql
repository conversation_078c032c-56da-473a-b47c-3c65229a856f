-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 26, 2025 at 02:41 PM
-- Table: autobooks_subscription_matching_rules
-- Records: 2

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_subscription_matching_rules`
--

DROP TABLE IF EXISTS `autobooks_subscription_matching_rules`;
CREATE TABLE `autobooks_subscription_matching_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 99,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `configuration` longtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `rule_name` (`rule_name`),
  KEY `priority` (`priority`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `autobooks_subscription_matching_rules`
--

INSERT INTO `autobooks_subscription_matching_rules` (`id`, `rule_name`, `priority`, `is_active`, `configuration`, `created_at`, `updated_at`) VALUES
('1', 'email_matching', '1', '1', '{\"enabled\":true,\"priority\":1,\"field_patterns\":[\"email\",\"email_address\",\"contact_email\",\"admin_email\",\"customer_email\",\"end_customer_contact_email\",\"subscription_contact_email\",\"endcust_primary_admin_email\",\"primary_email\",\"business_email\",\"work_email\"],\"case_sensitive\":false,\"exact_match_only\":true,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"confidence_score\":100,\"confidence_threshold\":null,\"preprocessing\":[]}', '2025-08-21 22:58:22', '2025-08-21 22:58:22'),
('2', 'company_name_matching', '2', '1', '{\"enabled\":true,\"priority\":2,\"field_patterns\":[\"company\",\"company_name\",\"customer\",\"customer_name\",\"client\",\"client_name\",\"account_name\",\"end_customer_name\",\"endcust_name\",\"organization\",\"organization_name\",\"business_name\",\"firm_name\",\"name\"],\"case_sensitive\":false,\"exact_match_only\":false,\"fuzzy_matching\":true,\"similarity_threshold\":70,\"confidence_score\":null,\"confidence_threshold\":70,\"preprocessing\":{\"remove_common_words\":[\"ltd\",\"limited\",\"inc\",\"corp\",\"corporation\",\"llc\",\"plc\"],\"normalize_spaces\":true,\"remove_punctuation\":true}}', '2025-08-21 22:58:22', '2025-08-21 22:58:22');

COMMIT;
