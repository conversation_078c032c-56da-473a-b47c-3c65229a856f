-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 25, 2025 at 11:04 PM
-- Table: orders_products_autodesk
-- Records: 5

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `orders_products_autodesk`
--

DROP TABLE IF EXISTS `orders_products_autodesk`;
CREATE TABLE `orders_products_autodesk` (
  `orders_products_autodesk_id` int(11) NOT NULL AUTO_INCREMENT,
  `orders_id` int(11) NOT NULL DEFAULT 0,
  `products_id` int(11) NOT NULL DEFAULT 0,
  `orderAction` varchar(64) DEFAULT NULL,
  `orders_products_id` int(11) NOT NULL,
  `subscriptionId` varchar(64) DEFAULT NULL,
  `opportunityLineItemId` varchar(64) DEFAULT NULL,
  `promotionCode` varchar(64) DEFAULT NULL,
  `referenceSubscriptionId` varchar(64) NOT NULL,
  `startDate` varchar(64) NOT NULL,
  `endDate` int(64) NOT NULL,
  PRIMARY KEY (`orders_products_autodesk_id`),
  KEY `idx1_orders_products` (`products_id`),
  KEY `idx2_orders_products` (`orders_id`),
  KEY `idx_orders_products_orders_id` (`orders_id`),
  KEY `idx_orders_products_products_id` (`products_id`),
  KEY `orders_products_id` (`orders_products_id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Dumping data for table `orders_products_autodesk`
--

INSERT INTO `orders_products_autodesk` (`orders_products_autodesk_id`, `orders_id`, `products_id`, `orderAction`, `orders_products_id`, `subscriptionId`, `opportunityLineItemId`, `promotionCode`, `referenceSubscriptionId`, `startDate`, `endDate`) VALUES
('1', '30374', '0', 'New', '0', NULL, NULL, NULL, '', '', '0'),
('2', '30374', '9891', 'Co-term', '0', NULL, NULL, NULL, '', '', '0'),
('3', '30379', '0', 'Renewal', '0', NULL, NULL, NULL, '', '', '0'),
('4', '30379', '9891', 'Renewal', '0', NULL, NULL, NULL, '', '', '0'),
('5', '30562', '9891', 'Renewal', '0', NULL, NULL, NULL, '', '', '0');

COMMIT;
