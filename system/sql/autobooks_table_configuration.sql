-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 25, 2025 at 11:04 PM
-- Table: configuration
-- Records: 723

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `configuration`
--

DROP TABLE IF EXISTS `configuration`;
CREATE TABLE `configuration` (
  `configuration_id` int(11) NOT NULL AUTO_INCREMENT,
  `configuration_title` varchar(255) NOT NULL,
  `configuration_key` varchar(255) NOT NULL,
  `configuration_value` mediumtext NOT NULL,
  `configuration_description` varchar(255) NOT NULL DEFAULT '',
  `configuration_group_id` int(11) NOT NULL DEFAULT 0,
  `sort_order` int(5) DEFAULT NULL,
  `last_modified` datetime DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `use_function` varchar(255) DEFAULT NULL,
  `set_function` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`configuration_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1708 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Dumping data for table `configuration`
--

INSERT INTO `configuration` (`configuration_id`, `configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`) VALUES
('1', 'Store Name', 'STORE_NAME', 'TCS CAD & BIM Solutions Ltd', 'The name of my store, used online and on printable documents', '1', '1', '2016-12-19 16:14:51', '2003-09-21 16:08:39', NULL, NULL),
('2', 'Store Owner', 'STORE_OWNER', 'TCS CAD & BIM Solutions Ltd', 'The name of my store owner', '1', '2', '2016-11-25 16:18:27', '2003-09-21 16:08:39', NULL, NULL),
('3', 'E-Mail Address', 'STORE_OWNER_EMAIL_ADDRESS', '<EMAIL>', 'The e-mail address of my store owner', '1', '3', '2007-06-04 11:49:03', '2003-09-21 16:08:39', NULL, NULL),
('4', 'E-Mail From', 'EMAIL_FROM', 'sales <<EMAIL>>', 'The e-mail address used in (sent) e-mails', '1', '4', '2007-06-04 11:49:20', '2003-09-21 16:08:39', NULL, NULL),
('5', 'Country', 'STORE_COUNTRY', '222', 'The country my store is located in <br><br><b>Note: Please remember to update the store zone.</b>', '1', '6', '2003-09-21 16:57:22', '2003-09-21 16:08:39', 'tep_get_country_name', 'tep_cfg_pull_down_country_list('),
('6', 'Zone', 'STORE_ZONE', '201', 'The zone my store is located in', '1', '7', '2003-12-04 22:05:11', '2003-09-21 16:08:39', 'tep_cfg_get_zone_name', 'tep_cfg_pull_down_zone_list('),
('7', 'Expected Sort Order', 'EXPECTED_PRODUCTS_SORT', 'asc', 'This is the sort order used in the expected products box.', '1', '8', '2004-03-31 10:50:21', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'asc\', \'desc\'),'),
('8', 'Expected Sort Field', 'EXPECTED_PRODUCTS_FIELD', 'products_name', 'The column to sort by in the expected products box.', '1', '9', '2004-03-31 10:50:32', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'products_name\', \'date_expected\'),'),
('9', 'Switch To Default Language Currency', 'USE_DEFAULT_LANGUAGE_CURRENCY', 'false', 'Automatically switch to the language\'s currency when it is changed', '1', '10', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('10', 'Send Extra Order Emails To', 'SEND_EXTRA_ORDER_EMAILS_TO', '', 'Send extra order emails to the following email addresses, in this format: Name 1 &lt;email@address1&gt;, Name 2 &lt;email@address2&gt;', '1', '11', '2006-12-11 15:57:33', '2003-09-21 16:08:39', NULL, NULL),
('11', 'Use Search-Engine Safe URLs (still in development)', 'SEARCH_ENGINE_FRIENDLY_URLS', 'false', 'Use search-engine safe urls for all site links', '1', '12', '2004-08-26 15:40:19', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('12', 'Display Cart After Adding Product', 'DISPLAY_CART', 'false', 'Display the shopping cart after adding a product (or return back to their origin)', '1', '14', '2023-12-01 14:13:35', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('13', 'Allow Guest To Tell A Friend', 'ALLOW_GUEST_TO_TELL_A_FRIEND', 'true', 'Allow guests to tell a friend about a product', '1', '15', '2003-09-23 19:59:34', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('14', 'Default Search Operator', 'ADVANCED_SEARCH_DEFAULT_OPERATOR', 'and', 'Default search operators', '1', '17', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'and\', \'or\'),'),
('15', 'Store Address', 'STORE_ADDRESS', 'Unit F Yorkway
Mandale Industrial Estate
Thornaby, Stockton-on-Tees
TS17 6DE', 'This is the Address of my store used on printable documents and displayed online', '1', '18', '2016-09-09 16:25:32', '2003-09-21 16:08:39', NULL, 'tep_cfg_textarea('),
('714', 'Store Phone', 'STORE_PHONE', '+44 (01642) 677582', 'This is the phone number of my store used on printable documents and displayed online', '1', '19', '2016-08-19 16:35:57', '2016-07-05 10:04:47', NULL, 'tep_cfg_textarea('),
('17', 'Tax Decimal Places', 'TAX_DECIMAL_PLACES', '2', 'Pad the tax value this amount of decimal places', '1', '20', '2004-02-16 15:38:56', '2003-09-21 16:08:39', NULL, NULL),
('18', 'Display Prices with Tax', 'DISPLAY_PRICE_WITH_TAX', 'false', 'Display prices with tax included (true) or add the tax at the end (false)', '1', '21', '2004-01-13 13:38:07', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('19', 'First Name', 'ENTRY_FIRST_NAME_MIN_LENGTH', '2', 'Minimum length of first name', '2', '1', NULL, '2003-09-21 16:08:39', NULL, NULL),
('20', 'Last Name', 'ENTRY_LAST_NAME_MIN_LENGTH', '2', 'Minimum length of last name', '2', '2', NULL, '2003-09-21 16:08:39', NULL, NULL),
('21', 'Date of Birth', 'ENTRY_DOB_MIN_LENGTH', '10', 'Minimum length of date of birth', '2', '3', NULL, '2003-09-21 16:08:39', NULL, NULL),
('22', 'E-Mail Address', 'ENTRY_EMAIL_ADDRESS_MIN_LENGTH', '6', 'Minimum length of e-mail address', '2', '4', NULL, '2003-09-21 16:08:39', NULL, NULL),
('23', 'Street Address', 'ENTRY_STREET_ADDRESS_MIN_LENGTH', '5', 'Minimum length of street address', '2', '5', NULL, '2003-09-21 16:08:39', NULL, NULL),
('24', 'Company', 'ENTRY_COMPANY_MIN_LENGTH', '2', 'Minimum length of company name', '2', '6', NULL, '2003-09-21 16:08:39', NULL, NULL),
('25', 'Post Code', 'ENTRY_POSTCODE_MIN_LENGTH', '4', 'Minimum length of post code', '2', '7', NULL, '2003-09-21 16:08:39', NULL, NULL),
('26', 'City', 'ENTRY_CITY_MIN_LENGTH', '3', 'Minimum length of city', '2', '8', NULL, '2003-09-21 16:08:39', NULL, NULL),
('27', 'State', 'ENTRY_STATE_MIN_LENGTH', '2', 'Minimum length of state', '2', '9', NULL, '2003-09-21 16:08:39', NULL, NULL),
('28', 'Telephone Number', 'ENTRY_TELEPHONE_MIN_LENGTH', '3', 'Minimum length of telephone number', '2', '10', NULL, '2003-09-21 16:08:39', NULL, NULL),
('29', 'Password', 'ENTRY_PASSWORD_MIN_LENGTH', '5', 'Minimum length of password', '2', '11', NULL, '2003-09-21 16:08:39', NULL, NULL),
('30', 'Credit Card Owner Name', 'CC_OWNER_MIN_LENGTH', '3', 'Minimum length of credit card owner name', '2', '12', NULL, '2003-09-21 16:08:39', NULL, NULL),
('31', 'Credit Card Number', 'CC_NUMBER_MIN_LENGTH', '10', 'Minimum length of credit card number', '2', '13', NULL, '2003-09-21 16:08:39', NULL, NULL),
('32', 'Review Text', 'REVIEW_TEXT_MIN_LENGTH', '50', 'Minimum length of review text', '2', '14', NULL, '2003-09-21 16:08:39', NULL, NULL),
('33', 'Best Sellers', 'MIN_DISPLAY_BESTSELLERS', '1', 'Minimum number of best sellers to display', '2', '15', NULL, '2003-09-21 16:08:39', NULL, NULL),
('34', 'Also Purchased', 'MIN_DISPLAY_ALSO_PURCHASED', '1', 'Minimum number of products to display in the \'This Customer Also Purchased\' box', '2', '16', NULL, '2003-09-21 16:08:39', NULL, NULL),
('35', 'Address Book Entries', 'MAX_ADDRESS_BOOK_ENTRIES', '5', 'Maximum address book entries a customer is allowed to have', '3', '1', NULL, '2003-09-21 16:08:39', NULL, NULL),
('36', 'Search Results', 'MAX_DISPLAY_SEARCH_RESULTS', '20', 'Amount of products to list', '3', '2', NULL, '2003-09-21 16:08:39', NULL, NULL),
('37', 'Page Links', 'MAX_DISPLAY_PAGE_LINKS', '5', 'Number of \'number\' links use for page-sets', '3', '3', NULL, '2003-09-21 16:08:39', NULL, NULL),
('38', 'Special Products', 'MAX_DISPLAY_SPECIAL_PRODUCTS', '9', 'Maximum number of products on special to display', '3', '4', NULL, '2003-09-21 16:08:39', NULL, NULL),
('39', 'New Products Module', 'MAX_DISPLAY_NEW_PRODUCTS', '3', 'Maximum number of new products to display in a category', '3', '5', '2003-09-23 21:37:52', '2003-09-21 16:08:39', NULL, NULL),
('40', 'Products Expected', 'MAX_DISPLAY_UPCOMING_PRODUCTS', '6', 'Maximum number of products expected to display', '3', '6', '2003-09-21 23:41:46', '2003-09-21 16:08:39', NULL, NULL),
('41', 'Manufacturers List', 'MAX_DISPLAY_MANUFACTURERS_IN_A_LIST', '', 'Used in manufacturers box; when the number of manufacturers exceeds this number, a drop-down list will be displayed instead of the default list', '3', '7', NULL, '2003-09-21 16:08:39', NULL, NULL),
('42', 'Manufacturers Select Size', 'MAX_MANUFACTURERS_LIST', '1', 'Used in manufacturers box; when this value is \'1\' the classic drop-down list will be used for the manufacturers box. Otherwise, a list-box with the specified number of rows will be displayed.', '3', '7', NULL, '2003-09-21 16:08:39', NULL, NULL),
('43', 'Length of Manufacturers Name', 'MAX_DISPLAY_MANUFACTURER_NAME_LEN', '15', 'Used in manufacturers box; maximum length of manufacturers name to display', '3', '8', NULL, '2003-09-21 16:08:39', NULL, NULL),
('44', 'New Reviews', 'MAX_DISPLAY_NEW_REVIEWS', '6', 'Maximum number of new reviews to display', '3', '9', NULL, '2003-09-21 16:08:39', NULL, NULL),
('45', 'Selection of Random Reviews', 'MAX_RANDOM_SELECT_REVIEWS', '10', 'How many records to select from to choose one random product review', '3', '10', NULL, '2003-09-21 16:08:39', NULL, NULL),
('46', 'Selection of Random New Products', 'MAX_RANDOM_SELECT_NEW', '6', 'How many records to select from to choose one random new product to display', '3', '11', '2003-09-21 23:40:03', '2003-09-21 16:08:39', NULL, NULL),
('47', 'Selection of Products on Special', 'MAX_RANDOM_SELECT_SPECIALS', '4', 'How many records to select from to choose one random product special to display', '3', '12', '2003-12-28 08:54:06', '2003-09-21 16:08:39', NULL, NULL),
('48', 'Categories To List Per Row', 'MAX_DISPLAY_CATEGORIES_PER_ROW', '3', 'How many categories to list per row', '3', '13', NULL, '2003-09-21 16:08:39', NULL, NULL),
('49', 'New Products Listing', 'MAX_DISPLAY_PRODUCTS_NEW', '10', 'Maximum number of new products to display in new products page', '3', '14', '2003-09-21 23:44:18', '2003-09-21 16:08:39', NULL, NULL),
('50', 'Best Sellers', 'MAX_DISPLAY_BESTSELLERS', '10', 'Maximum number of best sellers to display', '3', '15', NULL, '2003-09-21 16:08:39', NULL, NULL),
('51', 'Also Purchased', 'MAX_DISPLAY_ALSO_PURCHASED', '6', 'Maximum number of products to display in the \'This Customer Also Purchased\' box', '3', '16', NULL, '2003-09-21 16:08:39', NULL, NULL),
('52', 'Customer Order History Box', 'MAX_DISPLAY_PRODUCTS_IN_ORDER_HISTORY_BOX', '6', 'Maximum number of products to display in the customer order history box', '3', '17', NULL, '2003-09-21 16:08:39', NULL, NULL),
('53', 'Order History', 'MAX_DISPLAY_ORDER_HISTORY', '10', 'Maximum number of orders to display in the order history page', '3', '18', NULL, '2003-09-21 16:08:39', NULL, NULL),
('54', 'Small Image Width', 'SMALL_IMAGE_WIDTH', '200', 'The pixel width of small images', '4', '1', '2016-10-18 11:37:52', '2003-09-21 16:08:39', NULL, NULL),
('55', 'Small Image Height', 'SMALL_IMAGE_HEIGHT', '160', 'The pixel height of small images', '4', '2', '2016-10-18 11:38:10', '2003-09-21 16:08:39', NULL, NULL),
('56', 'Heading Image Width', 'HEADING_IMAGE_WIDTH', '23', 'The pixel width of heading images', '4', '3', '2003-09-24 11:06:47', '2003-09-21 16:08:39', NULL, NULL),
('57', 'Heading Image Height', 'HEADING_IMAGE_HEIGHT', '22', 'The pixel height of heading images', '4', '4', '2003-09-24 11:06:55', '2003-09-21 16:08:39', NULL, NULL),
('58', 'Subcategory Image Width', 'SUBCATEGORY_IMAGE_WIDTH', '150', 'The pixel width of subcategory images', '4', '5', '2016-11-25 15:15:21', '2003-09-21 16:08:39', NULL, NULL),
('59', 'Subcategory Image Height', 'SUBCATEGORY_IMAGE_HEIGHT', '150', 'The pixel height of subcategory images', '4', '6', '2016-11-25 15:15:31', '2003-09-21 16:08:39', NULL, NULL),
('60', 'Calculate Image Size', 'CONFIG_CALCULATE_IMAGE_SIZE', 'true', 'Calculate the size of images?', '4', '7', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('61', 'Image Required', 'IMAGE_REQUIRED', 'true', 'Enable to display broken images. Good for development.', '4', '8', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('62', 'Gender', 'ACCOUNT_GENDER', 'false', 'Display gender in the customers account', '5', '1', '2003-11-30 23:15:13', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('63', 'Date of Birth', 'ACCOUNT_DOB', 'false', 'Display date of birth in the customers account', '5', '2', '2003-09-23 00:32:04', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('64', 'Company', 'ACCOUNT_COMPANY', 'true', 'Display company in the customers account', '5', '3', '2004-03-01 20:33:08', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('65', 'Suburb', 'ACCOUNT_SUBURB', 'false', 'Display suburb in the customers account', '5', '4', '2003-09-23 00:32:28', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('66', 'State', 'ACCOUNT_STATE', 'false', 'Display state in the customers account', '5', '5', '2003-09-23 00:32:35', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('67', 'Installed Modules', 'MODULE_PAYMENT_INSTALLED', 'cc.php;cod.php;po.php', 'List of payment module filenames separated by a semi-colon. This is automatically updated. No need to edit. (Example: cc.php;cod.php;paypal.php)', '6', '0', '2018-06-13 10:20:44', '2003-09-21 16:08:39', NULL, NULL),
('68', 'Installed Modules', 'MODULE_ORDER_TOTAL_INSTALLED', 'ot_subtotal.php;ot_shipping.php;ot_tax.php;ot_total.php', 'List of order_total module filenames separated by a semi-colon. This is automatically updated. No need to edit. (Example: ot_subtotal.php;ot_tax.php;ot_shipping.php;ot_total.php)', '6', '0', '2016-07-05 10:21:50', '2003-09-21 16:08:39', NULL, NULL),
('69', 'Installed Modules', 'MODULE_SHIPPING_INSTALLED', 'TCSShip.php', 'List of shipping module filenames separated by a semi-colon. This is automatically updated. No need to edit. (Example: ups.php;flat.php;item.php)', '6', '0', '2016-10-28 16:16:13', '2003-09-21 16:08:39', NULL, NULL),
('84', 'Default Currency', 'DEFAULT_CURRENCY', 'GBP', 'Default Currency', '6', '0', NULL, '2003-09-21 16:08:39', NULL, NULL),
('85', 'Default Language', 'DEFAULT_LANGUAGE', 'en', 'Default Language', '6', '0', NULL, '2003-09-21 16:08:39', NULL, NULL),
('86', 'Default Order Status For New Orders', 'DEFAULT_ORDERS_STATUS_ID', '1', 'When a new order is created, this order status will be assigned to it.', '6', '0', NULL, '2003-09-21 16:08:39', NULL, NULL),
('439', 'Provide Free Shipping For Orders Made', 'MODULE_ORDER_TOTAL_SHIPPING_DESTINATION', 'national', 'Provide free shipping for orders sent to the set destination.', '6', '5', NULL, '2006-05-23 22:17:07', NULL, 'tep_cfg_select_option(array(\'national\', \'international\', \'both\'),'),
('438', 'Free Shipping For Orders Over', 'MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING_OVER', '200', 'Provide free shipping for orders over the set amount.', '6', '4', NULL, '2006-05-23 22:17:07', 'currencies->format', NULL),
('437', 'Allow Free Shipping', 'MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING', 'false', 'Do you want to allow free shipping?', '6', '3', NULL, '2006-05-23 22:17:07', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('436', 'Sort Order', 'MODULE_ORDER_TOTAL_SHIPPING_SORT_ORDER', '2', 'Sort order of display.', '6', '2', NULL, '2006-05-23 22:17:07', NULL, NULL),
('92', 'Display Sub-Total', 'MODULE_ORDER_TOTAL_SUBTOTAL_STATUS', 'true', 'Do you want to display the order sub-total cost?', '6', '1', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('93', 'Sort Order', 'MODULE_ORDER_TOTAL_SUBTOTAL_SORT_ORDER', '1', 'Sort order of display.', '6', '2', NULL, '2003-09-21 16:08:39', NULL, NULL),
('94', 'Display Tax', 'MODULE_ORDER_TOTAL_TAX_STATUS', 'true', 'Do you want to display the order tax value?', '6', '1', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('95', 'Sort Order', 'MODULE_ORDER_TOTAL_TAX_SORT_ORDER', '18', 'Sort order of display.', '6', '2', NULL, '2003-09-21 16:08:39', NULL, NULL),
('98', 'Country of Origin', 'SHIPPING_ORIGIN_COUNTRY', '222', 'Select the country of origin to be used in shipping quotes.', '7', '1', '2003-11-30 23:15:27', '2003-09-21 16:08:39', 'tep_get_country_name', 'tep_cfg_pull_down_country_list('),
('99', 'Postal Code', 'SHIPPING_ORIGIN_ZIP', 'NG9 2EU', 'Enter the Postal Code (ZIP) of the Store to be used in shipping quotes.', '7', '2', '2004-02-29 12:56:36', '2003-09-21 16:08:39', NULL, NULL),
('100', 'Enter the Maximum Package Weight you will ship', 'SHIPPING_MAX_WEIGHT', '*********', 'Carriers have a max weight limit for a single package. This is a common one for all.', '7', '3', '2004-03-03 12:17:02', '2003-09-21 16:08:39', NULL, NULL),
('101', 'Package Tare weight.', 'SHIPPING_BOX_WEIGHT', '5', 'What is the weight of typical packaging of small to medium packages?', '7', '4', '2004-03-03 12:52:18', '2003-09-21 16:08:39', NULL, NULL),
('102', 'Larger packages - percentage increase.', 'SHIPPING_BOX_PADDING', '2', 'For 10% enter 10', '7', '5', '2004-03-03 12:52:35', '2003-09-21 16:08:39', NULL, NULL),
('103', 'Display Product Image', 'PRODUCT_LIST_IMAGE', '10', 'Do you want to display the Product Image?', '8', '1', '2003-10-13 18:17:42', '2003-09-21 16:08:39', NULL, NULL),
('104', 'Display Product Manufaturer Name', 'PRODUCT_LIST_MANUFACTURER', '20', 'Do you want to display the Product Manufacturer Name?', '8', '2', '2016-10-20 11:03:49', '2003-09-21 16:08:39', NULL, NULL),
('105', 'Display Product Model', 'PRODUCT_LIST_MODEL', '30', 'Do you want to display the Product Model?', '8', '3', '2003-10-13 18:17:30', '2003-09-21 16:08:39', NULL, NULL),
('106', 'Display Product Name', 'PRODUCT_LIST_NAME', '40', 'Do you want to display the Product Name?', '8', '4', '2003-10-13 18:17:22', '2003-09-21 16:08:39', NULL, NULL),
('107', 'Display Product Price', 'PRODUCT_LIST_PRICE', '50', 'Do you want to display the Product Price', '8', '5', '2003-10-13 18:17:56', '2003-09-21 16:08:39', NULL, NULL),
('108', 'Display Product Quantity', 'PRODUCT_LIST_QUANTITY', '', 'Do you want to display the Product Quantity?', '8', '6', NULL, '2003-09-21 16:08:39', NULL, NULL),
('109', 'Display Product Weight', 'PRODUCT_LIST_WEIGHT', '', 'Do you want to display the Product Weight?', '8', '7', NULL, '2003-09-21 16:08:39', NULL, NULL),
('110', 'Display Buy Now column', 'PRODUCT_LIST_BUY_NOW', '60', 'Do you want to display the Buy Now column?', '8', '8', '2003-12-02 00:29:47', '2003-09-21 16:08:39', NULL, NULL),
('111', 'Display Category/Manufacturer Filter (0=disable; 1=enable)', 'PRODUCT_LIST_FILTER', '1', 'Do you want to display the Category/Manufacturer Filter?', '8', '9', NULL, '2003-09-21 16:08:39', NULL, NULL),
('112', 'Location of Prev/Next Navigation Bar (1-top, 2-bottom, 3-both)', 'PREV_NEXT_BAR_LOCATION', '3', 'Sets the location of the Prev/Next Navigation Bar (1-top, 2-bottom, 3-both)', '8', '10', '2003-10-13 18:16:29', '2003-09-21 16:08:39', NULL, NULL),
('113', 'Check stock level', 'STOCK_CHECK', 'false', 'Check to see if sufficent stock is available', '9', '1', '2004-04-22 10:55:04', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('114', 'Subtract stock', 'STOCK_LIMITED', 'false', 'Subtract product in stock by product orders', '9', '2', '2004-04-22 10:55:39', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('115', 'Allow Checkout', 'STOCK_ALLOW_CHECKOUT', 'true', 'Allow customer to checkout even if there is insufficient stock', '9', '3', '2004-04-22 10:55:23', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('116', 'Mark product out of stock', 'STOCK_MARK_PRODUCT_OUT_OF_STOCK', '<i class=\"glyphicon glyphicon-asterisk\"></i>', 'Display something on screen so customer can see which product has insufficient stock', '9', '4', '2004-04-22 10:56:36', '2003-09-21 16:08:39', NULL, NULL),
('117', 'Stock Re-order level', 'STOCK_REORDER_LEVEL', '5', 'Define when stock needs to be re-ordered', '9', '5', '2004-04-22 10:56:50', '2003-09-21 16:08:39', NULL, NULL),
('118', 'Store Page Parse Time', 'STORE_PAGE_PARSE_TIME', 'false', 'Store the time it takes to parse a page', '10', '1', '2017-01-19 10:47:48', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('119', 'Log Destination', 'STORE_PAGE_PARSE_TIME_LOG', '/tmp/page_parse_time.log', 'Directory and filename of the page parse time log', '10', '2', '2016-10-21 11:14:42', '2003-09-21 16:08:39', NULL, NULL),
('120', 'Log Date Format', 'STORE_PARSE_DATE_TIME_FORMAT', '%d/%m/%Y %H:%M:%S', 'The date format', '10', '3', NULL, '2003-09-21 16:08:39', NULL, NULL),
('121', 'Display The Page Parse Time', 'DISPLAY_PAGE_PARSE_TIME', 'false', 'Display the page parse time (store page parse time must be enabled)', '10', '4', '2017-01-03 09:29:34', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('122', 'Store Database Queries', 'STORE_DB_TRANSACTIONS', 'false', 'Store the database queries in the page parse time log (PHP4 only)', '10', '5', '2017-02-13 09:49:45', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('123', 'Use Cache', 'USE_CACHE', 'true', 'Use caching features', '11', '1', '2016-10-21 11:13:17', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('124', 'Cache Directory', 'DIR_FS_CACHE', '/var/www/vhosts/cadservices.co.uk/temp/www.cadservices.co.uk/', 'The directory where the cached files are saved', '11', '2', '2024-04-17 15:51:25', '2003-09-21 16:08:39', NULL, NULL),
('125', 'E-Mail Transport Method', 'EMAIL_TRANSPORT', 'sendmail', 'Defines if this server uses a local connection to sendmail or uses an SMTP connection via TCP/IP. Servers running on Windows and MacOS should change this setting to SMTP.', '12', '1', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'sendmail\', \'smtp\'),'),
('126', 'E-Mail Linefeeds', 'EMAIL_LINEFEED', 'LF', 'Defines the character sequence used to separate mail headers.', '12', '2', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'LF\', \'CRLF\'),'),
('127', 'Use MIME HTML When Sending Emails', 'EMAIL_USE_HTML', 'true', 'Send e-mails in HTML format', '12', '3', '2004-08-16 15:35:39', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('128', 'Verify E-Mail Addresses Through DNS', 'ENTRY_EMAIL_ADDRESS_CHECK', 'false', 'Verify e-mail address through a DNS server', '12', '4', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('129', 'Send E-Mails', 'SEND_EMAILS', 'true', 'Send out e-mails', '12', '5', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('130', 'Enable download', 'DOWNLOAD_ENABLED', 'false', 'Enable the products download functions.', '13', '1', '2005-05-01 20:18:21', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('131', 'Download by redirect', 'DOWNLOAD_BY_REDIRECT', 'false', 'Use browser redirection for download. Disable on non-Unix systems.', '13', '2', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('132', 'Expiry delay (days)', 'DOWNLOAD_MAX_DAYS', '7', 'Set number of days before the download link expires. 0 means no limit.', '13', '3', NULL, '2003-09-21 16:08:39', NULL, ''),
('133', 'Maximum number of downloads', 'DOWNLOAD_MAX_COUNT', '5', 'Set the maximum number of downloads. 0 means no download authorized.', '13', '4', NULL, '2003-09-21 16:08:39', NULL, ''),
('134', 'Enable GZip Compression', 'GZIP_COMPRESSION', 'false', 'Enable HTTP GZip compression.', '14', '1', '2018-02-19 16:10:13', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('135', 'Compression Level', 'GZIP_LEVEL', '5', 'Use this compression level 0-9 (0 = minimum, 9 = maximum).', '14', '2', NULL, '2003-09-21 16:08:39', NULL, NULL),
('136', 'Session Directory', 'SESSION_WRITE_DIRECTORY', '/tmp', 'If sessions are file based, store them in this directory.', '15', '1', NULL, '2003-09-21 16:08:39', NULL, NULL),
('137', 'Force Cookie Use', 'SESSION_FORCE_COOKIE_USE', 'True', 'Force the use of sessions when cookies are only enabled.', '15', '2', '2016-12-13 10:32:21', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('138', 'Check SSL Session ID', 'SESSION_CHECK_SSL_SESSION_ID', 'False', 'Validate the SSL_SESSION_ID on every secure HTTPS page request.', '15', '3', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('139', 'Check User Agent', 'SESSION_CHECK_USER_AGENT', 'False', 'Validate the clients browser user agent on every page request.', '15', '4', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('140', 'Check IP Address', 'SESSION_CHECK_IP_ADDRESS', 'False', 'Validate the clients IP address on every page request.', '15', '5', NULL, '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('141', 'Prevent Spider Sessions', 'SESSION_BLOCK_SPIDERS', 'True', 'Prevent known spiders from starting a session.', '15', '6', '2007-05-24 10:06:45', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('142', 'Recreate Session', 'SESSION_RECREATE', 'True', 'Recreate the session to generate a new session ID when the customer logs on or creates an account (PHP >=4.1 needed).', '15', '7', '2016-12-13 10:32:33', '2003-09-21 16:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('143', 'Latest News', 'MAX_DISPLAY_LATEST_NEWS', '4', 'maximum number of items to display in the Latest News box', '3', NULL, '2003-09-21 23:39:03', '2003-09-21 23:21:52', NULL, NULL),
('144', 'Default Reply Admin For Tickets', 'TICKET_DEFAULT_ADMIN_ID', '1', 'When you reply to ticket, this is the default person assigned to it.', '6', '0', NULL, '2003-09-22 14:21:51', NULL, NULL),
('145', 'Default Reply For Tickets', 'TICKET_DEFAULT_REPLY_ID', '1', 'When you reply to ticket, this answer is preselected.', '6', '0', NULL, '2003-09-22 14:21:51', NULL, NULL),
('146', 'Default Ticket Status For Tickets', 'TICKET_DEFAULT_STATUS_ID', '1', 'When a new ticket is created, this ticket status will be assigned to it.', '6', '0', NULL, '2003-09-22 14:21:51', NULL, NULL),
('147', 'Default Ticket Status for replis', 'TICKET_CUSTOMER_REPLY_STATUS_ID', '1', 'When a customer replies to ticket, this ticket status will be assigned to it.', '6', '0', NULL, '2003-09-22 14:21:51', NULL, NULL),
('148', 'Default Department For Tickets', 'TICKET_DEFAULT_DEPARTMENT_ID', '1', 'When a new department is created, this department will be assigned to it.', '6', '0', NULL, '2003-09-22 14:21:51', NULL, NULL),
('149', 'Default Priority For Tickets', 'TICKET_DEFAULT_PRIORITY_ID', '3', 'When a new ticket is created, this priority will be assigned to it.', '6', '0', NULL, '2003-09-22 14:21:51', NULL, NULL),
('150', 'E-Mail Address', 'AFFILIATE_EMAIL_ADDRESS', '<<EMAIL>>', 'The E Mail Address for the Affiliate Programm', '900', '1', '2003-11-30 23:17:05', '2003-09-22 22:39:09', NULL, NULL),
('151', 'Affiliate Pay Per Sale Payment % Rate', 'AFFILIATE_PERCENT', '2.0000', 'Percentage Rate for the Affiliate Program', '900', '2', '2003-12-28 09:03:52', '2003-09-22 22:39:09', NULL, NULL),
('152', 'Payment Threshold', 'AFFILIATE_THRESHOLD', '50.00', 'Payment Threshold for paying affiliates', '900', '3', NULL, '2003-09-22 22:39:09', NULL, NULL),
('153', 'Cookie Lifetime', 'AFFILIATE_COOKIE_LIFETIME', '7200', 'How long does the click count (seconds) if customer comes back', '900', '4', NULL, '2003-09-22 22:39:09', NULL, NULL),
('154', 'Billing Time', 'AFFILIATE_BILLING_TIME', '30', 'Orders billed must be at least \"30\" days old.<br>This is needed if a order is refunded', '900', '5', NULL, '2003-09-22 22:39:09', NULL, NULL),
('155', 'Order Min Status', 'AFFILIATE_PAYMENT_ORDER_MIN_STATUS', '3', 'The status an order must have at least, to be billed', '900', '6', NULL, '2003-09-22 22:39:09', NULL, NULL),
('156', 'Pay Affiliates with check', 'AFFILIATE_USE_CHECK', 'true', 'Pay Affiliates with check', '900', '7', NULL, '2003-09-22 22:39:09', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('157', 'Pay Affiliates with PayPal', 'AFFILIATE_USE_PAYPAL', 'true', 'Pay Affiliates with PayPal', '900', '8', NULL, '2003-09-22 22:39:09', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('158', 'Pay Affiliates by Bank', 'AFFILIATE_USE_BANK', 'true', 'Pay Affiliates by Bank', '900', '9', NULL, '2003-09-22 22:39:09', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('159', 'Individual Affiliate Percentage', 'AFFILATE_INDIVIDUAL_PERCENTAGE', 'true', 'Allow per Affiliate provision', '900', '10', NULL, '2003-09-22 22:39:09', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('160', 'Use Affiliate-tier', 'AFFILATE_USE_TIER', 'false', 'Multilevel Affiliate provisions', '900', '11', NULL, '2003-09-22 22:39:09', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('161', 'Number of Tierlevels', 'AFFILIATE_TIER_LEVELS', '', 'Number of Tierlevels', '900', '12', NULL, '2003-09-22 22:39:09', NULL, NULL),
('162', 'Percentage Rate for the Tierlevels', 'AFFILIATE_TIER_PERCENTAGE', '8.00;5.00;1.00', 'Percent Rates for the tierlevels<br>Example: 8.00;5.00;1.00', '900', '13', NULL, '2003-09-22 22:39:09', NULL, NULL),
('163', 'Display Image Width', 'DISPLAY_IMAGE_WIDTH', '1000', 'The pixel width of <b>product info</b> images', '4', '9', '2018-05-25 12:04:07', '2003-11-30 16:26:41', NULL, NULL),
('164', 'Display Image Height', 'DISPLAY_IMAGE_HEIGHT', '1000', 'The pixel height of <b>product info</b> images', '4', '10', '2016-08-03 16:16:39', '2003-11-30 16:26:41', NULL, NULL),
('165', 'Popup Image Width', 'POPUP_IMAGE_WIDTH', '', 'The pixel width of popup images. To show the original width image, leave blank the field!', '4', '11', '2003-11-30 16:26:41', '2003-11-30 16:26:41', NULL, NULL),
('166', 'Popup Image Height', 'POPUP_IMAGE_HEIGHT', '', 'The pixel height of popup images. To show the original height image, leave blank the field!', '4', '12', '2003-11-30 16:26:41', '2003-11-30 16:26:41', NULL, NULL),
('195', 'Display Total', 'MODULE_ORDER_TOTAL_TOTAL_STATUS', 'true', 'Do you want to display the total order value?', '6', '1', NULL, '2003-11-30 18:58:54', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('196', 'Sort Order', 'MODULE_ORDER_TOTAL_TOTAL_SORT_ORDER', '20', 'Sort order of display.', '6', '2', NULL, '2003-11-30 18:58:54', NULL, NULL),
('197', 'Header Text', 'FAMILY_HEADER_TEXT', 'UPGRADE OPTIONS FOR ABOVE', 'The text that will appear as the header of the Family Products v3.0 module.', '16', '1', '2004-10-22 10:38:42', '0000-00-00 00:00:00', NULL, NULL),
('198', 'Family Header Format', 'FAMILY_HEADER_FORMAT', 'Family Text', 'Please choose whether your headers of your families will be your default text or the actual name of the family.', '16', '2', '2004-03-16 17:53:22', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'Family Text\', \'Family Name\'),'),
('199', 'Family Display Type', 'FAMILY_DISPLAY_TYPE', 'List', 'Please choose whether you would like to display an infoBox or a list.', '16', '3', '2006-01-24 12:51:15', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'Box\', \'List\', \'None\'),'),
('200', 'Family Display Format', 'FAMILY_DISPLAY_FORMAT', 'Seperate', 'Please choose whether you would like to randomly select products frm all matching families, or if you would like to display seperate families.', '16', '4', '2006-01-24 12:51:34', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'Random\', \'Seperate\'),'),
('435', 'Display Shipping', 'MODULE_ORDER_TOTAL_SHIPPING_STATUS', 'true', 'Do you want to display the order shipping cost?', '6', '1', NULL, '2006-05-23 22:17:07', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1220', 'Accept VISA cards', 'MODULE_PAYMENT_CC_ACCEPT_VISA', 'True', 'Accept VISA cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1219', 'Accept MAESTRO cards', 'MODULE_PAYMENT_CC_ACCEPT_MAESTRO', 'True', 'Accept MAESTRO cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1217', 'Accept SOLO cards', 'MODULE_PAYMENT_CC_ACCEPT_SOLO', 'False', 'Accept SOLO cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1218', 'Accept JCB cards', 'MODULE_PAYMENT_CC_ACCEPT_JCB', 'False', 'Accept JCB cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('392', 'Sort order of display.', 'MODULE_PAYMENT_INSTITUTIONAL_PURCHASE_SORT_ORDER', '20', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2006-03-20 10:19:37', NULL, NULL),
('391', 'Enable Institutional Purchase Payment', 'MODULE_PAYMENT_INSTITUTIONAL_PURCHASE_STATUS', 'True', 'Do you want to accept Institutional Purchase Order payments?', '6', '0', NULL, '2006-03-20 10:19:37', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('284', 'Header Text', 'FAMILY_HEADER_TEXT', 'Upgrades & Extras for above', 'The text that will appear as the header of the Family Products v3.0 module.', '16', '1', '2004-04-05 15:35:28', '0000-00-00 00:00:00', NULL, NULL),
('285', 'Family Header Format', 'FAMILY_HEADER_FORMAT', 'Family Text', 'Please choose whether your headers of your families will be your default text or the actual name of the family.', '16', '2', '2003-10-13 22:56:28', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'Family Text\', \'Family Name\'),'),
('286', 'Family Display Type', 'FAMILY_DISPLAY_TYPE', 'List', 'Please choose whether you would like to display an infoBox or a list.', '16', '3', '2004-03-16 17:52:32', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'Box\', \'List\', \'None\'),'),
('287', 'Family Display Format', 'FAMILY_DISPLAY_FORMAT', 'Seperate', 'Please choose whether you would like to randomly select products frm all matching families, or if you would like to display seperate families.', '16', '4', '2006-01-24 12:52:23', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'Random\', \'Seperate\'),'),
('1248', 'Enable TCS Shipping Prices', 'MODULE_SHIPPING_TCSSHIP_STATUS', 'True', 'Do you want to offer individual shipping prices?', '6', '0', NULL, '2016-10-28 16:16:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1251', 'Sort Order', 'MODULE_SHIPPING_TCSSHIP_SORT_ORDER', '0', 'Sort order of display.', '6', '0', NULL, '2016-10-28 16:16:13', NULL, NULL),
('1249', 'Tax Class', 'MODULE_SHIPPING_TCSSHIP_TAX_CLASS', '1', 'Use the following tax class on the shipping fee.', '6', '0', NULL, '2016-10-28 16:16:13', 'tep_get_tax_class_title', 'tep_cfg_pull_down_tax_classes('),
('1250', 'Shipping Zone', 'MODULE_SHIPPING_TCSSHIP_ZONE', '0', 'If a zone is selected, only enable this shipping method for that zone.', '6', '0', NULL, '2016-10-28 16:16:13', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes('),
('316', 'Street Address2', 'ENTRY_STREET_ADDRESS2_MIN_LENGTH', '', 'Minimum length of street address2', '2', '5', '2004-10-01 00:37:02', '2004-07-25 16:22:27', NULL, NULL),
('1216', 'Accept SWITCH cards', 'MODULE_PAYMENT_CC_ACCEPT_SWITCH', 'True', 'Accept SWITCH cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1215', 'Accept MASTERCARD cards', 'MODULE_PAYMENT_CC_ACCEPT_MASTERCARD', 'True', 'Accept MASTERCARD cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1214', 'Accept ELECTRON cards', 'MODULE_PAYMENT_CC_ACCEPT_ELECTRON', 'False', 'Accept ELECTRON cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1213', 'Accept DELTA cards', 'MODULE_PAYMENT_CC_ACCEPT_DELTA', 'False', 'Accept DELTA cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1212', 'Accept DISCOVER/NOVUS cards', 'MODULE_PAYMENT_CC_ACCEPT_DISCOVERNOVUS', 'False', 'Accept DISCOVERNOVUS cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1211', 'Accept AUSTRALIAN BANKCARD cards', 'MODULE_PAYMENT_CC_ACCEPT_OZBANKCARD', 'False', 'Accept AUSTRALIAN BANK cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1210', 'Accept CARTE BLANCHE cards', 'MODULE_PAYMENT_CC_ACCEPT_CARTEBLANCHE', 'False', 'Accept CARTE BLANCHE cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1209', 'Accept AMERICAN EXPRESS cards', 'MODULE_PAYMENT_CC_ACCEPT_AMERICANEXPRESS', 'False', 'Accept AMERICAN EXPRESS cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1208', 'Accept DINERS CLUB cards', 'MODULE_PAYMENT_CC_ACCEPT_DINERSCLUB', 'False', 'Accept DINERS CLUB cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1207', 'Split  Card E-Mail Address', 'MODULE_PAYMENT_CC_EMAIL', '', 'If an e-mail address is entered, the middle digits of the  card number will be sent to the e-mail address (the outside digits are stored in the database with the middle digits censored)', '6', '0', NULL, '2016-10-21 10:12:13', NULL, NULL),
('1206', 'CVV Number Length', 'CC_CVV_MIN_LENGTH', '3', 'Define CVV length. The default is 3 and should not be changed unless the industry standard changes.', '6', '0', NULL, '2016-10-21 10:12:13', NULL, NULL),
('1205', 'Collect Start Date', 'USE_CC_START', 'True', 'Do you want to collect the Start Date?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1204', 'Collect Issue Number', 'USE_CC_ISS', 'True', 'Do you want to collect Issue Number?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1203', 'Collect CVV Number', 'USE_CC_CVV', 'True', 'Do you want to collect CVV Number?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1202', 'Set Order Status', 'MODULE_PAYMENT_CC_ORDER_STATUS_ID', '0', 'Set the status of orders made with this payment module to this value', '6', '0', NULL, '2016-10-21 10:12:13', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
('1201', 'Payment Zone', 'MODULE_PAYMENT_CC_ZONE', '0', 'If a zone is selected, only enable this payment method for that zone.', '6', '2', NULL, '2016-10-21 10:12:13', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes('),
('1200', 'Sort order of display.', 'MODULE_PAYMENT_CC_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-10-21 10:12:13', NULL, NULL),
('1199', 'Encrypt CC Info', 'CC_ENC', 'True', 'Do you want to encypt cc info?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1198', 'Enable CC Blacklist Check', 'CC_BLACK', 'False', 'Do you want to enable CC blacklist check?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1197', 'Enable CC Validation', 'CC_VAL', 'False', 'Do you want to enable CC validation and identify cards?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1196', 'Enable  Card Module', 'MODULE_PAYMENT_CC_STATUS', 'True', 'Do you want to accept  card payments?', '6', '0', NULL, '2016-10-21 10:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('362', 'Display Total', 'MODULE_PAYMENT_STATUS', 'true', 'Do you want to enable the Order Payment Fee?', '6', '1', NULL, '2004-08-17 14:40:33', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('363', 'Sort Order', 'MODULE_PAYMENT_SORT_ORDER', '888', 'Sort order of display.', '6', '2', NULL, '2004-08-17 14:40:33', NULL, NULL),
('364', 'Include Shipping', 'MODULE_PAYMENT_INC_SHIPPING', 'true', 'Include Shipping in calculation', '6', '5', NULL, '2004-08-17 14:40:33', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('365', 'Include Tax', 'MODULE_PAYMENT_INC_TAX', 'true', 'Include Tax in calculation.', '6', '6', NULL, '2004-08-17 14:40:33', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('366', 'Surcharge Percentage', 'MODULE_PAYMENT_PERCENTAGE', '3', 'Amount of Surcharge(percentage).', '6', '7', NULL, '2004-08-17 14:40:33', NULL, NULL),
('367', 'Calculate Tax', 'MODULE_PAYMENT_CALC_TAX', 'false', 'Re-calculate Tax on surcharged amount.', '6', '5', NULL, '2004-08-17 14:40:33', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('368', 'Minimum Amount', 'MODULE_PAYMENT_MINIMUM', '', 'Minimum order before fee', '6', '2', NULL, '2004-08-17 14:40:33', NULL, NULL),
('369', 'Payment Type', 'MODULE_PAYMENT_TYPE', 'moneyorder', 'Payment Type to pay surcharge', '6', '2', NULL, '2004-08-17 14:40:33', NULL, NULL),
('399', 'Shipping Zone', 'MODULE_SHIPPING_SIMPLEWEIGHT_ZONE', '2', 'If a zone is selected, only enable this shipping method for that zone.', '6', '0', NULL, '2006-05-22 10:09:52', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes('),
('400', 'Sort Order', 'MODULE_SHIPPING_SIMPLEWEIGHT_SORT_ORDER', '0', 'Sort order of display.', '6', '0', NULL, '2006-05-22 10:09:52', NULL, NULL),
('398', 'Tax Class', 'MODULE_SHIPPING_SIMPLEWEIGHT_TAX_CLASS', '1', 'Use the following tax class on the shipping fee.', '6', '0', NULL, '2006-05-22 10:09:52', 'tep_get_tax_class_title', 'tep_cfg_pull_down_tax_classes('),
('395', 'Enable Table Method', 'MODULE_SHIPPING_SIMPLEWEIGHT_STATUS', 'True', 'Do you want to offer table rate shipping?', '6', '0', NULL, '2006-05-22 10:09:52', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),'),
('396', 'Shipping Table', 'MODULE_SHIPPING_SIMPLEWEIGHT_COST', '1:2.00', 'The shipping cost is based on the total weight of items. Example: 1:2.00 $2 per kilogram', '6', '0', NULL, '2006-05-22 10:09:52', NULL, NULL),
('397', 'Handling Fee', 'MODULE_SHIPPING_SIMPLEWEIGHT_HANDLING', '0', 'Handling fee for this shipping method.', '6', '0', NULL, '2006-05-22 10:09:52', NULL, NULL),
('388', 'Zone 2 Countries', 'MODULE_SHIPPING_ZONES_COUNTRIES_2', 'IE,ALB', 'Comma separated list of two character ISO country codes that are part of Zone 2.', '6', '0', NULL, '2005-11-15 09:22:08', NULL, NULL),
('389', 'Zone 2 Shipping Table', 'MODULE_SHIPPING_ZONES_COST_2', '3:8.50,7:10.50,99:30.00', 'Shipping rates to Zone 2 destinations based on a group of maximum order weights. Example: 3:8.50,7:10.50,... Weights less than or equal to 3 would cost 8.50 for Zone 2 destinations.', '6', '0', NULL, '2005-11-15 09:22:08', NULL, NULL),
('390', 'Zone 2 Handling Fee', 'MODULE_SHIPPING_ZONES_HANDLING_2', '0', 'Handling Fee for this shipping zone', '6', '0', NULL, '2005-11-15 09:22:08', NULL, NULL),
('450', 'Google Maps Key', 'GOOGLE_MAPS_KEY', 'YOURKEY', 'Put your Google Maps API Key here.<br><br>You can get one at http://code.google.com/apis/maps/signup.html', '1', '25', NULL, '2009-08-22 12:18:00', NULL, 'tep_cfg_textarea('),
('451', 'Number of dropdowns used to assign products', 'FAMILY_INSERT_COUNT', '5', 'Number of dropdowns on \"Assign Products\" page.', '16', '5', '2012-08-20 11:12:49', '2012-08-20 11:12:49', NULL, NULL),
('452', 'Max. no. of products to display', 'FAMILY_DISPLAY_COUNT', '9', 'Maximum number of products to display in Families infobox on product_info page.', '16', '6', '2012-08-20 11:12:50', '2012-08-20 11:12:50', NULL, NULL),
('453', 'Number of dropdowns used to assign products', 'FAMILY_INSERT_COUNT', '5', 'Number of dropdowns on \"Assign Products\" page.', '16', '5', '2012-08-20 11:12:53', '2012-08-20 11:12:53', NULL, NULL),
('454', 'Max. no. of products to display', 'FAMILY_DISPLAY_COUNT', '9', 'Maximum number of products to display in Families infobox on product_info page.', '16', '6', '2012-08-20 11:12:53', '2012-08-20 11:12:53', NULL, NULL),
('455', 'Number of dropdowns used to assign products', 'FAMILY_INSERT_COUNT', '5', 'Number of dropdowns on \"Assign Products\" page.', '16', '5', '2003-09-09 11:14:45', '2003-09-09 11:14:45', NULL, NULL),
('456', 'Max. no. of products to display', 'FAMILY_DISPLAY_COUNT', '9', 'Maximum number of products to display in Families infobox on product_info page.', '16', '6', '2003-09-09 11:14:45', '2003-09-09 11:14:45', NULL, NULL),
('457', 'Auto Backup Interval', 'BACKUP_INTERVAL', '480', 'Alter the automatic dBase backup interval in minutes, i.e. 20mins (5 mins minimum).<br /><br />When a new backup is created the previous interim one is deleted, subject to the <b>Backup Archive Interval</b>', '901', '30', NULL, '2013-04-05 16:47:56', NULL, NULL),
('458', 'Enable Auto Backup', 'ENABLE_AUTO_BACKUP', 'true', 'Set to true to enable Auto Backup.', '901', '0', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('459', 'Backup Archive Interval', 'BACKUP_SAVE_INTERVAL', '24', 'Set Interval to keep backups in hours. Suggest 24 Hrs so a backup archive is kept once a day.<br /><br />That means when a new backup is created, the previous interim one is deleted so long as another older but less than this age exists.', '901', '31', NULL, '2013-04-05 16:47:56', NULL, NULL),
('460', 'GZip Backups', 'BACKUP_ZIP', 'gzip', 'Enable GZipping of auto-backup files.<br /><br />Once a backup has been successfully gzipped the uncompressed original is automatically deleted.', '901', '42', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'gzip\', \'false\'),'),
('461', 'Split Backups On Timeout', 'BACKUP_SPLIT', 'extend', 'Split backups if approaching max execution time or extend execution time.<br /><br />Splitting reduces problems with admin page loads/updates if backup takes too long but may result in invalid backups if database changes during split.', '901', '44', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'split\', \'extend\'),'),
('462', 'Auto Backup Displayed Time Format', 'BACKUP_DATETIME_FMT', 'true', 'True = Use the auto backup date/time format<br />(23rd Feb 2012 15:42) or<br /><br />
False = use your system date/time format,<br />ie 23/02/2012 15:34:33', '901', '46', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('463', 'Auto Run Auto Backup Delete', 'AUTO_BACKUP_DELETE', 'false', 'Auto Backup Delete is meant to be called as a cron job, however you can enable auto-backup to call it, which it will do whenever the log file is over a day old or does not exist.', '901', '48', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('464', 'Backup Delete Interval', 'BACKUP_DELETE', '30', 'The number of days to keep backups before they are permanently removed', '901', '50', NULL, '2013-04-05 16:47:56', '', ''),
('465', 'Email Cron Backups', 'BACKUP_MAIL_CRON', 'true', 'Email cron backups on completion.<br /><br />By default backups are emailed to the store owner (My Store configuration)', '901', '49', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('613', 'Auto Backup Interval', 'BACKUP_INTERVAL', '480', 'Alter the automatic dBase backup interval in minutes, i.e. 20mins (5 mins minimum).<br /><br />When a new backup is created the previous interim one is deleted, subject to the <b>Backup Archive Interval</b>', '901', '30', NULL, '2013-04-05 16:47:56', NULL, NULL),
('614', 'Enable Auto Backup', 'ENABLE_AUTO_BACKUP', 'true', 'Set to true to enable Auto Backup.', '901', '0', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('615', 'Backup Archive Interval', 'BACKUP_SAVE_INTERVAL', '24', 'Set Interval to keep backups in hours. Suggest 24 Hrs so a backup archive is kept once a day.<br /><br />That means when a new backup is created, the previous interim one is deleted so long as another older but less than this age exists.', '901', '31', NULL, '2013-04-05 16:47:56', NULL, NULL),
('616', 'GZip Backups', 'BACKUP_ZIP', 'gzip', 'Enable GZipping of auto-backup files.<br /><br />Once a backup has been successfully gzipped the uncompressed original is automatically deleted.', '901', '42', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'gzip\', \'false\'),'),
('617', 'Split Backups On Timeout', 'BACKUP_SPLIT', 'extend', 'Split backups if approaching max execution time or extend execution time.<br /><br />Splitting reduces problems with admin page loads/updates if backup takes too long but may result in invalid backups if database changes during split.', '901', '44', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'split\', \'extend\'),'),
('618', 'Auto Backup Displayed Time Format', 'BACKUP_DATETIME_FMT', 'true', 'True = Use the auto backup date/time format<br />(23rd Feb 2012 15:42) or<br /><br />
False = use your system date/time format,<br />ie 23/02/2012 15:34:33', '901', '46', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('619', 'Auto Run Auto Backup Delete', 'AUTO_BACKUP_DELETE', 'false', 'Auto Backup Delete is meant to be called as a cron job, however you can enable auto-backup to call it, which it will do whenever the log file is over a day old or does not exist.', '901', '48', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('620', 'Backup Delete Interval', 'BACKUP_DELETE', '30', 'The number of days to keep backups before they are permanently removed', '901', '50', NULL, '2013-04-05 16:47:56', '', ''),
('621', 'Email Cron Backups', 'BACKUP_MAIL_CRON', 'true', 'Email cron backups on completion.<br /><br />By default backups are emailed to the store owner (My Store configuration)', '901', '49', NULL, '2013-04-05 16:47:56', '', 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('622', 'Product Quantities In Shopping Cart', 'MAX_QTY_IN_CART', '50', 'Maximum number of product quantities that can be added to the shopping cart (0 for no limit)', '3', '19', '2021-03-24 12:57:16', '2014-01-22 15:39:13', NULL, NULL),
('623', 'Installed
Modules', 'MODULE_ACTION_RECORDER_INSTALLED', 'ar_admin_login.php;ar_contact_us.php;ar_reset_password.php;ar_tell_a_friend.php', 'List of action recorder module
filenames separated by a semi-colon. This is automatically updated. No need to edit.', '6', '0', '2016-12-14 09:20:49', '2014-01-22 15:39:31', NULL, NULL),
('624', 'Minimum
Minutes Per E-Mail', 'MODULE_ACTION_RECORDER_CONTACT_US_EMAIL_MINUTES', '15', 'Minimum number of
minutes to allow 1 e-mail to be sent (eg, 15 for 1 e-mail every 15 minutes)', '6', '0', NULL, '2014-01-22 15:39:31', NULL, NULL),
('625', 'Minimum
Minutes Per E-Mail', 'MODULE_ACTION_RECORDER_TELL_A_FRIEND_EMAIL_MINUTES', '15', 'Minimum number
of minutes to allow 1 e-mail to be sent (eg, 15 for 1 e-mail every 15 minutes)', '6', '0', NULL, '2014-01-22 15:39:31', NULL, NULL),
('626', 'Allowed
Minutes', 'MODULE_ACTION_RECORDER_ADMIN_LOGIN_MINUTES', '5', 'Number of minutes to allow login
attempts to occur.', '6', '0', NULL, '2014-01-22 15:39:31', NULL, NULL),
('627', 'Allowed
Attempts', 'MODULE_ACTION_RECORDER_ADMIN_LOGIN_ATTEMPTS', '3', 'Number of login attempts to allow
within the specified period.', '6', '0', NULL, '2014-01-22 15:39:31', NULL, NULL),
('1221', 'Allow Purchase Orders', 'MODULE_PAYMENT_PO_STATUS', 'True', 'Do you want to accept Purchase Orders?', '6', '6', NULL, '2016-10-21 10:18:20', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1222', 'Sort Order', 'MODULE_PAYMENT_PO_SORT_ORDER', '0', 'Sort order of display.', '6', '0', NULL, '2016-10-21 10:18:20', NULL, NULL),
('1612', 'Set Order Status', 'MODULE_PAYMENT_COD_ORDER_STATUS_ID', '0', 'Set the status of orders made with this payment module to this value', '6', '0', NULL, '2018-06-13 10:20:44', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
('1611', 'Sort order of display.', 'MODULE_PAYMENT_COD_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-06-13 10:20:44', NULL, NULL),
('1610', 'Payment Zone', 'MODULE_PAYMENT_COD_ZONE', '0', 'If a zone is selected, only enable this payment method for that zone.', '6', '2', NULL, '2018-06-13 10:20:44', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes('),
('1609', 'Enable Cash On Delivery Module', 'MODULE_PAYMENT_COD_STATUS', 'True', 'Do you want to accept Cash On Delevery payments?', '6', '1', NULL, '2018-06-13 10:20:44', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1231', 'Enable banner sql Module', 'MODULE_HEADER_TAGS_BANNERS_QRY_STATUS', 'True', '', '6', '1', NULL, '2016-10-24 11:04:50', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1232', 'Sort Order', 'MODULE_HEADER_TAGS_BANNERS_QRY_SORT_ORDER', '1', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-10-24 11:04:50', NULL, NULL),
('1264', 'Module Version', 'MODULE_HEADER_TAGS_DIV_EQUAL_HEIGHTS_VERSION', '1.0', 'The version of this module that you are running', '6', '0', NULL, '2016-11-30 14:47:42', NULL, 'tep_cfg_disabled('),
('1265', 'Enable New Equal Heights Module', 'MODULE_HEADER_TAGS_DIV_EQUAL_HEIGHTS_STATUS', 'True', 'Do you want to enable the New Equal Heights module?', '6', '1', NULL, '2016-11-30 14:47:42', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('715', 'Store Logo', 'STORE_LOGO', 'store_logo_3.png', 'This is the filename of your Store Logo.  This should be updated at admin > configuration > Store Logo', '6', '1000', NULL, '2016-07-05 10:04:47', NULL, NULL),
('1293', 'Reset SEO URLs Cache', 'SEO_URLS_CACHE_RESET', 'false', 'This will reset the cache data for SEO', '905', '25', '2020-01-22 16:38:31', '2016-11-30 15:18:16', 'tep_reset_cache_data_seo_urls', 'tep_cfg_select_option(array(\'reset\', \'false\'),'),
('1292', 'Remove all non-alphanumeric characters?', 'SEO_REMOVE_ALL_SPEC_CHARS', 'false', 'This will remove all non-letters and non-numbers.  This should be handy to remove all special characters with 1 setting.', '905', '24', '2016-12-01 10:29:07', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('718', 'Store Phone', 'STORE_PHONE', '+44 (01642) 677582', 'This is the phone number of my store used on printable documents and displayed online', '1', '19', '2016-08-19 16:36:31', '2016-07-05 10:08:13', NULL, 'tep_cfg_textarea('),
('719', 'Store Logo', 'STORE_LOGO', 'store_logo_3.png', 'This is the filename of your Store Logo.  This should be updated at admin > configuration > Store Logo', '6', '1000', NULL, '2016-07-05 10:08:13', NULL, NULL),
('720', 'Bootstrap Container', 'BOOTSTRAP_CONTAINER', 'container', 'What type of container should the page content be shown in? See http://getbootstrap.com/css/#overview-container', '904', '1', '2016-11-21 12:48:27', '2016-07-05 10:08:13', NULL, 'tep_cfg_select_option(array(\'container-fluid\', \'container\'), '),
('721', 'Bootstrap Content', 'BOOTSTRAP_CONTENT', '8', 'What width should the page content default to?  (8 = two thirds width, 6 = half width, 4 = one third width) Note that the Side Column(s) will adjust automatically.', '904', '2', NULL, '2016-07-05 10:08:13', NULL, 'tep_cfg_select_option(array(\'8\', \'6\', \'4\'), '),
('1623', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_COLORBOX_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-07-04 14:38:44', NULL, NULL),
('723', 'Installed Modules', 'MODULE_CONTENT_NAVBAR_INSTALLED', 'nb_sidebar_toggle.php;nb_social_links.php;nb_home.php;nb_account.php;nb_shopping_cart.php', 'List of navbar module filenames separated by a semi-colon. This is automatically updated. No need to edit.', '6', '0', '2017-12-08 16:50:05', '2016-07-05 10:09:34', NULL, NULL),
('1340', 'Number of characters to display in each RSS article.', 'NEWS_RSS_CHARACTERS', '250', 'If you keep this at 250 it will hide a little bit of each of article from your viewers. They will have to come to your store to finish.  The default is 250', '367', '7', NULL, '2017-02-10 11:01:34', NULL, NULL),
('1339', 'Number of articles to display in your RSS Feed.', 'NEWS_RSS_ARTICLE', '10', 'If you want all of your articles to display in the RSS type in something like 2000.  The default is 10', '367', '6', NULL, '2017-02-10 11:01:34', NULL, NULL),
('730', 'Enable Shopping Cart Module', 'MODULE_NAVBAR_SHOPPING_CART_STATUS', 'True', 'Do you want to add the module to your Navbar?', '6', '1', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('731', 'Content Placement', 'MODULE_NAVBAR_SHOPPING_CART_CONTENT_PLACEMENT', 'Right', 'Should the module be loaded in the Left or Right or the Home area of the Navbar?', '6', '1', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'Left\', \'Right\', \'Home\'), '),
('732', 'Sort Order', 'MODULE_NAVBAR_SHOPPING_CART_SORT_ORDER', '550', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:09:34', NULL, NULL),
('733', 'Enable Navbar Module', 'MODULE_CONTENT_NAVBAR_STATUS', 'True', 'Should the Navbar be shown? ', '6', '1', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('734', 'Navbar Style', 'MODULE_CONTENT_NAVBAR_STYLE', 'Default', 'What style should the Navbar have?  See http://getbootstrap.com/components/#navbar-inverted', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'Default\', \'Inverse\'), '),
('735', 'Navbar Corners', 'MODULE_CONTENT_NAVBAR_CORNERS', 'Yes', 'Should the Navbar have Corners?', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'Yes\', \'No\'), '),
('736', 'Navbar Margin', 'MODULE_CONTENT_NAVBAR_MARGIN', 'No', 'Should the Navbar have a bottom Margin?', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'Yes\', \'No\'), '),
('737', 'Sort Order', 'MODULE_CONTENT_NAVBAR_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:09:34', NULL, NULL),
('738', 'Enable Header Logo Module', 'MODULE_CONTENT_HEADER_LOGO_STATUS', 'True', 'Do you want to enable the Logo content module?', '6', '1', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('739', 'Content Width', 'MODULE_CONTENT_HEADER_LOGO_CONTENT_WIDTH', '4', 'What width container should the content be shown in?', '6', '1', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('740', 'Sort Order', 'MODULE_CONTENT_HEADER_LOGO_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:09:34', NULL, NULL),
('947', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL10_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:04:16', NULL, NULL),
('946', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL10_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:04:16', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('744', 'Enable Message Stack Notifications Module', 'MODULE_CONTENT_HEADER_MESSAGESTACK_STATUS', 'True', 'Should the Message Stack Notifications be shown in the header when needed? ', '6', '1', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('745', 'Sort Order', 'MODULE_CONTENT_HEADER_MESSAGESTACK_SORT_ORDER', '90', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:09:34', NULL, NULL),
('1113', 'Content Width', 'MODULE_CONTENT_HEADER_BANNER2_CONTENT_WIDTH', '2', 'What width container should the content be shown in?', '6', '1', NULL, '2016-09-15 12:01:52', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1112', 'Enable Header Banner Module', 'MODULE_CONTENT_HEADER_BANNER2_STATUS', 'True', 'Do you want to enable the Logo content module?', '6', '1', NULL, '2016-09-15 12:01:52', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1111', 'Sort Order', 'MODULE_CONTENT_HEADER_BANNER1_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-09-15 12:01:45', NULL, NULL),
('1287', 'Enable automatic redirects?', 'USE_SEO_REDIRECT', 'true', 'This will activate the automatic redirect code and send 301 headers for old to new URLs.', '905', '19', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1285', 'Enable Pollbooth cache?', 'USE_SEO_CACHE_POLLBOOTH', 'false', 'This will turn off caching for Pollbooth.', '905', '17', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1286', 'Enable Page Editor cache?', 'USE_SEO_CACHE_PAGE_EDITOR', 'false', 'This will turn off caching for the Page Editor pages.', '905', '18', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1275', 'Enable product cache?', 'USE_SEO_CACHE_PRODUCTS', 'true', 'This will turn off caching for the products.', '905', '7', '2016-12-01 11:07:52', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1274', 'Enable SEO cache to save queries?', 'USE_SEO_CACHE_GLOBAL', 'false', 'This is a global setting and will turn off caching completely.', '905', '6', '2016-12-01 15:40:57', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1269', 'Add cPath to product URLs?', 'SEO_ADD_CID_TO_PRODUCT_URLS', 'false', 'This setting will append the cPath to the end of product URLs (i.e. - some-product-p-1.html?cPath=xx).', '905', '1', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1270', 'Add category parent to product URLs?', 'SEO_ADD_CPATH_TO_PRODUCT_URLS', 'true', 'This setting will append the category parent(s) name to the product URLs (i.e. - parent-some-product-p-1.html).', '905', '2', '2016-12-01 16:10:22', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1271', 'Add category parent to begining of URLs?', 'SEO_ADD_CAT_PARENT', 'true', 'This setting will add the category parent(s) name to the beginning of the category URLs (i.e. - parent-category-c-1.html).', '905', '3', '2016-12-01 16:10:28', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1272', 'Filter Short Words', 'SEO_URLS_FILTER_SHORT_WORDS', '2', 'This setting will filter words less than or equal to the value from the URL.', '905', '4', '2016-12-19 10:34:24', '2016-11-30 15:18:16', NULL, NULL),
('1273', 'Output W3C valid URLs (parameter string)?', 'SEO_URLS_USE_W3C_VALID', 'false', 'This setting will output W3C valid URLs.', '905', '5', '2016-12-01 15:18:17', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('760', 'SEO Title Override?', 'MODULE_HEADER_TAGS_MANUFACTURER_TITLE_SEO_TITLE_OVERRIDE', 'True', 'Do you want to allow manufacturer names to be over-ridden by your SEO Titles (if set)?', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('761', 'SEO Breadcrumb Override?', 'MODULE_HEADER_TAGS_MANUFACTURER_TITLE_SEO_BREADCRUMB_OVERRIDE', 'True', 'Do you want to allow manufacturer names in the breadcrumb to be over-ridden by your SEO Titles (if set)?', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('762', 'SEO Title Override?', 'MODULE_HEADER_TAGS_PRODUCT_TITLE_SEO_TITLE_OVERRIDE', 'True', 'Do you want to allow product titles to be over-ridden by your SEO Titles (if set)?', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('763', 'SEO Breadcrumb Override?', 'MODULE_HEADER_TAGS_PRODUCT_TITLE_SEO_BREADCRUMB_OVERRIDE', 'False', 'Do you want to allow product names in the breadcrumb to be over-ridden by your SEO Titles (if set)?', '6', '0', NULL, '2016-07-05 10:09:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('764', 'Installed Modules', 'MODULE_BOXES_INSTALLED', 'bm_login.php;bm_shopping_cart.php;bm_sidebarBannerR10.php;bm_sidebarBannerR1.php;bm_sidebarBannerR3.php;bm_sidebarBannerR4.php;bm_sidebarBannerR5.php;bm_sidebarBannerR6.php;bm_sidebarBannerR7.php;bm_sidebarBannerR8.php;bm_sidebarBannerR9.php;bm_categories_superfish.php;bm_order_history.php;bm_search.php;bm_sidebarBannerL1.php;bm_sidebarBannerL10.php;bm_sidebarBannerL2.php;bm_sidebarBannerL3.php;bm_sidebarBannerL4.php;bm_sidebarBannerL5.php;bm_sidebarBannerL6.php;bm_sidebarBannerL7.php;bm_sidebarBannerL8.php;bm_sidebarBannerL9.php;bm_sidebarBannerR2.php', 'This is automatically updated. No need to edit.', '6', '0', '2019-04-17 12:19:49', '2016-07-05 10:10:18', NULL, NULL),
('765', 'Installed Template Block Groups', 'TEMPLATE_BLOCK_GROUPS', 'boxes;header_tags', 'This is automatically updated. No need to edit.', '6', '0', '2016-07-05 10:31:29', '2016-07-05 10:10:18', NULL, NULL),
('1079', 'Enable Special Products Module', 'MODULE_CONTENT_SPECIAL_PRODUCTS_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2016-08-19 16:25:09', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1282', 'Enable Links Manager cache?', 'USE_SEO_CACHE_LINKS', 'false', 'This will turn off caching for the Links Manager category pages.', '905', '14', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1283', 'Enable NewsDesk Articles cache?', 'USE_SEO_CACHE_NEWSDESK_ARTICLES', 'false', 'This will turn off caching for the NewsDesk Article pages.', '905', '15', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('775', 'Enable Shopping Cart Module', 'MODULE_BOXES_SHOPPING_CART_STATUS', 'False', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-05 10:12:20', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('776', 'Content Placement', 'MODULE_BOXES_SHOPPING_CART_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-05 10:12:20', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('777', 'Sort Order', 'MODULE_BOXES_SHOPPING_CART_SORT_ORDER', '2', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:12:20', NULL, NULL),
('778', 'Enable Order History Module', 'MODULE_BOXES_ORDER_HISTORY_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-05 10:12:38', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('779', 'Content Placement', 'MODULE_BOXES_ORDER_HISTORY_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-05 10:12:38', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('780', 'Sort Order', 'MODULE_BOXES_ORDER_HISTORY_SORT_ORDER', '1', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:12:38', NULL, NULL),
('781', 'Installed Modules', 'MODULE_CONTENT_INSTALLED', 'checkout_success/cm_cs_downloads;checkout_success/cm_cs_redirect_old_order;checkout_success/cm_cs_thank_you;footer_suffix/cm_footer_extra_banner;header/cm_header_infobanner;header/cm_header_logo;header/cm_header_banner1;header/cm_header_banner2;header/cm_header_banner3;header/cm_header_banner4;header/cm_header_messagestack;header/cm_header_breadcrumb;index/cm_i_new_products;index/cm_i_title;index/cm_i_customer_greeting;index/cm_i_special_products;index_nested/cm_in_title;index_nested/cm_in_category_description;index_nested/cm_in_category_listing;index_nested/cm_in_new_products;index_products/cm_ip_title;index_products/cm_ip_product_listing;login/cm_create_account_link;login/cm_login_form;navigation/cm_navbar;navigation/cm_infobanner;product_info/cm_pi_name;product_info/cm_pi_social_bookmarks;product_info/cm_pi_model;product_info/cm_pi_gtin;product_info/cm_pi_price;product_info/cm_pi_options_attributes;product_info/cm_pi_buy_button;product_info/cm_pi_gallery;product_info/cm_pi_video;product_info/cm_pi_description;product_info/cm_pi_moreinfo_button;product_info/cm_pi_products_variations_table;product_info/cm_pi_related_products;product_info_not_found/cm_pinf_message', 'This is automatically updated. No need to edit.', '6', '0', NULL, '2016-07-05 10:12:53', NULL, NULL),
('782', 'Enable Thank You Module', 'MODULE_CONTENT_CHECKOUT_SUCCESS_THANK_YOU_STATUS', 'True', 'Should the thank you block be shown on the checkout success page?', '6', '1', NULL, '2016-07-05 10:13:28', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('783', 'Sort Order', 'MODULE_CONTENT_CHECKOUT_SUCCESS_THANK_YOU_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:13:28', NULL, NULL),
('1261', 'Enable Banner Details Footer Module', 'MODULE_CONTENT_FOOTER_EXTRA_BANNER_STATUS', 'True', 'Do you want to enable the Banner content module?', '6', '1', NULL, '2016-11-30 10:23:31', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1281', 'Enable Information Pages cache?', 'USE_SEO_CACHE_INFO_PAGES', 'false', 'This will turn off caching for Information Pages.', '905', '13', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1258', 'Enable Login Module', 'MODULE_BOXES_LOGIN_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-11-28 16:24:35', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1259', 'Content Placement', 'MODULE_BOXES_LOGIN_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-11-28 16:24:35', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('1260', 'Sort Order', 'MODULE_BOXES_LOGIN_SORT_ORDER', '1', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-11-28 16:24:35', NULL, NULL),
('1276', 'Enable categories cache?', 'USE_SEO_CACHE_CATEGORIES', 'true', 'This will turn off caching for the categories.', '905', '8', '2016-12-01 11:07:59', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1277', 'Enable manufacturers cache?', 'USE_SEO_CACHE_MANUFACTURERS', 'true', 'This will turn off caching for the manufacturers.', '905', '9', '2016-12-01 11:08:06', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1278', 'Enable Articles Manager Articles cache?', 'USE_SEO_CACHE_ARTICLES', 'false', 'This will turn off caching for the Articles Manager articles.', '905', '10', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1279', 'Enable Articles Manager Topics cache?', 'USE_SEO_CACHE_TOPICS', 'false', 'This will turn off caching for the Articles Manager topics.', '905', '11', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1280', 'Enable FAQDesk Categories cache?', 'USE_SEO_CACHE_FAQDESK_CATEGORIES', 'false', 'This will turn off caching for the FAQDesk Category pages.', '905', '12', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1109', 'Enable Header Banner Module', 'MODULE_CONTENT_HEADER_BANNER1_STATUS', 'True', 'Do you want to enable the Logo content module?', '6', '1', NULL, '2016-09-15 12:01:45', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1110', 'Content Width', 'MODULE_CONTENT_HEADER_BANNER1_CONTENT_WIDTH', '2', 'What width container should the content be shown in?', '6', '1', NULL, '2016-09-15 12:01:45', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1106', 'Enable Header Breadcrumb Module', 'MODULE_CONTENT_HEADER_BREADCRUMB_STATUS', 'True', 'Do you want to enable the Breadcrumb content module?', '6', '1', NULL, '2016-09-13 15:45:29', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1107', 'Content Width', 'MODULE_CONTENT_HEADER_BREADCRUMB_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2016-09-13 15:45:29', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1108', 'Sort Order', 'MODULE_CONTENT_HEADER_BREADCRUMB_SORT_ORDER', '99', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-09-13 15:45:29', NULL, NULL),
('949', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL2_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:04:30', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('945', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL10_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:04:16', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('802', 'Enable Header Logo Module', 'MODULE_CONTENT_HEADER_LOGO_STATUS', 'True', 'Do you want to enable the Logo content module?', '6', '1', NULL, '2016-07-05 10:15:23', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('803', 'Content Width', 'MODULE_CONTENT_HEADER_LOGO_CONTENT_WIDTH', '4', 'What width container should the content be shown in?', '6', '1', NULL, '2016-07-05 10:15:23', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('804', 'Sort Order', 'MODULE_CONTENT_HEADER_LOGO_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:15:23', NULL, NULL),
('805', 'Enable Message Stack Notifications Module', 'MODULE_CONTENT_HEADER_MESSAGESTACK_STATUS', 'True', 'Should the Message Stack Notifications be shown in the header when needed? ', '6', '1', NULL, '2016-07-05 10:15:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('806', 'Sort Order', 'MODULE_CONTENT_HEADER_MESSAGESTACK_SORT_ORDER', '90', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:15:39', NULL, NULL),
('943', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL1_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:04:05', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('944', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL1_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:04:05', NULL, NULL),
('1284', 'Enable NewsDesk Categories cache?', 'USE_SEO_CACHE_NEWSDESK_CATEGORIES', 'false', 'This will turn off caching for the NewsDesk Category pages.', '905', '16', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('941', 'Installed Modules', 'MODULE_SOCIAL_BOOKMARKS_INSTALLED', 'sb_email.php;sb_facebook.php;sb_twitter.php', 'This is automatically updated. No need to edit.', '6', '0', '2017-11-15 15:53:13', '2016-07-05 16:15:48', NULL, NULL),
('810', 'Enable Featured Products Module', 'MODULE_CONTENT_CUSTOMER_GREETING_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2016-07-05 10:15:55', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('811', 'Content Width', 'MODULE_CONTENT_CUSTOMER_GREETING_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '1', NULL, '2016-07-05 10:15:55', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('812', 'Sort Order', 'MODULE_CONTENT_CUSTOMER_GREETING_SORT_ORDER', '100', 'Sort order of display. Lowest is displayed first.', '6', '4', NULL, '2016-07-05 10:15:55', NULL, NULL),
('1074', 'Enable New Products Module', 'MODULE_CONTENT_IN_NEW_PRODUCTS_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2016-08-19 16:24:55', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1651', 'English Title', 'MODULE_BOXES_CATEGORIES_SUPERFISH_FRONT_TITLE_ENGLISH', '', 'Enter the title that you want as the header in english. Leave this blank for no header or title.', '6', '10', NULL, '2019-04-15 12:15:40', NULL, NULL),
('816', 'Enable Category Description Module', 'MODULE_CONTENT_IN_CATEGORY_DESCRIPTION_STATUS', 'True', 'Should this module be enabled?', '6', '1', NULL, '2016-07-05 10:16:17', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('817', 'Content Width', 'MODULE_CONTENT_IN_CATEGORY_DESCRIPTION_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '3', NULL, '2016-07-05 10:16:17', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('818', 'Sort Order', 'MODULE_CONTENT_IN_CATEGORY_DESCRIPTION_SORT_ORDER', '100', 'Sort order of display. Lowest is displayed first.', '6', '2', NULL, '2016-07-05 10:16:17', NULL, NULL),
('819', 'Enable New User Module', 'MODULE_CONTENT_CREATE_ACCOUNT_LINK_STATUS', 'True', 'Do you want to enable the new user module?', '6', '1', NULL, '2016-07-05 10:17:25', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('820', 'Content Width', 'MODULE_CONTENT_CREATE_ACCOUNT_LINK_CONTENT_WIDTH', 'Half', 'Should the content be shown in a full or half width container?', '6', '1', NULL, '2016-07-05 10:17:25', NULL, 'tep_cfg_select_option(array(\'Full\', \'Half\'), '),
('821', 'Sort Order', 'MODULE_CONTENT_CREATE_ACCOUNT_LINK_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:17:25', NULL, NULL),
('942', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL1_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:04:05', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('822', 'Enable Login Form Module', 'MODULE_CONTENT_LOGIN_FORM_STATUS', 'True', 'Do you want to enable the login form module?', '6', '1', NULL, '2016-07-05 10:17:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('823', 'Content Width', 'MODULE_CONTENT_LOGIN_FORM_CONTENT_WIDTH', 'Half', 'Should the content be shown in a full or half width container?', '6', '1', NULL, '2016-07-05 10:17:34', NULL, 'tep_cfg_select_option(array(\'Full\', \'Half\'), '),
('824', 'Sort Order', 'MODULE_CONTENT_LOGIN_FORM_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:17:34', NULL, NULL),
('825', 'Enable GTIN Module', 'MODULE_CONTENT_PRODUCT_INFO_GTIN_STATUS', 'False', 'Should this module be shown on the product info page?', '6', '1', NULL, '2016-07-05 10:17:51', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('826', 'Content Width', 'MODULE_CONTENT_PRODUCT_INFO_GTIN_CONTENT_WIDTH', '6', 'What width container should the content be shown in?', '6', '1', NULL, '2016-07-05 10:17:51', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('827', 'Length of GTIN', 'MODULE_CONTENT_PRODUCT_INFO_GTIN_LENGTH', '13', 'Length of GTIN. 14 (Industry Standard), 13 (eg ISBN codes and EAN UCC-13), 12 (UPC), 8 (EAN UCC-8)', '6', '0', NULL, '2016-07-05 10:17:51', NULL, 'tep_cfg_select_option(array(\'14\', \'13\', \'12\', \'8\'), '),
('828', 'Sort Order', 'MODULE_CONTENT_PRODUCT_INFO_GTIN_SORT_ORDER', '6', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:17:51', NULL, NULL),
('829', 'Enable Navbar Module', 'MODULE_CONTENT_NAVBAR_STATUS', 'True', 'Should the Navbar be shown? ', '6', '1', NULL, '2016-07-05 10:17:59', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('830', 'Navbar Style', 'MODULE_CONTENT_NAVBAR_STYLE', 'Default', 'What style should the Navbar have?  See http://getbootstrap.com/components/#navbar-inverted', '6', '0', NULL, '2016-07-05 10:17:59', NULL, 'tep_cfg_select_option(array(\'Default\', \'Inverse\'), '),
('831', 'Navbar Corners', 'MODULE_CONTENT_NAVBAR_CORNERS', 'Yes', 'Should the Navbar have Corners?', '6', '0', NULL, '2016-07-05 10:17:59', NULL, 'tep_cfg_select_option(array(\'Yes\', \'No\'), '),
('832', 'Navbar Margin', 'MODULE_CONTENT_NAVBAR_MARGIN', 'No', 'Should the Navbar have a bottom Margin?', '6', '0', NULL, '2016-07-05 10:17:59', NULL, 'tep_cfg_select_option(array(\'Yes\', \'No\'), '),
('833', 'Navbar Fixed Position', 'MODULE_CONTENT_NAVBAR_FIXED', 'Top', 'Should the Navbar stay fixed on Top/Bottom of the page or Floating?', '6', '0', NULL, '2016-07-05 10:17:59', NULL, 'tep_cfg_select_option(array(\'Floating\', \'Top\', \'Bottom\'), '),
('834', 'Sort Order', 'MODULE_CONTENT_NAVBAR_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:17:59', NULL, NULL),
('835', 'Enable Category Listing Module', 'MODULE_CONTENT_IN_CATEGORY_LISTING_STATUS', 'True', 'Should this module be enabled?', '6', '1', NULL, '2016-07-05 10:18:15', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('836', 'Content Width', 'MODULE_CONTENT_IN_CATEGORY_LISTING_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '2', NULL, '2016-07-05 10:18:15', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('837', 'Category Width', 'MODULE_CONTENT_IN_CATEGORY_LISTING_CONTENT_WIDTH_EACH', '3', 'What width container should each Category be shown in?', '6', '3', NULL, '2016-07-05 10:18:15', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('838', 'Sort Order', 'MODULE_CONTENT_IN_CATEGORY_LISTING_SORT_ORDER', '200', 'Sort order of display. Lowest is displayed first.', '6', '4', NULL, '2016-07-05 10:18:15', NULL, NULL),
('1263', 'Sort Order', 'MODULE_CONTENT_FOOTER_EXTRA_BANNER_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-11-30 10:23:31', NULL, NULL),
('1262', 'Content Width', 'MODULE_CONTENT_FOOTER_EXTRA_BANNER_CONTENT_WIDTH', '6', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '1', NULL, '2016-11-30 10:23:31', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1294', 'Uninstall Ultimate SEO', 'SEO_URLS_DB_UNINSTALL', 'false', 'This will delete all of the entries in the configuration table for SEO', '905', '26', '2016-11-30 15:18:16', '2016-11-30 15:18:16', 'tep_reset_cache_data_seo_urls', 'tep_cfg_select_option(array(\'uninstall\', \'false\'),'),
('845', 'Enable Redirect Old Order Module', 'MODULE_CONTENT_CHECKOUT_SUCCESS_REDIRECT_OLD_ORDER_STATUS', 'True', 'Should customers be redirected when viewing old checkout success orders?', '6', '1', NULL, '2016-07-05 10:18:42', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('846', 'Redirect Minutes', 'MODULE_CONTENT_CHECKOUT_SUCCESS_REDIRECT_OLD_ORDER_MINUTES', '60', 'Redirect customers to the My Account page after an order older than this amount is viewed.', '6', '0', NULL, '2016-07-05 10:18:42', NULL, NULL),
('847', 'Sort Order', 'MODULE_CONTENT_CHECKOUT_SUCCESS_REDIRECT_OLD_ORDER_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:18:42', NULL, NULL),
('948', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL2_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:04:30', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('848', 'Enable Product Downloads Module', 'MODULE_CONTENT_CHECKOUT_SUCCESS_DOWNLOADS_STATUS', 'False', 'Should ordered product download links be shown on the checkout success page?', '6', '1', NULL, '2016-07-05 10:19:28', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('849', 'Sort Order', 'MODULE_CONTENT_CHECKOUT_SUCCESS_DOWNLOADS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '3', NULL, '2016-07-05 10:19:28', NULL, NULL),
('850', 'Installed Modules', 'MODULE_ADMIN_DASHBOARD_INSTALLED', 'd_customers_carts.php;d_orders.php', 'This is automatically updated. No need to edit.', '6', '0', '2024-07-08 10:20:26', '2016-07-05 10:19:57', NULL, NULL),
('1692', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:46:48', NULL, NULL),
('1693', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_STATUS', 'True', 'Do you want to allow product meta (facebook) pixels to be added to the page header?', '6', '1', NULL, '2025-01-21 10:49:59', NULL, NULL),
('853', 'Enable Orders Module', 'MODULE_ADMIN_DASHBOARD_ORDERS_STATUS', 'True', 'Do you want to show the latest orders on the dashboard?', '6', '1', NULL, '2016-07-05 10:20:14', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('854', 'Sort Order', 'MODULE_ADMIN_DASHBOARD_ORDERS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:20:14', NULL, NULL),
('1254', 'Use CKEditor', 'USE_CKEDITOR_ADMIN_TEXTAREA', 'true', 'Use CKEditor for WYSIWYG editing of textarea fields in admin', '1', '99', NULL, '2016-11-24 10:28:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1179', 'KissIT: Version', 'KISSIT_IMAGE_MODULE', '28', 'KISS Image Thumbnailer - Creates image thumbnails where the image size requested differs from the actual image size', '6', '0', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1252', 'Flat Rate', 'MODULE_SHIPPING_TCSSHIP_FLATRATE', '9', 'Flat Rate', '6', '0', NULL, '2016-10-28 16:16:13', NULL, NULL),
('1253', 'Min for Free ship', 'MODULE_SHIPPING_TCSSHIP_MINFREE', '200', 'what is the min amount required for free shipping', '6', '0', NULL, '2016-10-28 16:16:13', NULL, NULL),
('876', 'Pages', 'MODULE_HEADER_TAGS_DATEPICKER_JQUERY_PAGES', 'advanced_search.php;account_edit.php;create_account.php', 'The pages to add the Datepicker jQuery Scripts to.', '6', '0', NULL, '2016-07-05 10:48:48', 'ht_datepicker_jquery_show_pages', 'ht_datepicker_jquery_edit_pages('),
('874', 'Sort Order', 'MODULE_HEADER_TAGS_CANONICAL_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:48:29', NULL, NULL),
('875', 'Enable Datepicker jQuery Module', 'MODULE_HEADER_TAGS_DATEPICKER_JQUERY_STATUS', 'True', 'Do you want to enable the Datepicker module?', '6', '1', NULL, '2016-07-05 10:48:48', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('873', 'Enable Canonical Module', 'MODULE_HEADER_TAGS_CANONICAL_STATUS', 'True', 'Do you want to enable the Canonical module?', '6', '1', NULL, '2016-07-05 10:48:29', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('872', 'Installed Modules', 'MODULE_HEADER_TAGS_INSTALLED', 'ht_banners_qry.php;ht_canonical.php;ht_category_seo.php;ht_clickGuardian.php;ht_datepicker_jquery.php;ht_div_equal_heights.php;ht_google_adwords_conversion.php;ht_google_analytics.php;ht_grid_list_view.php;ht_manufacturer_seo.php;ht_opensearch.php;ht_pages_schema.php;ht_pages_seo.php;ht_product_attributes_class.php;ht_product_colorbox.php;ht_product_meta.php;ht_product_opengraph.php;ht_product_schema.php;ht_product_title.php;ht_robot_noindex.php;ht_table_click_jquery.php;ht_xmas_message_checkout.php', 'This is automatically updated. No need to edit.', '6', '0', '2025-01-21 10:53:42', '2016-07-05 10:31:29', NULL, NULL),
('877', 'Sort Order', 'MODULE_HEADER_TAGS_DATEPICKER_JQUERY_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:48:48', NULL, NULL),
('1268', 'Enable SEO URLs?', 'SEO_ENABLED', 'true', 'Enable the SEO URLs?  This is a global setting and will turn them off completely.', '905', '0', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1267', 'Sort Order', 'MODULE_HEADER_TAGS_DIV_EQUAL_HEIGHTS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '3', NULL, '2016-11-30 14:47:42', NULL, NULL),
('881', 'Enable Google AdWords Conversion Module', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_STATUS', 'True', 'Do you want to allow the Google AdWords Conversion Module on your checkout success page?', '6', '1', NULL, '2016-07-05 10:50:29', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('882', 'Conversion ID', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_ID', '', 'The Google AdWords Conversion ID', '6', '0', NULL, '2016-07-05 10:50:29', NULL, NULL),
('883', 'Tracking Notification Layout', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_FORMAT', '1', 'A small message will appear on your site telling customers that their visits on your site are being tracked. We recommend you use it.', '6', '0', NULL, '2016-07-05 10:50:29', 'tep_cfg_google_adwords_conversion_get_format', 'tep_cfg_google_adwords_conversion_set_format('),
('884', 'Page Background Color', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_COLOR', 'ffffff', 'Enter a HTML color to match the color of your website background page.', '6', '0', NULL, '2016-07-05 10:50:29', NULL, NULL),
('885', 'Conversion Label', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_LABEL', '', 'The alphanumeric code generated by Google for your AdWords Conversion', '6', '0', NULL, '2016-07-05 10:50:29', NULL, NULL),
('886', 'Javascript Placement', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_JS_PLACEMENT', 'Footer', 'Should the Google AdWords Conversion javascript be loaded in the header or footer?', '6', '1', NULL, '2016-07-05 10:50:29', NULL, 'tep_cfg_select_option(array(\'Header\', \'Footer\'), '),
('887', 'Sort Order', 'MODULE_HEADER_TAGS_GOOGLE_ADWORDS_CONVERSION_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:50:29', NULL, NULL),
('888', 'Enable Grid List javascript', 'MODULE_HEADER_TAGS_GRID_LIST_VIEW_STATUS', 'True', 'Do you want to enable the Grid/List Javascript module?', '6', '1', NULL, '2016-07-05 10:50:46', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('889', 'Pages', 'MODULE_HEADER_TAGS_GRID_LIST_VIEW_PAGES', 'advanced_search_result.php;index.php;products_new.php;specials.php', 'The pages to add the Grid List JS Scripts to.', '6', '4', NULL, '2016-07-05 10:50:46', 'ht_grid_list_view_show_pages', 'ht_grid_list_view_edit_pages('),
('890', 'Sort Order', 'MODULE_HEADER_TAGS_GRID_LIST_VIEW_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2016-07-05 10:50:46', NULL, NULL),
('1678', 'Sort Order', 'MODULE_HEADER_TAGS_GOOGLE_ANALYTICS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2023-07-31 15:55:25', NULL, NULL),
('1677', 'Javascript Placement', 'MODULE_HEADER_TAGS_GOOGLE_ANALYTICS_JS_PLACEMENT', 'Footer', 'Should the Google Analytics javascript be loaded in the header or footer?', '6', '1', NULL, '2023-07-31 15:55:25', NULL, 'tep_cfg_select_option(array(\'Header\', \'Footer\'), '),
('1676', 'E-Commerce Tracking', 'MODULE_HEADER_TAGS_GOOGLE_ANALYTICS_EC_TRACKING', 'True', 'Do you want to enable e-commerce tracking? (E-Commerce tracking must also be enabled in your Google Analytics profile settings)', '6', '1', NULL, '2023-07-31 15:55:25', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1675', 'Google Analytics ID', 'MODULE_HEADER_TAGS_GOOGLE_ANALYTICS_ID', 'UA-61730423-1', 'The Google Analytics profile ID to track.', '6', '0', NULL, '2023-07-31 15:55:25', NULL, NULL),
('896', 'Enable Pages SEO Module', 'MODULE_HEADER_TAGS_PAGES_SEO_STATUS', 'True', 'Do you want to allow this module to write SEO to your Pages?', '6', '1', NULL, '2016-07-05 10:51:33', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('897', 'Sort Order', 'MODULE_HEADER_TAGS_PAGES_SEO_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:51:33', NULL, NULL),
('1615', 'Sort Order', 'MODULE_CONTENT_PI_MOREINFO_SORT_ORDER', '90', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-06-27 10:20:03', NULL, NULL),
('1624', 'KissIT Use retina thumbs', 'KISSIT_USE_RETINA_IMAGES', 'true', 'Generate double resolution thumbs for retina devices.<br>Default: true.', '4', '35', NULL, '2018-08-03 12:05:17', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1614', 'Content Width', 'MODULE_CONTENT_PI_MOREINFO_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2018-06-27 10:20:03', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1613', 'Enable Review Button', 'MODULE_CONTENT_PI_MOREINFO_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-06-27 10:20:03', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('902', 'Enable Robot NoIndex Module', 'MODULE_HEADER_TAGS_ROBOT_NOINDEX_STATUS', 'True', 'Do you want to enable the Robot NoIndex module?', '6', '1', NULL, '2016-07-05 10:52:15', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('903', 'Pages', 'MODULE_HEADER_TAGS_ROBOT_NOINDEX_PAGES', 'account.php;account_edit.php;account_history.php;account_history_info.php;account_newsletters.php;account_notifications.php;account_password.php;address_book.php;address_book_process.php;checkout_confirmation.php;checkout_payment.php;checkout_payment_address.php;checkout_process.php;checkout_shipping.php;checkout_shipping_address.php;checkout_success.php;cookie_usage.php;create_account.php;create_account_success.php;login.php;logoff.php;password_forgotten.php;password_reset.php;product_reviews_write.php;shopping_cart.php;ssl_check.php;tell_a_friend.php', 'The pages to add the meta robot noindex tag to.', '6', '0', NULL, '2016-07-05 10:52:15', 'ht_robot_noindex_show_pages', 'ht_robot_noindex_edit_pages('),
('904', 'Sort Order', 'MODULE_HEADER_TAGS_ROBOT_NOINDEX_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:52:15', NULL, NULL),
('905', 'Enable Clickable Table Rows Module', 'MODULE_HEADER_TAGS_TABLE_CLICK_JQUERY_STATUS', 'True', 'Do you want to enable the Clickable Table Rows module?', '6', '1', NULL, '2016-07-05 10:52:43', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('906', 'Pages', 'MODULE_HEADER_TAGS_TABLE_CLICK_JQUERY_PAGES', 'checkout_shipping.php;checkout_payment.php', 'The pages to add the jQuery Scripts to.', '6', '0', NULL, '2016-07-05 10:52:43', 'ht_table_click_jquery_show_pages', 'ht_table_click_jquery_edit_pages('),
('907', 'Sort Order', 'MODULE_HEADER_TAGS_TABLE_CLICK_JQUERY_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:52:43', NULL, NULL),
('908', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_STATUS', 'True', 'Do you want to allow product meta tags to be added to the page header?', '6', '1', NULL, '2016-07-05 10:53:45', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('909', 'Enable Product Meta Module - Keywords', 'MODULE_HEADER_TAGS_PRODUCT_META_KEYWORDS_STATUS', 'Both', 'Keywords can be used for META, for SEARCH, or for BOTH.  If you are into the Chinese Market select Both (for Baidu Search Engine) otherwise select Search.', '6', '1', NULL, '2016-07-05 10:53:45', NULL, 'tep_cfg_select_option(array(\'Meta\', \'Search\', \'Both\'), '),
('910', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:53:45', NULL, NULL),
('911', 'Enable Product OpenGraph Module', 'MODULE_HEADER_TAGS_PRODUCT_OPENGRAPH_STATUS', 'True', 'Do you want to allow Open Graph Meta Tags (good for Facebook and Pinterest and other sites) to be added to your product page?  Note that your product thumbnails MUST be at least 200px by 200px.', '6', '1', NULL, '2016-07-05 10:54:02', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('912', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_OPENGRAPH_SORT_ORDER', '900', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:54:02', NULL, NULL),
('913', 'Enable Product Title Module', 'MODULE_HEADER_TAGS_PRODUCT_TITLE_STATUS', 'True', 'Do you want to allow product titles to be added to the page title?', '6', '1', NULL, '2016-07-05 10:54:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('914', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_TITLE_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 10:54:40', NULL, NULL),
('915', 'SEO Title Override?', 'MODULE_HEADER_TAGS_PRODUCT_TITLE_SEO_TITLE_OVERRIDE', 'True', 'Do you want to allow product titles to be over-ridden by your SEO Titles (if set)?', '6', '0', NULL, '2016-07-05 10:54:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('916', 'SEO Breadcrumb Override?', 'MODULE_HEADER_TAGS_PRODUCT_TITLE_SEO_BREADCRUMB_OVERRIDE', 'False', 'Do you want to allow product names in the breadcrumb to be over-ridden by your SEO Titles (if set)?', '6', '0', NULL, '2016-07-05 10:54:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('917', 'Enable Account Module', 'MODULE_NAVBAR_ACCOUNT_STATUS', 'True', 'Do you want to add the module to your Navbar?', '6', '1', NULL, '2016-07-05 11:06:56', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('918', 'Content Placement', 'MODULE_NAVBAR_ACCOUNT_CONTENT_PLACEMENT', 'Right', 'Should the module be loaded in the Left or Right or the Home area of the Navbar?', '6', '1', NULL, '2016-07-05 11:06:56', NULL, 'tep_cfg_select_option(array(\'Left\', \'Right\', \'Home\'), '),
('919', 'Sort Order', 'MODULE_NAVBAR_ACCOUNT_SORT_ORDER', '540', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 11:06:56', NULL, NULL),
('920', 'Enable Manufacturer Meta Module', 'MODULE_HEADER_TAGS_MANUFACTURERS_SEO_STATUS', 'True', 'Do you want to allow Manufacturer meta tags to be added to the page header?', '6', '1', NULL, '2016-07-05 11:07:17', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('921', 'Display Manufacturer Meta Description', 'MODULE_HEADER_TAGS_MANUFACTURERS_SEO_DESCRIPTION_STATUS', 'True', 'Manufacturer Descriptions help your site and your sites visitors.', '6', '1', NULL, '2016-07-05 11:07:17', NULL, 'tep_cfg_select_option(array(\'True\'), '),
('922', 'Display Manufacturer Meta Keywords', 'MODULE_HEADER_TAGS_MANUFACTURERS_SEO_KEYWORDS_STATUS', 'False', 'Manufacturer Keywords are almost pointless.  If you are into the Chinese Market select True (for Baidu Search Engine) otherwise select False.', '6', '1', NULL, '2016-07-05 11:07:17', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('923', 'Sort Order', 'MODULE_HEADER_TAGS_MANUFACTURERS_SEO_SORT_ORDER', '110', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 11:07:17', NULL, NULL),
('924', 'Enable OpenSearch Module', 'MODULE_HEADER_TAGS_OPENSEARCH_STATUS', 'True', 'Add shop search functionality to the browser?', '6', '1', NULL, '2016-07-05 11:07:30', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('925', 'Short Name', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_SHORT_NAME', 'TCS CAD and BIM Solutions', 'Short name to describe the search engine.', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('926', 'Description', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_DESCRIPTION', 'Search TCS CAD and BIM Solutions', 'Description of the search engine.', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('927', 'Contact', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_CONTACT', '<EMAIL>', 'E-Mail address of the search engine maintainer. (optional)', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('928', 'Tags', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_TAGS', '', 'Keywords to identify and categorize the search content, separated by an empty space. (optional)', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('929', 'Attribution', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_ATTRIBUTION', '', 'Attribution for the search content. (optional)', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('0', 'Require users to login to reply?', 'NEWS_REPLY', 'true', 'This makes users create an account at your store to reply to prevent spam.', '367', '1', '0000-00-00 00:00:00', '0000-00-00 00:00:00', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('930', 'Adult Content', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_ADULT_CONTENT', 'False', 'Search content contains material suitable only for adults.', '6', '0', NULL, '2016-07-05 11:07:30', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('931', '16x16 Icon', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_ICON', 'http://cadservices.co.uk/favicon.jpg', 'A 16x16 sized icon (must be in .ico format, eg http://server/favicon.ico). (optional)', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('932', '64x64 Image', 'MODULE_HEADER_TAGS_OPENSEARCH_SITE_IMAGE', 'http://cadservices.co.uk/images/tcslogo64.png', 'A 64x64 sized image (must be in .png format, eg http://server/images/logo.png). (optional)', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('933', 'Sort Order', 'MODULE_HEADER_TAGS_OPENSEARCH_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 11:07:30', NULL, NULL),
('934', 'Enable Category Meta Module', 'MODULE_HEADER_TAGS_CATEGORY_SEO_STATUS', 'True', 'Do you want to allow Category Meta Tags to be added to the page header?', '6', '1', NULL, '2016-07-05 11:08:00', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('935', 'Display Category Meta Description', 'MODULE_HEADER_TAGS_CATEGORY_SEO_DESCRIPTION_STATUS', 'True', 'These help your site and your sites visitors.', '6', '0', NULL, '2016-07-05 11:08:00', NULL, 'tep_cfg_select_option(array(\'True\'), '),
('936', 'Display Category Meta Keywords', 'MODULE_HEADER_TAGS_CATEGORY_SEO_KEYWORDS_STATUS', 'True', 'These are almost pointless.  If you are into the Chinese Market select True (for Baidu Search Engine) otherwise select False.', '6', '0', NULL, '2016-07-05 11:08:00', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('937', 'Sort Order', 'MODULE_HEADER_TAGS_CATEGORY_SEO_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-05 11:08:00', NULL, NULL),
('1266', 'Pages', 'MODULE_HEADER_TAGS_DIV_EQUAL_HEIGHTS_PAGES', 'advanced_search_result.php;index.php;products_new.php;specials.php', 'The pages to add the script to.', '6', '2', NULL, '2016-11-30 14:47:42', 'ht_div_equal_heights_show_pages', 'ht_div_equal_heights_edit_pages('),
('950', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL2_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:04:30', NULL, NULL),
('951', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL3_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:04:51', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('952', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL3_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:04:51', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('953', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL3_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:04:51', NULL, NULL),
('954', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL4_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:05:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('955', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL4_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:05:13', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('956', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL4_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:05:13', NULL, NULL),
('957', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL5_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:05:21', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('958', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL5_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:05:21', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('959', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL5_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:05:21', NULL, NULL),
('960', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL6_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:05:35', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('961', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL6_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:05:35', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('962', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL6_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:05:35', NULL, NULL),
('963', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL7_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:06:09', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('964', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL7_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:06:09', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('965', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL7_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:06:09', NULL, NULL),
('966', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL8_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:06:19', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('967', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL8_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:06:19', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('968', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL8_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:06:19', NULL, NULL),
('969', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERL9_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:06:29', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('970', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERL9_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:06:29', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('971', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERL9_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:06:29', NULL, NULL),
('972', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR1_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:06:42', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('973', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR1_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:06:42', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('974', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR1_SORT_ORDER', '11', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:06:42', NULL, NULL),
('975', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR10_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:06:56', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('976', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR10_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:06:56', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('977', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR10_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:06:56', NULL, NULL),
('978', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR2_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:07:09', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('979', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR2_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:07:09', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('980', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR2_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:07:09', NULL, NULL),
('981', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR3_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:07:25', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('982', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR3_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:07:25', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('983', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR3_SORT_ORDER', '12', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:07:25', NULL, NULL),
('984', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR4_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:07:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('985', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR4_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:07:40', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('986', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR4_SORT_ORDER', '13', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:07:40', NULL, NULL),
('987', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR6_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:07:52', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('988', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR6_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:07:52', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('989', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR6_SORT_ORDER', '15', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:07:52', NULL, NULL),
('990', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR5_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:08:04', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('991', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR5_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:08:04', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('992', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR5_SORT_ORDER', '14', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:08:04', NULL, NULL),
('993', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR7_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:08:18', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('994', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR7_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:08:18', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('995', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR7_SORT_ORDER', '16', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:08:18', NULL, NULL),
('996', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR8_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:09:14', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('997', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR8_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:09:14', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('998', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR8_SORT_ORDER', '17', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:09:14', NULL, NULL),
('999', 'Enable Information Module', 'MODULE_BOXES_SIDEBARBANNERR9_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2016-07-13 10:09:31', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1000', 'Content Placement', 'MODULE_BOXES_SIDEBARBANNERR9_CONTENT_PLACEMENT', 'Right Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2016-07-13 10:09:31', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('1001', 'Sort Order', 'MODULE_BOXES_SIDEBARBANNERR9_SORT_ORDER', '18', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-07-13 10:09:31', NULL, NULL),
('1290', 'Choose URL Rewrite Type', 'SEO_REWRITE_TYPE', 'Rewrite', 'Choose which SEO URL format to use.', '905', '22', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'Rewrite\'),'),
('1011', 'Sort Order', 'SPECIAL', 'desc', 'This is the sort order used in the output.', '1', '4', NULL, '2016-07-15 11:53:42', NULL, 'tep_cfg_select_option(array(\'asc\', \'desc\'), '),
('1005', 'Sort Order', 'SPECIAL', 'desc', 'This is the sort order used in the output.', '1', '4', NULL, '2016-07-15 11:41:05', NULL, 'tep_cfg_select_option(array(\'asc\', \'desc\'), '),
('1045', 'Content Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2016-07-15 12:12:13', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1046', 'Maximum Display', 'MODULE_CONTENT_SPECIAL_PRODUCTS_MAX_DISPLAY', '6', 'Maximum Number of products that should show in this module?', '6', '3', NULL, '2016-07-15 12:12:13', NULL, NULL),
('1047', 'Product Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_DISPLAY_EACH', '4', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2016-07-15 12:12:13', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1075', 'Content Width', 'MODULE_CONTENT_IN_NEW_PRODUCTS_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2016-08-19 16:24:55', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1076', 'Maximum Display', 'MODULE_CONTENT_IN_NEW_PRODUCTS_MAX_DISPLAY', '6', 'Maximum Number of products that should show in this module?', '6', '3', NULL, '2016-08-19 16:24:55', NULL, NULL),
('1077', 'Product Width', 'MODULE_CONTENT_IN_NEW_PRODUCTS_DISPLAY_EACH', '3', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2016-08-19 16:24:55', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1078', 'Sort Order', 'MODULE_CONTENT_IN_NEW_PRODUCTS_SORT_ORDER', '300', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2016-08-19 16:24:55', NULL, NULL),
('1650', 'Content Placement', 'MODULE_BOXES_CATEGORIES_SUPERFISH_CONTENT_PLACEMENT', 'Left Column', 'Where should the module be loaded?', '6', '2', NULL, '2019-04-15 12:15:40', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('1048', 'Sort Order', 'MODULE_CONTENT_SPECIAL_PRODUCTS_SORT_ORDER', '300', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2016-07-15 12:12:13', NULL, NULL),
('1044', 'Enable Special Products Module', 'MODULE_CONTENT_SPECIAL_PRODUCTS_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2016-07-15 12:12:13', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1648', 'Sort Order', 'MODULE_BOXES_CATEGORIES_SUPERFISH_SORT_ORDER', '1002', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2019-04-15 12:15:40', NULL, NULL),
('1649', 'Enable Superfish Categories Box', 'MODULE_BOXES_CATEGORIES_SUPERFISH_STATUS', 'True', 'Do you want to show the Superfish Categories box?', '6', '1', NULL, '2019-04-15 12:15:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1080', 'Content Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2016-08-19 16:25:09', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1081', 'Maximum Display', 'MODULE_CONTENT_SPECIAL_PRODUCTS_MAX_DISPLAY', '6', 'Maximum Number of products that should show in this module?', '6', '3', NULL, '2016-08-19 16:25:09', NULL, NULL),
('1082', 'Product Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_DISPLAY_EACH', '4', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2016-08-19 16:25:09', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1083', 'Sort Order', 'MODULE_CONTENT_SPECIAL_PRODUCTS_SORT_ORDER', '300', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2016-08-19 16:25:09', NULL, NULL),
('1084', 'Enable Special Products Module', 'MODULE_CONTENT_SPECIAL_PRODUCTS_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2016-08-19 16:26:07', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1085', 'Content Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2016-08-19 16:26:07', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1086', 'Maximum Display', 'MODULE_CONTENT_SPECIAL_PRODUCTS_MAX_DISPLAY', '6', 'Maximum Number of products that should show in this module?', '6', '3', NULL, '2016-08-19 16:26:07', NULL, NULL),
('1087', 'Product Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_DISPLAY_EACH', '4', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2016-08-19 16:26:07', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1088', 'Sort Order', 'MODULE_CONTENT_SPECIAL_PRODUCTS_SORT_ORDER', '300', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2016-08-19 16:26:07', NULL, NULL),
('1089', 'Enable Special Products Module', 'MODULE_CONTENT_SPECIAL_PRODUCTS_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2016-08-19 16:27:29', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1090', 'Content Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2016-08-19 16:27:29', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1091', 'Maximum Display', 'MODULE_CONTENT_SPECIAL_PRODUCTS_MAX_DISPLAY', '6', 'Maximum Number of products that should show in this module?', '6', '3', NULL, '2016-08-19 16:27:29', NULL, NULL),
('1092', 'Product Width', 'MODULE_CONTENT_SPECIAL_PRODUCTS_DISPLAY_EACH', '4', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2016-08-19 16:27:29', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1093', 'Sort Order', 'MODULE_CONTENT_SPECIAL_PRODUCTS_SORT_ORDER', '300', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2016-08-19 16:27:29', NULL, NULL),
('1289', 'Enable performance checker?', 'USE_SEO_PERFORMANCE_CHECK', 'false', 'This will cause the code to track all database queries so that its affect on the speed of the page can be determined. Enabling it will cause a small speed loss.', '905', '21', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1288', 'Enable use Header Tags SEO as name?', 'USE_SEO_HEADER_TAGS', 'false', 'This will cause the title set in Header Tags SEO to be used instead of the categories or products name.', '905', '20', '2016-12-01 16:06:11', '2016-11-30 15:18:16', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),'),
('1119', 'Content Width', 'MODULE_CONTENT_HEADER_BANNER4_CONTENT_WIDTH', '2', 'What width container should the content be shown in?', '6', '1', NULL, '2016-09-15 12:56:00', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1117', 'Sort Order', 'MODULE_CONTENT_HEADER_BANNER3_SORT_ORDER', '30', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-09-15 12:01:58', NULL, NULL),
('1118', 'Enable Header Banner Module', 'MODULE_CONTENT_HEADER_BANNER4_STATUS', 'True', 'Do you want to enable the Logo content module?', '6', '1', NULL, '2016-09-15 12:56:00', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1116', 'Content Width', 'MODULE_CONTENT_HEADER_BANNER3_CONTENT_WIDTH', '2', 'What width container should the content be shown in?', '6', '1', NULL, '2016-09-15 12:01:58', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1114', 'Sort Order', 'MODULE_CONTENT_HEADER_BANNER2_SORT_ORDER', '20', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-09-15 12:01:52', NULL, NULL),
('1115', 'Enable Header Banner Module', 'MODULE_CONTENT_HEADER_BANNER3_STATUS', 'True', 'Do you want to enable the Logo content module?', '6', '1', NULL, '2016-09-15 12:01:58', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1120', 'Sort Order', 'MODULE_CONTENT_HEADER_BANNER4_SORT_ORDER', '40', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-09-15 12:56:00', NULL, NULL),
('1291', 'Enter special character conversions', 'SEO_CHAR_CONVERT_SET', '', 'This setting will convert characters.<br><br>The format <b>MUST</b> be in the form: <b>char=>conv,char2=>conv2</b>', '905', '23', '2016-11-30 15:18:16', '2016-11-30 15:18:16', NULL, NULL),
('1322', 'Security Check Extended Last Run', 'MODULE_SECURITY_CHECK_EXTENDED_LAST_RUN_DATETIME', '1488557780', 'The date and time the last extended security check was performed.', '6', NULL, NULL, '2017-01-18 10:39:09', NULL, NULL),
('1136', 'Enable Home Module', 'MODULE_NAVBAR_HOME_STATUS', 'True', 'Do you want to add the module to your Navbar?', '6', '1', NULL, '2016-09-27 11:19:48', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1137', 'Content Placement', 'MODULE_NAVBAR_HOME_CONTENT_PLACEMENT', 'Left', 'Should the module be loaded in the Left or Right or the Home area of the Navbar?', '6', '1', NULL, '2016-09-27 11:19:48', NULL, 'tep_cfg_select_option(array(\'Left\', \'Right\', \'Home\'), '),
('1138', 'Sort Order', 'MODULE_NAVBAR_HOME_SORT_ORDER', '520', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2016-09-27 11:19:48', NULL, NULL),
('1384', 'Enable E-Mail Module', 'MODULE_SOCIAL_BOOKMARKS_EMAIL_STATUS', 'True', 'Do you want to allow products to be shared through e-mail?', '6', '1', NULL, '2017-11-15 15:52:41', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1354', 'Secure IP', 'GOOGLE_XML_SITEMAP_SECURE_IP', '127.0.0.1', 'Enter the IP that you want to allow to run the script manually.- <b>Manual mode only</b>', '907', '40', NULL, '2017-08-03 15:01:38', NULL, NULL),
('1352', 'Manual IP', 'GOOGLE_XML_SITEMAP_MANUAL_IP', '127.0.0.1', 'Enter the IP that you want to cause the manual run to happen. Separate muliple IP\'s with a comma.- <b>Manual mode only</b>', '907', '30', NULL, '2017-08-03 15:01:38', NULL, NULL),
('1353', 'Mobile URL', 'GOOGLE_XML_SITEMAP_MOBILE_URL', '', 'Enter the full url to the mobile site, if different from the main site.', '907', '35', NULL, '2017-08-03 15:01:38', NULL, NULL),
('1351', 'Manual Run', 'GOOGLE_XML_SITEMAP_MANUAL_RUN', '7', 'Enter the number of days to wait before the script is updated. - <b>Manual mode only</b>', '907', '25', NULL, '2017-08-03 15:01:38', NULL, NULL),
('1350', 'Exclude these pages', 'GOOGLE_XML_SITEMAP_EXCLUDE_PAGES', 'advanced_search_result.php,advanced_search.php,password_reset.php', 'Add these pages to the built-in file exclude list. This will prevent the links from being added to the pages site map. Separate each name with a comma.', '907', '20', NULL, '2017-08-03 15:01:38', NULL, 'tep_cfg_textarea('),
('1349', 'Enable Diagnostic Output', 'GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC', 'false', 'Set to true if you would like debug information displayed. This is useful if the site maps are not being created correctly.<br>(true=on false=off)', '907', '15', NULL, '2017-08-03 15:01:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1348', 'Enable Article Manager Topics Map', 'GOOGLE_XML_SITEMAP_CREATE_TOPICS', 'false', 'Set to true if you would like a site map created for your Article Manager topics (Article manager must be installed to use this option).<br>(true=on false=off)', '907', '12', NULL, '2017-08-03 15:01:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1347', 'Enable Standard Pages Map', 'GOOGLE_XML_SITEMAP_CREATE_PAGES', 'true', 'Set to true if you would like a site map created for your standard pages.<br>(true=on false=off)', '907', '10', '2017-08-03 15:02:42', '2017-08-03 15:01:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1344', 'Enable Images Map', 'GOOGLE_XML_SITEMAP_CREATE_IMAGES', 'false', 'Set to true if you would like a site map created for your images.<br>(true=on false=off)', '907', '3', NULL, '2017-08-03 15:01:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1345', 'Enable Manufacturers Map', 'GOOGLE_XML_SITEMAP_CREATE_MANU', 'true', 'Set to true if you would like a site map created for your manufactureres.<br>(true=on false=off)', '907', '5', '2017-08-03 15:02:23', '2017-08-03 15:01:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1346', 'Enable Specials Map', 'GOOGLE_XML_SITEMAP_CREATE_SPECIALS', 'true', 'Set to true if you would like a site map created for your specials.<br>(true=on false=off)', '907', '7', '2017-08-03 15:02:55', '2017-08-03 15:01:38', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1337', 'Number of months to display on the storefront.', 'NEWS_MONTH_ROWS', '12', 'This is the menu that is on the left on the news store front. The default is 12', '367', '4', NULL, '2017-02-10 11:01:34', NULL, NULL),
('1338', 'Number of characters to display in each subtitle.', 'NEWS_CHARACTERS', '100', 'This is the sub-title that is displayed below the title on the front news page. The default is 100.', '367', '5', NULL, '2017-02-10 11:01:34', NULL, NULL),
('1414', 'Enable Hamburger Button Module', 'MODULE_NAVBAR_SIDEBAR_TOGGLE_STATUS', 'True', 'Do you want to add the module to your Navbar?', '6', '1', NULL, '2017-12-08 16:50:05', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1415', 'Content Placement', 'MODULE_NAVBAR_SIDEBAR_TOGGLE_CONTENT_PLACEMENT', 'Home', 'Should the module be loaded in the Left or Right or the Home area of the Navbar?', '6', '1', NULL, '2017-12-08 16:50:05', NULL, 'tep_cfg_select_option(array(\'Left\', \'Right\', \'Home\'), '),
('1416', 'Sort Order', 'MODULE_NAVBAR_SIDEBAR_TOGGLE_SORT_ORDER', '500', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-12-08 16:50:05', NULL, NULL),
('1180', 'KissIT Product Main Image Width', 'KISSIT_MAIN_PRODUCT_IMAGE_WIDTH', '250', 'KissIT Product Main Image Width.<br /><br />', '4', '20', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1181', 'KissIT Product Main Image Height', 'KISSIT_MAIN_PRODUCT_IMAGE_HEIGHT', '250', 'KissIT Product Main Image Height.<br /><br />', '4', '21', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1182', 'KissIT min image size', 'KISSIT_MIN_IMAGE_SIZE', '25', 'Minimum image size in px to trigger Thumbnail creation.<br>Recommended range 10-50.<br>Default: 25.', '4', '22', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1183', 'KissIT Disable Image Upsize', 'KISS_DISABLE_UPSIZE', 'true', 'Keep original image size if the original image is smaller than the requested thumbnail size.', '4', '23', '2016-11-29 15:46:42', '2016-10-17 16:47:31', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1184', 'KissIT Product Watermark Size', 'KISSIT_MAIN_PRODUCT_WATERMARK_SIZE', '0', 'KissIT Product Main Watermark size relativ to the image size (1.0=100%, 0.5 = 50%, 0=no watermark).<br>Change requires Thumbs cache reset.', '4', '24', '2016-10-18 11:39:21', '2016-10-17 16:47:31', NULL, NULL),
('1185', 'KissIT Watermark File Name', 'KISSIT_MAIN_PRODUCT_WATERMARK_IMAGE', 'watermark.png', 'Name of Watermark image file placed in the folder /images. Remember to use a png file with transparent background.<br>Change requires Thumbs cache reset.', '4', '25', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1186', 'KissIT Watermark position in image', 'KISSIT_MAIN_PRODUCT_WATERMARK_PLACEMENT', 'center', 'Position of the watermark in the image reletiv within the image.<br>Change requires Thumbs cache reset.', '4', '26', NULL, '2016-10-17 16:47:31', NULL, 'tep_cfg_select_option(array(\'top-right\', \'top-left\', \'center\',\'bottom-right\', \'bottom-left\'), '),
('1187', 'KissIT min image width to apply Watermark', 'KISSIT_MAIN_PRODUCT_WATERMARK_MIN_IMAGE_WIDTH', '60', 'The minimum width of thumbnail images to apply the watermark.<br>Change requires Thumbs cache reset.', '4', '27', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1188', 'KissIT min image height to apply Watermark', 'KISSIT_MAIN_PRODUCT_WATERMARK_MIN_IMAGE_HEIGHT', '60', 'The minimum height of thumbnail images to apply the watermark.<br>Change requires Thumbs cache reset.', '4', '28', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1189', 'KissIT missing image', 'KISSIT_DEFAULT_MISSING_IMAGE', 'noimg.jpg', 'The missing image image shown if no image can be found.', '4', '30', '2016-12-19 10:55:42', '2016-10-17 16:47:31', NULL, NULL),
('1190', 'KissIT JPEG Quality', 'KISSIT_JPEG_QUALITY', '80', '    Jpeg quality setting, range 0-100.<br>75 corresponds to Photoshop setting 9.<br>Change requires Thumbs cache reset.', '4', '31', '2017-04-07 09:51:25', '2016-10-17 16:47:31', NULL, NULL),
('1191', 'KissIT apply Background', 'KISSIT_APPLY_BACKGROUND', 'true', 'Apply additional Background to fit required proportion and size.<br>Applies to JPEG. PNG/GIF will use transparent Background.<br>Change requires Thumbs cache reset.', '4', '32', NULL, '2016-10-17 16:47:31', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1192', 'KissIT Background Color', 'KISSIT_BACKGROUND_COLOR', '255,255,255', 'RGB color of the added Background.<br><br>Comma separated values:<br>Default: white => 255,255,255.<br>Change requires Thumbs cache reset.', '4', '33', NULL, '2016-10-17 16:47:31', NULL, NULL),
('1193', 'KissIT sharpen Thumbnail', 'KISSIT_SHARPEN_THUMBNAIL', 'none', 'Apply Sharpening Filter to Thumbnails.<br>Default: medium.<br>Change requires Thumbs cache reset.', '4', '34', '2017-04-20 16:27:09', '2016-10-17 16:47:31', NULL, 'tep_cfg_select_option(array(\'none\', \'soft\', \'medium\',\'strong\'), '),
('1194', 'KissIT thumb directory', 'KISSIT_THUMBS_MAIN_DIR', 'thumbs/', 'The name of the thumbs directory inside \"images\".<br>default: \'thumbs\'<br>Change requires Thumbs cache reset.', '4', '38', '2016-12-19 10:08:08', '2016-10-17 16:47:31', NULL, NULL),
('1195', 'KissIT Reset thumbs', 'KISSIT_RESET_IMAGE_THUMBS', 'false', 'Reset thumbs cache.', '4', '39', '2024-07-07 21:00:58', '2016-10-17 16:47:31', 'tep_cfg_reset_thumbs_cache', 'tep_cfg_select_option(array(\'reset\', \'false\'), '),
('1295', 'Allowed Minutes', 'MODULE_ACTION_RECORDER_RESET_PASSWORD_MINUTES', '5', 'Number of minutes to allow password resets to occur.', '6', '0', NULL, '2016-12-14 09:20:49', NULL, NULL),
('1296', 'Allowed Attempts', 'MODULE_ACTION_RECORDER_RESET_PASSWORD_ATTEMPTS', '1', 'Number of password reset attempts to allow within the specified period.', '6', '0', NULL, '2016-12-14 09:20:49', NULL, NULL),
('1297', 'ON/OFF', 'VIEW_COUNTER_ENABLED', 'true', '<center><b><h2>View Counter</h2><i>Developed by:</i><br>Jack York @ Oscommerce Solution<br><a href=\"//oscommerce-solution.com//check_unreleased_updates.php?id=1.6&name=ViewCounter\" target=\"_blank\">Check Updates</a></b></center><br>Site monitoring', '906', '1', '2019-01-23 11:35:28', '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1298', 'Active Time', 'VIEW_COUNTER_ACTIVE_TIME', '10', 'The number of minutes to show active visitors.', '906', '2', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1299', 'Bad Bot Trap', 'VIEW_COUNTER_BAD_BOT_TRAP', 'Off', 'Choose how to handle the bad bot trap.<br><br><b>Ban:</b> Ban the IP<br><b>Email:</b> Send an Email containing the IP<br><b>Both:</b> Both of the above<br><b>Off:</b> Nothing will be done', '906', '3', '2016-12-21 10:00:34', '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'Ban\', \'Email\', \'Both\', \'Off\'), '),
('1300', 'Block Countries Shop', 'VIEW_COUNTER_BLOCK_COUNTRIES_SHOP', 'Off', '<br><b>Off:</b> Not Enabled.<br><br><b>Internal Only:</b> Only use the internal database.<br><br><b>Internal & External:</b> Use the internal database and external sites. More likely to find the country but may slow the site down.', '906', '4', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'Off\', \'Internal\', \'External\'), '),
('1301', 'Block Countries Allow Bots', 'VIEW_COUNTER_BLOCK_COUNTRIES_BOTS', 'Off', '<b>Off</b>: If block countries shop is enabled, block all, even search engines.<br><br><b>On</b>: If block countries shop is enabled, do not block search engines.', '906', '5', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'Off\', \'On\'), '),
('1302', 'Countries Check Admin', 'VIEW_COUNTER_COUNTRIES_CHECK_ADMIN', 'Internal', '<br><b>Internal:</b> Only use the internal database.<br><br><b>External:</b> Use the internal database and external sites. This is more likely to find the country but may slow the site down.', '906', '6', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'Internal\', \'External\'), '),
('1303', 'Date Format', 'VIEW_COUNTER_DATE_FORMAT', '%a, %D %T', 'The format of the displayed date and time. This requires a valid string so only change it if you are familiar with the format.<br><br><b>12 hr:</b> %a, %D %r => Sun, 14th 01:39:56 PM<br><b>24 hr:</b> %a, %D %T => Sun, 14th 13:39:56<br><br>', '906', '7', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1304', 'Force Reset', 'VIEW_COUNTER_FORCE_RESET', '2', 'The maximum number of days to keep the IP\'s in the database. The View counter table will grow very large if not cleared and data over a few days is probably of no use.', '906', '8', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1305', 'Force Reset Storage', 'VIEW_COUNTER_FORCE_RESET_STORAGE', '14', 'The maximum number of days to keep the IP\'s in the storage table. This table is used for reports. It will grow very large if not cleared on a regular basis.', '906', '9', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1306', 'Hide Admin Links', 'VIEW_COUNTER_HIDE_ADMIN_LINKS', '', 'Do not show admin links for these IP\'s. Use a comma separted list for multiple IP\'s.', '906', '10', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1307', 'Item Name Length', 'VIEW_COUNTER_ITEM_NAME_LENGTH', '20', 'The length of the names of the categories, manufacturers and products used in the monitor and report sections.', '906', '11', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1308', 'Kick Off', 'VIEW_COUNTER_ENABLE_KILL_SESSION', 'false', 'Enable the ability to kick off customers.', '906', '12', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1309', 'Page Refresh Period', 'VIEW_COUNTER_PAGE_REFRESH', '120', 'How often, in seconds, to refresh the main page in admin.', '906', '13', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1310', 'Rows Per Page', 'VIEW_COUNTER_MAX_ROWS', '100', 'The maximum number of rows to display in the list.', '906', '14', '2016-12-21 10:16:09', '2016-12-21 09:55:35', NULL, NULL),
('1311', 'Send Emails To', 'VIEW_COUNTER_SEND_EMAILS_TO', '', 'The email address any emails created by View Counter will be sent to. If left empty, the shops email address will be used.', '906', '15', NULL, '2016-12-21 09:55:35', NULL, NULL),
('1312', 'Show Flags', 'VIEW_COUNTER_SHOW_FLAGS', 'true', 'Display the country flags. Turning this off will speed up the display. <br>(true=on false=off)', '906', '16', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1313', 'Show Account Details', 'VIEW_COUNTER_SHOW_ACCOUNT_DETAILS', 'true', 'Display the account details of the visitor, if possible. If multiple accounts exist, they will all be shown. Enabling this option may cause a slow-down, depending upon the site. <br>(true=on false=off)', '906', '17', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1314', 'Version Checker', 'VIEW_COUNTER_ENABLE_VERSION_CHECKER', 'false', 'Enable to automatically check if a new version is available. <br>(true=on false=off)', '906', '18', NULL, '2016-12-21 09:55:35', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1315', 'Enable Search Module', 'MODULE_BOXES_SEARCH_STATUS', 'True', 'Do you want to add the module to your shop?', '6', '1', NULL, '2017-01-03 16:13:24', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1316', 'Content Placement', 'MODULE_BOXES_SEARCH_CONTENT_PLACEMENT', 'Left Column', 'Should the module be loaded in the left or right column?', '6', '1', NULL, '2017-01-03 16:13:25', NULL, 'tep_cfg_select_option(array(\'Left Column\', \'Right Column\'), '),
('1317', 'Sort Order', 'MODULE_BOXES_SEARCH_SORT_ORDER', '', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-01-03 16:13:25', NULL, NULL),
('1411', 'Content Placement', 'MODULE_NAVBAR_SOCIAL_LINKS_CONTENT_PLACEMENT', 'Right', 'Should the module be loaded in the Left or Right or the Home area of the Navbar?', '6', '1', NULL, '2017-11-16 10:08:39', NULL, 'tep_cfg_select_option(array(\'Left\', \'Right\', \'Home\'), '),
('1410', 'Enable Account Module', 'MODULE_NAVBAR_SOCIAL_LINKS_STATUS', 'True', 'Do you want to add the module to your Navbar?', '6', '1', NULL, '2017-11-16 10:08:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1383', 'Sort Order', 'MODULE_SOCIAL_BOOKMARKS_FACEBOOK_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-11-15 15:52:34', NULL, NULL),
('1382', 'Enable Facebook Module', 'MODULE_SOCIAL_BOOKMARKS_FACEBOOK_STATUS', 'True', 'Do you want to allow products to be shared through Facebook?', '6', '1', NULL, '2017-11-15 15:52:34', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1691', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_STATUS', 'True', 'Do you want to allow product meta (facebook) pixels to be added to the page header?', '6', '1', NULL, '2025-01-21 10:46:48', NULL, NULL),
('1412', 'Sort Order', 'MODULE_NAVBAR_SOCIAL_LINKS_SORT_ORDER', '510', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-11-16 10:08:39', NULL, NULL),
('1374', 'Enable Twitter Module', 'MODULE_SOCIAL_BOOKMARKS_TWITTER_STATUS', 'True', 'Do you want to allow products to be shared through Twitter?', '6', '1', NULL, '2017-11-15 15:52:18', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1375', 'Sort Order', 'MODULE_SOCIAL_BOOKMARKS_TWITTER_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-11-15 15:52:18', NULL, NULL),
('1690', 'Sort Order', 'MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2024-07-08 10:20:26', NULL, NULL),
('1689', 'Enable Customers Module', 'MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_STATUS', 'True', 'Do you want to show the newest customers on the dashboard?', '6', '1', NULL, '2024-07-08 10:20:26', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1385', 'Sort Order', 'MODULE_SOCIAL_BOOKMARKS_EMAIL_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-11-15 15:52:41', NULL, NULL),
('1506', 'Site URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_URL', 'http://www.cadservices.co.uk', 'Site URL', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1491', 'GooglePlus URL', 'MODULE_HEADER_TAGS_PAGES_GOOGLEPLUS', 'https://plus.google.com/+CadservicesCoUk', 'GooglePlus URL', '6', '0', NULL, '2018-02-16 10:14:57', NULL, NULL),
('1513', 'Site Logo', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_PHONE', '+44-01642-677582', 'Phone Number', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1512', 'Site Logo', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_LOGO', 'http://www.cadservices.co.uk/images/TCS_dark.jpg', 'Path to company LOGO', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1510', 'Linkedin URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_LINKEDIN', 'https://www.linkedin.com/company/18335061', 'Linkedin URL', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1469', 'Twitter URL', 'MODULE_HEADER_TAGS_PAGES_GOOGLEPLUS', '', 'GooglePlus URL', '6', '0', NULL, '2018-02-16 10:06:27', NULL, NULL),
('1508', 'Facebook URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_FACEBOOK', 'https://www.facebook.com/TCSCAD', 'Facebook URL', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1509', 'Twitter URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_TWITTER', 'https://twitter.com/tcscad', 'Twitter URL', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1459', 'Twitter URL', 'MODULE_HEADER_TAGS_PAGES_GOOGLEPLUS', '', 'GooglePlus URL', '6', '0', NULL, '2018-02-16 10:05:24', NULL, NULL),
('1511', 'GooglePlus URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_GOOGLEPLUS', 'https://plus.google.com/+CadservicesCoUk', 'GooglePlus URL', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1504', 'Sort Order', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1424', 'Sort Order', 'MODULE_CONTENT_INFO_BANNER_SORT_ORDER', '100', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2017-12-19 15:17:40', NULL, NULL),
('1423', 'Enable Banner Module', 'MODULE_CONTENT_INFO_BANNER_STATUS', 'False', 'Should the Banner be shown? ', '6', '1', NULL, '2017-12-19 15:17:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1507', 'Search URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_SEARCH_URL', 'https://www.cadservices.co.uk/advanced_search_result.php?keywords={search_term_string}', 'Search url with {search_term_string} in place of keywords (search the site with the search term {search_term_string} and post the URL here making sure the chars havent been escaped)', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1505', 'Company Name', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_COMPANY_NAME', 'TCS CAD and BIM Solutions Ltd', 'Company Name', '6', '0', NULL, '2018-02-16 14:16:01', NULL, NULL),
('1503', 'Enable Pages SEO Module', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_STATUS', 'True', 'Do you want to allow this module to write SEO to your Pages?', '6', '1', NULL, '2018-02-16 14:16:01', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1514', 'Enable Chemo Compress', 'CHEMO_COMPRESS_ENABLE', 'true', 'This will compress your page code via server side and speed-up page loading. This is not GZip and if GZip is enabled (above), it will not work. Instead use the GZip Compression script in the main .htaccess file.<br><br>Enable Chemo Compress?', '14', '3', '2018-02-19 16:09:53', '2018-02-01 02:34:46', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1529', 'Enable Social Bookmarks Module', 'MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-02-23 11:14:59', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1530', 'Sort Order', 'MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-02-23 11:14:59', NULL, NULL),
('1531', 'Enable Description Module', 'MODULE_CONTENT_PI_DESCRIPTION_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:34:15', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1532', 'Content Width', 'MODULE_CONTENT_PI_DESCRIPTION_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:34:15', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1533', 'Sort Order', 'MODULE_CONTENT_PI_DESCRIPTION_SORT_ORDER', '60', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:34:15', NULL, NULL),
('1534', 'Enable Buy Button', 'MODULE_CONTENT_PI_BUY_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:34:46', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1535', 'Content Width', 'MODULE_CONTENT_PI_BUY_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:34:46', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1536', 'Sort Order', 'MODULE_CONTENT_PI_BUY_SORT_ORDER', '35', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:34:46', NULL, NULL),
('1625', 'KissIT Retina Factor', 'KISSIT_RETINA_FACTOR', '1.5', 'Minimum factor between original image siza and required image size to create retina image. Should be a number between 1 and 2.<br>Default 1.5.', '4', '36', NULL, '2018-08-03 12:05:17', NULL, NULL),
('1539', 'Enable Model Module', 'MODULE_CONTENT_PI_MODEL_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:35:25', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1540', 'Content Width', 'MODULE_CONTENT_PI_MODEL_CONTENT_WIDTH', '6', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:35:25', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1541', 'Sort Order', 'MODULE_CONTENT_PI_MODEL_SORT_ORDER', '5', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:35:25', NULL, NULL),
('1542', 'Enable Name Module', 'MODULE_CONTENT_PI_NAME_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:35:33', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1543', 'Content Width', 'MODULE_CONTENT_PI_NAME_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:35:33', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1544', 'Sort Order', 'MODULE_CONTENT_PI_NAME_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:35:33', NULL, NULL),
('1636', 'PayPal App Parameter', 'OSCOM_APP_PAYPAL_EC_STATUS', '', 'A parameter for the PayPal Application.', '6', '0', NULL, '2018-12-12 16:55:49', NULL, NULL),
('1635', 'Sort Order', 'MODULE_HEADER_TAGS_CLICKGUARDIAN_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-12-10 10:03:39', NULL, NULL),
('1634', 'Enable No Script Module', 'MODULE_HEADER_TAGS_CLICKGUARDIAN_STATUS', 'True', 'Add message for people with .js turned off?', '6', '1', NULL, '2018-12-10 10:03:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1548', 'Enable Price Module', 'MODULE_CONTENT_PI_PRICE_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:37:35', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1549', 'Content Width', 'MODULE_CONTENT_PI_PRICE_CONTENT_WIDTH', '6', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:37:35', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1550', 'Sort Order', 'MODULE_CONTENT_PI_PRICE_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:37:35', NULL, NULL),
('1551', 'Enable Message Module', 'MODULE_CONTENT_PINF_MESSAGE_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:37:50', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1552', 'Content Width', 'MODULE_CONTENT_PINF_MESSAGE_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:37:50', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1553', 'Sort Order', 'MODULE_CONTENT_PINF_MESSAGE_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:37:50', NULL, NULL),
('1603', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_SCHEMA_SORT_ORDER', '950', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-06-05 16:34:26', NULL, NULL),
('1598', 'Enable Video Module', 'MODULE_CONTENT_PI_VIDEO_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-06-01 17:00:58', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1599', 'Content Width', 'MODULE_CONTENT_PI_VIDEO_CONTENT_WIDTH', '6', 'What width container should the content be shown in?', '6', '1', NULL, '2018-06-01 17:00:58', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1600', 'Sort Order', 'MODULE_CONTENT_PI_VIDEO_SORT_ORDER', '45', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-06-01 17:00:58', NULL, NULL),
('1601', 'Enable Product Schema Module', 'MODULE_HEADER_TAGS_PRODUCT_SCHEMA_STATUS', 'True', 'Do you want to allow product schema to be added to your product page?', '6', '1', NULL, '2018-06-05 16:34:26', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1602', 'Placement', 'MODULE_HEADER_TAGS_PRODUCT_SCHEMA_PLACEMENT', 'Header', 'Where should the code be placed?', '6', '1', NULL, '2018-06-05 16:34:26', NULL, 'tep_cfg_select_option(array(\'Header\', \'Footer\'), '),
('1562', 'Enable Options & Attributes', 'MODULE_CONTENT_PI_OA_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-04-06 12:38:19', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1563', 'Content Width', 'MODULE_CONTENT_PI_OA_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2018-04-06 12:38:19', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1564', 'Add Helper Text', 'MODULE_CONTENT_PI_OA_HELPER', 'True', 'Should first option in dropdown be Helper Text?', '6', '1', NULL, '2018-04-06 12:38:19', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1565', 'Enforce Selection', 'MODULE_CONTENT_PI_OA_ENFORCE', 'True', 'Should customer be forced to select option(s)?', '6', '1', NULL, '2018-04-06 12:38:19', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1566', 'Sort Order', 'MODULE_CONTENT_PI_OA_SORT_ORDER', '15', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-04-06 12:38:19', NULL, NULL),
('1567', 'Enable Product Listing Module', 'MODULE_CONTENT_IP_PRODUCT_LISTING_STATUS', 'True', 'Should this module be enabled?', '6', '1', NULL, '2018-04-06 12:38:56', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1568', 'Content Width', 'MODULE_CONTENT_IP_PRODUCT_LISTING_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '2', NULL, '2018-04-06 12:38:56', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1569', 'Item Width', 'MODULE_CONTENT_IP_PRODUCT_LISTING_CONTENT_WIDTH_EACH', '6', 'What width container should each Item be shown in?', '6', '3', NULL, '2018-04-06 12:38:56', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1570', 'Sort Order', 'MODULE_CONTENT_IP_PRODUCT_LISTING_SORT_ORDER', '200', 'Sort order of display. Lowest is displayed first.', '6', '4', NULL, '2018-04-06 12:38:56', NULL, NULL),
('1594', 'Sort Order', 'MODULE_CONTENT_IP_TITLE_SORT_ORDER', '50', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2018-04-06 14:46:51', NULL, NULL),
('1593', 'Content Width', 'MODULE_CONTENT_IP_TITLE_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2018-04-06 14:46:51', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1592', 'Enable Title Module', 'MODULE_CONTENT_IP_TITLE_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2018-04-06 14:46:51', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1574', 'Enable Title Module', 'MODULE_CONTENT_IN_TITLE_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2018-04-06 12:40:08', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1575', 'Content Width', 'MODULE_CONTENT_IN_TITLE_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2018-04-06 12:40:08', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1576', 'Sort Order', 'MODULE_CONTENT_IN_TITLE_SORT_ORDER', '50', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2018-04-06 12:40:08', NULL, NULL),
('1583', 'Enable Title Module', 'MODULE_CONTENT_I_TITLE_STATUS', 'True', 'Do you want to enable this module?', '6', '1', NULL, '2018-04-06 12:41:05', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1584', 'Content Width', 'MODULE_CONTENT_I_TITLE_CONTENT_WIDTH', '12', 'What width container should the content be shown in? (12 = full width, 6 = half width).', '6', '2', NULL, '2018-04-06 12:41:05', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1585', 'Sort Order', 'MODULE_CONTENT_I_TITLE_SORT_ORDER', '50', 'Sort order of display. Lowest is displayed first.', '6', '5', NULL, '2018-04-06 12:41:05', NULL, NULL),
('1622', 'Thumbnail Layout', 'MODULE_HEADER_TAGS_PRODUCT_COLORBOX_LAYOUT', '155', 'Layout of Thumbnails', '6', '0', NULL, '2018-07-04 14:38:44', 'ht_product_colorbox_thumbnail_number', NULL),
('1621', 'Pages', 'MODULE_HEADER_TAGS_PRODUCT_COLORBOX_PAGES', 'product_info.php', 'The pages to add the Colorbox Scripts to.', '6', '0', NULL, '2018-07-04 14:38:44', 'ht_product_colorbox_show_pages', 'ht_product_colorbox_edit_pages('),
('1620', 'Enable Colorbox Script', 'MODULE_HEADER_TAGS_PRODUCT_COLORBOX_STATUS', 'True', 'Do you want to enable the Colorbox Scripts?', '6', '1', NULL, '2018-07-04 14:38:44', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1626', 'KissIT use small image subset', 'KISSIT_USE_SUBSET', 'true', 'Create a set of half and quarter sized thumbs for big images and include them via html5 srcset.<br>Default: true.', '4', '37', NULL, '2018-08-03 12:05:17', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1627', 'KissIT use watermark for banners', 'KISSIT_BANNER_WATERMARK', 'false', 'Use watermark for images without defined width and height. Usually banners and store logo in standard stores.<br>Change requires Thumbs cache reset.<br>Default: false.', '4', '29', NULL, '2018-08-03 12:05:17', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1633', 'Sort Order', 'MODULE_CONTENT_PI_GALLERY_SORT_ORDER', '40', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2018-08-03 12:12:57', NULL, NULL),
('1632', 'Content Width', 'MODULE_CONTENT_PI_GALLERY_CONTENT_WIDTH', '6', 'What width container should the content be shown in?', '6', '1', NULL, '2018-08-03 12:12:57', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1631', 'Enable Gallery Module', 'MODULE_CONTENT_PI_GALLERY_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', NULL, '2018-08-03 12:12:57', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1637', 'PayPal App Parameter', 'OSCOM_APP_PAYPAL_DP_STATUS', '', 'A parameter for the PayPal Application.', '6', '0', NULL, '2018-12-12 16:55:49', NULL, NULL),
('1638', 'PayPal App Parameter', 'OSCOM_APP_PAYPAL_HS_STATUS', '', 'A parameter for the PayPal Application.', '6', '0', NULL, '2018-12-12 16:55:49', NULL, NULL),
('1639', 'PayPal App Parameter', 'OSCOM_APP_PAYPAL_PS_STATUS', '', 'A parameter for the PayPal Application.', '6', '0', NULL, '2018-12-12 16:55:49', NULL, NULL),
('1640', 'PayPal App Parameter', 'OSCOM_APP_PAYPAL_LOGIN_STATUS', '', 'A parameter for the PayPal Application.', '6', '0', NULL, '2018-12-12 16:55:49', NULL, NULL),
('1643', 'Autofill Create Account', 'VIEW_COUNTER_AUTOFILL', 'false', 'Autofill some of the fields on the create account and new address pages.<br>(true=on false=off)', '906', '1', NULL, '2019-02-25 11:00:47', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1642', 'PayPal App Parameter', 'OSCOM_APP_PAYPAL_VERSION_CHECK', '10', 'A parameter for the PayPal Application.', '6', '0', NULL, '2018-12-13 09:34:55', NULL, NULL),
('1644', '<font color=purple>Data Skimmers Minimum Count (Pro Version Only)</font>', 'VIEW_COUNTER_DATA_SKIMMER_MIN_COUNT', '100', 'The minimum number of clicks before an IP is considered to be a skimmer.', '906', '2', NULL, '2019-02-25 11:00:47', NULL, NULL),
('1645', '<font color=purple>Data Skimmers Show Bots (Pro Version Only)</font>', 'VIEW_COUNTER_DATA_SKIMMER_SHOW_BOTS', 'false', 'Include search bots when evaluating skimmers. This may be useful to identify bad bots and spoofers. If enabled, be careful when banning any that show since they may be legitimate bots.<br>(true=on false=off)', '906', '3', NULL, '2019-02-25 11:00:47', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'), '),
('1646', '<font color=purple>Data Skimmers Period of Time (Pro Version Only)</font>', 'VIEW_COUNTER_DATA_SKIMMER_PERIOD', '24', 'The amount of time, in hours, to check for skimmers. So 24 would mean to check the log for the last 24 hours.', '906', '4', NULL, '2019-02-25 11:00:47', NULL, NULL),
('1647', 'Good IP List', 'VIEW_COUNTER_GOOD_IP_LIST', '', 'Ignore these IP\'s when banning. Use a comma separated list for multiple IP\'s.', '906', '5', NULL, '2019-02-25 11:00:47', NULL, NULL),
('1652', 'Enable Banner Module', 'MODULE_CONTENT_INFO_BANNER_STATUS', 'True', 'Should the Banner be shown? ', '6', '1', NULL, '2019-12-19 11:28:40', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1653', 'Sort Order', 'MODULE_CONTENT_INFO_BANNER_SORT_ORDER', '10', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2019-12-19 11:28:40', NULL, NULL),
('1664', 'Enable Xmas message Module', 'MODULE_HEADER_TAGS_XMAS_MESSAGE_CHECKOUT_STATUS', 'False', 'Do you want to allow this module to add xmas message to your checkout?', '6', '1', NULL, '2019-12-19 12:31:23', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1665', 'Message', 'MODULE_HEADER_TAGS_XMAS_MESSAGE_MESSAGE', 'TCS will close for the Christmas and New year holidays at 12pm on Friday 20th December 2024 and will re-open on Thursday the 2nd January 2022.  All orders placed after 11am on the 20th of December 2022 will be processed on the 2nd January 2022', 'Message to be displayed', '6', '0', NULL, '2019-12-19 12:31:23', NULL, NULL),
('1666', 'Sort Order', 'MODULE_HEADER_TAGS_XMAS_MESSAGE_CHECKOUT_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2019-12-19 12:31:23', NULL, NULL),
('1674', 'Enable Google Analytics Module', 'MODULE_HEADER_TAGS_GOOGLE_ANALYTICS_STATUS', 'True', 'Do you want to add Google Analytics to your shop?', '6', '1', NULL, '2023-07-31 15:55:25', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1667', 'Enable Reviews Module', 'MODULE_CONTENT_PRODUCT_INFO_RELATED_PRODUCTS_STATUS', 'True', 'Should the Also Purchased block be shown on the product info page?', '6', '1', NULL, '2021-05-18 16:32:39', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1668', 'Content Width', 'MODULE_CONTENT_PRODUCT_INFO_RELATED_PRODUCTS_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2021-05-18 16:32:39', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1669', 'Number of Products', 'MODULE_CONTENT_PRODUCT_INFO_RELATED_PRODUCTS_CONTENT_LIMIT', '4', 'How many products (maximum) should be shown?', '6', '1', NULL, '2021-05-18 16:32:39', NULL, NULL),
('1670', 'Product Width', 'MODULE_CONTENT_PRODUCT_INFO_RELATED_PRODUCTS_DISPLAY_EACH', '3', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2021-05-18 16:32:39', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1671', 'Sort Order', 'MODULE_CONTENT_PRODUCT_INFO_RELATED_PRODUCTS_SORT_ORDER', '100000', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2021-05-18 16:32:39', NULL, NULL),
('1682', 'Enable Product Title Module', 'MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_STATUS', 'True', 'Do you want to allow product titles to be added to the page title?', '6', '1', NULL, '2024-05-09 22:26:53', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1683', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_ATTRIBUTES_SORT_ORDER', '-100', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2024-05-09 22:26:53', NULL, NULL),
('1684', 'Enable Reviews Module', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_STATUS', 'True', 'Should the Also Purchased block be shown on the product info page?', '6', '1', NULL, '2024-05-10 14:26:35', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1685', 'Content Width', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', NULL, '2024-05-10 14:26:35', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1686', 'Number of Products', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_CONTENT_LIMIT', '99', 'How many products (maximum) should be shown?', '6', '1', NULL, '2024-05-10 14:26:35', NULL, NULL),
('1687', 'Product Width', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_DISPLAY_EACH', '3', 'What width container should each product be shown in? (12 = full width, 6 = half width).', '6', '4', NULL, '2024-05-10 14:26:35', NULL, 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), '),
('1688', 'Sort Order', 'MODULE_CONTENT_PRODUCT_INFO_PRODUCT_VARIATIONS_TABLE_SORT_ORDER', '999', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2024-05-10 14:26:35', NULL, NULL),
('1694', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:49:59', NULL, NULL),
('1695', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_STATUS', 'True', 'Do you want to allow product meta (facebook) pixels to be added to the page header?', '6', '1', NULL, '2025-01-21 10:51:21', NULL, NULL),
('1696', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:51:21', NULL, NULL),
('1697', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_STATUS', 'True', 'Do you want to allow product meta (facebook) pixels to be added to the page header?', '6', '1', NULL, '2025-01-21 10:51:50', NULL, NULL),
('1698', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:51:50', NULL, NULL),
('1699', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_STATUS', 'True', 'Do you want to allow product meta (facebook) pixels to be added to the page header?', '6', '1', NULL, '2025-01-21 10:52:24', NULL, NULL),
('1700', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:52:24', NULL, NULL),
('1701', 'Enable Twitter Card Module', 'MODULE_HEADER_TAGS_TWITTER_PRODUCT_CARD_STATUS', 'True', 'Do you want to allow Twitter Card tags to be added to your product information pages?', '6', '1', NULL, '2025-01-21 10:53:42', NULL, 'tep_cfg_select_option(array(\'True\', \'False\'), '),
('1702', 'Choose Twitter Card Type', 'MODULE_HEADER_TAGS_TWITTER_PRODUCT_CARD_TYPE', 'summary_large_image', 'Choose Summary or Summary Large Image.  Note that your product images MUST be at least h120px by w120px (Summary) or h150px x w280px (Summary Large Image).', '6', '1', NULL, '2025-01-21 10:53:42', NULL, 'tep_cfg_select_option(array(\'summary\', \'summary_large_image\'), '),
('1703', 'Twitter Author @username', 'MODULE_HEADER_TAGS_TWITTER_PRODUCT_CARD_USER_ID', '', 'Your @username at Twitter', '6', '0', NULL, '2025-01-21 10:53:42', NULL, NULL),
('1704', 'Twitter Shop @username', 'MODULE_HEADER_TAGS_TWITTER_PRODUCT_CARD_SITE_ID', '', 'Your shops @username at Twitter (or leave blank if it is the same as your @username above).', '6', '0', NULL, '2025-01-21 10:53:42', NULL, NULL),
('1705', 'Sort Order', 'MODULE_HEADER_TAGS_TWITTER_PRODUCT_CARD_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:53:42', NULL, NULL),
('1706', 'Enable Product Meta Module', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_STATUS', 'True', 'Do you want to allow product meta (facebook) pixels to be added to the page header?', '6', '1', NULL, '2025-01-21 10:55:55', NULL, NULL),
('1707', 'Sort Order', 'MODULE_HEADER_TAGS_PRODUCT_META_PIXELS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', NULL, '2025-01-21 10:56:10', NULL, NULL);

COMMIT;
