-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 26, 2025 at 02:41 PM
-- Table: autobooks_default_field_configuration
-- Records: 1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_default_field_configuration`
--

DROP TABLE IF EXISTS `autobooks_default_field_configuration`;
CREATE TABLE `autobooks_default_field_configuration` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `configuration_name` varchar(255) NOT NULL DEFAULT 'default' COMMENT 'Configuration identifier',
  `visible_fields` longtext DEFAULT NULL COMMENT 'JSON array of fields visible by default',
  `field_order` longtext DEFAULT NULL COMMENT 'JSON array defining field display order',
  `apply_to_new_tables` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Apply to new data table configurations',
  `apply_to_csv_import` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Apply to CSV import column selection',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `configuration_name` (`configuration_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Default field configuration for new tables and CSV imports';

-- Dumping data for table `autobooks_default_field_configuration`
--

INSERT INTO `autobooks_default_field_configuration` (`id`, `configuration_name`, `visible_fields`, `field_order`, `apply_to_new_tables`, `apply_to_csv_import`, `created_at`, `updated_at`) VALUES
('1', 'default', '[\"subscription_reference\",\"email\",\"product_name\",\"start_date\",\"end_date\",\"status\",\"state\",\"country\",\"postal_code\",\"reseller_name\",\"sold_to_name\",\"vendor_name\",\"end_customer_contact_name\"]', '[\"address\",\"agreement_autorenew\",\"agreement_support_level\",\"city\",\"company_name\",\"contact_name\",\"end_customer_contact_name\",\"quotation_deal_registration_number\",\"email\",\"end_date\",\"flaer_phase\",\"quotation_resellerpo_previous\",\"product_deployment\",\"product_family\",\"product_list_price\",\"product_market_segment\",\"product_name\",\"product_part\",\"product_release\",\"product_sku\",\"product_sku_description\",\"product_type\",\"quotation_due_date\",\"quotation_id\",\"quotation_status\",\"quotation_type\",\"quotation_vendor_id\",\"reseller_name\",\"start_date\",\"status\",\"subscription_days_due\",\"subscription_level\",\"subscription_quantity\",\"subscription_reference\",\"subscription_serial_number\",\"vendor_id\",\"price\",\"sold_to_name\",\"state\",\"country\",\"currency\",\"quantity\",\"vendor_name\",\"postal_code\",\"serial_number\",\"agreement_status\",\"end_customer_contact_phone\",\"end_customer_country\",\"end_customer_account_type\",\"agreement_program_name\",\"agreement_terms\",\"agreement_type\",\"end_customer_city\",\"end_customer_state\",\"end_customer_zip_code\",\"end_customer_address_2\",\"end_customer_address_3\",\"end_customer_industry_segment\",\"sold_to_number\",\"end_customer_vendor_id\",\"reseller_number\",\"reseller_vendor_id\",\"agreement_days_due\",\"updated\"]', '1', '1', '2025-08-22 12:36:40', '2025-08-22 12:36:43');

COMMIT;
