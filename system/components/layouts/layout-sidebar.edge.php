@props([
    'title' => 'main view',
    'description' => 'collapse-able sidebar using alpine js',
    'page' => 'dashboard',
    'routes' => ROUTE_TREE,
])
<div class="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:flex-col transition-all duration-300"
        :class="collapsed ? 'w-11' : 'w-44'">
    <!-- Sidebar -->
    <div class="overflow-y-auto flex flex-col grow gap-y-5 px-0 pb-0 bg-indigo-600">
        <div  @click="toggle()" class="h-16 p-2 flex mx-0 space-y-1 shrink-0 items-center font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700 cursor-pointer">
            <h2 class="flex"><svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6"><path stroke-linecap="round" stroke-linejoin="round" d="m21 7.5-2.25-1.313M21 7.5v2.25m0-2.25-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3 2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75 2.25-1.313M12 21.75V19.5m0 2.25-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"></path></svg>
                &nbsp;<span class="flex transition-opacity duration-300" :class="collapsed ? 'opacity-0' : 'opacity-100'">Autobooks</span>
            </h2>
        </div>
        <nav class="flex flex-col flex-1">
            <ul role="list" class="flex flex-col flex-1 gap-y-7">
                <li>
                    <ul id="main-nav-tree" :class='collapsed ? "mx-0 space-y-1" : "mx-0 space-y-1"' class="transition-all duration-300">
                       <x-nav-tree :items="$routes"></x-nav-tree>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>
</div>


