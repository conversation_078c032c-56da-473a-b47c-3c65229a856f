@props([
    'sorting' => [],
    'available_columns' => []
])

@if(empty($sorting))
    <div id="no-sorting-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No sorting rules</h3>
        <p class="mt-1 text-sm text-gray-500">Results will be returned in database default order.</p>
    </div>
@else
    @foreach($sorting as $index => $sort)
        <x-data-source-sorting-item
            :sort="$sort"
            :sort_index="$index"
            :available_columns="$available_columns"
        />
    @endforeach
@endif
