@props([
    'type' => 'info', // 'error', 'warning', 'info', 'success'
    'title' => null,
    'message' => null,
    'icon' => null, // Custom icon override
    'size' => 'default', // 'small', 'default', 'large'
    'layout' => 'default', // 'default', 'compact', 'centered'
    'dismissible' => false,
    'show_icon' => true,
    'class' => '', // Additional CSS classes
    'id' => null
])

@php
// Define type-based styling
$type_configs = [
    'error' => [
        'container' => 'bg-red-50 border-red-200 text-red-700',
        'icon_color' => 'text-red-400',
        'title_color' => 'text-red-800',
        'message_color' => 'text-red-700',
        'default_icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>',
        'default_title' => 'Error'
    ],
    'warning' => [
        'container' => 'bg-yellow-50 border-yellow-200 text-yellow-700',
        'icon_color' => 'text-yellow-400',
        'title_color' => 'text-yellow-800',
        'message_color' => 'text-yellow-700',
        'default_icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>',
        'default_title' => 'Warning'
    ],
    'info' => [
        'container' => 'bg-blue-50 border-blue-200 text-blue-700',
        'icon_color' => 'text-blue-400',
        'title_color' => 'text-blue-800',
        'message_color' => 'text-blue-700',
        'default_icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
        'default_title' => 'Information'
    ],
    'success' => [
        'container' => 'bg-green-50 border-green-200 text-green-700',
        'icon_color' => 'text-green-400',
        'title_color' => 'text-green-800',
        'message_color' => 'text-green-700',
        'default_icon' => '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"></path>',
        'default_title' => 'Success'
    ]
];

// Get configuration for the specified type
$config = $type_configs[$type] ?? $type_configs['info'];

// Size-based styling
$size_configs = [
    'small' => [
        'container' => 'p-3',
        'icon_size' => 'h-4 w-4',
        'title_size' => 'text-sm font-medium',
        'message_size' => 'text-xs'
    ],
    'default' => [
        'container' => 'p-4',
        'icon_size' => 'h-5 w-5',
        'title_size' => 'text-sm font-medium',
        'message_size' => 'text-sm'
    ],
    'large' => [
        'container' => 'p-6',
        'icon_size' => 'h-6 w-6',
        'title_size' => 'text-lg font-medium',
        'message_size' => 'text-sm'
    ]
];

$size_config = $size_configs[$size] ?? $size_configs['default'];

// Layout-based styling
$layout_configs = [
    'default' => [
        'wrapper' => 'flex',
        'icon_wrapper' => 'flex-shrink-0',
        'content_wrapper' => 'ml-3',
        'text_align' => ''
    ],
    'compact' => [
        'wrapper' => 'flex items-center space-x-2',
        'icon_wrapper' => 'flex-shrink-0',
        'content_wrapper' => '',
        'text_align' => ''
    ],
    'centered' => [
        'wrapper' => 'text-center',
        'icon_wrapper' => 'flex items-center justify-center mb-4',
        'content_wrapper' => '',
        'text_align' => 'text-center'
    ]
];

$layout_config = $layout_configs[$layout] ?? $layout_configs['default'];

// Build final classes
$container_classes = implode(' ', [
    'border rounded-lg',
    $config['container'],
    $size_config['container'],
    $class
]);

$icon_classes = implode(' ', [
    $size_config['icon_size'],
    $config['icon_color']
]);

$title_classes = implode(' ', [
    $size_config['title_size'],
    $config['title_color']
]);

$message_classes = implode(' ', [
    $size_config['message_size'],
    $config['message_color']
]);

// Determine content
$display_title = $title ?? $config['default_title'];
$display_icon = $icon ?? $config['default_icon'];
$has_content = $title || $message || $slot->isNotEmpty();
@endphp

@if($has_content)
<div class="{{ $container_classes }}" @if($id) id="{{ $id }}" @endif>
    <div class="{{ $layout_config['wrapper'] }}">
        @if($show_icon)
            <div class="{{ $layout_config['icon_wrapper'] }}">
                <svg class="{{ $icon_classes }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {!! $display_icon !!}
                </svg>
            </div>
        @endif
        
        <div class="{{ $layout_config['content_wrapper'] }}">
            @if($title)
                <h3 class="{{ $title_classes }} {{ $layout_config['text_align'] }}">{{ $display_title }}</h3>
            @endif
            
            @if($message)
                <div class="{{ $message_classes }} {{ $layout_config['text_align'] }} @if($title) mt-2 @endif">
                    {{ $message }}
                </div>
            @endif
            
            @if($slot && $slot->isNotEmpty())
                <div class="{{ $layout_config['text_align'] }} @if($title || $message) mt-2 @endif">
                    {{ $slot }}
                </div>
            @endif
        </div>
        
        @if($dismissible)
            <div class="ml-auto pl-3">
                <div class="-mx-1.5 -my-1.5">
                    <button type="button" 
                            class="inline-flex rounded-md p-1.5 {{ $config['icon_color'] }} hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
                            onclick="this.closest('.border').remove()">
                        <span class="sr-only">Dismiss</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                        </svg>
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>
@endif
