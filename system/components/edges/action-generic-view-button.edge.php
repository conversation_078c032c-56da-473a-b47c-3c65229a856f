@props([
    'field_value' => '',
    'item' => [],
    'icon' => '👁️',
    'table_name' => '',
    'id_field' => 'id',
    'data_source' => null,
    'label' => 'View'
])

@php
    // Determine the record ID value
    $record_id = $field_value;
    if (empty($record_id) && isset($item[$id_field])) {
        $record_id = $item[$id_field];
    }
    
    // Prepare the HTMX values
    $hx_vals = [
        'table_name' => $table_name,
        'record_id' => $record_id,
        'id_field' => $id_field,
        'generic' => true
    ];
    
    if ($data_source) {
        $hx_vals['data_source'] = $data_source;
    }
@endphp

<button type="button"
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
        hx-post="{{ APP_ROOT }}/api/generic_view"
        hx-target="#modal-container"
        hx-swap="innerHTML"
        hx-vals='{{ json_encode($hx_vals) }}'
        @click="showModal = true"
        title="View record details">
    <span class="mr-1">{{ $icon }}</span>
    {{ $label }}
</button>
