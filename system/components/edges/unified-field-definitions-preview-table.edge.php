@php
    // Include the callback functions file to access the data function
    if (file_exists(FS_SYS_FUNCTIONS . DS . 'unified_field_definitions_callbacks.fn.php')) {
        require_once FS_SYS_FUNCTIONS . DS . 'unified_field_definitions_callbacks.fn.php';
    }

    // Get the default fields data for the preview table
    $preview_data = get_unified_field_definitions_preview_data();
    $preview_items = $preview_data['items'] ?? [];
    $preview_columns = $preview_data['columns'] ?? [];
    $preview_fields = $preview_data['available_fields'] ?? [];
@endphp

@if(!empty($preview_columns))
    <x-data-table 
        table_name="unified_field_definitions_default_preview"
        :items="$preview_items"
        :columns="$preview_columns"
        callback="render_unified_field_definitions_preview_table"
        :available_fields="$preview_fields"
        current_data_source_type="unified_field_definitions_default"
        :current_data_source_id="null"
        show_search="false"
        show_pagination="false"
        empty_message="Configure fields above with 'Show by default' to see preview data here."
    />
    
    <!-- Configuration Info -->
    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="text-sm text-blue-800">
            <p><strong>Configuration:</strong> unified_field_definitions_default</p>
            <p><strong>Fields shown:</strong> {{ count($preview_columns) }} fields marked as "Show by default"</p>
            <p><strong>Sample data:</strong> {{ count($preview_items) }} preview rows</p>
            <p class="mt-2"><strong>Fields included:</strong> 
                @foreach($preview_columns as $col)
                    <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                        {{ $col['label'] }} ({{ $col['category'] }})
                    </span>
                @endforeach
            </p>
        </div>
    </div>
@else
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <svg class="w-12 h-12 text-yellow-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="text-lg font-medium text-yellow-800 mb-2">No Default Fields Configured</h3>
        <p class="text-yellow-700 mb-4">
            No fields are currently set to "Show by default". Edit field definitions above to mark fields as default display fields.
        </p>
        <p class="text-sm text-yellow-600">
            To configure default fields, edit any field definition above and check "Show by default in data tables" in the Display Configuration section.
        </p>
    </div>
@endif
