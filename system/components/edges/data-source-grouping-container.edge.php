@props([
    'grouping' => [],
    'available_columns' => []
])

@if(empty($grouping))
    <div id="no-grouping-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No grouping rules</h3>
        <p class="mt-1 text-sm text-gray-500">Results will not be grouped. Add columns to group by.</p>
    </div>
@else
    @foreach($grouping as $index => $group)
        <x-data-source-grouping-item
            :group="$group"
            :group_index="$index"
            :available_columns="$available_columns"
        />
    @endforeach
@endif
