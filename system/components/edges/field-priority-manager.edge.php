@props([
    'field_definitions' => [],
    'show_advanced' => false
])

@php
    use system\unified_field_definitions;
    
    // Get all field definitions if not provided
    if (empty($field_definitions)) {
        $field_definitions = unified_field_definitions::get_all_fields();
    }
    
    // Group fields by category
    $categories = [];
    foreach ($field_definitions as $field_name => $definition) {
        $category = $definition['category'] ?? 'other';
        $categories[$category][] = [
            'name' => $field_name,
            'definition' => $definition
        ];
    }
    
    // Sort categories and fields
    ksort($categories);
    foreach ($categories as $category => $fields) {
        usort($categories[$category], function($a, $b) {
            $priorityA = $a['definition']['matching']['priority'] ?? 10;
            $priorityB = $b['definition']['matching']['priority'] ?? 10;
            return $priorityA <=> $priorityB;
        });
    }
@endphp

<div class="bg-white border border-gray-200 rounded-lg p-4" x-data="{ 
    showAdvanced: {{ $show_advanced ? 'true' : 'false' }},
    editingField: null,
    fieldChanges: {},
    
    editField(fieldName) {
        this.editingField = fieldName;
    },
    
    saveField(fieldName) {
        // Save field changes via HTMX
        this.editingField = null;
    },
    
    cancelEdit() {
        this.editingField = null;
        this.fieldChanges = {};
    },
    
    updatePriority(fieldName, priority) {
        if (!this.fieldChanges[fieldName]) {
            this.fieldChanges[fieldName] = {};
        }
        this.fieldChanges[fieldName].priority = priority;
    },
    
    toggleFieldEnabled(fieldName, enabled) {
        if (!this.fieldChanges[fieldName]) {
            this.fieldChanges[fieldName] = {};
        }
        this.fieldChanges[fieldName].enabled = enabled;
    }
}">
    
    <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-900">Field Matching Priorities</h4>
        <div class="flex items-center space-x-2">
            <button type="button" 
                    @click="showAdvanced = !showAdvanced"
                    class="text-xs text-blue-600 hover:text-blue-800 underline">
                <span x-show="!showAdvanced">Show Advanced Settings</span>
                <span x-show="showAdvanced">Hide Advanced Settings</span>
            </button>
        </div>
    </div>
    
    <div class="text-xs text-gray-600 mb-4">
        Lower priority numbers have higher precedence. Fields with priority 0 will be matched first, then priority 1, etc.
    </div>
    
    <!-- Category-based field display -->
    <div class="space-y-4">
        @foreach($categories as $category => $fields)
            <div class="border border-gray-200 rounded-lg">
                <div class="bg-gray-50 px-3 py-2 border-b border-gray-200">
                    <h5 class="text-sm font-medium text-gray-700 capitalize">{{ ucwords(str_replace('_', ' ', $category)) }}</h5>
                </div>
                <div class="p-3 space-y-2">
                    @foreach($fields as $field)
                        @php
                            $field_name = $field['name'];
                            $definition = $field['definition'];
                            $matching = $definition['matching'] ?? [];
                            $enabled = $matching['enabled'] ?? false;
                            $priority = $matching['priority'] ?? 10;
                            $confidence_threshold = $matching['confidence_threshold'] ?? 70;
                        @endphp
                        
                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded border"
                             :class="editingField === '{{ $field_name }}' ? 'ring-2 ring-blue-500' : ''">
                            
                            <!-- Field info -->
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                               id="enabled_{{ $field_name }}"
                                               name="field_priorities[{{ $field_name }}][enabled]"
                                               {{ $enabled ? 'checked' : '' }}
                                               @change="toggleFieldEnabled('{{ $field_name }}', $event.target.checked)"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="enabled_{{ $field_name }}" class="ml-2 text-sm text-gray-700">
                                            {{ $definition['label'] ?? ucwords(str_replace('_', ' ', $field_name)) }}
                                        </label>
                                    </div>
                                    
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                            Priority: {{ $priority }}
                                        </span>
                                        @if($enabled)
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Enabled</span>
                                        @else
                                            <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Disabled</span>
                                        @endif
                                    </div>
                                </div>
                                
                                <div class="text-xs text-gray-500 mt-1">
                                    Patterns: {{ implode(', ', array_slice($definition['patterns'] ?? [], 0, 3)) }}
                                    @if(count($definition['patterns'] ?? []) > 3)
                                        <span class="text-gray-400">... and {{ count($definition['patterns']) - 3 }} more</span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Priority controls -->
                            <div class="flex items-center space-x-2">
                                <div x-show="editingField !== '{{ $field_name }}'">
                                    <button type="button" 
                                            @click="editField('{{ $field_name }}')"
                                            class="text-xs text-blue-600 hover:text-blue-800 underline">
                                        Edit
                                    </button>
                                </div>
                                
                                <div x-show="editingField === '{{ $field_name }}'" class="flex items-center space-x-2">
                                    <label class="text-xs text-gray-600">Priority:</label>
                                    <select name="field_priorities[{{ $field_name }}][priority]"
                                            @change="updatePriority('{{ $field_name }}', $event.target.value)"
                                            class="text-xs border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                                        @for($i = 0; $i <= 20; $i++)
                                            <option value="{{ $i }}" {{ $priority == $i ? 'selected' : '' }}>{{ $i }}</option>
                                        @endfor
                                    </select>
                                    
                                    <button type="button" 
                                            @click="saveField('{{ $field_name }}')"
                                            hx-post="{{ APP_ROOT }}/api/unified_field_definitions/update_field_priority"
                                            hx-include="closest form"
                                            hx-target="#field-priority-status"
                                            class="text-xs bg-green-100 text-green-700 hover:bg-green-200 px-2 py-1 rounded">
                                        Save
                                    </button>
                                    
                                    <button type="button" 
                                            @click="cancelEdit()"
                                            class="text-xs bg-gray-100 text-gray-700 hover:bg-gray-200 px-2 py-1 rounded">
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced settings -->
                        <div x-show="showAdvanced && editingField === '{{ $field_name }}'" 
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             class="ml-4 p-3 bg-blue-50 rounded border border-blue-200">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                                <div>
                                    <label class="block text-gray-700 font-medium mb-1">Confidence Threshold:</label>
                                    <select name="field_priorities[{{ $field_name }}][confidence_threshold]"
                                            class="w-full border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                                        @for($i = 30; $i <= 100; $i += 10)
                                            <option value="{{ $i }}" {{ $confidence_threshold == $i ? 'selected' : '' }}>{{ $i }}%</option>
                                        @endfor
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-700 font-medium mb-1">Matching Options:</label>
                                    <div class="space-y-1">
                                        <label class="flex items-center">
                                            <input type="checkbox" 
                                                   name="field_priorities[{{ $field_name }}][exact_match_only]"
                                                   {{ ($matching['exact_match_only'] ?? false) ? 'checked' : '' }}
                                                   class="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <span class="ml-1 text-gray-700">Exact match only</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" 
                                                   name="field_priorities[{{ $field_name }}][fuzzy_matching]"
                                                   {{ ($matching['fuzzy_matching'] ?? false) ? 'checked' : '' }}
                                                   class="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <span class="ml-1 text-gray-700">Enable fuzzy matching</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach
    </div>
    
    <!-- Status area -->
    <div id="field-priority-status" class="mt-4"></div>
    
    <!-- Bulk actions -->
    <div class="mt-4 pt-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-xs text-gray-600">
                Changes are saved individually. Use the Edit button next to each field to modify its settings.
            </div>
            <button type="button" 
                    hx-post="{{ APP_ROOT }}/api/unified_field_definitions/reset_all_priorities"
                    hx-confirm="Are you sure you want to reset all field priorities to defaults?"
                    hx-target="#field-priority-status"
                    class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-3 py-1 rounded">
                Reset All to Defaults
            </button>
        </div>
    </div>
</div>
