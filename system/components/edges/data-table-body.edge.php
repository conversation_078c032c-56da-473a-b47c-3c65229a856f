@props([
    'items' => [], // data array of items
    'columns' => [], // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
    'column_preferences' => [],
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],
    'items_per_page' => 30, //max items to display before pagination
    'data_source_type' => 'hardcoded',
    'data_source_id' => null
])
@use system\data_table_storage

@php
    // Ensure arrays are properly initialized
    $items = $items ?? [];
    $columns = $columns ?? [];
    $column_preferences = $column_preferences ?? [];
@endphp

<tbody class="bg-white data_table_body">
    @if (empty($items))
        <!-- No items/rows configured fallback -->
        <tr>
            <td colspan="{{ count($columns) > 0 ? count($columns) : 1 }}" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
                    <p class="text-gray-500 mb-4">
                        There are no rows configured or available for this table.
                    </p>
                    @if($data_source_type === 'hardcoded')
                        <p class="text-sm text-gray-400">
                            This table is configured to use hardcoded data, but no data has been provided.
                        </p>
                    @elseif($data_source_type === 'data_source' && $data_source_id)
                        <p class="text-sm text-gray-400">
                            This table is configured to use data source ID: {{ $data_source_id }}, but no data was returned.
                        </p>
                    @else
                        <p class="text-sm text-gray-400">
                            No data source has been configured for this table.
                        </p>
                    @endif
                </div>
            </td>
        </tr>
    @else
        {{ $slot }}
    @endif
</tbody>
