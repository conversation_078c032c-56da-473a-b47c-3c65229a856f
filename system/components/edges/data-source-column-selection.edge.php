@props([
    'table_columns' => [],
    'table_display_info' => [],
    'selected_columns' => [],
    'column_aliases' => []
])

@if(empty($table_columns))
    <div class="text-center py-6 text-gray-500">
        Add tables to select columns
    </div>
@else
    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900">Column Selection</h4>
            <div class="flex items-center space-x-2">
                <button type="button"
                        hx-post="{{ APP_ROOT }}/api/data_sources/select_all_columns_with_preview"
                        hx-target="#column-selection-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        class="text-xs text-indigo-600 hover:text-indigo-800">
                    Select All
                </button>
                <span class="text-gray-300">|</span>
                <button type="button"
                        hx-post="{{ APP_ROOT }}/api/data_sources/clear_all_columns_with_preview"
                        hx-target="#column-selection-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        class="text-xs text-indigo-600 hover:text-indigo-800">
                    Clear All
                </button>
            </div>
        </div>
        <div class="grid grid-cols-4 gap-4">
            @foreach($table_columns as $display_key => $columns)
                @php
                    $display_info = $table_display_info[$display_key] ?? [
                        'table_name' => $display_key,
                        'alias' => null,
                        'display_name' => $display_key
                    ];
                    $actual_table_name = $display_info['table_name'];
                    $table_alias = $display_info['alias'];
                @endphp
                <div class="border border-gray-200 rounded-lg">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                        <div class="flex items-center justify-between">
                            <h5 class="text-sm font-medium text-gray-900">{{ $display_info['display_name'] }}</h5>
                            <div class="flex items-center space-x-2">
                                <button type="button"
                                        hx-post="{{ APP_ROOT }}/api/data_sources/select_table_columns_with_preview"
                                        hx-target="#column-selection-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-vals='{"table_name": "{{ $actual_table_name }}", "table_alias": "{{ $table_alias }}"}'
                                        class="text-xs text-indigo-600 hover:text-indigo-800">
                                    Select All
                                </button>
                                <span class="text-gray-300">|</span>
                                <button type="button"
                                        hx-post="{{ APP_ROOT }}/api/data_sources/clear_table_columns_with_preview"
                                        hx-target="#column-selection-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-vals='{"table_name": "{{ $actual_table_name }}", "table_alias": "{{ $table_alias }}"}'
                                        class="text-xs text-indigo-600 hover:text-indigo-800">
                                    Clear
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead>
                            <tr>
                                <th scope="col" class="relative px-7 sm:w-12 sm:px-6">
                                    <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                        <input type="checkbox"
                                               class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                        <svg viewBox="0 0 14 14" fill="none"
                                             class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                            <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                                  stroke-linejoin="round"
                                                  class="opacity-0 group-has-[:checked]:opacity-100"/>
                                            <path d="M3 7H11" stroke-width="2" stroke-linecap="round"
                                                  stroke-linejoin="round"
                                                  class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                        </svg>
                                    </div>
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Title
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Type
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Flags
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Alias
                                </th>

                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                            @foreach($columns as $column)
                                @php
                                    // Use alias name if available, otherwise use table name
                                    $table_ref = $table_alias ?: $actual_table_name;
                                    $column_key = $table_ref . '.' . $column['Field'];
                                    $is_selected = in_array($column_key, $selected_columns);
                                    $column_alias = $column_aliases[$column_key] ?? '';
                                @endphp


                                <tr class="group has-[:checked]:bg-gray-50">

                                    <td class="relative px-7 sm:w-12 sm:px-6">
                                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>
                                        <!-- Checkbox -->
                                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                            <input
                                                    type="checkbox"
                                                    name="selected_columns[]"
                                                    value="{{ $column_key }}"
                                                    {{ $is_selected ? 'checked' : '' }}
                                                    hx-post="{{ APP_ROOT }}/api/data_sources/update_column_selection"
                                                    hx-target="#column-selection-container"
                                                    hx-swap="innerHTML"
                                                    hx-include="form"
                                                    hx-trigger="change"
                                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                            <svg viewBox="0 0 14 14" fill="none"
                                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                                      stroke-linejoin="round"
                                                      class="opacity-0 group-has-[:checked]:opacity-100"/>
                                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round"
                                                      stroke-linejoin="round"
                                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                            </svg>
                                        </div>
                                    </td>
                                    <!-- Column Info -->
                                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">{{ $column['Field'] }}</td>

                                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">{{ $column['Type'] }}</td>
                                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                                        @if($column['Key'] === 'PRI')
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                        PK
                                                    </span>
                                        @endif
                                    </td><!-- Alias Input (only show if column is selected) -->
                                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                                        @if($is_selected)
                                            <x-forms-input
                                                    name="column_aliases[{{ $column_key }}]"
                                                    :value="$column_alias"
                                                    borderless='true'
                                                    placeholder="Column Alias (optional) e.g., full_name, user_email"
                                                    class_suffix="text-sm"
                                                    hx-post="{{ APP_ROOT }}/api/data_sources/update_column_alias"
                                                    hx-target="#query-preview-container"
                                                    hx-swap="innerHTML"
                                                    hx-include="form"
                                                    hx-trigger="input changed delay:500ms"
                                            />
                                        @else
                                            <x-forms-input
                                                    borderless='true'
                                                    disabled
                                            />
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Selection Summary -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div class="flex items-center">
                <svg class="h-4 w-4 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm text-blue-800">
                    <span id="selected-count">{{ count($selected_columns) }}</span> columns selected
                    @if(count($selected_columns) === 0)
                        <span class="text-blue-600">
                            (No columns selected - will default to all columns with table prefixes when querying)
                        </span>
                    @endif
                </span>
            </div>
        </div>

        <!-- Hidden input to store selected columns -->
        <input type="hidden" name="selected_columns_json" id="selected-columns-json-input"
               value="{{ json_encode($selected_columns) }}">
    </div>
@endif

<script>
    // Update selected columns JSON input when checkboxes change
    document.addEventListener('DOMContentLoaded', function () {
        updateSelectedColumnsJsonInput();
    });

    document.addEventListener('htmx:afterSwap', function (event) {
        if (event.target.id === 'column-selection-container') {
            updateSelectedColumnsJsonInput();
        }
    });

    function updateSelectedColumnsJsonInput() {
        try {
            // Get all checked column checkboxes
            const checkedColumns = document.querySelectorAll('#column-selection-container input[name="selected_columns[]"]:checked');
            const selectedColumns = Array.from(checkedColumns).map(input => input.value);

            // Update the JSON input
            const jsonInput = document.getElementById('selected-columns-json-input');
            if (jsonInput) {
                jsonInput.value = JSON.stringify(selectedColumns);
                console.log('Updated selected_columns_json input:', selectedColumns);
            }
        } catch (error) {
            console.error('Error updating selected columns JSON input:', error);
        }
    }
</script>

<!-- Column count updated via HTMX responses -->


