@props([
    'table_name' => '',
    'table_index' => 0,
    'available_tables' => []
])

@php
    // Find table info
    $table_info = null;
    foreach ($available_tables as $table) {
        if ($table['name'] === $table_name) {
            $table_info = $table;
            break;
        }
    }
@endphp

<div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
    <div class="flex-1">
        <div class="flex items-center space-x-2">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            
            <div class="flex-1">
                <div class="text-sm font-medium text-gray-900">
                    {{ $table_info['display_name'] ?? $table_name }}
                </div>
                <div class="text-xs text-gray-500">
                    {{ $table_name }} 
                    @if($table_info)
                        ({{ number_format($table_info['row_count']) }} rows)
                    @endif
                </div>
            </div>
        </div>
        
        @if($table_info && !empty($table_info['description']))
            <p class="mt-2 text-xs text-gray-600">{{ $table_info['description'] }}</p>
        @endif
    </div>
    
    <button type="button"
            hx-post="{{ APP_ROOT }}/api/data_sources/remove_explicit_table"
            hx-target="closest div"
            hx-swap="outerHTML"
            hx-include="form"
            hx-vals='{"table_index": "{{ $table_index }}", "table_name": "{{ $table_name }}"}'
            class="inline-flex items-center p-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
    </button>
    
    <!-- Hidden input to store the table name -->
    <input type="hidden" name="explicit_tables[{{ $table_index }}]" value="{{ $table_name }}">
</div>
