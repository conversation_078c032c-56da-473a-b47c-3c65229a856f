@props([
    'mapping_method' => 'manual',
    'available_tables' => [],
    'selected_table_patterns' => [],
    'selected_explicit_tables' => [],
    'reference_table' => '',
    'column_mappings' => [],
    'unified_mappings' => []
])

@php
    use system\data_source_manager;
    use system\unified_field_mapper;
    
    // Get all tables that would be included based on patterns and explicit selection
    $included_tables = [];
    
    // Add tables matching wildcard patterns
    foreach ($selected_table_patterns as $pattern) {
        $pattern_regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';
        foreach ($available_tables as $table) {
            if (preg_match($pattern_regex, $table['name']) && !in_array($table['name'], $included_tables)) {
                $included_tables[] = $table['name'];
            }
        }
    }

    // Debug: Log pattern matching results
    $debug_info = [];
    foreach ($selected_table_patterns as $pattern) {
        $pattern_regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';
        $matches = [];
        foreach ($available_tables as $table) {
            if (preg_match($pattern_regex, $table['name'])) {
                $matches[] = $table['name'];
            }
        }
        $debug_info[$pattern] = $matches;
    }
    
    // Add explicit tables
    foreach ($selected_explicit_tables as $table_name) {
        if (!in_array($table_name, $included_tables)) {
            $included_tables[] = $table_name;
        }
    }
    
    // Get column information for all included tables
    $table_columns = [];
    $all_columns = [];
    foreach ($included_tables as $table_name) {
        $table_info = data_source_manager::get_table_info($table_name);
        if ($table_info && !empty($table_info['columns'])) {
            $table_columns[$table_name] = $table_info['columns'];
            foreach ($table_info['columns'] as $column) {
                $column_key = $table_name . '.' . $column['Field'];
                $all_columns[$column_key] = [
                    'table' => $table_name,
                    'column' => $column['Field'],
                    'type' => $column['Type'],
                    'display' => $table_name . '.' . $column['Field']
                ];
            }
        }
    }
    
    // Get reference table columns if specified
    $reference_columns = [];
    if (!empty($reference_table)) {
        $ref_table_info = data_source_manager::get_table_info($reference_table);
        if ($ref_table_info && !empty($ref_table_info['columns'])) {
            $reference_columns = $ref_table_info['columns'];
        }
    }
    
    // Generate unified field mapper suggestions if using that method
    $unified_suggestions = [];
    if ($mapping_method === 'unified_field_mapper') {
        $all_column_names = array_keys($all_columns);
        $unified_suggestions = unified_field_mapper::suggest_field_mappings($all_column_names);

        // If no existing unified_mappings configuration, set defaults
        if (empty($unified_mappings)) {
            $unified_mappings = ['min_confidence' => 75];
        }
    }
@endphp

<div class="bg-white border border-gray-200 rounded-lg p-4">
    <h4 class="text-sm font-medium text-gray-900 mb-4">Column Mapping Configuration</h4>
    
    @if(empty($included_tables))
        <div class="text-center py-6 text-gray-500">
            No tables selected. Add wildcard patterns or explicit tables to configure column mappings.
        </div>
    @else
        @if($mapping_method === 'manual')
            <!-- Manual Mapping Interface -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <p class="text-sm text-gray-600">
                        Define custom mappings between source columns and output columns.
                    </p>
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_sources/add_column_mapping"
                            hx-target="#column-mappings-container"
                            hx-swap="beforeend"
                            hx-include="form"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Add Mapping
                    </button>
                </div>
                
                <div id="column-mappings-container" class="space-y-3">
                    @if(empty($column_mappings))
                        <div class="text-center py-4 text-gray-500" id="no-mappings-message">
                            No column mappings defined. Add mappings to specify how columns should be merged.
                        </div>
                    @else
                        @foreach($column_mappings as $index => $mapping)
                            <x-column-mapping-item
                                :mapping="$mapping"
                                :mapping_index="$index"
                                :all_columns="$all_columns"
                                :reference_columns="$reference_columns"
                            />
                        @endforeach
                    @endif
                </div>
            </div>
            
        @elseif($mapping_method === 'unified_field_mapper')
            <!-- Unified Field Mapper Interface -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <p class="text-sm text-gray-600">
                        Automatic intelligent mapping using unified field mapper rules. All mappings with sufficient confidence will be applied automatically.
                    </p>

                    <!-- Configuration Options -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-700">Min Confidence:</label>
                            <select name="min_confidence"
                                    class="text-sm rounded border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    hx-post="{{ APP_ROOT }}/api/data_sources/update_mapping_method"
                                    hx-target="#column-mapping-configuration"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change">
                                <option value="50" {{ ($unified_mappings['min_confidence'] ?? 75) == 50 ? 'selected' : '' }}>50%</option>
                                <option value="75" {{ ($unified_mappings['min_confidence'] ?? 75) == 75 ? 'selected' : '' }}>75%</option>
                                <option value="90" {{ ($unified_mappings['min_confidence'] ?? 75) == 90 ? 'selected' : '' }}>90%</option>
                            </select>
                        </div>
                    </div>
                </div>

                @if(!empty($unified_suggestions))
                    <div class="space-y-4" x-data="{
                        showAlternatives: {},
                        overrides: {},
                        toggleAlternatives(column) {
                            this.showAlternatives[column] = !this.showAlternatives[column];
                        },
                        selectAlternative(column, alternative) {
                            this.overrides[column] = alternative;
                            this.showAlternatives[column] = false;
                        },
                        clearOverride(column) {
                            delete this.overrides[column];
                        }
                    }">
                        <div class="flex items-center justify-between">
                            <h5 class="text-sm font-medium text-gray-900">Intelligent Field Mapping</h5>
                            <div class="text-xs text-gray-500">
                                Priority-based matching with contextual scoring
                            </div>
                        </div>

                        @php
                            $min_confidence = $unified_mappings['min_confidence'] ?? 75;
                            $applied_mappings = [];
                            $skipped_mappings = [];
                            $manual_overrides = $unified_mappings['overrides'] ?? [];

                            foreach ($unified_suggestions as $source_column => $suggestion) {
                                // Check for manual override
                                if (isset($manual_overrides[$source_column])) {
                                    $suggestion = array_merge($suggestion, $manual_overrides[$source_column]);
                                    $suggestion['is_override'] = true;
                                }

                                if ($suggestion['confidence'] >= $min_confidence || ($suggestion['is_override'] ?? false)) {
                                    $applied_mappings[$source_column] = $suggestion;
                                } else {
                                    $skipped_mappings[$source_column] = $suggestion;
                                }
                            }
                        @endphp

                        @if(!empty($applied_mappings))
                            <div class="space-y-3">
                                <h6 class="text-xs font-medium text-green-800 bg-green-100 px-2 py-1 rounded">
                                    ✓ Applied Mappings ({{ count($applied_mappings) }})
                                </h6>
                                @foreach($applied_mappings as $source_column => $suggestion)
                                    <div class="border border-green-200 rounded-lg bg-green-50">
                                        <!-- Main mapping display -->
                                        <div class="p-3">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-3">
                                                    @if($suggestion['is_override'] ?? false)
                                                        <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                    @endif
                                                    <div class="flex-1">
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-sm font-medium text-gray-900">{{ $source_column }}</span>
                                                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                            </svg>
                                                            <span class="text-sm font-semibold {{ ($suggestion['is_override'] ?? false) ? 'text-blue-700' : 'text-green-700' }}">
                                                                {{ $suggestion['label'] ?? $suggestion['category'] }}
                                                            </span>
                                                            @if($suggestion['is_override'] ?? false)
                                                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Manual Override</span>
                                                            @endif
                                                        </div>
                                                        <div class="text-xs text-gray-500 mt-1">
                                                            @if(isset($suggestion['final_score']))
                                                                Score: {{ $suggestion['final_score'] }}
                                                                (Confidence: {{ $suggestion['confidence'] }}%, Priority: {{ $suggestion['priority'] ?? 'N/A' }}, Context: {{ $suggestion['contextual_score'] ?? 'N/A' }}) |
                                                            @else
                                                                Confidence: {{ $suggestion['confidence'] }}% |
                                                            @endif
                                                            Maps to: {{ implode(', ', $suggestion['normalized_fields']) }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    @if(!empty($suggestion['alternatives']))
                                                        <button type="button"
                                                                @click="toggleAlternatives('{{ $source_column }}')"
                                                                class="text-xs text-blue-600 hover:text-blue-800 underline">
                                                            <span x-show="!showAlternatives['{{ $source_column }}']">Show alternatives ({{ count($suggestion['alternatives']) }})</span>
                                                            <span x-show="showAlternatives['{{ $source_column }}']">Hide alternatives</span>
                                                        </button>
                                                    @endif
                                                    @if($suggestion['is_override'] ?? false)
                                                        <button type="button"
                                                                @click="clearOverride('{{ $source_column }}')"
                                                                class="text-xs text-red-600 hover:text-red-800 underline">
                                                            Clear override
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Alternatives section -->
                                        @if(!empty($suggestion['alternatives']))
                                            <div x-show="showAlternatives['{{ $source_column }}']"
                                                 x-transition:enter="transition ease-out duration-200"
                                                 x-transition:enter-start="opacity-0 transform scale-95"
                                                 x-transition:enter-end="opacity-100 transform scale-100"
                                                 class="border-t border-green-200 bg-white p-3">
                                                <h6 class="text-xs font-medium text-gray-700 mb-2">Alternative Matches:</h6>
                                                <div class="space-y-2">
                                                    @foreach($suggestion['alternatives'] as $alt_index => $alternative)
                                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                                            <div class="flex-1">
                                                                <div class="flex items-center space-x-2">
                                                                    <span class="text-sm font-medium text-gray-700">{{ $alternative['label'] }}</span>
                                                                    <span class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                                                                        Score: {{ $alternative['final_score'] }}
                                                                    </span>
                                                                </div>
                                                                <div class="text-xs text-gray-500 mt-1">
                                                                    Confidence: {{ $alternative['confidence'] }}%, Priority: {{ $alternative['priority'] }}, Context: {{ $alternative['contextual_score'] }} |
                                                                    Maps to: {{ implode(', ', $alternative['normalized_fields']) }}
                                                                </div>
                                                            </div>
                                                            <button type="button"
                                                                    @click="selectAlternative('{{ $source_column }}', {{ json_encode($alternative) }})"
                                                                    class="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded">
                                                                Select
                                                            </button>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Hidden inputs to store applied mapping data -->
                                    <input type="hidden" name="unified_mappings[applied][{{ $source_column }}][category]" value="{{ $suggestion['category'] }}">
                                    <input type="hidden" name="unified_mappings[applied][{{ $source_column }}][field_name]" value="{{ $suggestion['field_name'] ?? $suggestion['category'] }}">
                                    <input type="hidden" name="unified_mappings[applied][{{ $source_column }}][confidence]" value="{{ $suggestion['confidence'] }}">
                                    <input type="hidden" name="unified_mappings[applied][{{ $source_column }}][final_score]" value="{{ $suggestion['final_score'] ?? $suggestion['confidence'] }}">
                                    <input type="hidden" name="unified_mappings[applied][{{ $source_column }}][normalized_fields]" value="{{ json_encode($suggestion['normalized_fields']) }}">
                                    @if($suggestion['is_override'] ?? false)
                                        <input type="hidden" name="unified_mappings[overrides][{{ $source_column }}]" value="{{ json_encode($suggestion) }}">
                                    @endif
                                @endforeach
                            </div>
                        @endif

                        @if(!empty($skipped_mappings))
                            <div class="space-y-3">
                                <h6 class="text-xs font-medium text-yellow-800 bg-yellow-100 px-2 py-1 rounded">
                                    ⚠ Skipped (Low Confidence) ({{ count($skipped_mappings) }})
                                </h6>
                                @foreach($skipped_mappings as $source_column => $suggestion)
                                    <div class="border border-yellow-200 rounded-lg bg-yellow-50">
                                        <div class="p-3">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-3">
                                                    <svg class="h-5 w-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                    </svg>
                                                    <div class="flex-1">
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-sm font-medium text-gray-900">{{ $source_column }}</span>
                                                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                            </svg>
                                                            <span class="text-sm text-yellow-700">{{ $suggestion['label'] ?? $suggestion['category'] }}</span>
                                                        </div>
                                                        <div class="text-xs text-gray-500 mt-1">
                                                            @if(isset($suggestion['final_score']))
                                                                Score: {{ $suggestion['final_score'] }}
                                                                (Confidence: {{ $suggestion['confidence'] }}%, Priority: {{ $suggestion['priority'] ?? 'N/A' }}, Context: {{ $suggestion['contextual_score'] ?? 'N/A' }}) |
                                                            @else
                                                                Confidence: {{ $suggestion['confidence'] }}% |
                                                            @endif
                                                            Below {{ $min_confidence }}% threshold | Would map to: {{ implode(', ', $suggestion['normalized_fields']) }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <button type="button"
                                                            @click="selectAlternative('{{ $source_column }}', {{ json_encode(array_merge($suggestion, ['is_override' => true])) }})"
                                                            class="text-xs bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded">
                                                        Force Apply
                                                    </button>
                                                    @if(!empty($suggestion['alternatives']))
                                                        <button type="button"
                                                                @click="toggleAlternatives('{{ $source_column }}')"
                                                                class="text-xs text-blue-600 hover:text-blue-800 underline">
                                                            <span x-show="!showAlternatives['{{ $source_column }}']">Show alternatives ({{ count($suggestion['alternatives']) }})</span>
                                                            <span x-show="showAlternatives['{{ $source_column }}']">Hide alternatives</span>
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Alternatives section for skipped mappings -->
                                        @if(!empty($suggestion['alternatives']))
                                            <div x-show="showAlternatives['{{ $source_column }}']"
                                                 x-transition:enter="transition ease-out duration-200"
                                                 x-transition:enter-start="opacity-0 transform scale-95"
                                                 x-transition:enter-end="opacity-100 transform scale-100"
                                                 class="border-t border-yellow-200 bg-white p-3">
                                                <h6 class="text-xs font-medium text-gray-700 mb-2">Alternative Matches:</h6>
                                                <div class="space-y-2">
                                                    @foreach($suggestion['alternatives'] as $alt_index => $alternative)
                                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                                            <div class="flex-1">
                                                                <div class="flex items-center space-x-2">
                                                                    <span class="text-sm font-medium text-gray-700">{{ $alternative['label'] }}</span>
                                                                    <span class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                                                                        Score: {{ $alternative['final_score'] }}
                                                                    </span>
                                                                </div>
                                                                <div class="text-xs text-gray-500 mt-1">
                                                                    Confidence: {{ $alternative['confidence'] }}%, Priority: {{ $alternative['priority'] }}, Context: {{ $alternative['contextual_score'] }} |
                                                                    Maps to: {{ implode(', ', $alternative['normalized_fields']) }}
                                                                </div>
                                                            </div>
                                                            <button type="button"
                                                                    @click="selectAlternative('{{ $source_column }}', {{ json_encode(array_merge($alternative, ['is_override' => true])) }})"
                                                                    class="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded">
                                                                Select
                                                            </button>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        <!-- Store configuration -->
                        <input type="hidden" name="unified_mappings[min_confidence]" value="{{ $min_confidence }}">

                        <!-- Dynamic override storage -->
                        <div x-data="{ overrideData: {} }" x-init="
                            $watch('overrides', (value) => {
                                overrideData = value;
                                // Update hidden inputs for overrides
                                Object.keys(value).forEach(column => {
                                    const override = value[column];
                                    const input = document.querySelector(`input[name='unified_mappings[overrides][${column}]']`);
                                    if (input) {
                                        input.value = JSON.stringify(override);
                                    } else {
                                        // Create new hidden input
                                        const newInput = document.createElement('input');
                                        newInput.type = 'hidden';
                                        newInput.name = `unified_mappings[overrides][${column}]`;
                                        newInput.value = JSON.stringify(override);
                                        document.querySelector('form').appendChild(newInput);
                                    }
                                });
                            })
                        ">
                            <!-- Override inputs will be dynamically created here -->
                        </div>
                    </div>
                @else
                    <div class="text-center py-6 text-gray-500">
                        No mapping suggestions available. The unified field mapper couldn't identify mappable columns.
                    </div>
                @endif
            </div>
        @endif
        
        <!-- Data Preview Section -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <h5 class="text-sm font-medium text-gray-900">Data Preview</h5>
                <button type="button"
                        hx-post="{{ APP_ROOT }}/api/data_sources/preview_merged_data"
                        hx-target="#merged-data-preview"
                        hx-swap="innerHTML"
                        hx-include="form"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Update Preview
                </button>
            </div>
            
            <div id="merged-data-preview" class="border border-gray-200 rounded-lg">
                <div class="text-center py-8 text-gray-500">
                    Click "Update Preview" to see merged data with current mapping configuration.
                </div>
            </div>
        </div>
        
        <!-- Table Summary -->
        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
            <h6 class="text-sm font-medium text-blue-900 mb-2">Included Tables ({{ count($included_tables) }})</h6>
            <div class="flex flex-wrap gap-2">
                @foreach($included_tables as $table_name)
                    @php
                        $table_info = null;
                        foreach ($available_tables as $table) {
                            if ($table['name'] === $table_name) {
                                $table_info = $table;
                                break;
                            }
                        }
                        $column_count = isset($table_columns[$table_name]) ? count($table_columns[$table_name]) : 0;
                    @endphp
                    <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-xs">
                        {{ $table_info['display_name'] ?? $table_name }}
                        <span class="text-blue-600">({{ $column_count }} cols)</span>
                    </span>
                @endforeach
            </div>
        </div>
    @endif
</div>
