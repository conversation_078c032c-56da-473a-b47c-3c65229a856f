@props([
    'title' => 'add_flag',
    'description' => 'A form to add a flag',
    'content' => [], // An array of [('header'|'body'|'footer') => 'content']
    'sub_id' => '',
    'sub_num' => '',
    'labels' => [
         ['name' => 'Email', 'value' => 'email'],
         ['name' => 'Phone', 'value' => 'phone'],
         ['name' => 'In-person', 'value' => 'in-person'],
         ['name' => 'Phone', 'value' => 'phone'],
    ],
])
@php
    $label_html = 'Add Note: <span class="text-blue-600  font-bold">' . $endcust_name . '</span>' . ' Subscription: <span class="text-blue-600 font-bold">' . $target_reference . '</span>';
@endphp
<div class="w-[672px]">
    <x-layout-card :label="$label_html">
        <form
                x-data="{ labelled: null }"
                action="#"
                class="relative"
                hx-replace="none"
                hx-post="{{ APP_ROOT }}/api/history/add_flag"
        >
            <!-- Add hidden input field to store the label value -->
            <input type="hidden" name="label" x-bind:value="labelled ? labelled.value : ''">
            <input type="hidden" name="target" value="{{ $target }}">
            <input type="hidden" name="target_reference" value="{{ $target_reference }}">
            <input type="hidden" name="endcust_name" value="{{ $endcust_name }}">
            <input type="hidden" name="customer_csn" value="{{ $customer_csn }}">
            <input type="hidden" name="email_address" value="{{ $email_address }}">
            <input type="hidden" name="user_id" value="{{ $user_id }}">
            <div class="rounded-lg bg-white outline outline-1 -outline-offset-1 outline-gray-300 focus-within:outline focus-within:outline-2 focus-within:-outline-offset-2 focus-within:outline-indigo-600">

                <label for="message" class="sr-only">message </label>
                <textarea
                        id="message"
                        name="message"
                        rows="2"
                        placeholder="Write a message..."
                        class="block w-full resize-none px-3 py-1.5 text-base text-gray-900 placeholder:text-gray-400 focus:outline focus:outline-0 sm:text-sm/6"
                >
                </textarea>

                <div aria-hidden="true">
                    <div class="py-2">
                        <div class="h-9"></div>
                    </div>
                    <div class="h-px"></div>
                    <div class="py-2">
                        <div class="py-px">
                            <div class="h-9"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="absolute inset-x-px bottom-0">
                <div class="flex flex-nowrap justify-end space-x-2 px-2 py-2 sm:px-3">
                    <div x-data="{ open: false }" class="relative shrink-0">
                        <label class="sr-only"> Add a label </label>
                        <button @click="open = !open" type="button"
                                class="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3">
                            @icon('tag')
                            <span x-text="labelled ? labelled.name : 'Label'" class="hidden truncate sm:ml-2 sm:block">Label</span>
                        </button>

                        <div
                                x-show="open"
                                @click.outside="open = false"
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="opacity-0 transform scale-95"
                                x-transition:enter-end="opacity-100 transform scale-100"
                                x-transition:leave="transition ease-in duration-75"
                                x-transition:leave-start="opacity-100 transform scale-100"
                                x-transition:leave-end="opacity-0 transform scale-95"
                                class="absolute right-0 z-10 mt-1 max-h-56 w-52 overflow-auto rounded-lg bg-white py-3 text-base shadow outline outline-1 outline-black/5 focus:outline-none"
                                style="display: none"
                        >
                            @foreach ($labels as $label)
                                <button
                                        @click="labelled = { name: '{{ $label['name'] }}', value: '{{ $label['value'] }}' }; open = false"
                                        type="button"
                                        class="text-gray-900 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100 focus:outline-none focus:ring-0"
                                >
                                    {{ $label['name'] }}
                                </button>
                            @endforeach
                            <button
                                    @click="labelled = null; open = false"
                                    type="button"
                                    class="text-gray-900 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100 focus:outline-none focus:ring-0"
                            >
                                Unlabelled
                            </button>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between space-x-3 border-t border-gray-200 px-2 py-2 sm:px-3">
                    <div class="flex">
                        <button
                                type="button"
                                class="group -my-2 -ml-2 inline-flex items-center rounded-full px-3 py-2 text-left text-gray-400"
                        >
                            @icon('paperclip')
                            <span class="text-sm italic text-gray-500 group-hover:text-gray-600">
            Attach a file
          </span>
                        </button>
                    </div>
                    <div class="shrink-0">
                        <button
                                type="submit"
                                class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                        >
                            Create
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </x-layout-card>
</div>

