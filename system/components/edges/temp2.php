<div id="column-manager-autodesk_subscriptions" x-data="{
         open: false,
         newColumnName: '',
         showAddColumn: false
     }">
                     });
                 });
             }
         });

         $watch('open', value =&gt; {
             if (value) {
                 $nextTick(() =&gt; {
                     const container = $refs.columnList;
                     if (container &amp;&amp; window.Sortable &amp;&amp; !container._sortable) {
                         // Re-initialize if needed
                     }
                 });
             }
         });
     " class="relative inline-block text-left" @click.away="open = false">


    <button type="button" @click="open = !open"
            class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            aria-expanded="false" aria-haspopup="true">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z"></path>
        </svg>
        Columns
        <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5"></path>
        </svg>
    </button>


    <div x-show="open" x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-20 mt-2 w-96 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
         style="height: 80vh; display: none;" role="menu" aria-orientation="vertical" tabindex="-1">


        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
                <div class="flex gap-2">

                    <button type="button" @click="showAddColumn = !showAddColumn"
                            class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                        + Column
                    </button>
                    <button type="button"
                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_all_columns"
                            hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;}"
                            hx-target=".data_table" hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        Show All
                    </button>
                    <button type="button"
                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/hide_all_columns"
                            hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;}"
                            hx-target=".data_table" hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                        Hide All
                    </button>
                </div>
            </div>


            <div class="flex items-center space-x-3 mb-3">
                <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
                <select id="data-source-select"
                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source_and_columns"
                        hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;}"
                        hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change" name="data_source_selection"
                        class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="hardcoded">
                        Default (Hardcoded Data)
                    </option>
                    <optgroup label="Other">
                        <option value="1">
                            Autodesk_autorenew
                        </option>
                        <option value="2">
                            test_send
                        </option>
                    </optgroup>
                    <optgroup label="Autodesk Integration">
                        <option value="30" selected="">
                            Autodesk Subscriptions - Complete subscription data with customer relationships
                        </option>
                        <option value="31">
                            Autodesk Accounts - Customer account information
                        </option>
                        <option value="32">
                            Expiring Subscriptions - Active subscriptions expiring within 90 days
                        </option>
                        <option value="33">
                            Autodesk Email History - Email communication history
                        </option>
                        <option value="34">
                            Copy of Autodesk Subscriptions - Complete subscription data with customer relationships
                        </option>
                        <option value="35">
                            Full Autodesk Subscriptions - Complete subscription data with customer relationships
                        </option>
                    </optgroup>
                </select>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Data Source
                    </span>
            </div>


            <div x-show="showAddColumn" x-transition="" class="mb-3 p-2 bg-blue-50 rounded border"
                 style="display: none;">
                <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_column"
                      hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;}"
                      hx-target=".data_table" hx-swap="outerHTML"
                      @htmx:after-request="showAddColumn = false; newColumnName = ''" class="flex gap-2 items-center">
                    <input type="text" name="column_name" x-model="newColumnName"
                           placeholder="Column name (e.g., Contact Info)"
                           class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded" required="">
                    <button type="submit" class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Add
                    </button>
                    <button type="button" @click="showAddColumn = false"
                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </form>
            </div>

            <div class="text-xs text-gray-500">
                Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
            </div>
        </div>


        <div class="flex-1 overflow-y-auto p-4">
            <div x-ref="columnList"
                 class="sortable column-sortable space-y-3"
                 data-sortable-handle=".column-drag-handle"
                 data-sortable-filter=".field-item"
                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"
                 hx-trigger="sortableEnd"
                 hx-target=".data_table"
                 hx-swap="outerHTML"
                 hx-include="this"
                 hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "data_source": "30"}'>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_9_2a0d15709399fe8056cd9c0dcd396632">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_9_2a0d15709399fe8056cd9c0dcd396632
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Customer</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    2 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_0.style.display = $refs.renameInput_0.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_0.style.display === 'block') { $refs.renameInput_0.focus(); $refs.renameInput_0.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_0" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_0.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Customer" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_0.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_0.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="sortable field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                         data-sortable-handle=".field-drag-handle, .action-drag-handle"
                         data-sortable-group="fieldsAndActions"
                         hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field"
                         hx-trigger="sortableEnd"
                         hx-target=".data_table"
                         hx-swap="outerHTML"
                         hx-include="closest .column-sortable"
                         hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "data_source": "30"}'>

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_0.style.display = $refs.actionForm_0.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_0" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_0.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_0.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_name">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_name</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;, &quot;field_name&quot;: &quot;endcust_name&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_account_csn">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_account_csn</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_9_2a0d15709399fe8056cd9c0dcd396632&quot;, &quot;field_name&quot;: &quot;endcust_account_csn&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Id</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_1.style.display = $refs.renameInput_1.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_1.style.display === 'block') { $refs.renameInput_1.focus(); $refs.renameInput_1.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_1" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_1.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Id" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_1.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_1.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_1.style.display = $refs.actionForm_1.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_1" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_1.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_1.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_id">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_id</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_0_cb4fa582c4c7f9dd6dc2fccfa150ea1b&quot;, &quot;field_name&quot;: &quot;subs_id&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_1_9516adb5b45361820e47ff8dcdb18ce2">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_1_9516adb5b45361820e47ff8dcdb18ce2
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Ids</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    2 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_2.style.display = $refs.renameInput_2.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_2.style.display === 'block') { $refs.renameInput_2.focus(); $refs.renameInput_2.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_2" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_2.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Ids" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_2.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_2.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_2.style.display = $refs.actionForm_2.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_2" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_2.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_2.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_subscriptionId">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_subscriptionId</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;, &quot;field_name&quot;: &quot;subs_subscriptionId&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_subscriptionReferenceNumber">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_subscriptionReferenceNumber</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_1_9516adb5b45361820e47ff8dcdb18ce2&quot;, &quot;field_name&quot;: &quot;subs_subscriptionReferenceNumber&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_2_8c2bbee4e6042ff69ee6e520b28fe7b0">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_2_8c2bbee4e6042ff69ee6e520b28fe7b0
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_2_8c2bbee4e6042ff69ee6e520b28fe7b0&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">SubscriptionReferenceNumber</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_3.style.display = $refs.renameInput_3.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_3.style.display === 'block') { $refs.renameInput_3.focus(); $refs.renameInput_3.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_2_8c2bbee4e6042ff69ee6e520b28fe7b0&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_3" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_2_8c2bbee4e6042ff69ee6e520b28fe7b0&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_3.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="SubscriptionReferenceNumber"
                                   placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_3.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_3.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_2_8c2bbee4e6042ff69ee6e520b28fe7b0&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_3.style.display = $refs.actionForm_3.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_3" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_2_8c2bbee4e6042ff69ee6e520b28fe7b0&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_3.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_3.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_3_281a0f1408cea80f73a2a47a2d3b7f88">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_3_281a0f1408cea80f73a2a47a2d3b7f88
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_3_281a0f1408cea80f73a2a47a2d3b7f88&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Quantity</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_4.style.display = $refs.renameInput_4.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_4.style.display === 'block') { $refs.renameInput_4.focus(); $refs.renameInput_4.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_3_281a0f1408cea80f73a2a47a2d3b7f88&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_4" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_3_281a0f1408cea80f73a2a47a2d3b7f88&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_4.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Quantity" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_4.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_4.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_3_281a0f1408cea80f73a2a47a2d3b7f88&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_4.style.display = $refs.actionForm_4.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_4" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_3_281a0f1408cea80f73a2a47a2d3b7f88&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_4.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_4.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_4_7cb10236b42be9cb3a374f85923fba0b">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_4_7cb10236b42be9cb3a374f85923fba0b
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Deets</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    2 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_5.style.display = $refs.renameInput_5.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_5.style.display === 'block') { $refs.renameInput_5.focus(); $refs.renameInput_5.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_5" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_5.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Deets" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_5.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_5.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_5.style.display = $refs.actionForm_5.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_5" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_5.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_5.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_status">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_status</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;, &quot;field_name&quot;: &quot;subs_status&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_quantity">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_quantity</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_4_7cb10236b42be9cb3a374f85923fba0b&quot;, &quot;field_name&quot;: &quot;subs_quantity&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_5_3895d5b13f20f13cd549c4bd659a3f35">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_5_3895d5b13f20f13cd549c4bd659a3f35
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Dates</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    2 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_6.style.display = $refs.renameInput_6.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_6.style.display === 'block') { $refs.renameInput_6.focus(); $refs.renameInput_6.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_6" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_6.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Dates" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_6.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_6.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_6.style.display = $refs.actionForm_6.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_6" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_6.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_6.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_startDate">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_startDate</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;, &quot;field_name&quot;: &quot;subs_startDate&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_endDate">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_endDate</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_5_3895d5b13f20f13cd549c4bd659a3f35&quot;, &quot;field_name&quot;: &quot;subs_endDate&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">EndDate</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_7.style.display = $refs.renameInput_7.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_7.style.display === 'block') { $refs.renameInput_7.focus(); $refs.renameInput_7.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_7" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_7.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="EndDate" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_7.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_7.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_7.style.display = $refs.actionForm_7.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_7" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_7.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_7.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_7_6eae894e9cf944fe99f4ecc1e647b8e4">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_7_6eae894e9cf944fe99f4ecc1e647b8e4
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_7_6eae894e9cf944fe99f4ecc1e647b8e4&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Id</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_8.style.display = $refs.renameInput_8.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_8.style.display === 'block') { $refs.renameInput_8.focus(); $refs.renameInput_8.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_7_6eae894e9cf944fe99f4ecc1e647b8e4&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_8" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_7_6eae894e9cf944fe99f4ecc1e647b8e4&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_8.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Id" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_8.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_8.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_7_6eae894e9cf944fe99f4ecc1e647b8e4&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_8.style.display = $refs.actionForm_8.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_8" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_7_6eae894e9cf944fe99f4ecc1e647b8e4&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_8.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_8.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_id">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_id</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_7_6eae894e9cf944fe99f4ecc1e647b8e4&quot;, &quot;field_name&quot;: &quot;endcust_id&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_8_691981d95f08241e3b5c73772b36588a">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_8_691981d95f08241e3b5c73772b36588a
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_8_691981d95f08241e3b5c73772b36588a&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Account Csn</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_9.style.display = $refs.renameInput_9.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_9.style.display === 'block') { $refs.renameInput_9.focus(); $refs.renameInput_9.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_8_691981d95f08241e3b5c73772b36588a&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_9" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_8_691981d95f08241e3b5c73772b36588a&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_9.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Account Csn" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_9.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_9.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_8_691981d95f08241e3b5c73772b36588a&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_9.style.display = $refs.actionForm_9.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_9" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_8_691981d95f08241e3b5c73772b36588a&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_9.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_9.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_10_27642de9e05d6c3bf6714d4bc017ef1f">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_10_27642de9e05d6c3bf6714d4bc017ef1f
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Name</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    2 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_10.style.display = $refs.renameInput_10.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_10.style.display === 'block') { $refs.renameInput_10.focus(); $refs.renameInput_10.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_10" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_10.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Name" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_10.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_10.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_10.style.display = $refs.actionForm_10.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_10" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_10.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_10.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_first_name">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_first_name</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;, &quot;field_name&quot;: &quot;endcust_first_name&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_last_name">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_last_name</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_10_27642de9e05d6c3bf6714d4bc017ef1f&quot;, &quot;field_name&quot;: &quot;endcust_last_name&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_11_65cc133083b5dbc280a7c9a6f6fa7964">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_11_65cc133083b5dbc280a7c9a6f6fa7964
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_11_65cc133083b5dbc280a7c9a6f6fa7964&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Last Name</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_11.style.display = $refs.renameInput_11.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_11.style.display === 'block') { $refs.renameInput_11.focus(); $refs.renameInput_11.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_11_65cc133083b5dbc280a7c9a6f6fa7964&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_11" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_11_65cc133083b5dbc280a7c9a6f6fa7964&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_11.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Last Name" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_11.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_11.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_11_65cc133083b5dbc280a7c9a6f6fa7964&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_11.style.display = $refs.actionForm_11.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_11" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_11_65cc133083b5dbc280a7c9a6f6fa7964&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_11.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_11.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_12_abc01573ee70362ec6ca14aef7740b6b">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_12_abc01573ee70362ec6ca14aef7740b6b
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Quote Id</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    3 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_12.style.display = $refs.renameInput_12.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_12.style.display === 'block') { $refs.renameInput_12.focus(); $refs.renameInput_12.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_12" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_12.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Quote Id" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_12.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_12.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_12.style.display = $refs.actionForm_12.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_12" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_12.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_12.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="lastquote_quote_id">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">lastquote_quote_id</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;, &quot;field_name&quot;: &quot;lastquote_quote_id&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="lastquote_quote_number">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">lastquote_quote_number</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;, &quot;field_name&quot;: &quot;lastquote_quote_number&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="lastquote_quoted_date">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700">lastquote_quoted_date</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table" hx-swap="outerHTML"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_12_abc01573ee70362ec6ca14aef7740b6b&quot;, &quot;field_name&quot;: &quot;lastquote_quoted_date&quot;}"
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>


                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_13_3fa70f7dd6019a6e0d8eec7bb133022f">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_13_3fa70f7dd6019a6e0d8eec7bb133022f
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_13_3fa70f7dd6019a6e0d8eec7bb133022f&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Quote Number</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_13.style.display = $refs.renameInput_13.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_13.style.display === 'block') { $refs.renameInput_13.focus(); $refs.renameInput_13.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_13_3fa70f7dd6019a6e0d8eec7bb133022f&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_13" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_13_3fa70f7dd6019a6e0d8eec7bb133022f&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_13.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Quote Number" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_13.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_13.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_13_3fa70f7dd6019a6e0d8eec7bb133022f&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_13.style.display = $refs.actionForm_13.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_13" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_13_3fa70f7dd6019a6e0d8eec7bb133022f&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_13.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_13.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_14_3845d1ad32aed7fc7ca573032f380116">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: false for column col_14_3845d1ad32aed7fc7ca573032f380116
                            -->
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_14_3845d1ad32aed7fc7ca573032f380116&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Quoted Date</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_14.style.display = $refs.renameInput_14.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_14.style.display === 'block') { $refs.renameInput_14.focus(); $refs.renameInput_14.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_14_3845d1ad32aed7fc7ca573032f380116&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_14" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_14_3845d1ad32aed7fc7ca573032f380116&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_14.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Quoted Date" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_14.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_14.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_14_3845d1ad32aed7fc7ca573032f380116&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_14.style.display = $refs.actionForm_14.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_14" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_14_3845d1ad32aed7fc7ca573032f380116&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_14.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_14.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_new_1754434879">


                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">

                            <!-- data-table-column-manager.edge.php > include() 324: is_checked: true for column col_new_1754434879
                            -->
                            <input type="checkbox" checked=""
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_new_1754434879&quot;, &quot;data_source&quot;: &quot;30&quot;}"
                                   hx-target=".data_table" hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">Actions</span>
                        </label>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                    Display                                </span>
                        </div>


                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                        </div>


                        <button type="button"
                                @click="$refs.renameInput_15.style.display = $refs.renameInput_15.style.display === 'none' ? 'block' : 'none'; if($refs.renameInput_15.style.display === 'block') { $refs.renameInput_15.focus(); $refs.renameInput_15.select(); }"
                                class="ml-2 text-blue-400 hover:text-blue-600" title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>


                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_new_1754434879&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600" title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>


                    <div x-ref="renameInput_15" style="display: none;" class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                              hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_new_1754434879&quot;}"
                              hx-target=".data_table" hx-swap="outerHTML"
                              @htmx:after-request="$refs.renameInput_15.style.display = 'none'"
                              class="flex gap-2 items-center">
                            <input type="text" name="new_label" value="Actions" placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="$refs.renameInput_15.style.display = 'none'"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button" @click="$refs.renameInput_15.style.display = 'none'"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>


                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                        <div class="mb-2">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table" hx-swap="outerHTML" hx-trigger="change[target.value != '']"
                                    hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_new_1754434879&quot;}"
                                    name="field_name" @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber
                                </option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                            </select>
                        </div>


                        <div class="mb-2">
                            <button type="button"
                                    @click="$refs.actionForm_15.style.display = $refs.actionForm_15.style.display === 'none' ? 'block' : 'none'"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>


                        <div x-ref="actionForm_15" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;, &quot;column_id&quot;: &quot;col_new_1754434879&quot;}"
                                  hx-target=".data_table" hx-swap="outerHTML"
                                  @htmx:after-request="$refs.actionForm_15.style.display = 'none'" class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">
                                            subs_subscriptionReferenceNumber
                                        </option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required=""
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button" @click="$refs.actionForm_15.style.display = 'none'"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="action-item inline-flex items-center gap-x-1 bg-green-50 px-2 py-1 m-1 rounded border border-green-200 text-xs cursor-move"
                             data-action-id="action_68929084eca4b">

                            <div class="action-drag-handle text-green-400 hover:text-green-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"></path>
                                </svg>
                            </div>


                            <span class="text-sm">
                                            @switch($action['icon'])
                                                @case('edit') ✏️

                                                    </span></div>
                    </div>
                </div>
            </div>


            <div class="p-4 border-t border-gray-200 bg-gray-50">
                <div class="flex justify-between items-center">
                    <div class="flex gap-2 items-center">
                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/hide_all_columns"
                                hx-vals="{&quot;table_name&quot;: &quot;autodesk_subscriptions&quot;, &quot;callback&quot;: &quot;&quot;}"
                                hx-target=".data_table" hx-swap="outerHTML"
                                class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                            Hide All
                        </button>
                        <span class="text-xs text-gray-500">16 columns, 15 fields</span>
                    </div>
                    <button type="button" @click="open = false"
                            class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Done
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>