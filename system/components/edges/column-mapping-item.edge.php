@props([
    'mapping' => [],
    'mapping_index' => 0,
    'all_columns' => [],
    'reference_columns' => []
])

@php
    // Extract mapping data
    $source_columns = $mapping['source_columns'] ?? [];
    $output_column = $mapping['output_column'] ?? '';
    $merge_strategy = $mapping['merge_strategy'] ?? 'first_non_empty';
    
    // Build options for source columns
    $source_column_options = ['' => 'Select source column...'];
    foreach ($all_columns as $column_key => $column_info) {
        $source_column_options[$column_key] = $column_info['display'] . ' (' . $column_info['type'] . ')';
    }
    
    // Build options for output columns (from reference table if available)
    $output_column_options = [];
    if (!empty($reference_columns)) {
        $output_column_options[''] = 'Select output column...';
        foreach ($reference_columns as $column) {
            $output_column_options[$column['Field']] = $column['Field'] . ' (' . $column['Type'] . ')';
        }
    }
    
    // Merge strategy options
    $merge_strategy_options = [
        'first_non_empty' => 'First non-empty value',
        'concatenate' => 'Concatenate with separator',
        'sum' => 'Sum (numeric values)',
        'average' => 'Average (numeric values)',
        'max' => 'Maximum value',
        'min' => 'Minimum value',
        'count' => 'Count non-empty values'
    ];
@endphp

<div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- Source Columns Selection -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Source Columns</label>
            <div class="space-y-2" id="source-columns-{{ $mapping_index }}">
                @if(empty($source_columns))
                    <div class="text-sm text-gray-500" id="no-source-columns-{{ $mapping_index }}">
                        No source columns selected
                    </div>
                @else
                    @foreach($source_columns as $col_index => $source_column)
                        <div class="flex items-center space-x-2">
                            <x-forms-select
                                name="column_mappings[{{ $mapping_index }}][source_columns][{{ $col_index }}]"
                                :options="$source_column_options"
                                :selected="$source_column"
                                class_suffix="text-sm flex-1"
                            />
                            <button type="button"
                                    hx-post="{{ APP_ROOT }}/api/data_sources/remove_source_column"
                                    hx-target="#source-columns-{{ $mapping_index }}"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-vals='{"mapping_index": "{{ $mapping_index }}", "column_index": "{{ $col_index }}"}'
                                    class="p-1 text-red-600 hover:text-red-800">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    @endforeach
                @endif
            </div>
            
            <button type="button"
                    hx-post="{{ APP_ROOT }}/api/data_sources/add_source_column"
                    hx-target="#source-columns-{{ $mapping_index }}"
                    hx-swap="innerHTML"
                    hx-include="form"
                    hx-vals='{"mapping_index": "{{ $mapping_index }}"}'
                    class="mt-2 inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Source
            </button>
        </div>
        
        <!-- Output Column -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Output Column</label>
            @if(!empty($reference_columns))
                <x-forms-select
                    name="column_mappings[{{ $mapping_index }}][output_column]"
                    :options="$output_column_options"
                    :selected="$output_column"
                    class_suffix="text-sm"
                />
            @else
                <x-forms-input
                    name="column_mappings[{{ $mapping_index }}][output_column]"
                    :value="$output_column"
                    placeholder="Enter output column name"
                    class_suffix="text-sm"
                />
            @endif
            <p class="mt-1 text-xs text-gray-500">
                Name for the merged column in the output dataset
            </p>
        </div>
        
        <!-- Merge Strategy -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Merge Strategy</label>
            <x-forms-select
                name="column_mappings[{{ $mapping_index }}][merge_strategy]"
                :options="$merge_strategy_options"
                :selected="$merge_strategy"
                class_suffix="text-sm"
            />
            
            @if($merge_strategy === 'concatenate')
                <div class="mt-2">
                    <x-forms-input
                        name="column_mappings[{{ $mapping_index }}][separator]"
                        :value="$mapping['separator'] ?? ', '"
                        placeholder="Separator (e.g., ', ')"
                        class_suffix="text-sm"
                        label="Separator"
                    />
                </div>
            @endif
        </div>
    </div>
    
    <!-- Remove Mapping Button -->
    <div class="mt-4 flex justify-end">
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_sources/remove_column_mapping"
                hx-target="closest div.border"
                hx-swap="outerHTML"
                hx-include="form"
                hx-vals='{"mapping_index": "{{ $mapping_index }}"}'
                class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Remove Mapping
        </button>
    </div>
</div>
