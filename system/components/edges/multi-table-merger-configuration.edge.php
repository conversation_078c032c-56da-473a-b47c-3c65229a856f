@props([
    'available_tables' => [],
    'data_source' => null
])

@php
    // Extract existing configuration with debugging
    $selected_table_patterns = $data_source['table_patterns'] ?? [];
    $selected_explicit_tables = $data_source['explicit_tables'] ?? [];
    $mapping_method = $data_source['mapping_method'] ?? 'like_for_like';
    $reference_table = $data_source['reference_table'] ?? '';
    $column_mappings = $data_source['column_mappings'] ?? [];
    $unified_mappings = $data_source['unified_mappings'] ?? [];


@endphp

<div class="space-y-6" x-data="{
    mappingMethod: '{{ $mapping_method }}',
    showColumnMapping: function() {
        return this.mappingMethod === 'manual' || this.mappingMethod === 'unified_field_mapper';
    },
    showAdvancedFeatures: function() {
        return this.mappingMethod !== 'like_for_like' || Object.keys({{ json_encode($column_mappings) }}).length > 0;
    }
}">

    <!-- Table Selection Section -->
    <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-4">Table Selection</h4>
        
        <!-- Wildcard Pattern Selection -->
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Wildcard Patterns</label>
                <div class="space-y-2" id="wildcard-patterns-container">
                    @if(empty($selected_table_patterns))
                        <div class="text-sm text-gray-500" id="no-patterns-message">
                            No wildcard patterns defined. Add patterns to automatically select matching tables.
                        </div>
                    @else
                        @foreach($selected_table_patterns as $index => $pattern)
                            <x-wildcard-pattern-item
                                :pattern="$pattern"
                                :pattern_index="$index"
                                :available_tables="$available_tables"
                            />
                        @endforeach
                    @endif
                </div>
                
                <div class="flex items-center space-x-2 mt-3">
                    <input type="text" 
                           id="new-pattern-input"
                           name="new-pattern-input"
                           placeholder="e.g., subscription_*, user_data_*"
                           class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_sources/add_wildcard_pattern"
                            hx-target="#wildcard-patterns-container"
                            hx-swap="beforeend"
                            hx-include="#new-pattern-input, form"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Add Pattern
                    </button>
                </div>
            </div>

            <!-- Explicit Table Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Additional Explicit Tables</label>
                <div class="space-y-2" id="explicit-tables-container">
                    @if(empty($selected_explicit_tables))
                        <div class="text-sm text-gray-500" id="no-explicit-tables-message">
                            No explicit tables selected. Add specific tables that don't match wildcard patterns.
                        </div>
                    @else
                        @foreach($selected_explicit_tables as $index => $table_name)
                            <x-explicit-table-item
                                :table_name="$table_name"
                                :table_index="$index"
                                :available_tables="$available_tables"
                            />
                        @endforeach
                    @endif
                </div>
                
                <div class="flex items-center space-x-2 mt-3">
                    @php
                        $explicit_table_options = ['' => 'Select a table to add...'];
                        foreach ($available_tables as $table) {
                            if (!in_array($table['name'], $selected_explicit_tables)) {
                                $explicit_table_options[$table['name']] = $table['display_name'] . ' (' . number_format($table['row_count']) . ' rows)';
                            }
                        }
                    @endphp
                    <x-forms-select 
                        id="add-explicit-table-select"
                        name="add_explicit_table"
                        :options="$explicit_table_options"
                        class_suffix="text-sm flex-1"
                    />
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_sources/add_explicit_table"
                            hx-target="#explicit-tables-container"
                            hx-swap="beforeend"
                            hx-include="#add-explicit-table-select, form"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Add Table
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Column Mapping Method Selection -->
    <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-4">Column Mapping Method</h4>
        
        @php
            $mapping_method_options = [
                'like_for_like' => 'Like-for-like (Direct column name matching)',
                'manual' => 'Manual mapping (User-defined column mappings)',
                'unified_field_mapper' => 'Unified field mapper (Automatic intelligent mapping)'
            ];
        @endphp
        
        <x-forms-select
            name="mapping_method"
            :options="$mapping_method_options"
            :selected="$mapping_method"
            x-model="mappingMethod"
            hx-post="{{ APP_ROOT }}/api/data_sources/update_mapping_method"
            hx-target="#column-mapping-configuration"
            hx-swap="innerHTML"
            hx-include="form"
            hx-trigger="change"
        />
        
        <div class="mt-3 text-sm text-gray-600">
            <div x-show="mappingMethod === 'like_for_like'">
                Columns with identical names across tables will be merged automatically. No configuration required.
            </div>
            <div x-show="mappingMethod === 'manual'">
                Define custom mappings between source table columns and output columns.
            </div>
            <div x-show="mappingMethod === 'unified_field_mapper'">
                Use intelligent field mapping rules to automatically identify and merge related columns.
            </div>
        </div>
    </div>

    <!-- Column Mapping Configuration (conditional) -->
    <div id="column-mapping-configuration" x-show="showColumnMapping()">
        <div class="text-center py-6 text-gray-500">
            Select a mapping method to configure column mappings.
        </div>
    </div>

    <!-- Reference Table Helper (conditional) -->
    <div class="bg-white border border-gray-200 rounded-lg p-4" x-show="showColumnMapping()">
        <h4 class="text-sm font-medium text-gray-900 mb-4">Reference Table Helper (Optional)</h4>
        
        @php
            $reference_table_options = ['' => 'No reference table'];
            foreach ($available_tables as $table) {
                $reference_table_options[$table['name']] = $table['display_name'] . ' (' . number_format($table['row_count']) . ' rows)';
            }
        @endphp
        
        <x-forms-select
            name="reference_table"
            :options="$reference_table_options"
            :selected="$reference_table"
            hx-post="{{ APP_ROOT }}/api/data_sources/update_reference_table"
            hx-target="#column-mapping-configuration"
            hx-swap="innerHTML"
            hx-include="form"
            class_suffix="mb-3"
        />
        
        <p class="text-sm text-gray-600">
            Select an existing table to use as a reference for easier column mapping setup. 
            This table's structure will be used to suggest mappings but won't be saved as part of the data source.
        </p>
    </div>

    <!-- Hidden inputs to store configuration -->
    <input type="hidden" name="table_patterns" value="{{ json_encode($selected_table_patterns) }}">
    <input type="hidden" name="explicit_tables" value="{{ json_encode($selected_explicit_tables) }}">
    <input type="hidden" name="column_mappings" value="{{ json_encode($column_mappings) }}">
    <input type="hidden" name="unified_mappings" value="{{ json_encode($unified_mappings) }}">
</div>

<script>
// Update hidden inputs when patterns or tables change
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.target.id === 'wildcard-patterns-container' ||
        event.target.id === 'explicit-tables-container') {
        updateHiddenInputs();

        // Also trigger an update of the column mapping if it's visible
        const columnMappingConfig = document.getElementById('column-mapping-configuration');
        if (columnMappingConfig && columnMappingConfig.style.display !== 'none') {
            // Find the mapping method select and trigger its change event
            const mappingMethodSelect = document.querySelector('select[name="mapping_method"]');
            if (mappingMethodSelect && mappingMethodSelect.value !== 'like_for_like') {
                // Trigger HTMX request to refresh column mapping
                htmx.trigger(mappingMethodSelect, 'change');
            }
        }
    }
});

// Also update when individual pattern inputs change
document.addEventListener('input', function(event) {
    if (event.target.name && event.target.name.startsWith('table_patterns[')) {
        updateHiddenInputs();
    }
});

function updateHiddenInputs() {
    try {
        // Update table patterns
        const patternInputs = document.querySelectorAll('[name^="table_patterns["]');
        const patterns = Array.from(patternInputs).map(input => input.value).filter(value => value.trim());
        const patternsHidden = document.querySelector('[name="table_patterns"]');
        if (patternsHidden) {
            patternsHidden.value = JSON.stringify(patterns);
        }

        // Update explicit tables
        const tableInputs = document.querySelectorAll('[name^="explicit_tables["]');
        const tables = Array.from(tableInputs).map(input => input.value).filter(value => value.trim());
        const tablesHidden = document.querySelector('[name="explicit_tables"]');
        if (tablesHidden) {
            tablesHidden.value = JSON.stringify(tables);
        }

        console.log('Updated hidden inputs - patterns:', patterns, 'tables:', tables);
    } catch (error) {
        console.error('Error updating hidden inputs:', error);
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateHiddenInputs();
});
</script>
