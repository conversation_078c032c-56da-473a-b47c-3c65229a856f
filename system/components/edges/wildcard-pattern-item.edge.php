@props([
    'pattern' => '',
    'pattern_index' => 0,
    'available_tables' => []
])

@php
    // Find matching tables for this pattern
    $matching_tables = [];
    $pattern_regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';
    
    foreach ($available_tables as $table) {
        if (preg_match($pattern_regex, $table['name'])) {
            $matching_tables[] = $table;
        }
    }
@endphp

<div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
    <div class="flex-1">
        <div class="flex items-center space-x-2">
            <input type="text" 
                   name="table_patterns[{{ $pattern_index }}]"
                   value="{{ $pattern }}"
                   class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                   hx-post="{{ APP_ROOT }}/api/data_sources/update_pattern_matches"
                   hx-target="#pattern-matches-{{ $pattern_index }}"
                   hx-swap="innerHTML"
                   hx-include="form"
                   hx-trigger="input changed delay:500ms">
            
            <span class="text-sm text-gray-500">
                ({{ count($matching_tables) }} matches)
            </span>
        </div>
        
        <div id="pattern-matches-{{ $pattern_index }}" class="mt-2">
            @if(count($matching_tables) > 0)
                <div class="text-xs text-gray-600">
                    <strong>Matching tables:</strong>
                    @foreach($matching_tables as $index => $table)
                        <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-md mr-1 mb-1">
                            {{ $table['display_name'] }}
                            <span class="text-blue-600">({{ number_format($table['row_count']) }})</span>
                        </span>
                    @endforeach
                </div>
            @else
                <div class="text-xs text-red-600">
                    No matching tables found for pattern "{{ $pattern }}"
                </div>
            @endif
        </div>
    </div>
    
    <button type="button"
            hx-post="{{ APP_ROOT }}/api/data_sources/remove_wildcard_pattern"
            hx-target="closest div"
            hx-swap="outerHTML"
            hx-include="form"
            hx-vals='{"pattern_index": "{{ $pattern_index }}"}'
            class="inline-flex items-center p-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
    </button>
</div>
