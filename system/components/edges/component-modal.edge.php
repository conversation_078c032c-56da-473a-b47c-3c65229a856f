@props([
    'title' => 'model',
    'description' => 'A model',
    'content' => [], // An array of [('header'|'body'|'footer') => 'content']
    'title' => '',
])

<!-- Modal Container -->
<div x-show="showModal"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;"
     x-data="{
        tabs: [],
        activeTab: null,

        switchTab(tabId) {
            // Store current tab content before switching
            this.storeCurrentTabContent();

            // Switch active tab
            this.tabs.forEach(tab => tab.active = tab.id === tabId);
            this.activeTab = tabId;

            // Load the content for the new active tab
            this.loadTabContent(tabId);
        },

        storeCurrentTabContent() {
            const currentTab = this.tabs.find(tab => tab.id === this.activeTab);
            if (currentTab) {
                const modalBody = document.getElementById('modal_body');
                if (modalBody) {
                    currentTab.content = modalBody.innerHTML;
                }
            }
        },

        loadTabContent(tabId) {
            const tab = this.tabs.find(tab => tab.id === tabId);
            if (tab && tab.content) {
                const modalBody = document.getElementById('modal_body');
                if (modalBody) {
                    modalBody.innerHTML = tab.content;
                }
            }
        },

        closeTab(tabId) {
            if (this.tabs.length === 1) return; // Don't close last tab

            const tabIndex = this.tabs.findIndex(tab => tab.id === tabId);
            if (tabIndex === -1) return;

            this.tabs.splice(tabIndex, 1);

            // If we closed the active tab, switch to the first remaining tab
            if (this.activeTab === tabId) {
                this.activeTab = this.tabs[0].id;
                this.tabs[0].active = true;
                this.loadTabContent(this.activeTab);
            }
        },

        togglePin(tabId) {
            const tab = this.tabs.find(tab => tab.id === tabId);
            if (tab) {
                tab.pinned = !tab.pinned;
            }
        },

        hasPinnedTabs() {
            return this.tabs.some(tab => tab.pinned);
        }
     }">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 transition-opacity">
            <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showModal = false"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full sm:p-6">
            <div class="absolute top-0 right-0 pt-4 pr-4">
                <button type="button"
                        @click="showModal = false"
                        class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Tab Bar (only show if there are tabs) -->
            <div x-show="tabs.length > 0" id="modal_tab_bar" class="border-b border-gray-200 mb-4">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <template x-for="tab in tabs" :key="tab.id">
                        <div class="flex items-center group">
                            <button
                                @click="switchTab(tab.id)"
                                :class="tab.active ?
                                    'border-indigo-500 text-indigo-600' :
                                    'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center"
                                x-text="tab.title">
                            </button>

                            <!-- Pin button -->
                            <button
                                @click="togglePin(tab.id)"
                                :class="tab.pinned ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-600'"
                                class="ml-2 p-1 rounded-full hover:bg-gray-100"
                                :title="tab.pinned ? 'Unpin tab' : 'Pin tab'">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- Close button (only show if more than one tab) -->
                            <button
                                x-show="tabs.length > 1"
                                @click="closeTab(tab.id)"
                                class="ml-1 p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    </template>
                </nav>
            </div>

            <!-- Tab Content -->
            <div id="modal_body">
                <!-- Modal content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Modal tab management event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Store reference to modal Alpine component
    let modalComponent = null;

    // Function to get modal Alpine component
    function getModalComponent() {
        if (!modalComponent) {
            const modalElement = document.querySelector('[x-show="showModal"]');
            if (modalElement && modalElement._x_dataStack && modalElement._x_dataStack.length > 0) {
                modalComponent = modalElement._x_dataStack[0];
            }
        }
        return modalComponent;
    }

    // Listen for add new tab events from HTMX
    document.body.addEventListener('addModalTab', function(event) {
        const tabData = event.detail;
        const component = getModalComponent();

        if (component) {
            // Check if tab already exists
            const existingTab = component.tabs.find(tab => tab.id === tabData.id);

            if (!existingTab) {
                // Store current tab content before switching
                component.storeCurrentTabContent();

                // Deactivate all tabs
                component.tabs.forEach(tab => tab.active = false);

                // Add new tab with current modal body content
                const modalBody = document.getElementById('modal_body');
                const currentContent = modalBody ? modalBody.innerHTML : '';

                component.tabs.push({
                    id: tabData.id,
                    title: tabData.title,
                    content: currentContent,
                    active: true,
                    pinned: tabData.pinned || false
                });

                component.activeTab = tabData.id;
            } else {
                // Switch to existing tab
                component.switchTab(tabData.id);
            }
        }
    });

    // Listen for switch tab events from HTMX
    document.body.addEventListener('switchModalTab', function(event) {
        const tabId = event.detail;
        const component = getModalComponent();

        if (component) {
            component.switchTab(tabId);
        }
    });

    // Handle modal opening - check if we should reset tabs
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        if (event.detail.target && event.detail.target.id === 'modal_body') {
            const component = getModalComponent();

            if (component) {
                // Check if modal is currently closed and no pinned tabs
                const modalElement = document.querySelector('[x-show="showModal"]');
                const isModalVisible = modalElement && modalElement.style.display !== 'none';

                if (!isModalVisible && !component.hasPinnedTabs()) {
                    // Reset tabs when opening modal fresh (no default tab needed)
                    component.tabs = [];
                    component.activeTab = null;
                }
            }
        }
    });

    // Reset modal component reference when modal closes
    document.addEventListener('click', function(event) {
        if (event.target.closest('[x-show="showModal"]') === null) {
            modalComponent = null;
        }
    });
});
</script>


