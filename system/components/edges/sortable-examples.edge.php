{{-- 
Universal Sortable Examples
This file demonstrates how to use the new universal sortable system with HTMX inline attributes.
--}}

{{-- Example 1: Basic Navigation Tree Sortable --}}
<div class="sortable nav-sortable"
     data-sortable-handle=".drag-handle"
     data-sortable-group="nav-tree"
     data-sortable-item-selector=".sortable-item"
     hx-post="{{ APP_ROOT }}/api/nav_tree/reorder_navigation"
     hx-trigger="sortableEnd"
     hx-swap="none"
     hx-include="this"
     hx-vals='{"parent_path": "{{ $parent_path ?? '' }}"}'
     data-parent-path="{{ $parent_path ?? '' }}">
    
    <div class="sortable-item" data-sort-id="item-1">
        <div class="drag-handle">⋮⋮</div>
        <span>Navigation Item 1</span>
    </div>
    
    <div class="sortable-item" data-sort-id="item-2">
        <div class="drag-handle">⋮⋮</div>
        <span>Navigation Item 2</span>
    </div>
    
    <div class="sortable-item" data-sort-id="item-3">
        <div class="drag-handle">⋮⋮</div>
        <span>Navigation Item 3</span>
    </div>
</div>

{{-- Example 2: Column Manager Sortable --}}
<div class="sortable column-sortable"
     data-sortable-handle=".column-drag-handle"
     hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/reorder_columns"
     hx-trigger="sortableEnd"
     hx-target=".data_table"
     hx-swap="outerHTML"
     hx-include="this"
     hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "data_source": "{{ $data_source }}"}'
     data-table-name="{{ $table_name }}"
     data-callback="{{ $callback }}"
     data-data-source="{{ $data_source }}">
    
    <div class="column-item" data-sort-id="col-1" data-column-id="col-1">
        <div class="column-drag-handle">⋮⋮</div>
        <span>Column 1</span>
    </div>
    
    <div class="column-item" data-sort-id="col-2" data-column-id="col-2">
        <div class="column-drag-handle">⋮⋮</div>
        <span>Column 2</span>
    </div>
</div>

{{-- Example 3: Field Container Sortable (Cross-container dragging) --}}
<div class="sortable field-container"
     data-sortable-handle=".field-drag-handle, .action-drag-handle"
     data-sortable-group="fieldsAndActions"
     hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/move_field"
     hx-trigger="sortableEnd"
     hx-target=".data_table"
     hx-swap="outerHTML"
     hx-include="closest .column-sortable"
     data-column-id="column-1">
    
    <div class="field-item" data-sort-id="field-1" data-field-name="field_name_1">
        <div class="field-drag-handle">⋮⋮</div>
        <span>Field 1</span>
    </div>
    
    <div class="action-item" data-sort-id="action-1" data-action-id="action_1">
        <div class="action-drag-handle">⋮⋮</div>
        <span>Action 1</span>
    </div>
</div>

<div class="sortable field-container"
     data-sortable-handle=".field-drag-handle, .action-drag-handle"
     data-sortable-group="fieldsAndActions"
     hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/move_field"
     hx-trigger="sortableEnd"
     hx-target=".data_table"
     hx-swap="outerHTML"
     hx-include="closest .column-sortable"
     data-column-id="column-2">
    
    <div class="field-item" data-sort-id="field-2" data-field-name="field_name_2">
        <div class="field-drag-handle">⋮⋮</div>
        <span>Field 2</span>
    </div>
</div>

{{-- Example 4: Simple List Sortable --}}
<ul class="sortable simple-list"
    data-sortable-handle=".drag-handle"
    hx-post="{{ APP_ROOT }}/api/lists/reorder"
    hx-trigger="sortableEnd"
    hx-target="#list-container"
    hx-swap="outerHTML"
    hx-include="this">
    
    <li data-sort-id="list-item-1">
        <span class="drag-handle">⋮⋮</span>
        List Item 1
    </li>
    
    <li data-sort-id="list-item-2">
        <span class="drag-handle">⋮⋮</span>
        List Item 2
    </li>
    
    <li data-sort-id="list-item-3">
        <span class="drag-handle">⋮⋮</span>
        List Item 3
    </li>
</ul>

{{-- 
Key Points for Using Universal Sortable:

1. Add class="sortable" to the container element

2. Configure sortable behavior with data attributes:
   - data-sortable-handle: CSS selector for drag handle (default: '.drag-handle')
   - data-sortable-group: Group name for cross-container dragging (optional)
   - data-sortable-animation: Animation duration in ms (default: 150)
   - data-sortable-ghost-class: CSS class for ghost element (default: 'sortable-ghost')
   - data-sortable-chosen-class: CSS class for chosen element (default: 'sortable-chosen')
   - data-sortable-drag-class: CSS class for dragging element (default: 'sortable-drag')
   - data-sortable-filter: CSS selector for elements to exclude (default: '.htmx-indicator')
   - data-sortable-item-selector: CSS selector for valid sortable items (optional)

3. Add HTMX attributes for handling the sort:
   - hx-post: URL to send the sort data to
   - hx-trigger: Should be "sortableEnd" (triggered by the universal sortable script)
   - hx-target: Target element for the response
   - hx-swap: How to swap the response
   - hx-include: What data to include with the request
   - hx-vals: Additional static values to send

4. Add data-sort-id attributes to sortable items for identification

5. The script automatically sends these values with the HTMX request:
   - sortedIds: JSON array of sorted item IDs
   - fromIndex: Original index of moved item
   - toIndex: New index of moved item
   - movedId: ID of the moved item
   - fromContainer: ID of source container (for cross-container moves)
   - toContainer: ID of target container (for cross-container moves)

6. Your server endpoint can access these values along with any form data or hx-vals
--}}
