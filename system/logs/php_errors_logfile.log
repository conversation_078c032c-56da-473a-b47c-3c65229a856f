[php_errors] [2025-08-25 23:54:34] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(73) "count(): Argument #1 ($value) must be of type Countable|array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(379)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(2006) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(379): count()\n#1 [internal function]: edge\edge::edgeTemplate\data_table_column_manager_panel\{closure}()\n#2 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(379): array_map()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#5 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(182): edge\edge::render()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(135): edge\edge::render()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(511): api\data_table\column_preferences\regenerate_table()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\data_table\column_preferences\reorder_columns()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n  ["request_uri"]: string(82) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"count(): Argument #1 ($value) must be of type Countable|array, null given","file":"data-table-column-manager-panel.edge.php","line":379,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(379): count()\n#1 [internal function]: edge\\edge::edgeTemplate\\data_table_column_manager_panel\\{closure}()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(379): array_map()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(182): edge\\edge::render()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(135): edge\\edge::render()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(511): api\\data_table\\column_preferences\\regenerate_table()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\data_table\\column_preferences\\reorder_columns()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table\/column_preferences\/reorder_columns","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 00:00:32] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(69) "in_array(): Argument #2 ($haystack) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(240)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(1844) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(240): in_array()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(182): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(80): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(354): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#14 {main}"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"in_array(): Argument #2 ($haystack) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":240,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(240): in_array()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(182): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(80): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(354): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#14 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 00:00:35] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(69) "in_array(): Argument #2 ($haystack) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(240)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(1844) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(240): in_array()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(182): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(80): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(354): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#14 {main}"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"in_array(): Argument #2 ($haystack) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":240,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(240): in_array()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(182): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(80): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(354): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#14 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 08:07:23] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Surveying Solutions Ltd","address1":"34-36 Rose Street North Lane","city":"Edinburgh","postal_code":"EH2 2NP","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":"A-********","quoteCreatedTime":"2025-08-26T09:07:18+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Fraser","lastName":"Dixon","phone":"+************","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Surveying Solutions Ltd","addressLine1":"34-36 Rose Street North Lane","city":"Edinburgh","stateProvinceCode":null,"stateProvince":null,"postalCode":"EH2 2NP","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"Adam","lastName":"Monteith","phone":"+**********","preferredLanguage":"en"},"admin":null,"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-10-23","endDate":"2026-10-22","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-10-22","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-10-23","endDate":"2026-10-22","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14056"}\n-->\n
[php_errors] [2025-08-26 08:07:23] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Surveying Solutions Ltd","address1":"34-36 Rose Street North Lane","city":"Edinburgh","postal_code":"EH2 2NP","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":"A-********","quoteCreatedTime":"2025-08-26T09:07:18+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Fraser","lastName":"Dixon","phone":"+************","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Surveying Solutions Ltd","addressLine1":"34-36 Rose Street North Lane","city":"Edinburgh","stateProvinceCode":null,"stateProvince":null,"postalCode":"EH2 2NP","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"Adam","lastName":"Monteith","phone":"+**********","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-10-23","endDate":"2026-10-22","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-10-22","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-10-23","endDate":"2026-10-22","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14056"}\n-->\n
[php_errors] [2025-08-26 08:07:24] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 08:07:24] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 08:16:18] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Surveying Solutions Ltd","address1":"34-36 Rose Street North Lane","city":"Edinburgh","postal_code":"EH2 2NP","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T09:16:11+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Surveying Solutions Ltd","addressLine1":"34-36 Rose Street North Lane","city":"Edinburgh","stateProvinceCode":null,"stateProvince":null,"postalCode":"EH2 2NP","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"Adam","lastName":"Monteith","phone":"+**********","preferredLanguage":"en"},"admin":null,"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-10-23","endDate":"2026-10-22","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-10-22","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-10-23","endDate":"2026-10-22","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 08:16:18] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 08:16:18] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Surveying Solutions Ltd","address1":"34-36 Rose Street North Lane","city":"Edinburgh","postal_code":"EH2 2NP","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T09:16:11+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Surveying Solutions Ltd","addressLine1":"34-36 Rose Street North Lane","city":"Edinburgh","stateProvinceCode":null,"stateProvince":null,"postalCode":"EH2 2NP","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"Adam","lastName":"Monteith","phone":"+**********","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-10-23","endDate":"2026-10-22","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-10-22","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-10-23","endDate":"2026-10-22","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 08:16:18] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 08:19:43] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Ramsay McMichael Consulting","address1":"Standard Bldg 102 Hope Street","city":"Glasgow","postal_code":"G2 6PH","country_code":"GB","state_province":"LANARKSHIRE","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T09:19:37+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Ramsay McMichael Consulting","addressLine1":"Standard Bldg 102 Hope Street","city":"Glasgow","stateProvinceCode":null,"stateProvince":"LANARKSHIRE","postalCode":"G2 6PH","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"John","lastName":"Stewart","phone":"+************","preferredLanguage":"en"},"admin":null,"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-09-02","endDate":"2026-09-01","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-09-01","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-09-02","endDate":"2026-09-01","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 08:19:43] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 08:19:43] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Ramsay McMichael Consulting","address1":"Standard Bldg 102 Hope Street","city":"Glasgow","postal_code":"G2 6PH","country_code":"GB","state_province":"LANARKSHIRE","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T09:19:37+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Ramsay McMichael Consulting","addressLine1":"Standard Bldg 102 Hope Street","city":"Glasgow","stateProvinceCode":null,"stateProvince":"LANARKSHIRE","postalCode":"G2 6PH","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"John","lastName":"Stewart","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-09-02","endDate":"2026-09-01","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-09-01","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-09-02","endDate":"2026-09-01","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 08:19:43] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 09:37:38] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Jameson Builders","address1":"46 Chipstead Valley Road","city":"Coulsdon","postal_code":"CR5 2RA","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T10:37:32+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":1230,"totalDiscount":0,"totalNetAmount":1230,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":1230,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Jameson Builders","addressLine1":"46 Chipstead Valley Road","city":"Coulsdon","stateProvinceCode":null,"stateProvince":null,"postalCode":"CR5 2RA","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"*********","email":"<EMAIL>","firstName":"Heidi","lastName":"Jain","phone":"+************","preferredLanguage":"en"},"admin":null,"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-10-22","endDate":"2026-10-21","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":3,"subscription":{"id":"**************","quantity":3,"endDate":"2025-10-21","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":1230,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":1230,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":1230,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-10-22","endDate":"2026-10-21","extendedSRP":1230,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":1230}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 09:37:38] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 09:37:39] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Jameson Builders","address1":"46 Chipstead Valley Road","city":"Coulsdon","postal_code":"CR5 2RA","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T10:37:32+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":1230,"totalDiscount":0,"totalNetAmount":1230,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":1230,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Jameson Builders","addressLine1":"46 Chipstead Valley Road","city":"Coulsdon","stateProvinceCode":null,"stateProvince":null,"postalCode":"CR5 2RA","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"*********","email":"<EMAIL>","firstName":"Heidi","lastName":"Jain","phone":"+************","preferredLanguage":"en"},"admin":null,"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-10-22","endDate":"2026-10-21","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":3,"subscription":{"id":"**************","quantity":3,"endDate":"2025-10-21","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":1230,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":1230,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":1230,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-10-22","endDate":"2026-10-21","extendedSRP":1230,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":1230}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 09:37:39] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 11:08:22] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"GJR ARCHITECTS Ltd","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","city":"Exeter","postal_code":"EX5 1DP","country_code":"GB","state_province":"DEVON","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T12:08:16+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":0,"totalDiscount":0,"totalNetAmount":0,"estimatedTax":null,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":0,"currency":"GBP","priceRegionCode":null},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Aurangzaib","lastName":"Mahmood","phone":"+***********","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"GJR ARCHITECTS Ltd","addressLine1":"Axehayes Farm 1 Park 7 The Studio","addressLine2":"Clyst St. Mary","city":"Exeter","stateProvinceCode":null,"stateProvince":"DEVON","postalCode":"EX5 1DP","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Helen","lastName":"Barlow","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14424"}\n-->\n
[php_errors] [2025-08-26 11:08:22] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 11:08:22] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"GJR ARCHITECTS Ltd","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","city":"Exeter","postal_code":"EX5 1DP","country_code":"GB","state_province":"DEVON","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T12:08:16+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":0,"totalDiscount":0,"totalNetAmount":0,"estimatedTax":null,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":0,"currency":"GBP","priceRegionCode":null},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Aurangzaib","lastName":"Mahmood","phone":"+***********","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"GJR ARCHITECTS Ltd","addressLine1":"Axehayes Farm 1 Park 7 The Studio","addressLine2":"Clyst St. Mary","city":"Exeter","stateProvinceCode":null,"stateProvince":"DEVON","postalCode":"EX5 1DP","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Helen","lastName":"Barlow","phone":"+************","preferredLanguage":"en"},"admin":null,"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14424"}\n-->\n
[php_errors] [2025-08-26 11:08:22] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 11:17:32] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(101) "system\data_source_manager::get_data_source_data(): Return value must be of type array, none returned"\n  ["file"]: string(29) "data_source_manager.class.php"\n  ["line"]: int(721)\n  ["full_file"]: string(119) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php"\n  ["trace"]: string(707) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(162): system\data_source_manager::get_data_source_data()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#5 {main}"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_source_manager::get_data_source_data(): Return value must be of type array, none returned","file":"data_source_manager.class.php","line":721,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(162): system\\data_source_manager::get_data_source_data()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#5 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 11:17:36] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(101) "system\data_source_manager::get_data_source_data(): Return value must be of type array, none returned"\n  ["file"]: string(29) "data_source_manager.class.php"\n  ["line"]: int(721)\n  ["full_file"]: string(119) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php"\n  ["trace"]: string(707) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(162): system\data_source_manager::get_data_source_data()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#5 {main}"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_source_manager::get_data_source_data(): Return value must be of type array, none returned","file":"data_source_manager.class.php","line":721,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(162): system\\data_source_manager::get_data_source_data()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#5 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 11:18:19] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(101) "system\data_source_manager::get_data_source_data(): Return value must be of type array, none returned"\n  ["file"]: string(29) "data_source_manager.class.php"\n  ["line"]: int(721)\n  ["full_file"]: string(119) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php"\n  ["trace"]: string(707) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(162): system\data_source_manager::get_data_source_data()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#5 {main}"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_source_manager::get_data_source_data(): Return value must be of type array, none returned","file":"data_source_manager.class.php","line":721,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(162): system\\data_source_manager::get_data_source_data()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#5 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 12:09:47] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(50) "Undefined array key "accounts.nurtureReseller.csn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"accounts.nurtureReseller.csn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"accounts.nurtureReseller.csn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276140","solutionProvider_id":"2","soldTo_id":"2595"}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276140"}\n-->\n
[php_errors] [2025-08-26 12:09:47] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(56) "Undefined array key "endCustomer.account.endCustomerCsn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"endCustomer.account.endCustomerCsn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"endCustomer.account.endCustomerCsn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276140","solutionProvider_id":"2","soldTo_id":"2595","nurtureReseller_csn":null}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276140"}\n-->\n
[php_errors] [2025-08-26 12:09:47] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(51) "Undefined array key "accounts.solutionProvider.csn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"accounts.solutionProvider.csn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"accounts.solutionProvider.csn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276140","solutionProvider_id":"2","soldTo_id":"2595","nurtureReseller_csn":null,"endCustomer_csn":null}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276140"}\n-->\n
[php_errors] [2025-08-26 12:09:47] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(41) "Undefined array key "accounts.soldTo.csn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"accounts.soldTo.csn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"accounts.soldTo.csn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276140","solutionProvider_id":"2","soldTo_id":"2595","nurtureReseller_csn":null,"endCustomer_csn":null,"solutionProvider_csn":null}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276140"}\n-->\n
[php_errors] [2025-08-26 12:09:47] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 12:09:47] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-08-26 12:09:53] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(50) "Undefined array key "accounts.nurtureReseller.csn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"accounts.nurtureReseller.csn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"accounts.nurtureReseller.csn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276143","solutionProvider_id":"2","soldTo_id":"2595"}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276143"}\n-->\n
[php_errors] [2025-08-26 12:09:53] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(56) "Undefined array key "endCustomer.account.endCustomerCsn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"endCustomer.account.endCustomerCsn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"endCustomer.account.endCustomerCsn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276143","solutionProvider_id":"2","soldTo_id":"2595","nurtureReseller_csn":null}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276143"}\n-->\n
[php_errors] [2025-08-26 12:09:53] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(51) "Undefined array key "accounts.solutionProvider.csn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"accounts.solutionProvider.csn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"accounts.solutionProvider.csn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276143","solutionProvider_id":"2","soldTo_id":"2595","nurtureReseller_csn":null,"endCustomer_csn":null}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276143"}\n-->\n
[php_errors] [2025-08-26 12:09:53] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(41) "Undefined array key "accounts.soldTo.csn""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"accounts.soldTo.csn\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"accounts.soldTo.csn\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"subscriptionId":"**************","quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","autoRenew":"OFF","recordType":"Attribute based","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","endCustomer_id":"276143","solutionProvider_id":"2","soldTo_id":"2595","nurtureReseller_csn":null,"endCustomer_csn":null,"solutionProvider_csn":null}\n         1: {"nurtureReseller_id":"<group_insert_id>nurtureReseller<\/group_insert_id>","endCustomer_id":"<group_insert_id>endCustomer<\/group_insert_id>","solutionProvider_id":"<group_insert_id>solutionProvider<\/group_insert_id>","soldTo_id":"<group_insert_id>soldTo<\/group_insert_id>","nurtureReseller_csn":"accounts.nurtureReseller.csn","endCustomer_csn":"endCustomer.account.endCustomerCsn","solutionProvider_csn":"accounts.solutionProvider.csn","soldTo_csn":"accounts.soldTo.csn"}\n         2: {"subscriptionId":"**************","subscriptionReferenceNumber":null,"switch":{"fromSubscriptions":[],"toSubscription":null},"quantity":1,"status":"Active","startDate":"2025-08-26","endDate":"2026-08-25","term":"Annual","billingBehavior":"Recurring","billingFrequency":"Annual","intendedUsage":"COM","connectivity":"Online","connectivityInterval":"30 Days","opportunityNumber":null,"servicePlan":"Standard","accessModel":"Single User","paymentMethod":"Credit Card","recordType":"Attribute based","renewalCounter":null,"autoRenew":"OFF","offeringId":"OD-000280","offeringCode":"RVTLTS","offeringName":"AutoCAD Revit LT Suite","marketingName":"AutoCAD Revit LT Suite","currency":null,"annualDeclaredValue":null,"pricingMethod":{"code":null,"description":null},"accounts":{"soldTo":{"csn":"**********","name":"GJR ARCHITECTS Ltd"},"solutionProvider":{"csn":"**********","name":"TCS CAD & BIM Solutions Limited","localLanguageName":null,"type":"Reseller","address1":"Unit F, Yorkway","address2":"Mandale Ind Est","address3":null,"city":"Stockton On Tees","stateProvince":null,"postalCode":"TS17 6BX","country":"United Kingdom","stateProvinceCode":null,"countryCode":"GB"},"nurtureReseller":{"csn":null,"name":null,"lockDate":null,"nurtureDiscountEligibility":false}},"endCustomer":{"account":{"csn":"**********","name":"GJR ARCHITECTS Ltd","type":"End Customer","address1":"Axehayes Farm 1 Park 7 The Studio","address2":"Clyst St. Mary","address3":null,"city":"Exeter","stateProvince":"DEVON","postalCode":"EX5 1DP","country":"United Kingdom","individualFlag":false,"namedAccountFlag":true,"namedAccountGroup":"Territory","parentIndustryGroup":"AEC","parentIndustrySegment":"Architecture Services","primaryAdminFirstName":"Helen","primaryAdminLastName":"Barlow","primaryAdminEmail":"<EMAIL>","teamId":"8318025","teamName":"Helen","stateProvinceCode":null,"countryCode":"GB"},"purchaser":{"first":"Helen","last":"Barlow","email":"<EMAIL>","status":"Active","portalRegistration":null,"doNotCall":false,"doNotEmail":false,"doNotMail":false}}}\n         3: []\n         4: {"soldTo":"2595","solutionProvider":"2","endCustomer":"276143"}\n-->\n
[php_errors] [2025-08-26 12:09:53] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 12:09:53] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-08-26 12:17:13] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(270) "system\data_table_storage::process_columns_from_structure(): Argument #2 ($column_preferences) must be of type array, null given, called in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php on line 687"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(728)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(1276) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(687): system\data_table_storage::process_columns_from_structure()\n#1 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(39): system\data_table_storage::prepare_template_data()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(2): edge\edge::render()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n  ["request_uri"]: string(55) "/baffletrain/autocadlt/autobooks/autodesk/subscriptions"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::process_columns_from_structure(): Argument #2 ($column_preferences) must be of type array, null given, called in \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php on line 687","file":"data_table_storage.class.php","line":728,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(687): system\\data_table_storage::process_columns_from_structure()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(39): system\\data_table_storage::prepare_template_data()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(2): edge\\edge::render()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/subscriptions","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 12:33:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"AMW DESIGN Ltd","address1":"6 Lingfield Road","city":"Stockton-on-tees","postal_code":"TS19 7PJ","country_code":"GB","state_province":"CLEVELAND","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T13:33:37+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"AMW DESIGN Ltd","addressLine1":"6 Lingfield Road","city":"Stockton-on-tees","stateProvinceCode":null,"stateProvince":"CLEVELAND","postalCode":"TS19 7PJ","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"*********","email":"<EMAIL>","firstName":"Adam","lastName":"Walton","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-09-25","endDate":"2026-09-24","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-09-24","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-09-25","endDate":"2026-09-24","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 12:33:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 12:33:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"AMW DESIGN Ltd","address1":"6 Lingfield Road","city":"Stockton-on-tees","postal_code":"TS19 7PJ","country_code":"GB","state_province":"CLEVELAND","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T13:33:37+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":0,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":410,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"AMW DESIGN Ltd","addressLine1":"6 Lingfield Road","city":"Stockton-on-tees","stateProvinceCode":null,"stateProvince":"CLEVELAND","postalCode":"TS19 7PJ","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"*********","email":"<EMAIL>","firstName":"Adam","lastName":"Walton","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-09-25","endDate":"2026-09-24","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"**************","quantity":1,"endDate":"2025-09-24","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-09-25","endDate":"2026-09-24","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 12:33:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 12:38:40] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"GIGANT Ltd","address1":"UNIT 25 31 BARNES WALLIS RD,","address2":"SEGENSWORTH EAST","city":"Fareham","postal_code":"PO15 5TT","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T13:38:34+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":82,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":492,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"GIGANT Ltd","addressLine1":"UNIT 25 31 BARNES WALLIS RD,","addressLine2":"SEGENSWORTH EAST","city":"Fareham","stateProvinceCode":null,"stateProvince":null,"postalCode":"PO15 5TT","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"Ian","lastName":"Street","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-08-30","endDate":"2026-08-29","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"56717693784427","quantity":1,"endDate":"2025-08-29","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-08-30","endDate":"2026-08-29","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 12:38:40] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 12:38:40] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"GIGANT Ltd","address1":"UNIT 25 31 BARNES WALLIS RD,","address2":"SEGENSWORTH EAST","city":"Fareham","postal_code":"PO15 5TT","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T13:38:34+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":410,"totalDiscount":0,"totalNetAmount":410,"estimatedTax":82,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":492,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"GIGANT Ltd","addressLine1":"UNIT 25 31 BARNES WALLIS RD,","addressLine2":"SEGENSWORTH EAST","city":"Fareham","stateProvinceCode":null,"stateProvince":null,"postalCode":"PO15 5TT","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"********","email":"<EMAIL>","firstName":"Ian","lastName":"Street","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-08-30","endDate":"2026-08-29","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":1,"subscription":{"id":"56717693784427","quantity":1,"endDate":"2025-08-29","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":410,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":410,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":410,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-08-30","endDate":"2026-08-29","extendedSRP":410,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":410}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 12:38:40] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:06:09] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"ALMA SHEET METAL LTD","address1":"Unit 4 Mottram Way","city":"Macclesfield","postal_code":"SK10 2DH","country_code":"GB","state_province":"CHESHIRE","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T14:06:02+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":1230,"totalDiscount":0,"totalNetAmount":1230,"estimatedTax":246,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":1476,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"ALMA SHEET METAL LTD","addressLine1":"Unit 4 Mottram Way","city":"Macclesfield","stateProvinceCode":null,"stateProvince":"CHESHIRE","postalCode":"SK10 2DH","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"*********","email":"<EMAIL>","firstName":"Jim","lastName":"Ford","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-09-04","endDate":"2026-09-03","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":3,"subscription":{"id":"**************","quantity":3,"endDate":"2025-09-03","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":1230,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":1230,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":1230,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-09-04","endDate":"2026-09-03","extendedSRP":1230,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":1230}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 13:06:09] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:06:09] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"ALMA SHEET METAL LTD","address1":"Unit 4 Mottram Way","city":"Macclesfield","postal_code":"SK10 2DH","country_code":"GB","state_province":"CHESHIRE","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T14:06:02+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":1230,"totalDiscount":0,"totalNetAmount":1230,"estimatedTax":246,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":1476,"currency":"GBP","priceRegionCode":"E5"},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"ALMA SHEET METAL LTD","addressLine1":"Unit 4 Mottram Way","city":"Macclesfield","stateProvinceCode":null,"stateProvince":"CHESHIRE","postalCode":"SK10 2DH","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"*********","email":"<EMAIL>","firstName":"Jim","lastName":"Ford","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[{"lineNumber":1,"quoteLineNumber":"**********","startDate":"2025-09-04","endDate":"2026-09-03","offeringId":"OD-000031","offeringCode":"ACDLT","offeringName":"AutoCAD LT","marketingName":"AutoCAD LT","action":"Renewal","unitOfMeasure":"EA","quantity":3,"subscription":{"id":"**************","quantity":3,"endDate":"2025-09-03","term":{"code":"A01","description":"Annual"}},"referenceSubscription":null,"promotionCode":null,"promotionDescription":null,"promotionEndDate":null,"annualDeclaredValue":null,"declaredValueBasedOn":null,"scopeOfUse":null,"scopeDetails":null,"numberOfProjectsIncluded":null,"referenceSubscriptions":null,"agentLineReference":null,"specialProgramDiscountCode":null,"specialProgramDiscountDescription":null,"pricing":{"currency":"GBP","unitSRP":410,"extendedSRP":1230,"specialProgramDiscountAmount":0,"renewalDiscountPercent":0,"renewalDiscountAmount":0,"transactionVolumeDiscountPercent":0,"transactionVolumeDiscountAmount":0,"serviceDurationDiscountPercent":0,"serviceDurationDiscountAmount":0,"promotionDiscountPercent":0,"promotionDiscountAmount":0,"discountsApplied":0,"extendedDiscountedSRP":1230,"endUserAdditionalDiscountPercent":0,"endUserAdditionalDiscountAmount":0,"exclusiveDiscountsApplied":0,"endUserPrice":1230,"tax":null,"billPlans":[{"plan":1,"billDate":"2025-08-26","startDate":"2025-09-04","endDate":"2026-09-03","extendedSRP":1230,"discountsApplied":0,"exclusiveDiscountsApplied":0,"amount":1230}]},"offer":{"term":{"code":"A01","description":"Annual"},"accessModel":{"code":"S","description":"Single User"},"intendedUsage":{"code":"COM","description":"Commercial"},"connectivity":{"code":"C100","description":"Online"},"connectivityInterval":{"code":"C04","description":"30 Days"},"billingBehavior":{"code":"A200","description":"Recurring"},"billingType":{"code":"B100","description":"Up front"},"billingFrequency":{"code":"B05","description":"Annual"},"pricingMethod":{"code":"QTY","description":"Quantity Based"},"servicePlan":{"code":"STND","description":"Standard"}}}]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 13:06:09] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:12:50] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"AZUMI Ltd","address1":"Kings Court 2-16 Goodge Street","city":"London","postal_code":"W1T 2QA","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T14:12:44+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":0,"totalDiscount":0,"totalNetAmount":0,"estimatedTax":null,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":0,"currency":"GBP","priceRegionCode":null},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"AZUMI Ltd","addressLine1":"Kings Court 2-16 Goodge Street","city":"London","stateProvinceCode":null,"stateProvince":null,"postalCode":"W1T 2QA","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Jacob","lastName":"Wojciechowski","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 13:12:50] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:12:50] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"AZUMI Ltd","address1":"Kings Court 2-16 Goodge Street","city":"London","postal_code":"W1T 2QA","country_code":"GB","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T14:12:44+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":0,"totalDiscount":0,"totalNetAmount":0,"estimatedTax":null,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":0,"currency":"GBP","priceRegionCode":null},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"AZUMI Ltd","addressLine1":"Kings Court 2-16 Goodge Street","city":"London","stateProvinceCode":null,"stateProvince":null,"postalCode":"W1T 2QA","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Jacob","lastName":"Wojciechowski","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 13:12:50] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:16:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Arkitectonic","address1":"Flat 21 Bel Air Apartments La Rue De Trachy","address2":"St. Helier","city":"Jersey","postal_code":"JE2 3BA","country_code":"GB","state_province":"Jersey","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T14:16:38+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":0,"totalDiscount":0,"totalNetAmount":0,"estimatedTax":null,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":0,"currency":"GBP","priceRegionCode":null},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Arkitectonic","addressLine1":"Flat 21 Bel Air Apartments La Rue De Trachy","addressLine2":"St. Helier","city":"Jersey","stateProvinceCode":null,"stateProvince":"Jersey","postalCode":"JE2 3BA","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Dianna","lastName":"Clark","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 13:16:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:16:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(30) "Undefined array key "customer""\n  ["file"]: string(23) "data_importer.class.php"\n  ["line"]: int(519)\n  ["full_file"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"customer\"","file":"data_importer.class.php","line":519,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 519\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"customer\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php"\n         3: 519\n      <strong>Function:</strong> process_extra_fields, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php, Line: 326\n         <strong>Arguments:</strong> \n         0: {"account_csn":"**********","name":"Arkitectonic","address1":"Flat 21 Bel Air Apartments La Rue De Trachy","address2":"St. Helier","city":"Jersey","postal_code":"JE2 3BA","country_code":"GB","state_province":"Jersey","individual_flag":false}\n         1: {"account_type":"customer"}\n         2: {"quoteNumber":"*********","pdfLink":null,"opportunityNumber":null,"quoteCreatedTime":"2025-08-26T14:16:38+01:00","quoteExpirationDate":null,"quotedDate":null,"quoteStatus":"Draft","quoteLanguage":"en","timeZone":"Europe\/London","paymentTerms":{"code":null,"display":false,"description":null},"pricing":{"totalListAmount":0,"totalDiscount":0,"totalNetAmount":0,"estimatedTax":null,"totalTradeInAmount":0,"totalTradeInTaxAmount":0,"totalAmount":0,"currency":"GBP","priceRegionCode":null},"agentAccount":{"accountCsn":"**********","name":"TCS CAD & BIM Solutions Limited","addressLine1":"Unit F, Yorkway","addressLine2":"Mandale Ind Est","city":"Stockton On Tees","stateProvinceCode":null,"stateProvince":null,"postalCode":"TS17 6BX","countryCode":"GB","country":"United Kingdom"},"agentContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Emily","lastName":"Wood","phone":"+01642 677582","preferredLanguage":"en"},"endCustomer":{"accountCsn":"**********","isIndividual":false,"name":"Arkitectonic","addressLine1":"Flat 21 Bel Air Apartments La Rue De Trachy","addressLine2":"St. Helier","city":"Jersey","stateProvinceCode":null,"stateProvince":"Jersey","postalCode":"JE2 3BA","countryCode":"GB","country":"United Kingdom"},"quoteContact":{"contactCsn":"**********","email":"<EMAIL>","firstName":"Dianna","lastName":"Clark","phone":"+************","preferredLanguage":"en"},"admin":{"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"},"salesRep":null,"skipDDACheck":false,"additionalRecipients":[],"quoteNotes":null,"originatedBy":"Partner","skipM2SDiscountValidation":false,"agentQuoteReference":null,"lineItems":[]}\n         3: []\n         4: {"agent_account":"2","agent_contact":"14060"}\n-->\n
[php_errors] [2025-08-26 13:16:44] [functions.php:201] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:129)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 201\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:129)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-08-26 13:19:28] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:19:28] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:19:52] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(5)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":5,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:19:52] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(5)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":5,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:20:14] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(5)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":5,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:20:14] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(5)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":5,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:20:17] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(73) "count(): Argument #1 ($value) must be of type Countable|array, null given"\n  ["file"]: string(54) "data_sources_e90554140b905f72ed2f26b9085d22f4.comp.php"\n  ["line"]: int(3)\n  ["full_file"]: string(109) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/views/data_sources_e90554140b905f72ed2f26b9085d22f4.comp.php"\n  ["trace"]: string(666) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/data_sources_e90554140b905f72ed2f26b9085d22f4.comp.php(3): count()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#5 {main}"\n  ["request_uri"]: string(52) "/baffletrain/autocadlt/autobooks/system/data_sources"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"count(): Argument #1 ($value) must be of type Countable|array, null given","file":"data_sources_e90554140b905f72ed2f26b9085d22f4.comp.php","line":3,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/data_sources_e90554140b905f72ed2f26b9085d22f4.comp.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/data_sources_e90554140b905f72ed2f26b9085d22f4.comp.php(3): count()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#5 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/data_sources","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:20:20] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(5)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":5,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:20:20] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(5)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":5,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:21:13] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(67) "array_map(): Argument #2 ($array) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(1843) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(1): array_map()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(1): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(1): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(1): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#14 {main}"\n  ["request_uri"]: string(55) "/baffletrain/autocadlt/autobooks/autodesk/subscriptions"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"array_map(): Argument #2 ($array) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(1): array_map()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(1): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(1): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(1): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#14 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/subscriptions","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:21:30] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(67) "array_map(): Argument #2 ($array) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(1843) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(1): array_map()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(1): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(1): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(1): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#14 {main}"\n  ["request_uri"]: string(55) "/baffletrain/autocadlt/autobooks/autodesk/subscriptions"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"array_map(): Argument #2 ($array) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(1): array_map()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(1): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(1): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(1): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#14 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/subscriptions","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:21:37] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:21:37] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:03] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(67) "array_map(): Argument #2 ($array) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(2256) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(1): array_map()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(1): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(1): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php(276): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/orders.fn.php(91): data_table\data_table::process_data_table()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/orders/orders.view.php(2): generate_orders_table()\n#12 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-view.edge.php(1): include('...')\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#17 {main}"\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/orders"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"array_map(): Argument #2 ($array) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(1): array_map()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(1): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(1): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table.class.php(276): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/orders.fn.php(91): data_table\\data_table::process_data_table()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/orders\/orders.view.php(2): generate_orders_table()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-view.edge.php(1): include('...')\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#17 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/orders","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:03] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:03] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:04] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(67) "array_map(): Argument #2 ($array) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(2267) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(1): array_map()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(1): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(1): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php(276): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php(78): data_table\data_table::process_data_table()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/customers/customers.view.php(2): generate_customer_table()\n#12 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-view.edge.php(1): include('...')\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#17 {main}"\n  ["request_uri"]: string(51) "/baffletrain/autocadlt/autobooks/autodesk/customers"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"array_map(): Argument #2 ($array) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(1): array_map()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(1): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(1): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table.class.php(276): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/customers.fn.php(78): data_table\\data_table::process_data_table()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/customers\/customers.view.php(2): generate_customer_table()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-view.edge.php(1): include('...')\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#17 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/customers","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:04] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:04] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:06] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(67) "array_map(): Argument #2 ($array) must be of type array, null given"\n  ["file"]: string(40) "data-table-column-manager-panel.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(94) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php"\n  ["trace"]: string(1839) "#0 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php(1): array_map()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php(1): edge\edge::render()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(1): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/dashboard_7d20dfb372a432ef98f53048308642ea.comp.php(1): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#14 {main}"\n  ["request_uri"]: string(42) "/baffletrain/autocadlt/autobooks/dashboard"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"array_map(): Argument #2 ($array) must be of type array, null given","file":"data-table-column-manager-panel.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager-panel.edge.php(1): array_map()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php(1): edge\\edge::render()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(1): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/dashboard_7d20dfb372a432ef98f53048308642ea.comp.php(1): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#14 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/dashboard","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:06] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/api/unified/dashboard_stats"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/unified\/dashboard_stats","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:09] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(68) "/baffletrain/autocadlt/autobooks/api/notifications/get_notifications"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_notifications","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:23:09] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(10) "ParseError"\n  ["message"]: string(38) "syntax error, unexpected token "endif""\n  ["file"]: string(19) "layout-api.edge.php"\n  ["line"]: int(1)\n  ["full_file"]: string(73) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php"\n  ["trace"]: string(403) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#3 {main}"\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/api/notifications/get_unread_count"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"ParseError","message":"syntax error, unexpected token \"endif\"","file":"layout-api.edge.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#3 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/notifications\/get_unread_count","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 13:36:26] [functions.php:225] 
[php_errors] [2025-08-26 13:36:28] [functions.php:225] 
[php_errors] [2025-08-26 13:36:35] [functions.php:225] 
[php_errors] [2025-08-26 13:37:31] [functions.php:225] 
[php_errors] [2025-08-26 14:02:09] [functions.php:225] 
[php_errors] [2025-08-26 14:02:15] [functions.php:225] 
[php_errors] [2025-08-26 14:02:28] [functions.php:225] 
[php_errors] [2025-08-26 14:10:29] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(270) "system\data_table_storage::process_columns_from_structure(): Argument #2 ($column_preferences) must be of type array, null given, called in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php on line 687"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(728)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(1276) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(687): system\data_table_storage::process_columns_from_structure()\n#1 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php(39): system\data_table_storage::prepare_template_data()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(2): edge\edge::render()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(181): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n  ["request_uri"]: string(55) "/baffletrain/autocadlt/autobooks/autodesk/subscriptions"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::process_columns_from_structure(): Argument #2 ($column_preferences) must be of type array, null given, called in \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php on line 687","file":"data_table_storage.class.php","line":728,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(687): system\\data_table_storage::process_columns_from_structure()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php(39): system\\data_table_storage::prepare_template_data()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscriptions_4aab0818650ec1233d2fa1d597b98b4b.comp.php(2): edge\\edge::render()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(181): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/subscriptions","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 14:10:40] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(259) "system\data_table_storage::save_configuration(): Argument #4 ($data_source_id) must be of type ?int, string given, called in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php on line 28"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(75)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(1044) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(28): system\data_table_storage::save_configuration()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(577): api\data_table\column_preferences\save_column_preferences()\n#2 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\data_table\column_preferences\reorder_columns()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#7 {main}"\n  ["request_uri"]: string(82) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::save_configuration(): Argument #4 ($data_source_id) must be of type ?int, string given, called in \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php on line 28","file":"data_table_storage.class.php","line":75,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(28): system\\data_table_storage::save_configuration()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(577): api\\data_table\\column_preferences\\save_column_preferences()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\data_table\\column_preferences\\reorder_columns()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#7 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table\/column_preferences\/reorder_columns","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 14:11:01] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(259) "system\data_table_storage::save_configuration(): Argument #4 ($data_source_id) must be of type ?int, string given, called in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php on line 28"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(75)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(1044) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(28): system\data_table_storage::save_configuration()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(577): api\data_table\column_preferences\save_column_preferences()\n#2 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\data_table\column_preferences\reorder_columns()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#7 {main}"\n  ["request_uri"]: string(82) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::save_configuration(): Argument #4 ($data_source_id) must be of type ?int, string given, called in \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php on line 28","file":"data_table_storage.class.php","line":75,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(28): system\\data_table_storage::save_configuration()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(577): api\\data_table\\column_preferences\\save_column_preferences()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\data_table\\column_preferences\\reorder_columns()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#7 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table\/column_preferences\/reorder_columns","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-08-26 14:15:51] [functions.php:224] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(259) "system\data_table_storage::save_configuration(): Argument #4 ($data_source_id) must be of type ?int, string given, called in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php on line 28"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(75)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(1044) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(28): system\data_table_storage::save_configuration()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php(577): api\data_table\column_preferences\save_column_preferences()\n#2 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\data_table\column_preferences\reorder_columns()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(147): include('...')\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(140): edge\edge::phprender()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#7 {main}"\n  ["request_uri"]: string(82) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 224\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::save_configuration(): Argument #4 ($data_source_id) must be of type ?int, string given, called in \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php on line 28","file":"data_table_storage.class.php","line":75,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(28): system\\data_table_storage::save_configuration()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php(577): api\\data_table\\column_preferences\\save_column_preferences()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\data_table\\column_preferences\\reorder_columns()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(147): include('...')\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(140): edge\\edge::phprender()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#7 {main}","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table\/column_preferences\/reorder_columns","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
