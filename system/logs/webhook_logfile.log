[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 09:45:08
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:36]  Provided signature: sha256=11ee8a292e6780479c0b7ed9015e13afb9a6c256d2c59b0d77c5e3ff4cd08d15
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:37]  Calculated signature: sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8d56f773a4368a8e\n    [X-B3-Traceid] => 68a59921d6ef70cea18754091004f23f\n    [B3] => 68a59921d6ef70cea18754091004f23f-8d56f773a4368a8e-1\n    [Traceparent] => 00-68a59921d6ef70cea18754091004f23f-8d56f773a4368a8e-01\n    [X-Amzn-Trace-Id] => Root=1-68a59921-d6ef70cea18754091004f23f;Parent=8d56f773a4368a8e;Sampled=1\n    [X-Adsk-Signature] => sha256=11ee8a292e6780479c0b7ed9015e13afb9a6c256d2c59b0d77c5e3ff4cd08d15\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Ordered","message":"Quote# Q-995219 status changed to Ordered.","modifiedAt":"2025-08-20T09:45:05.184Z"},"publishedAt":"2025-08-20T09:45:05.000Z","csn":"5103159758"}
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 09:45:08
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:36]  Provided signature: sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:37]  Calculated signature: sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 017c3ac81c6cfa56\n    [X-B3-Traceid] => 68a59921d6ef70cea18754091004f23f\n    [B3] => 68a59921d6ef70cea18754091004f23f-017c3ac81c6cfa56-1\n    [Traceparent] => 00-68a59921d6ef70cea18754091004f23f-017c3ac81c6cfa56-01\n    [X-Amzn-Trace-Id] => Root=1-68a59921-d6ef70cea18754091004f23f;Parent=017c3ac81c6cfa56;Sampled=1\n    [X-Adsk-Signature] => sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Ordered","message":"Quote# Q-995219 status changed to Ordered.","modifiedAt":"2025-08-20T09:45:05.184Z"},"publishedAt":"2025-08-20T09:45:05.000Z","csn":"5103159758"}
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 10:10:19
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:36]  Provided signature: sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:37]  Calculated signature: sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7acd0a11cdd4e537\n    [X-B3-Traceid] => 68a59f09043f834e109e9d755d22a94d\n    [B3] => 68a59f09043f834e109e9d755d22a94d-7acd0a11cdd4e537-1\n    [Traceparent] => 00-68a59f09043f834e109e9d755d22a94d-7acd0a11cdd4e537-01\n    [X-Amzn-Trace-Id] => Root=1-68a59f09-043f834e109e9d755d22a94d;Parent=7acd0a11cdd4e537;Sampled=1\n    [X-Adsk-Signature] => sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"a7f76573-ab85-494e-bf70-3088e81a056f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","quantity":1,"endDate":"2026-09-14","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-20T09:45:10.000+0000"},"publishedAt":"2025-08-20T10:10:17.000Z","csn":"5103159758"}
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 10:10:19
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:36]  Provided signature: sha256=b7bbe4ad66adbfc9ac2da46b95571306927d611e15ef7ee30c6245eea6839fb6
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:37]  Calculated signature: sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cb8e73c716d57d9d\n    [X-B3-Traceid] => 68a59f09043f834e109e9d755d22a94d\n    [B3] => 68a59f09043f834e109e9d755d22a94d-cb8e73c716d57d9d-1\n    [Traceparent] => 00-68a59f09043f834e109e9d755d22a94d-cb8e73c716d57d9d-01\n    [X-Amzn-Trace-Id] => Root=1-68a59f09-043f834e109e9d755d22a94d;Parent=cb8e73c716d57d9d;Sampled=1\n    [X-Adsk-Signature] => sha256=b7bbe4ad66adbfc9ac2da46b95571306927d611e15ef7ee30c6245eea6839fb6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"a7f76573-ab85-494e-bf70-3088e81a056f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","quantity":1,"endDate":"2026-09-14","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-20T09:45:10.000+0000"},"publishedAt":"2025-08-20T10:10:17.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:11:19
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:36]  Provided signature: sha256=a8400acf525803ca46860707b715509776924a56ae6b6b1d75e32620da215497
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:37]  Calculated signature: sha256=a8400acf525803ca46860707b715509776924a56ae6b6b1d75e32620da215497
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5e0946ca92a5d4c0\n    [X-B3-Traceid] => 68a5ad5576d67c18701b0bf57b3e5f29\n    [B3] => 68a5ad5576d67c18701b0bf57b3e5f29-5e0946ca92a5d4c0-1\n    [Traceparent] => 00-68a5ad5576d67c18701b0bf57b3e5f29-5e0946ca92a5d4c0-01\n    [X-Amzn-Trace-Id] => Root=1-68a5ad55-76d67c18701b0bf57b3e5f29;Parent=5e0946ca92a5d4c0;Sampled=1\n    [X-Adsk-Signature] => sha256=a8400acf525803ca46860707b715509776924a56ae6b6b1d75e32620da215497\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d531a53a-540c-4f9b-8c7c-63793b4769e6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"d531a53a-540c-4f9b-8c7c-63793b4769e6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73832204736884","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-20T10:56:15.000+0000"},"publishedAt":"2025-08-20T11:11:17.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:11:19
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:36]  Provided signature: sha256=60fab605e52e00db4d3b772e912c031fcb3887c7d63f801e5c9ff23231712d32
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:37]  Calculated signature: sha256=a8400acf525803ca46860707b715509776924a56ae6b6b1d75e32620da215497
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1df2faa720585782\n    [X-B3-Traceid] => 68a5ad5576d67c18701b0bf57b3e5f29\n    [B3] => 68a5ad5576d67c18701b0bf57b3e5f29-1df2faa720585782-1\n    [Traceparent] => 00-68a5ad5576d67c18701b0bf57b3e5f29-1df2faa720585782-01\n    [X-Amzn-Trace-Id] => Root=1-68a5ad55-76d67c18701b0bf57b3e5f29;Parent=1df2faa720585782;Sampled=1\n    [X-Adsk-Signature] => sha256=60fab605e52e00db4d3b772e912c031fcb3887c7d63f801e5c9ff23231712d32\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d531a53a-540c-4f9b-8c7c-63793b4769e6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:11:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"d531a53a-540c-4f9b-8c7c-63793b4769e6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73832204736884","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-20T10:56:15.000+0000"},"publishedAt":"2025-08-20T11:11:17.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:21:23
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:36]  Provided signature: sha256=81a8ea761ebc2b80a186ab2c91f9480b8d653f63717551c3b1c83886e01880da
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:37]  Calculated signature: sha256=734b084ac861651cfd93e1c4079b903a3ad717927f54857951daf09682944c13
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 88dbec23d3e95e8e\n    [X-B3-Traceid] => 68a5afb072db4ef4a531d0e19bff87cc\n    [B3] => 68a5afb072db4ef4a531d0e19bff87cc-88dbec23d3e95e8e-1\n    [Traceparent] => 00-68a5afb072db4ef4a531d0e19bff87cc-88dbec23d3e95e8e-01\n    [X-Amzn-Trace-Id] => Root=1-68a5afb0-72db4ef4a531d0e19bff87cc;Parent=88dbec23d3e95e8e;Sampled=1\n    [X-Adsk-Signature] => sha256=81a8ea761ebc2b80a186ab2c91f9480b8d653f63717551c3b1c83886e01880da\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 55a024d6-ec4c-4ce1-9ae0-aedaede1e299\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"55a024d6-ec4c-4ce1-9ae0-aedaede1e299","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983948","transactionId":"4eed2cf9-fd17-5477-baf3-4df20da2aa7a","quoteStatus":"Order Submitted","message":"Quote# Q-983948 status changed to Order Submitted.","modifiedAt":"2025-08-20T11:21:19.929Z"},"publishedAt":"2025-08-20T11:21:20.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:21:23
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:36]  Provided signature: sha256=734b084ac861651cfd93e1c4079b903a3ad717927f54857951daf09682944c13
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:37]  Calculated signature: sha256=734b084ac861651cfd93e1c4079b903a3ad717927f54857951daf09682944c13
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d6911b281eae51a3\n    [X-B3-Traceid] => 68a5afb072db4ef4a531d0e19bff87cc\n    [B3] => 68a5afb072db4ef4a531d0e19bff87cc-d6911b281eae51a3-1\n    [Traceparent] => 00-68a5afb072db4ef4a531d0e19bff87cc-d6911b281eae51a3-01\n    [X-Amzn-Trace-Id] => Root=1-68a5afb0-72db4ef4a531d0e19bff87cc;Parent=d6911b281eae51a3;Sampled=1\n    [X-Adsk-Signature] => sha256=734b084ac861651cfd93e1c4079b903a3ad717927f54857951daf09682944c13\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 55a024d6-ec4c-4ce1-9ae0-aedaede1e299\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:21:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"55a024d6-ec4c-4ce1-9ae0-aedaede1e299","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983948","transactionId":"4eed2cf9-fd17-5477-baf3-4df20da2aa7a","quoteStatus":"Order Submitted","message":"Quote# Q-983948 status changed to Order Submitted.","modifiedAt":"2025-08-20T11:21:19.929Z"},"publishedAt":"2025-08-20T11:21:20.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:21:24
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:36]  Provided signature: sha256=d3112c556296afd5c41d02cc240e345d3867c57afb94caea9e42517748a88f85
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:37]  Calculated signature: sha256=b853e6338c0d65531f7f154311edf80add4c0c420390cc0656c502e7a2f61754
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 89b79aca18c5fed0\n    [X-B3-Traceid] => 68a5afb1c8f8214f7264d9a87ab664a1\n    [B3] => 68a5afb1c8f8214f7264d9a87ab664a1-89b79aca18c5fed0-1\n    [Traceparent] => 00-68a5afb1c8f8214f7264d9a87ab664a1-89b79aca18c5fed0-01\n    [X-Amzn-Trace-Id] => Root=1-68a5afb1-c8f8214f7264d9a87ab664a1;Parent=89b79aca18c5fed0;Sampled=1\n    [X-Adsk-Signature] => sha256=d3112c556296afd5c41d02cc240e345d3867c57afb94caea9e42517748a88f85\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cff8b8a8-f74c-42ce-81cd-f692fea8f378\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"cff8b8a8-f74c-42ce-81cd-f692fea8f378","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983948","transactionId":"4eed2cf9-fd17-5477-baf3-4df20da2aa7a","quoteStatus":"Ordered","message":"Quote# Q-983948 status changed to Ordered.","modifiedAt":"2025-08-20T11:21:21.523Z"},"publishedAt":"2025-08-20T11:21:21.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:21:24
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:36]  Provided signature: sha256=b853e6338c0d65531f7f154311edf80add4c0c420390cc0656c502e7a2f61754
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:37]  Calculated signature: sha256=b853e6338c0d65531f7f154311edf80add4c0c420390cc0656c502e7a2f61754
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f05699e70f88d661\n    [X-B3-Traceid] => 68a5afb1c8f8214f7264d9a87ab664a1\n    [B3] => 68a5afb1c8f8214f7264d9a87ab664a1-f05699e70f88d661-1\n    [Traceparent] => 00-68a5afb1c8f8214f7264d9a87ab664a1-f05699e70f88d661-01\n    [X-Amzn-Trace-Id] => Root=1-68a5afb1-c8f8214f7264d9a87ab664a1;Parent=f05699e70f88d661;Sampled=1\n    [X-Adsk-Signature] => sha256=b853e6338c0d65531f7f154311edf80add4c0c420390cc0656c502e7a2f61754\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cff8b8a8-f74c-42ce-81cd-f692fea8f378\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:21:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"cff8b8a8-f74c-42ce-81cd-f692fea8f378","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983948","transactionId":"4eed2cf9-fd17-5477-baf3-4df20da2aa7a","quoteStatus":"Ordered","message":"Quote# Q-983948 status changed to Ordered.","modifiedAt":"2025-08-20T11:21:21.523Z"},"publishedAt":"2025-08-20T11:21:21.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:36:36
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:36]  Provided signature: sha256=b8cf9cda76215322b75063561b8b60d574999a552310c4bf6233751cb18122c6
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:37]  Calculated signature: sha256=fccfa29e46685157113023752b72f0600b6007ba55364bb3ac58c614059b26bb
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5d380392bd9a6501\n    [X-B3-Traceid] => 68a5b3413871c53e2dbafd2840e9c14e\n    [B3] => 68a5b3413871c53e2dbafd2840e9c14e-5d380392bd9a6501-1\n    [Traceparent] => 00-68a5b3413871c53e2dbafd2840e9c14e-5d380392bd9a6501-01\n    [X-Amzn-Trace-Id] => Root=1-68a5b341-3871c53e2dbafd2840e9c14e;Parent=5d380392bd9a6501;Sampled=1\n    [X-Adsk-Signature] => sha256=b8cf9cda76215322b75063561b8b60d574999a552310c4bf6233751cb18122c6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c3f40a1c-d5a2-4107-8561-7ee8f58d415d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"c3f40a1c-d5a2-4107-8561-7ee8f58d415d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","status":"Active","quantity":2,"endDate":"2026-09-05","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-20T11:21:31.000+0000"},"publishedAt":"2025-08-20T11:36:33.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:36:36
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:36]  Provided signature: sha256=fccfa29e46685157113023752b72f0600b6007ba55364bb3ac58c614059b26bb
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:37]  Calculated signature: sha256=fccfa29e46685157113023752b72f0600b6007ba55364bb3ac58c614059b26bb
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ac46669adb25f3fa\n    [X-B3-Traceid] => 68a5b3413871c53e2dbafd2840e9c14e\n    [B3] => 68a5b3413871c53e2dbafd2840e9c14e-ac46669adb25f3fa-1\n    [Traceparent] => 00-68a5b3413871c53e2dbafd2840e9c14e-ac46669adb25f3fa-01\n    [X-Amzn-Trace-Id] => Root=1-68a5b341-3871c53e2dbafd2840e9c14e;Parent=ac46669adb25f3fa;Sampled=1\n    [X-Adsk-Signature] => sha256=fccfa29e46685157113023752b72f0600b6007ba55364bb3ac58c614059b26bb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c3f40a1c-d5a2-4107-8561-7ee8f58d415d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:36:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"c3f40a1c-d5a2-4107-8561-7ee8f58d415d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","status":"Active","quantity":2,"endDate":"2026-09-05","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-20T11:21:31.000+0000"},"publishedAt":"2025-08-20T11:36:33.000Z","csn":"5103159758"}
[webhook] [2025-08-20 11:38:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 11:38:49
[webhook] [2025-08-20 11:38:49] [adwsapi_v2.php:36]  Provided signature: sha256=caa0ce2d3a1a371e98803a42585f7ca0bc5d49d0a7d14296148d7cb7471e2fac
[webhook] [2025-08-20 11:38:49] [adwsapi_v2.php:37]  Calculated signature: sha256=8181a515d2d6039e29fd5af6f32f01d0543d7dd62f78ea85d2e712150ec4eeec
[webhook] [2025-08-20 11:38:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 32913874c95f452e\n    [X-B3-Traceid] => 68a5b3c7036ea9e76b8533522f7049f9\n    [B3] => 68a5b3c7036ea9e76b8533522f7049f9-32913874c95f452e-1\n    [Traceparent] => 00-68a5b3c7036ea9e76b8533522f7049f9-32913874c95f452e-01\n    [X-Amzn-Trace-Id] => Root=1-68a5b3c7-036ea9e76b8533522f7049f9;Parent=32913874c95f452e;Sampled=1\n    [X-Adsk-Signature] => sha256=caa0ce2d3a1a371e98803a42585f7ca0bc5d49d0a7d14296148d7cb7471e2fac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => de95a763-5881-4f50-8c25-724758a470d3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 11:38:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"de95a763-5881-4f50-8c25-724758a470d3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-20T11:18:39.000+0000"},"publishedAt":"2025-08-20T11:38:47.000Z","csn":"5103159758"}
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 12:52:41
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:36]  Provided signature: sha256=ce362e2ef5d5ab2ee61cb93603eb17d834f8678be0846c4487235573ead35f5a
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:37]  Calculated signature: sha256=ce362e2ef5d5ab2ee61cb93603eb17d834f8678be0846c4487235573ead35f5a
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3b7aec43b084dd6c\n    [X-B3-Traceid] => 68a5c5175ab6aa357cbdf37a11e82ab3\n    [B3] => 68a5c5175ab6aa357cbdf37a11e82ab3-3b7aec43b084dd6c-1\n    [Traceparent] => 00-68a5c5175ab6aa357cbdf37a11e82ab3-3b7aec43b084dd6c-01\n    [X-Amzn-Trace-Id] => Root=1-68a5c517-5ab6aa357cbdf37a11e82ab3;Parent=3b7aec43b084dd6c;Sampled=1\n    [X-Adsk-Signature] => sha256=ce362e2ef5d5ab2ee61cb93603eb17d834f8678be0846c4487235573ead35f5a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755694359196-75326363131554-9033712530-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755694359196-75326363131554-9033712530-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75326363131554","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T12:52:39.196Z"},"publishedAt":"2025-08-20T12:52:39.000Z","csn":"5103159758"}
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 12:52:41
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:36]  Provided signature: sha256=b567a1801fe2a7f51dd72af2236ef2176b502c7a21fc2325041310c4489cd29b
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:37]  Calculated signature: sha256=ce362e2ef5d5ab2ee61cb93603eb17d834f8678be0846c4487235573ead35f5a
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 78e058e887048a36\n    [X-B3-Traceid] => 68a5c5175ab6aa357cbdf37a11e82ab3\n    [B3] => 68a5c5175ab6aa357cbdf37a11e82ab3-78e058e887048a36-1\n    [Traceparent] => 00-68a5c5175ab6aa357cbdf37a11e82ab3-78e058e887048a36-01\n    [X-Amzn-Trace-Id] => Root=1-68a5c517-5ab6aa357cbdf37a11e82ab3;Parent=78e058e887048a36;Sampled=1\n    [X-Adsk-Signature] => sha256=b567a1801fe2a7f51dd72af2236ef2176b502c7a21fc2325041310c4489cd29b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755694359196-75326363131554-9033712530-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 12:52:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755694359196-75326363131554-9033712530-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75326363131554","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T12:52:39.196Z"},"publishedAt":"2025-08-20T12:52:39.000Z","csn":"5103159758"}
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 13:01:37
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:36]  Provided signature: sha256=998b2bf986da9677b7b3fe93b409ee322f247bb5929cb06ad23acccc737eb801
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:37]  Calculated signature: sha256=9029f32fa6909764505568e3b7896779e3bcea3a3318a1aff50859238227be18
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 432c0a609c55f89e\n    [X-B3-Traceid] => 68a5c72e78c203c31f80800451628772\n    [B3] => 68a5c72e78c203c31f80800451628772-432c0a609c55f89e-1\n    [Traceparent] => 00-68a5c72e78c203c31f80800451628772-432c0a609c55f89e-01\n    [X-Amzn-Trace-Id] => Root=1-68a5c72e-78c203c31f80800451628772;Parent=432c0a609c55f89e;Sampled=1\n    [X-Adsk-Signature] => sha256=998b2bf986da9677b7b3fe93b409ee322f247bb5929cb06ad23acccc737eb801\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755694894836-69140838535597-9033709346-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755694894836-69140838535597-9033709346-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69140838535597","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T13:01:34.836Z"},"publishedAt":"2025-08-20T13:01:34.000Z","csn":"5103159758"}
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 13:01:37
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:36]  Provided signature: sha256=9029f32fa6909764505568e3b7896779e3bcea3a3318a1aff50859238227be18
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:37]  Calculated signature: sha256=9029f32fa6909764505568e3b7896779e3bcea3a3318a1aff50859238227be18
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8077af6f2efbf7cd\n    [X-B3-Traceid] => 68a5c72e78c203c31f80800451628772\n    [B3] => 68a5c72e78c203c31f80800451628772-8077af6f2efbf7cd-1\n    [Traceparent] => 00-68a5c72e78c203c31f80800451628772-8077af6f2efbf7cd-01\n    [X-Amzn-Trace-Id] => Root=1-68a5c72e-78c203c31f80800451628772;Parent=8077af6f2efbf7cd;Sampled=1\n    [X-Adsk-Signature] => sha256=9029f32fa6909764505568e3b7896779e3bcea3a3318a1aff50859238227be18\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755694894836-69140838535597-9033709346-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 13:01:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755694894836-69140838535597-9033709346-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69140838535597","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T13:01:34.836Z"},"publishedAt":"2025-08-20T13:01:34.000Z","csn":"5103159758"}
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 14:26:44
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:36]  Provided signature: sha256=a4477f3f62963fa4c7d8ebdadc85a7105c6185ce37ebf352abbd89801d4fef9b
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:37]  Calculated signature: sha256=b255dbaa523bf7e659a2477cb126264ce7a6ffc0ac65fc5c7c7af2f0ed1ca1fa
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ab8189abca95512b\n    [X-B3-Traceid] => 68a5db210499b8ca82429e0a908ceb33\n    [B3] => 68a5db210499b8ca82429e0a908ceb33-ab8189abca95512b-1\n    [Traceparent] => 00-68a5db210499b8ca82429e0a908ceb33-ab8189abca95512b-01\n    [X-Amzn-Trace-Id] => Root=1-68a5db21-0499b8ca82429e0a908ceb33;Parent=ab8189abca95512b;Sampled=1\n    [X-Adsk-Signature] => sha256=a4477f3f62963fa4c7d8ebdadc85a7105c6185ce37ebf352abbd89801d4fef9b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e885d9ca-b047-4406-b315-c22f43186e1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"e885d9ca-b047-4406-b315-c22f43186e1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1020126","transactionId":"1d3c44d5-6dd4-533a-9e7f-25cab08ff6a6","quoteStatus":"Draft","message":"Quote# Q-1020126 status changed to Draft.","modifiedAt":"2025-08-20T14:26:41.649Z"},"publishedAt":"2025-08-20T14:26:42.000Z","csn":"5103159758"}
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 14:26:44
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:36]  Provided signature: sha256=b255dbaa523bf7e659a2477cb126264ce7a6ffc0ac65fc5c7c7af2f0ed1ca1fa
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:37]  Calculated signature: sha256=b255dbaa523bf7e659a2477cb126264ce7a6ffc0ac65fc5c7c7af2f0ed1ca1fa
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bd3dd5f32381215d\n    [X-B3-Traceid] => 68a5db210499b8ca82429e0a908ceb33\n    [B3] => 68a5db210499b8ca82429e0a908ceb33-bd3dd5f32381215d-1\n    [Traceparent] => 00-68a5db210499b8ca82429e0a908ceb33-bd3dd5f32381215d-01\n    [X-Amzn-Trace-Id] => Root=1-68a5db21-0499b8ca82429e0a908ceb33;Parent=bd3dd5f32381215d;Sampled=1\n    [X-Adsk-Signature] => sha256=b255dbaa523bf7e659a2477cb126264ce7a6ffc0ac65fc5c7c7af2f0ed1ca1fa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e885d9ca-b047-4406-b315-c22f43186e1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 14:26:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"e885d9ca-b047-4406-b315-c22f43186e1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1020126","transactionId":"1d3c44d5-6dd4-533a-9e7f-25cab08ff6a6","quoteStatus":"Draft","message":"Quote# Q-1020126 status changed to Draft.","modifiedAt":"2025-08-20T14:26:41.649Z"},"publishedAt":"2025-08-20T14:26:42.000Z","csn":"5103159758"}
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 14:28:01
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:36]  Provided signature: sha256=69b698f15edd0c59dcef5ecdee7d66f60ce11cc6f1f03538620dda091284e21d
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:37]  Calculated signature: sha256=c9582e62ee019507ee1a88e0944fce6fc048a3ee6ae27113f3880eedf6237e63
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a4b6372745fc504d\n    [X-B3-Traceid] => 68a5db6f0391ce92615cfdfe7d63346e\n    [B3] => 68a5db6f0391ce92615cfdfe7d63346e-a4b6372745fc504d-1\n    [Traceparent] => 00-68a5db6f0391ce92615cfdfe7d63346e-a4b6372745fc504d-01\n    [X-Amzn-Trace-Id] => Root=1-68a5db6f-0391ce92615cfdfe7d63346e;Parent=a4b6372745fc504d;Sampled=1\n    [X-Adsk-Signature] => sha256=69b698f15edd0c59dcef5ecdee7d66f60ce11cc6f1f03538620dda091284e21d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a5c4d4a9-837f-4129-8151-b2d7c92d4304\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"a5c4d4a9-837f-4129-8151-b2d7c92d4304","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1020130","transactionId":"2e8f305b-9f26-5b81-b1a4-5144428513ad","quoteStatus":"Draft","message":"Quote# Q-1020130 status changed to Draft.","modifiedAt":"2025-08-20T14:27:58.816Z"},"publishedAt":"2025-08-20T14:27:59.000Z","csn":"5103159758"}
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 14:28:01
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:36]  Provided signature: sha256=c9582e62ee019507ee1a88e0944fce6fc048a3ee6ae27113f3880eedf6237e63
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:37]  Calculated signature: sha256=c9582e62ee019507ee1a88e0944fce6fc048a3ee6ae27113f3880eedf6237e63
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 72d1637444479d3b\n    [X-B3-Traceid] => 68a5db6f0391ce92615cfdfe7d63346e\n    [B3] => 68a5db6f0391ce92615cfdfe7d63346e-72d1637444479d3b-1\n    [Traceparent] => 00-68a5db6f0391ce92615cfdfe7d63346e-72d1637444479d3b-01\n    [X-Amzn-Trace-Id] => Root=1-68a5db6f-0391ce92615cfdfe7d63346e;Parent=72d1637444479d3b;Sampled=1\n    [X-Adsk-Signature] => sha256=c9582e62ee019507ee1a88e0944fce6fc048a3ee6ae27113f3880eedf6237e63\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a5c4d4a9-837f-4129-8151-b2d7c92d4304\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 14:28:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"a5c4d4a9-837f-4129-8151-b2d7c92d4304","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1020130","transactionId":"2e8f305b-9f26-5b81-b1a4-5144428513ad","quoteStatus":"Draft","message":"Quote# Q-1020130 status changed to Draft.","modifiedAt":"2025-08-20T14:27:58.816Z"},"publishedAt":"2025-08-20T14:27:59.000Z","csn":"5103159758"}
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 14:29:14
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:36]  Provided signature: sha256=38f7b6cd384528b7ae7a77bcac9e67c10ed853ec20beb2123645e9722a8d1246
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:37]  Calculated signature: sha256=72c6c15862dcc6d09d6e626d5bbea0d58db6c48af578641eb3cb96e9e3d265e7
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f759734563a015ba\n    [X-B3-Traceid] => 68a5dbb820e61acde9bc144c51aa6138\n    [B3] => 68a5dbb820e61acde9bc144c51aa6138-f759734563a015ba-1\n    [Traceparent] => 00-68a5dbb820e61acde9bc144c51aa6138-f759734563a015ba-01\n    [X-Amzn-Trace-Id] => Root=1-68a5dbb8-20e61acde9bc144c51aa6138;Parent=f759734563a015ba;Sampled=1\n    [X-Adsk-Signature] => sha256=38f7b6cd384528b7ae7a77bcac9e67c10ed853ec20beb2123645e9722a8d1246\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d96acee4-408f-4d7d-ac90-d44b43db8a0d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"d96acee4-408f-4d7d-ac90-d44b43db8a0d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1020130","transactionId":"2e8f305b-9f26-5b81-b1a4-5144428513ad","quoteStatus":"Quoted","message":"Quote# Q-1020130 status changed to Quoted.","modifiedAt":"2025-08-20T14:29:11.840Z"},"publishedAt":"2025-08-20T14:29:12.000Z","csn":"5103159758"}
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 14:29:14
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:36]  Provided signature: sha256=72c6c15862dcc6d09d6e626d5bbea0d58db6c48af578641eb3cb96e9e3d265e7
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:37]  Calculated signature: sha256=72c6c15862dcc6d09d6e626d5bbea0d58db6c48af578641eb3cb96e9e3d265e7
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9293a56dd47d09f5\n    [X-B3-Traceid] => 68a5dbb820e61acde9bc144c51aa6138\n    [B3] => 68a5dbb820e61acde9bc144c51aa6138-9293a56dd47d09f5-1\n    [Traceparent] => 00-68a5dbb820e61acde9bc144c51aa6138-9293a56dd47d09f5-01\n    [X-Amzn-Trace-Id] => Root=1-68a5dbb8-20e61acde9bc144c51aa6138;Parent=9293a56dd47d09f5;Sampled=1\n    [X-Adsk-Signature] => sha256=72c6c15862dcc6d09d6e626d5bbea0d58db6c48af578641eb3cb96e9e3d265e7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d96acee4-408f-4d7d-ac90-d44b43db8a0d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 14:29:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"d96acee4-408f-4d7d-ac90-d44b43db8a0d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1020130","transactionId":"2e8f305b-9f26-5b81-b1a4-5144428513ad","quoteStatus":"Quoted","message":"Quote# Q-1020130 status changed to Quoted.","modifiedAt":"2025-08-20T14:29:11.840Z"},"publishedAt":"2025-08-20T14:29:12.000Z","csn":"5103159758"}
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 15:39:16
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:36]  Provided signature: sha256=85b0ccf6d4f629d28b71a350d1fc4754598a92e5e11ed29aa2992c25ccdf28d2
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:37]  Calculated signature: sha256=b8a057a987b02d38a37ee3afe0704f4a337b90fc9a388fb7c24b79dd361d2c73
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 99d996c5b2728417\n    [X-B3-Traceid] => 68a5ec2266ff4a39558c63471d63589d\n    [B3] => 68a5ec2266ff4a39558c63471d63589d-99d996c5b2728417-1\n    [Traceparent] => 00-68a5ec2266ff4a39558c63471d63589d-99d996c5b2728417-01\n    [X-Amzn-Trace-Id] => Root=1-68a5ec22-66ff4a39558c63471d63589d;Parent=99d996c5b2728417;Sampled=1\n    [X-Adsk-Signature] => sha256=85b0ccf6d4f629d28b71a350d1fc4754598a92e5e11ed29aa2992c25ccdf28d2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 88a977c3-59da-4ba3-987d-816568c3d34c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"88a977c3-59da-4ba3-987d-816568c3d34c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-20T15:09:08.000+0000"},"publishedAt":"2025-08-20T15:39:14.000Z","csn":"5103159758"}
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 15:39:16
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:36]  Provided signature: sha256=b8a057a987b02d38a37ee3afe0704f4a337b90fc9a388fb7c24b79dd361d2c73
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:37]  Calculated signature: sha256=b8a057a987b02d38a37ee3afe0704f4a337b90fc9a388fb7c24b79dd361d2c73
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d5a292f4cd331e37\n    [X-B3-Traceid] => 68a5ec2266ff4a39558c63471d63589d\n    [B3] => 68a5ec2266ff4a39558c63471d63589d-d5a292f4cd331e37-1\n    [Traceparent] => 00-68a5ec2266ff4a39558c63471d63589d-d5a292f4cd331e37-01\n    [X-Amzn-Trace-Id] => Root=1-68a5ec22-66ff4a39558c63471d63589d;Parent=d5a292f4cd331e37;Sampled=1\n    [X-Adsk-Signature] => sha256=b8a057a987b02d38a37ee3afe0704f4a337b90fc9a388fb7c24b79dd361d2c73\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 88a977c3-59da-4ba3-987d-816568c3d34c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 15:39:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"88a977c3-59da-4ba3-987d-816568c3d34c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-20T15:09:08.000+0000"},"publishedAt":"2025-08-20T15:39:14.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:09:50
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:36]  Provided signature: sha256=a15aaadc383aa20242765b88f0004baf7bf67cf47996db7fc2a4231a70739686
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:37]  Calculated signature: sha256=a15aaadc383aa20242765b88f0004baf7bf67cf47996db7fc2a4231a70739686
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 63d351a97076465e\n    [X-B3-Traceid] => 68a5f34b06f5bab834d202690f5d7f4e\n    [B3] => 68a5f34b06f5bab834d202690f5d7f4e-63d351a97076465e-1\n    [Traceparent] => 00-68a5f34b06f5bab834d202690f5d7f4e-63d351a97076465e-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f34b-06f5bab834d202690f5d7f4e;Parent=63d351a97076465e;Sampled=1\n    [X-Adsk-Signature] => sha256=a15aaadc383aa20242765b88f0004baf7bf67cf47996db7fc2a4231a70739686\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c42ff49d-4d11-4ec6-bda0-44e82ea96f74\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"c42ff49d-4d11-4ec6-bda0-44e82ea96f74","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71079825161867","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:54:45.000+0000"},"publishedAt":"2025-08-20T16:09:47.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:09:50
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:36]  Provided signature: sha256=d379d2d8145f7734d2c8569b959817ac2bf0cdda38f8b61f4eae0b4d78b638a6
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:37]  Calculated signature: sha256=a15aaadc383aa20242765b88f0004baf7bf67cf47996db7fc2a4231a70739686
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4fe80e60516d1a6d\n    [X-B3-Traceid] => 68a5f34b06f5bab834d202690f5d7f4e\n    [B3] => 68a5f34b06f5bab834d202690f5d7f4e-4fe80e60516d1a6d-1\n    [Traceparent] => 00-68a5f34b06f5bab834d202690f5d7f4e-4fe80e60516d1a6d-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f34b-06f5bab834d202690f5d7f4e;Parent=4fe80e60516d1a6d;Sampled=1\n    [X-Adsk-Signature] => sha256=d379d2d8145f7734d2c8569b959817ac2bf0cdda38f8b61f4eae0b4d78b638a6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c42ff49d-4d11-4ec6-bda0-44e82ea96f74\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:09:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"c42ff49d-4d11-4ec6-bda0-44e82ea96f74","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71079825161867","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:54:45.000+0000"},"publishedAt":"2025-08-20T16:09:47.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:10:13
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:36]  Provided signature: sha256=79692a0f7736609e74cb6cf14969568c5e17bdbeebcbec492c4cb80af446b467
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:37]  Calculated signature: sha256=79692a0f7736609e74cb6cf14969568c5e17bdbeebcbec492c4cb80af446b467
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 601d563537b321b3\n    [X-B3-Traceid] => 68a5f36354bbd0cc1c5338b06e6baeb3\n    [B3] => 68a5f36354bbd0cc1c5338b06e6baeb3-601d563537b321b3-1\n    [Traceparent] => 00-68a5f36354bbd0cc1c5338b06e6baeb3-601d563537b321b3-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f363-54bbd0cc1c5338b06e6baeb3;Parent=601d563537b321b3;Sampled=1\n    [X-Adsk-Signature] => sha256=79692a0f7736609e74cb6cf14969568c5e17bdbeebcbec492c4cb80af446b467\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 27b1ff77-87f1-45a3-a1e6-1e00d2fc7387\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"27b1ff77-87f1-45a3-a1e6-1e00d2fc7387","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71079825162968","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:55:07.000+0000"},"publishedAt":"2025-08-20T16:10:11.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:10:13
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:36]  Provided signature: sha256=043687d878e3868b95e68bd26b24051d5cbd2e21b6e47f65cb29be461118689b
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:37]  Calculated signature: sha256=79692a0f7736609e74cb6cf14969568c5e17bdbeebcbec492c4cb80af446b467
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 464a54a80e691972\n    [X-B3-Traceid] => 68a5f36354bbd0cc1c5338b06e6baeb3\n    [B3] => 68a5f36354bbd0cc1c5338b06e6baeb3-464a54a80e691972-1\n    [Traceparent] => 00-68a5f36354bbd0cc1c5338b06e6baeb3-464a54a80e691972-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f363-54bbd0cc1c5338b06e6baeb3;Parent=464a54a80e691972;Sampled=1\n    [X-Adsk-Signature] => sha256=043687d878e3868b95e68bd26b24051d5cbd2e21b6e47f65cb29be461118689b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 27b1ff77-87f1-45a3-a1e6-1e00d2fc7387\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:10:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"27b1ff77-87f1-45a3-a1e6-1e00d2fc7387","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71079825162968","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:55:07.000+0000"},"publishedAt":"2025-08-20T16:10:11.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:10:34
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:36]  Provided signature: sha256=8d4ca40b72665eab614448f8a70fb1d1b8a566f9d261a6c77c18ec542d3f8943
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:37]  Calculated signature: sha256=478a4e0052d36405c5e468a52f2fbf1ffdffd45ae7e296d3afc27c88649351c3
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 318845e83f4f48e9\n    [X-B3-Traceid] => 68a5f378568eb11305b658b73bc20d71\n    [B3] => 68a5f378568eb11305b658b73bc20d71-318845e83f4f48e9-1\n    [Traceparent] => 00-68a5f378568eb11305b658b73bc20d71-318845e83f4f48e9-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f378-568eb11305b658b73bc20d71;Parent=318845e83f4f48e9;Sampled=1\n    [X-Adsk-Signature] => sha256=8d4ca40b72665eab614448f8a70fb1d1b8a566f9d261a6c77c18ec542d3f8943\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e267193c-d448-4240-87aa-01de4cf68003\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"e267193c-d448-4240-87aa-01de4cf68003","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73314215486158","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-20T15:55:29.000+0000"},"publishedAt":"2025-08-20T16:10:32.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:10:34
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:36]  Provided signature: sha256=478a4e0052d36405c5e468a52f2fbf1ffdffd45ae7e296d3afc27c88649351c3
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:37]  Calculated signature: sha256=478a4e0052d36405c5e468a52f2fbf1ffdffd45ae7e296d3afc27c88649351c3
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f01d7e1bb1f2c0de\n    [X-B3-Traceid] => 68a5f378568eb11305b658b73bc20d71\n    [B3] => 68a5f378568eb11305b658b73bc20d71-f01d7e1bb1f2c0de-1\n    [Traceparent] => 00-68a5f378568eb11305b658b73bc20d71-f01d7e1bb1f2c0de-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f378-568eb11305b658b73bc20d71;Parent=f01d7e1bb1f2c0de;Sampled=1\n    [X-Adsk-Signature] => sha256=478a4e0052d36405c5e468a52f2fbf1ffdffd45ae7e296d3afc27c88649351c3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e267193c-d448-4240-87aa-01de4cf68003\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:10:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"e267193c-d448-4240-87aa-01de4cf68003","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73314215486158","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-20T15:55:29.000+0000"},"publishedAt":"2025-08-20T16:10:32.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:10:49
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:36]  Provided signature: sha256=9dcbc27d0d474fcd9fc1c16a0ccf9afeda1441bf0213d6b1bab181871f414ec7
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:37]  Calculated signature: sha256=4b6dd3f219fb08ac7fc207b07463bffdc637d7b9b112e5be1584a74194ea7f19
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a4a414c92a524900\n    [X-B3-Traceid] => 68a5f3860dfe48233b36142f5308211f\n    [B3] => 68a5f3860dfe48233b36142f5308211f-a4a414c92a524900-1\n    [Traceparent] => 00-68a5f3860dfe48233b36142f5308211f-a4a414c92a524900-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f386-0dfe48233b36142f5308211f;Parent=a4a414c92a524900;Sampled=1\n    [X-Adsk-Signature] => sha256=9dcbc27d0d474fcd9fc1c16a0ccf9afeda1441bf0213d6b1bab181871f414ec7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1eb84ba8-08e3-4b48-9635-f67477307519\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1eb84ba8-08e3-4b48-9635-f67477307519","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73712837059216","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:55:44.000+0000"},"publishedAt":"2025-08-20T16:10:46.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:10:49
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:36]  Provided signature: sha256=4b6dd3f219fb08ac7fc207b07463bffdc637d7b9b112e5be1584a74194ea7f19
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:37]  Calculated signature: sha256=4b6dd3f219fb08ac7fc207b07463bffdc637d7b9b112e5be1584a74194ea7f19
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9f5ff11162354abb\n    [X-B3-Traceid] => 68a5f3860dfe48233b36142f5308211f\n    [B3] => 68a5f3860dfe48233b36142f5308211f-9f5ff11162354abb-1\n    [Traceparent] => 00-68a5f3860dfe48233b36142f5308211f-9f5ff11162354abb-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f386-0dfe48233b36142f5308211f;Parent=9f5ff11162354abb;Sampled=1\n    [X-Adsk-Signature] => sha256=4b6dd3f219fb08ac7fc207b07463bffdc637d7b9b112e5be1584a74194ea7f19\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1eb84ba8-08e3-4b48-9635-f67477307519\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:10:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1eb84ba8-08e3-4b48-9635-f67477307519","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73712837059216","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:55:44.000+0000"},"publishedAt":"2025-08-20T16:10:46.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:11:09
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:36]  Provided signature: sha256=3e6345f534f09723e555bc6244833cbd57e1a3357d221e7ed61ae4162d0f8c7b
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:37]  Calculated signature: sha256=3e6345f534f09723e555bc6244833cbd57e1a3357d221e7ed61ae4162d0f8c7b
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1cb23c16cb900701\n    [X-B3-Traceid] => 68a5f39b7f5f825e1851f49a30532b0f\n    [B3] => 68a5f39b7f5f825e1851f49a30532b0f-1cb23c16cb900701-1\n    [Traceparent] => 00-68a5f39b7f5f825e1851f49a30532b0f-1cb23c16cb900701-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f39b-7f5f825e1851f49a30532b0f;Parent=1cb23c16cb900701;Sampled=1\n    [X-Adsk-Signature] => sha256=3e6345f534f09723e555bc6244833cbd57e1a3357d221e7ed61ae4162d0f8c7b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 408ead5e-ce07-444a-82d2-7d88dd943aa5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"408ead5e-ce07-444a-82d2-7d88dd943aa5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73858227484216","quantity":4,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:56:04.000+0000"},"publishedAt":"2025-08-20T16:11:07.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:11:09
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:36]  Provided signature: sha256=75c17796632daba3173c4e782fc811b3a063daa7c1339b6af68db3e40e9af98e
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:37]  Calculated signature: sha256=3e6345f534f09723e555bc6244833cbd57e1a3357d221e7ed61ae4162d0f8c7b
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6d4a917070a7a050\n    [X-B3-Traceid] => 68a5f39b7f5f825e1851f49a30532b0f\n    [B3] => 68a5f39b7f5f825e1851f49a30532b0f-6d4a917070a7a050-1\n    [Traceparent] => 00-68a5f39b7f5f825e1851f49a30532b0f-6d4a917070a7a050-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f39b-7f5f825e1851f49a30532b0f;Parent=6d4a917070a7a050;Sampled=1\n    [X-Adsk-Signature] => sha256=75c17796632daba3173c4e782fc811b3a063daa7c1339b6af68db3e40e9af98e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 408ead5e-ce07-444a-82d2-7d88dd943aa5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:11:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"408ead5e-ce07-444a-82d2-7d88dd943aa5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73858227484216","quantity":4,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:56:04.000+0000"},"publishedAt":"2025-08-20T16:11:07.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:11:31
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:36]  Provided signature: sha256=a48c1a1040cc37002a2fa0a0cc0932bb5fe896104a96eb9e3fceacd544653ecd
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:37]  Calculated signature: sha256=a48c1a1040cc37002a2fa0a0cc0932bb5fe896104a96eb9e3fceacd544653ecd
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f608f79e5de10642\n    [X-B3-Traceid] => 68a5f3b14c51cffc32bdd3f0720f18e0\n    [B3] => 68a5f3b14c51cffc32bdd3f0720f18e0-f608f79e5de10642-1\n    [Traceparent] => 00-68a5f3b14c51cffc32bdd3f0720f18e0-f608f79e5de10642-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f3b1-4c51cffc32bdd3f0720f18e0;Parent=f608f79e5de10642;Sampled=1\n    [X-Adsk-Signature] => sha256=a48c1a1040cc37002a2fa0a0cc0932bb5fe896104a96eb9e3fceacd544653ecd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 470285ff-1c69-438f-b640-fe09dcb003a7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"470285ff-1c69-438f-b640-fe09dcb003a7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73982345172995","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:56:26.000+0000"},"publishedAt":"2025-08-20T16:11:29.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:11:31
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:36]  Provided signature: sha256=c5df310b53b5eebbd65f3ebd46574e2e50521746c04f3931202ba1d7d5fa41a6
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:37]  Calculated signature: sha256=a48c1a1040cc37002a2fa0a0cc0932bb5fe896104a96eb9e3fceacd544653ecd
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1c1393026fa1ba40\n    [X-B3-Traceid] => 68a5f3b14c51cffc32bdd3f0720f18e0\n    [B3] => 68a5f3b14c51cffc32bdd3f0720f18e0-1c1393026fa1ba40-1\n    [Traceparent] => 00-68a5f3b14c51cffc32bdd3f0720f18e0-1c1393026fa1ba40-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f3b1-4c51cffc32bdd3f0720f18e0;Parent=1c1393026fa1ba40;Sampled=1\n    [X-Adsk-Signature] => sha256=c5df310b53b5eebbd65f3ebd46574e2e50521746c04f3931202ba1d7d5fa41a6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 470285ff-1c69-438f-b640-fe09dcb003a7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:11:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"470285ff-1c69-438f-b640-fe09dcb003a7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73982345172995","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-20T15:56:26.000+0000"},"publishedAt":"2025-08-20T16:11:29.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:19:18
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:36]  Provided signature: sha256=970e400fab7217ba5415320d0978e2daebb407dba26f89dc70204f76be686845
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:37]  Calculated signature: sha256=ba40121876a2ba2e899837afee3d29fee3671518706f41aed5a7e2ebadc42b16
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 568b0cfdf88c8ffe\n    [X-B3-Traceid] => 68a5f58360dc7ec32f481e103953b3f6\n    [B3] => 68a5f58360dc7ec32f481e103953b3f6-568b0cfdf88c8ffe-1\n    [Traceparent] => 00-68a5f58360dc7ec32f481e103953b3f6-568b0cfdf88c8ffe-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f583-60dc7ec32f481e103953b3f6;Parent=568b0cfdf88c8ffe;Sampled=1\n    [X-Adsk-Signature] => sha256=970e400fab7217ba5415320d0978e2daebb407dba26f89dc70204f76be686845\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755706755950-69400848050282-9033785035-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755706755950-69400848050282-9033785035-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T16:19:15.950Z"},"publishedAt":"2025-08-20T16:19:16.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:19:18
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:36]  Provided signature: sha256=ba40121876a2ba2e899837afee3d29fee3671518706f41aed5a7e2ebadc42b16
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:37]  Calculated signature: sha256=ba40121876a2ba2e899837afee3d29fee3671518706f41aed5a7e2ebadc42b16
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 39771f86b07abe7b\n    [X-B3-Traceid] => 68a5f58360dc7ec32f481e103953b3f6\n    [B3] => 68a5f58360dc7ec32f481e103953b3f6-39771f86b07abe7b-1\n    [Traceparent] => 00-68a5f58360dc7ec32f481e103953b3f6-39771f86b07abe7b-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f583-60dc7ec32f481e103953b3f6;Parent=39771f86b07abe7b;Sampled=1\n    [X-Adsk-Signature] => sha256=ba40121876a2ba2e899837afee3d29fee3671518706f41aed5a7e2ebadc42b16\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755706755950-69400848050282-9033785035-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:19:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755706755950-69400848050282-9033785035-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T16:19:15.950Z"},"publishedAt":"2025-08-20T16:19:16.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:27:46
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:36]  Provided signature: sha256=75af786383c0e4cc4337f86df3d1018a3f855a8552053be27852df9d44bde2d9
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:37]  Calculated signature: sha256=6925e9438ed3c27d622f173640ea54ee8eeb30a7b659a6f981a4e1a0b77fdb43
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3ae010e420aea2c6\n    [X-B3-Traceid] => 68a5f77f3bbc8abe32f3f3675f79ddf9\n    [B3] => 68a5f77f3bbc8abe32f3f3675f79ddf9-3ae010e420aea2c6-1\n    [Traceparent] => 00-68a5f77f3bbc8abe32f3f3675f79ddf9-3ae010e420aea2c6-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f77f-3bbc8abe32f3f3675f79ddf9;Parent=3ae010e420aea2c6;Sampled=1\n    [X-Adsk-Signature] => sha256=75af786383c0e4cc4337f86df3d1018a3f855a8552053be27852df9d44bde2d9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755707263766-69477960142838-9033784732-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755707263766-69477960142838-9033784732-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T16:27:43.767Z"},"publishedAt":"2025-08-20T16:27:43.000Z","csn":"5103159758"}
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 16:27:46
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:36]  Provided signature: sha256=6925e9438ed3c27d622f173640ea54ee8eeb30a7b659a6f981a4e1a0b77fdb43
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:37]  Calculated signature: sha256=6925e9438ed3c27d622f173640ea54ee8eeb30a7b659a6f981a4e1a0b77fdb43
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f860bcb7c2c7aac5\n    [X-B3-Traceid] => 68a5f77f3bbc8abe32f3f3675f79ddf9\n    [B3] => 68a5f77f3bbc8abe32f3f3675f79ddf9-f860bcb7c2c7aac5-1\n    [Traceparent] => 00-68a5f77f3bbc8abe32f3f3675f79ddf9-f860bcb7c2c7aac5-01\n    [X-Amzn-Trace-Id] => Root=1-68a5f77f-3bbc8abe32f3f3675f79ddf9;Parent=f860bcb7c2c7aac5;Sampled=1\n    [X-Adsk-Signature] => sha256=6925e9438ed3c27d622f173640ea54ee8eeb30a7b659a6f981a4e1a0b77fdb43\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755707263766-69477960142838-9033784732-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 16:27:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755707263766-69477960142838-9033784732-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T16:27:43.767Z"},"publishedAt":"2025-08-20T16:27:43.000Z","csn":"5103159758"}
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 20:36:53
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:36]  Provided signature: sha256=461fd6484db75b27e50979b1979c6f96b9feba44bd08844d7e6dab7194ecfd9f
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:37]  Calculated signature: sha256=ef8bbd50bc634ce7d3e012608bd5a52116ff4b26953bbf5af3265b6d69913b41
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bcd5f89b3e43485f\n    [X-B3-Traceid] => 68a631e374f463fd13c1611b089f090d\n    [B3] => 68a631e374f463fd13c1611b089f090d-bcd5f89b3e43485f-1\n    [Traceparent] => 00-68a631e374f463fd13c1611b089f090d-bcd5f89b3e43485f-01\n    [X-Amzn-Trace-Id] => Root=1-68a631e3-74f463fd13c1611b089f090d;Parent=bcd5f89b3e43485f;Sampled=1\n    [X-Adsk-Signature] => sha256=461fd6484db75b27e50979b1979c6f96b9feba44bd08844d7e6dab7194ecfd9f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755722211484-69400848050282-9033785035-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755722211484-69400848050282-9033785035-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T20:36:51.484Z"},"publishedAt":"2025-08-20T20:36:51.000Z","csn":"5103159758"}
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 20:36:53
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:36]  Provided signature: sha256=ef8bbd50bc634ce7d3e012608bd5a52116ff4b26953bbf5af3265b6d69913b41
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:37]  Calculated signature: sha256=ef8bbd50bc634ce7d3e012608bd5a52116ff4b26953bbf5af3265b6d69913b41
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 665f3c4ce43fa006\n    [X-B3-Traceid] => 68a631e374f463fd13c1611b089f090d\n    [B3] => 68a631e374f463fd13c1611b089f090d-665f3c4ce43fa006-1\n    [Traceparent] => 00-68a631e374f463fd13c1611b089f090d-665f3c4ce43fa006-01\n    [X-Amzn-Trace-Id] => Root=1-68a631e3-74f463fd13c1611b089f090d;Parent=665f3c4ce43fa006;Sampled=1\n    [X-Adsk-Signature] => sha256=ef8bbd50bc634ce7d3e012608bd5a52116ff4b26953bbf5af3265b6d69913b41\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755722211484-69400848050282-9033785035-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 20:36:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755722211484-69400848050282-9033785035-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69400848050282","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T20:36:51.484Z"},"publishedAt":"2025-08-20T20:36:51.000Z","csn":"5103159758"}
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 20:37:35
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:36]  Provided signature: sha256=4d4fb3af3e0b172690dec404103520afd9bfa22999a11748b5e99864069cbf1a
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:37]  Calculated signature: sha256=e759ba2c233bc5670d3a989b1d88fa040894e79fc875d9f44b191a0d8f28725b
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c14f1a1c6bec088d\n    [X-B3-Traceid] => 68a6320d36752324732b65e028634c33\n    [B3] => 68a6320d36752324732b65e028634c33-c14f1a1c6bec088d-1\n    [Traceparent] => 00-68a6320d36752324732b65e028634c33-c14f1a1c6bec088d-01\n    [X-Amzn-Trace-Id] => Root=1-68a6320d-36752324732b65e028634c33;Parent=c14f1a1c6bec088d;Sampled=1\n    [X-Adsk-Signature] => sha256=4d4fb3af3e0b172690dec404103520afd9bfa22999a11748b5e99864069cbf1a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755722253652-69477960142838-9033784732-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755722253652-69477960142838-9033784732-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T20:37:33.652Z"},"publishedAt":"2025-08-20T20:37:33.000Z","csn":"5103159758"}
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 20:37:35
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:36]  Provided signature: sha256=e759ba2c233bc5670d3a989b1d88fa040894e79fc875d9f44b191a0d8f28725b
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:37]  Calculated signature: sha256=e759ba2c233bc5670d3a989b1d88fa040894e79fc875d9f44b191a0d8f28725b
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6489ce75fb15e988\n    [X-B3-Traceid] => 68a6320d36752324732b65e028634c33\n    [B3] => 68a6320d36752324732b65e028634c33-6489ce75fb15e988-1\n    [Traceparent] => 00-68a6320d36752324732b65e028634c33-6489ce75fb15e988-01\n    [X-Amzn-Trace-Id] => Root=1-68a6320d-36752324732b65e028634c33;Parent=6489ce75fb15e988;Sampled=1\n    [X-Adsk-Signature] => sha256=e759ba2c233bc5670d3a989b1d88fa040894e79fc875d9f44b191a0d8f28725b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755722253652-69477960142838-9033784732-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 20:37:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755722253652-69477960142838-9033784732-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-20T20:37:33.652Z"},"publishedAt":"2025-08-20T20:37:33.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:00:32
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:36]  Provided signature: sha256=04a0ad46e53a5d27c98d40c8193819aad9dd12a095c33a8db9f499e19f600be4
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:37]  Calculated signature: sha256=b1c2dff966df4cc2e15bbfbc5053ed14afb569b3e0d9a62ed598bd653cbf89cb
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ba1fe50576b5f5b5\n    [X-B3-Traceid] => 68a6538d985439344eae0762264d7118\n    [B3] => 68a6538d985439344eae0762264d7118-ba1fe50576b5f5b5-1\n    [Traceparent] => 00-68a6538d985439344eae0762264d7118-ba1fe50576b5f5b5-01\n    [X-Amzn-Trace-Id] => Root=1-68a6538d-985439344eae0762264d7118;Parent=ba1fe50576b5f5b5;Sampled=1\n    [X-Adsk-Signature] => sha256=04a0ad46e53a5d27c98d40c8193819aad9dd12a095c33a8db9f499e19f600be4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86eeb7c8-be42-4ea4-8a36-7909017ae8bd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"86eeb7c8-be42-4ea4-8a36-7909017ae8bd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-945834","transactionId":"4ddf1ffa-95d0-5844-aa5e-7f02ab0c7b34","quoteStatus":"Expired","message":"Quote# Q-945834 status changed to Expired.","modifiedAt":"2025-08-20T23:00:29.088Z"},"publishedAt":"2025-08-20T23:00:29.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:00:32
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:36]  Provided signature: sha256=b1c2dff966df4cc2e15bbfbc5053ed14afb569b3e0d9a62ed598bd653cbf89cb
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:37]  Calculated signature: sha256=b1c2dff966df4cc2e15bbfbc5053ed14afb569b3e0d9a62ed598bd653cbf89cb
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b17828a849b7e2fe\n    [X-B3-Traceid] => 68a6538d985439344eae0762264d7118\n    [B3] => 68a6538d985439344eae0762264d7118-b17828a849b7e2fe-1\n    [Traceparent] => 00-68a6538d985439344eae0762264d7118-b17828a849b7e2fe-01\n    [X-Amzn-Trace-Id] => Root=1-68a6538d-985439344eae0762264d7118;Parent=b17828a849b7e2fe;Sampled=1\n    [X-Adsk-Signature] => sha256=b1c2dff966df4cc2e15bbfbc5053ed14afb569b3e0d9a62ed598bd653cbf89cb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86eeb7c8-be42-4ea4-8a36-7909017ae8bd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:00:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"86eeb7c8-be42-4ea4-8a36-7909017ae8bd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-945834","transactionId":"4ddf1ffa-95d0-5844-aa5e-7f02ab0c7b34","quoteStatus":"Expired","message":"Quote# Q-945834 status changed to Expired.","modifiedAt":"2025-08-20T23:00:29.088Z"},"publishedAt":"2025-08-20T23:00:29.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:00:43
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:36]  Provided signature: sha256=920b4ccb6caed243a5b50eb6f219902f1487332e8d9bbec422444c29f2bed453
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:37]  Calculated signature: sha256=920b4ccb6caed243a5b50eb6f219902f1487332e8d9bbec422444c29f2bed453
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:00:43
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e6f44a198be5ac94\n    [X-B3-Traceid] => 68a65398ad5f4f2baac9cbd8650ac1b6\n    [B3] => 68a65398ad5f4f2baac9cbd8650ac1b6-e6f44a198be5ac94-1\n    [Traceparent] => 00-68a65398ad5f4f2baac9cbd8650ac1b6-e6f44a198be5ac94-01\n    [X-Amzn-Trace-Id] => Root=1-68a65398-ad5f4f2baac9cbd8650ac1b6;Parent=e6f44a198be5ac94;Sampled=1\n    [X-Adsk-Signature] => sha256=920b4ccb6caed243a5b50eb6f219902f1487332e8d9bbec422444c29f2bed453\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 01eec202-7929-403b-a290-010c9490b3dc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"01eec202-7929-403b-a290-010c9490b3dc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-944964","transactionId":"7d050f9d-7971-5430-850f-6f657a47c776","quoteStatus":"Expired","message":"Quote# Q-944964 status changed to Expired.","modifiedAt":"2025-08-20T23:00:30.299Z"},"publishedAt":"2025-08-20T23:00:40.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:36]  Provided signature: sha256=5f52b12cac114121f0eda21c79ff09e2aba7b9a3d48b6bf04a944a445a96ad6e
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:37]  Calculated signature: sha256=920b4ccb6caed243a5b50eb6f219902f1487332e8d9bbec422444c29f2bed453
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5729dbf2f6e6f035\n    [X-B3-Traceid] => 68a65398ad5f4f2baac9cbd8650ac1b6\n    [B3] => 68a65398ad5f4f2baac9cbd8650ac1b6-5729dbf2f6e6f035-1\n    [Traceparent] => 00-68a65398ad5f4f2baac9cbd8650ac1b6-5729dbf2f6e6f035-01\n    [X-Amzn-Trace-Id] => Root=1-68a65398-ad5f4f2baac9cbd8650ac1b6;Parent=5729dbf2f6e6f035;Sampled=1\n    [X-Adsk-Signature] => sha256=5f52b12cac114121f0eda21c79ff09e2aba7b9a3d48b6bf04a944a445a96ad6e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 01eec202-7929-403b-a290-010c9490b3dc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:00:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"01eec202-7929-403b-a290-010c9490b3dc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-944964","transactionId":"7d050f9d-7971-5430-850f-6f657a47c776","quoteStatus":"Expired","message":"Quote# Q-944964 status changed to Expired.","modifiedAt":"2025-08-20T23:00:30.299Z"},"publishedAt":"2025-08-20T23:00:40.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:00:56
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:36]  Provided signature: sha256=6c66dfe948bb912b1f2c8fbe3e64668456f3a0dbfe4bc0a4e85c674dca77bed9
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:37]  Calculated signature: sha256=ac71c8e6bcb0339afd8015d00095bd89f4ad9ed81c251d452fcb43490aeae989
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 32e5c0389f05520c\n    [X-B3-Traceid] => 68a653a6e44df7423f8ac5a43aa1fdcb\n    [B3] => 68a653a6e44df7423f8ac5a43aa1fdcb-32e5c0389f05520c-1\n    [Traceparent] => 00-68a653a6e44df7423f8ac5a43aa1fdcb-32e5c0389f05520c-01\n    [X-Amzn-Trace-Id] => Root=1-68a653a6-e44df7423f8ac5a43aa1fdcb;Parent=32e5c0389f05520c;Sampled=1\n    [X-Adsk-Signature] => sha256=6c66dfe948bb912b1f2c8fbe3e64668456f3a0dbfe4bc0a4e85c674dca77bed9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1e302cc3-6b80-4f8b-ae95-39fddb8657b0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"1e302cc3-6b80-4f8b-ae95-39fddb8657b0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-944954","transactionId":"27721d32-fb21-5684-bb18-7dcaf9bffcfa","quoteStatus":"Expired","message":"Quote# Q-944954 status changed to Expired.","modifiedAt":"2025-08-20T23:00:42.594Z"},"publishedAt":"2025-08-20T23:00:54.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:00:56
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:36]  Provided signature: sha256=ac71c8e6bcb0339afd8015d00095bd89f4ad9ed81c251d452fcb43490aeae989
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:37]  Calculated signature: sha256=ac71c8e6bcb0339afd8015d00095bd89f4ad9ed81c251d452fcb43490aeae989
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ea8ced166fe9324b\n    [X-B3-Traceid] => 68a653a6e44df7423f8ac5a43aa1fdcb\n    [B3] => 68a653a6e44df7423f8ac5a43aa1fdcb-ea8ced166fe9324b-1\n    [Traceparent] => 00-68a653a6e44df7423f8ac5a43aa1fdcb-ea8ced166fe9324b-01\n    [X-Amzn-Trace-Id] => Root=1-68a653a6-e44df7423f8ac5a43aa1fdcb;Parent=ea8ced166fe9324b;Sampled=1\n    [X-Adsk-Signature] => sha256=ac71c8e6bcb0339afd8015d00095bd89f4ad9ed81c251d452fcb43490aeae989\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1e302cc3-6b80-4f8b-ae95-39fddb8657b0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:00:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"1e302cc3-6b80-4f8b-ae95-39fddb8657b0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-944954","transactionId":"27721d32-fb21-5684-bb18-7dcaf9bffcfa","quoteStatus":"Expired","message":"Quote# Q-944954 status changed to Expired.","modifiedAt":"2025-08-20T23:00:42.594Z"},"publishedAt":"2025-08-20T23:00:54.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:02:13
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:36]  Provided signature: sha256=79e4ca12440bb68e7eff346a072dd345dc0c23cbf3d921695a705a6b60866c9e
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:37]  Calculated signature: sha256=0d27a21bc1546439002e6cd475d9ac5fff64bdf8eb44d52ae70891912c451b67
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 13bba7110921a960\n    [X-B3-Traceid] => 68a653f359b27dcd3cb54a4a212cb62d\n    [B3] => 68a653f359b27dcd3cb54a4a212cb62d-13bba7110921a960-1\n    [Traceparent] => 00-68a653f359b27dcd3cb54a4a212cb62d-13bba7110921a960-01\n    [X-Amzn-Trace-Id] => Root=1-68a653f3-59b27dcd3cb54a4a212cb62d;Parent=13bba7110921a960;Sampled=1\n    [X-Adsk-Signature] => sha256=79e4ca12440bb68e7eff346a072dd345dc0c23cbf3d921695a705a6b60866c9e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755730931256-574-95603322\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755730931256-574-95603322","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-95603322","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-20T23:02:11.256Z"},"publishedAt":"2025-08-20T23:02:11.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:02:13
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:36]  Provided signature: sha256=0d27a21bc1546439002e6cd475d9ac5fff64bdf8eb44d52ae70891912c451b67
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:37]  Calculated signature: sha256=0d27a21bc1546439002e6cd475d9ac5fff64bdf8eb44d52ae70891912c451b67
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5e42e8a9a6917ee4\n    [X-B3-Traceid] => 68a653f359b27dcd3cb54a4a212cb62d\n    [B3] => 68a653f359b27dcd3cb54a4a212cb62d-5e42e8a9a6917ee4-1\n    [Traceparent] => 00-68a653f359b27dcd3cb54a4a212cb62d-5e42e8a9a6917ee4-01\n    [X-Amzn-Trace-Id] => Root=1-68a653f3-59b27dcd3cb54a4a212cb62d;Parent=5e42e8a9a6917ee4;Sampled=1\n    [X-Adsk-Signature] => sha256=0d27a21bc1546439002e6cd475d9ac5fff64bdf8eb44d52ae70891912c451b67\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755730931256-574-95603322\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:02:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755730931256-574-95603322","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-95603322","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-20T23:02:11.256Z"},"publishedAt":"2025-08-20T23:02:11.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:03:46
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:36]  Provided signature: sha256=0296c70b55dbd35e3ddc9e313f8df59fe3a66432be6e9f55ce1ae0192fb3b53d
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:37]  Calculated signature: sha256=d963a0166352a0e62ea659e5315f2f6f24eddad6fa700386628eb73fcc1e0b79
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4748de7bfd452d79\n    [X-B3-Traceid] => 68a6545051c01c377bbc0aca1c39d78a\n    [B3] => 68a6545051c01c377bbc0aca1c39d78a-4748de7bfd452d79-1\n    [Traceparent] => 00-68a6545051c01c377bbc0aca1c39d78a-4748de7bfd452d79-01\n    [X-Amzn-Trace-Id] => Root=1-68a65450-51c01c377bbc0aca1c39d78a;Parent=4748de7bfd452d79;Sampled=1\n    [X-Adsk-Signature] => sha256=0296c70b55dbd35e3ddc9e313f8df59fe3a66432be6e9f55ce1ae0192fb3b53d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755731024441-574-94938079\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755731024441-574-94938079","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-94938079","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-20T23:03:44.441Z"},"publishedAt":"2025-08-20T23:03:44.000Z","csn":"5103159758"}
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 23:03:46
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:36]  Provided signature: sha256=d963a0166352a0e62ea659e5315f2f6f24eddad6fa700386628eb73fcc1e0b79
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:37]  Calculated signature: sha256=d963a0166352a0e62ea659e5315f2f6f24eddad6fa700386628eb73fcc1e0b79
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7cf78cb3b6a5bb6c\n    [X-B3-Traceid] => 68a6545051c01c377bbc0aca1c39d78a\n    [B3] => 68a6545051c01c377bbc0aca1c39d78a-7cf78cb3b6a5bb6c-1\n    [Traceparent] => 00-68a6545051c01c377bbc0aca1c39d78a-7cf78cb3b6a5bb6c-01\n    [X-Amzn-Trace-Id] => Root=1-68a65450-51c01c377bbc0aca1c39d78a;Parent=7cf78cb3b6a5bb6c;Sampled=1\n    [X-Adsk-Signature] => sha256=d963a0166352a0e62ea659e5315f2f6f24eddad6fa700386628eb73fcc1e0b79\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755731024441-574-94938079\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 23:03:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755731024441-574-94938079","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-94938079","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-20T23:03:44.441Z"},"publishedAt":"2025-08-20T23:03:44.000Z","csn":"5103159758"}
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 00:00:58
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:36]  Provided signature: sha256=aa0d7e1ba83ff560839dc7402b6e0f99718ef8c33e3c480901d466718b5ffa3c
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:37]  Calculated signature: sha256=f65f4eade37bbb6a18ce1100ad8d61a0de5e710e8a1dfe3d4549540d3df2a425
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f30dd505a9835c26\n    [X-B3-Traceid] => 68a661b766778e827cddd3585fcba111\n    [B3] => 68a661b766778e827cddd3585fcba111-f30dd505a9835c26-1\n    [Traceparent] => 00-68a661b766778e827cddd3585fcba111-f30dd505a9835c26-01\n    [X-Amzn-Trace-Id] => Root=1-68a661b7-66778e827cddd3585fcba111;Parent=f30dd505a9835c26;Sampled=1\n    [X-Adsk-Signature] => sha256=aa0d7e1ba83ff560839dc7402b6e0f99718ef8c33e3c480901d466718b5ffa3c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e427e28f-6d84-4702-bfd4-2719413d8dda\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"e427e28f-6d84-4702-bfd4-2719413d8dda","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-21T00:00:55Z"},"publishedAt":"2025-08-21T00:00:55.000Z","country":"GB"}
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 00:00:58
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:36]  Provided signature: sha256=f65f4eade37bbb6a18ce1100ad8d61a0de5e710e8a1dfe3d4549540d3df2a425
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:37]  Calculated signature: sha256=f65f4eade37bbb6a18ce1100ad8d61a0de5e710e8a1dfe3d4549540d3df2a425
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c63b515666ffbb4d\n    [X-B3-Traceid] => 68a661b766778e827cddd3585fcba111\n    [B3] => 68a661b766778e827cddd3585fcba111-c63b515666ffbb4d-1\n    [Traceparent] => 00-68a661b766778e827cddd3585fcba111-c63b515666ffbb4d-01\n    [X-Amzn-Trace-Id] => Root=1-68a661b7-66778e827cddd3585fcba111;Parent=c63b515666ffbb4d;Sampled=1\n    [X-Adsk-Signature] => sha256=f65f4eade37bbb6a18ce1100ad8d61a0de5e710e8a1dfe3d4549540d3df2a425\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e427e28f-6d84-4702-bfd4-2719413d8dda\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 00:00:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"e427e28f-6d84-4702-bfd4-2719413d8dda","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-21T00:00:55Z"},"publishedAt":"2025-08-21T00:00:55.000Z","country":"GB"}
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 07:40:09
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:36]  Provided signature: sha256=7a36b301c850b7cc4d80f9079d71e3e55498b3c372fc6e509bbc9ef5e5d074ab
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:37]  Calculated signature: sha256=883441052f17ef8c53df707a958ebdde4acf40e1d21b9546f80bdce64d44132f
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a09f7b356582cb05\n    [X-B3-Traceid] => 68a6cd5632fd9d1f2d6865316eb75439\n    [B3] => 68a6cd5632fd9d1f2d6865316eb75439-a09f7b356582cb05-1\n    [Traceparent] => 00-68a6cd5632fd9d1f2d6865316eb75439-a09f7b356582cb05-01\n    [X-Amzn-Trace-Id] => Root=1-68a6cd56-32fd9d1f2d6865316eb75439;Parent=a09f7b356582cb05;Sampled=1\n    [X-Adsk-Signature] => sha256=7a36b301c850b7cc4d80f9079d71e3e55498b3c372fc6e509bbc9ef5e5d074ab\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f452a53d-bf30-4634-a545-e5eb59e1f3fe\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"f452a53d-bf30-4634-a545-e5eb59e1f3fe","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","status":"Active","quantity":2,"endDate":"2026-08-21","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-21T07:25:04.000+0000"},"publishedAt":"2025-08-21T07:40:06.000Z","csn":"5103159758"}
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 07:40:09
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:36]  Provided signature: sha256=883441052f17ef8c53df707a958ebdde4acf40e1d21b9546f80bdce64d44132f
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:37]  Calculated signature: sha256=883441052f17ef8c53df707a958ebdde4acf40e1d21b9546f80bdce64d44132f
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 760d8eafe5839c3f\n    [X-B3-Traceid] => 68a6cd5632fd9d1f2d6865316eb75439\n    [B3] => 68a6cd5632fd9d1f2d6865316eb75439-760d8eafe5839c3f-1\n    [Traceparent] => 00-68a6cd5632fd9d1f2d6865316eb75439-760d8eafe5839c3f-01\n    [X-Amzn-Trace-Id] => Root=1-68a6cd56-32fd9d1f2d6865316eb75439;Parent=760d8eafe5839c3f;Sampled=1\n    [X-Adsk-Signature] => sha256=883441052f17ef8c53df707a958ebdde4acf40e1d21b9546f80bdce64d44132f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f452a53d-bf30-4634-a545-e5eb59e1f3fe\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 07:40:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"f452a53d-bf30-4634-a545-e5eb59e1f3fe","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","status":"Active","quantity":2,"endDate":"2026-08-21","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-21T07:25:04.000+0000"},"publishedAt":"2025-08-21T07:40:06.000Z","csn":"5103159758"}
[webhook] [2025-08-21 08:11:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 08:11:39
[webhook] [2025-08-21 08:11:39] [adwsapi_v2.php:36]  Provided signature: sha256=9caecee4d5c5f3840e2a3fba84a9498223bf20a1f3a70ba107cc4de228a083e1
[webhook] [2025-08-21 08:11:39] [adwsapi_v2.php:37]  Calculated signature: sha256=9caecee4d5c5f3840e2a3fba84a9498223bf20a1f3a70ba107cc4de228a083e1
[webhook] [2025-08-21 08:11:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f68af5963350c3b6\n    [X-B3-Traceid] => 68a6d4b98949e8655c9dd67f24ded454\n    [B3] => 68a6d4b98949e8655c9dd67f24ded454-f68af5963350c3b6-1\n    [Traceparent] => 00-68a6d4b98949e8655c9dd67f24ded454-f68af5963350c3b6-01\n    [X-Amzn-Trace-Id] => Root=1-68a6d4b9-8949e8655c9dd67f24ded454;Parent=f68af5963350c3b6;Sampled=1\n    [X-Adsk-Signature] => sha256=9caecee4d5c5f3840e2a3fba84a9498223bf20a1f3a70ba107cc4de228a083e1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cd888c86-99cf-4325-837a-043f73da2a37\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 08:11:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"cd888c86-99cf-4325-837a-043f73da2a37","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Draft","message":"Quote# Q-1022321 status changed to Draft.","modifiedAt":"2025-08-21T08:11:37.302Z"},"publishedAt":"2025-08-21T08:11:37.000Z","csn":"5103159758"}
[webhook] [2025-08-21 08:11:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 08:11:40
[webhook] [2025-08-21 08:11:40] [adwsapi_v2.php:36]  Provided signature: sha256=d87b37e6dba5dd6e4137fd21cf1e269b963ff0f5932baf6842e86ff9445e3c14
[webhook] [2025-08-21 08:11:40] [adwsapi_v2.php:37]  Calculated signature: sha256=9caecee4d5c5f3840e2a3fba84a9498223bf20a1f3a70ba107cc4de228a083e1
[webhook] [2025-08-21 08:11:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b33a3de3387e9475\n    [X-B3-Traceid] => 68a6d4b98949e8655c9dd67f24ded454\n    [B3] => 68a6d4b98949e8655c9dd67f24ded454-b33a3de3387e9475-1\n    [Traceparent] => 00-68a6d4b98949e8655c9dd67f24ded454-b33a3de3387e9475-01\n    [X-Amzn-Trace-Id] => Root=1-68a6d4b9-8949e8655c9dd67f24ded454;Parent=b33a3de3387e9475;Sampled=1\n    [X-Adsk-Signature] => sha256=d87b37e6dba5dd6e4137fd21cf1e269b963ff0f5932baf6842e86ff9445e3c14\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cd888c86-99cf-4325-837a-043f73da2a37\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 08:11:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"cd888c86-99cf-4325-837a-043f73da2a37","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Draft","message":"Quote# Q-1022321 status changed to Draft.","modifiedAt":"2025-08-21T08:11:37.302Z"},"publishedAt":"2025-08-21T08:11:37.000Z","csn":"5103159758"}
[webhook] [2025-08-21 08:12:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 08:12:22
[webhook] [2025-08-21 08:12:22] [adwsapi_v2.php:36]  Provided signature: sha256=9224446d1a0e867c51861ca78a2cbc624ea13704bee1d14bd5869c9a39306c2d
[webhook] [2025-08-21 08:12:22] [adwsapi_v2.php:37]  Calculated signature: sha256=9224446d1a0e867c51861ca78a2cbc624ea13704bee1d14bd5869c9a39306c2d
[webhook] [2025-08-21 08:12:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f73e63cc360ff3ce\n    [X-B3-Traceid] => 68a6d4e40978c55f378ee287abb181ce\n    [B3] => 68a6d4e40978c55f378ee287abb181ce-f73e63cc360ff3ce-1\n    [Traceparent] => 00-68a6d4e40978c55f378ee287abb181ce-f73e63cc360ff3ce-01\n    [X-Amzn-Trace-Id] => Root=1-68a6d4e4-0978c55f378ee287abb181ce;Parent=f73e63cc360ff3ce;Sampled=1\n    [X-Adsk-Signature] => sha256=9224446d1a0e867c51861ca78a2cbc624ea13704bee1d14bd5869c9a39306c2d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4c3a1abb-2887-481b-85a5-fd9a2a3c3caf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 08:12:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"4c3a1abb-2887-481b-85a5-fd9a2a3c3caf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Quoted","message":"Quote# Q-1022321 status changed to Quoted.","modifiedAt":"2025-08-21T08:12:20.422Z"},"publishedAt":"2025-08-21T08:12:20.000Z","csn":"5103159758"}
[webhook] [2025-08-21 08:12:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 08:12:23
[webhook] [2025-08-21 08:12:23] [adwsapi_v2.php:36]  Provided signature: sha256=c7aa509489705f1613c22ed22e06123fecdc23e67647c87ac4a00bee7f7b841a
[webhook] [2025-08-21 08:12:23] [adwsapi_v2.php:37]  Calculated signature: sha256=9224446d1a0e867c51861ca78a2cbc624ea13704bee1d14bd5869c9a39306c2d
[webhook] [2025-08-21 08:12:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 61788e73e0f73a08\n    [X-B3-Traceid] => 68a6d4e40978c55f378ee287abb181ce\n    [B3] => 68a6d4e40978c55f378ee287abb181ce-61788e73e0f73a08-1\n    [Traceparent] => 00-68a6d4e40978c55f378ee287abb181ce-61788e73e0f73a08-01\n    [X-Amzn-Trace-Id] => Root=1-68a6d4e4-0978c55f378ee287abb181ce;Parent=61788e73e0f73a08;Sampled=1\n    [X-Adsk-Signature] => sha256=c7aa509489705f1613c22ed22e06123fecdc23e67647c87ac4a00bee7f7b841a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4c3a1abb-2887-481b-85a5-fd9a2a3c3caf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 08:12:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"4c3a1abb-2887-481b-85a5-fd9a2a3c3caf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Quoted","message":"Quote# Q-1022321 status changed to Quoted.","modifiedAt":"2025-08-21T08:12:20.422Z"},"publishedAt":"2025-08-21T08:12:20.000Z","csn":"5103159758"}
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 11:39:58
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:36]  Provided signature: sha256=d0752a20c4356346906edbd56a68dcc1ef09185408a53fd22c8a072e3a28a470
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:37]  Calculated signature: sha256=d0752a20c4356346906edbd56a68dcc1ef09185408a53fd22c8a072e3a28a470
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec4ef9127c1b39bb\n    [X-B3-Traceid] => 68a7058b2b904ec539c4a5900d856c6d\n    [B3] => 68a7058b2b904ec539c4a5900d856c6d-ec4ef9127c1b39bb-1\n    [Traceparent] => 00-68a7058b2b904ec539c4a5900d856c6d-ec4ef9127c1b39bb-01\n    [X-Amzn-Trace-Id] => Root=1-68a7058b-2b904ec539c4a5900d856c6d;Parent=ec4ef9127c1b39bb;Sampled=1\n    [X-Adsk-Signature] => sha256=d0752a20c4356346906edbd56a68dcc1ef09185408a53fd22c8a072e3a28a470\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 38a91e2e-a41d-4b6b-97ab-c218b2c7d4b2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"38a91e2e-a41d-4b6b-97ab-c218b2c7d4b2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-21T11:19:47.000+0000"},"publishedAt":"2025-08-21T11:39:55.000Z","csn":"5103159758"}
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 11:39:58
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:36]  Provided signature: sha256=fd1d1b6a4e6350740a2ef84dc4f31c1356a7437ed97c077bd12a9d9583d379ed
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:37]  Calculated signature: sha256=d0752a20c4356346906edbd56a68dcc1ef09185408a53fd22c8a072e3a28a470
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f0069a2c94e5332a\n    [X-B3-Traceid] => 68a7058b2b904ec539c4a5900d856c6d\n    [B3] => 68a7058b2b904ec539c4a5900d856c6d-f0069a2c94e5332a-1\n    [Traceparent] => 00-68a7058b2b904ec539c4a5900d856c6d-f0069a2c94e5332a-01\n    [X-Amzn-Trace-Id] => Root=1-68a7058b-2b904ec539c4a5900d856c6d;Parent=f0069a2c94e5332a;Sampled=1\n    [X-Adsk-Signature] => sha256=fd1d1b6a4e6350740a2ef84dc4f31c1356a7437ed97c077bd12a9d9583d379ed\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 38a91e2e-a41d-4b6b-97ab-c218b2c7d4b2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 11:39:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"38a91e2e-a41d-4b6b-97ab-c218b2c7d4b2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-21T11:19:47.000+0000"},"publishedAt":"2025-08-21T11:39:55.000Z","csn":"5103159758"}
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 12:53:20
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:36]  Provided signature: sha256=6c9a937d14671d145578787a995ae33719422c5d49325a21b60759eda843b352
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:37]  Calculated signature: sha256=6c9a937d14671d145578787a995ae33719422c5d49325a21b60759eda843b352
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8f02111a91d75251\n    [X-B3-Traceid] => 68a716bd23a0eedd4200be8a76248f2a\n    [B3] => 68a716bd23a0eedd4200be8a76248f2a-8f02111a91d75251-1\n    [Traceparent] => 00-68a716bd23a0eedd4200be8a76248f2a-8f02111a91d75251-01\n    [X-Amzn-Trace-Id] => Root=1-68a716bd-23a0eedd4200be8a76248f2a;Parent=8f02111a91d75251;Sampled=1\n    [X-Adsk-Signature] => sha256=6c9a937d14671d145578787a995ae33719422c5d49325a21b60759eda843b352\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755780797237-69270257497660-9033787120-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755780797237-69270257497660-9033787120-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-21T12:53:17.237Z"},"publishedAt":"2025-08-21T12:53:17.000Z","csn":"5103159758"}
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 12:53:20
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:36]  Provided signature: sha256=d2a43b586d41d0fdd5b347d86a523e5ee547f9f9130af9de5587fc7599bec54c
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:37]  Calculated signature: sha256=6c9a937d14671d145578787a995ae33719422c5d49325a21b60759eda843b352
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a178b5f3f11773fb\n    [X-B3-Traceid] => 68a716bd23a0eedd4200be8a76248f2a\n    [B3] => 68a716bd23a0eedd4200be8a76248f2a-a178b5f3f11773fb-1\n    [Traceparent] => 00-68a716bd23a0eedd4200be8a76248f2a-a178b5f3f11773fb-01\n    [X-Amzn-Trace-Id] => Root=1-68a716bd-23a0eedd4200be8a76248f2a;Parent=a178b5f3f11773fb;Sampled=1\n    [X-Adsk-Signature] => sha256=d2a43b586d41d0fdd5b347d86a523e5ee547f9f9130af9de5587fc7599bec54c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755780797237-69270257497660-9033787120-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 12:53:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755780797237-69270257497660-9033787120-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-21T12:53:17.237Z"},"publishedAt":"2025-08-21T12:53:17.000Z","csn":"5103159758"}
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 20:31:16
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:36]  Provided signature: sha256=a7f49da64c1c4e9d7148e9269378c4662abd3ba4ec4d343af316cf5817dda1a0
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:37]  Calculated signature: sha256=b1bb1ee1ee6dc7913cf65ba10b6c18f1506f3fd615a57afaad063ef5ee5f65f2
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 04c59de3d8182237\n    [X-B3-Traceid] => 68a78211e3cfef0fc18fc973cc6c4d37\n    [B3] => 68a78211e3cfef0fc18fc973cc6c4d37-04c59de3d8182237-1\n    [Traceparent] => 00-68a78211e3cfef0fc18fc973cc6c4d37-04c59de3d8182237-01\n    [X-Amzn-Trace-Id] => Root=1-68a78211-e3cfef0fc18fc973cc6c4d37;Parent=04c59de3d8182237;Sampled=1\n    [X-Adsk-Signature] => sha256=a7f49da64c1c4e9d7148e9269378c4662abd3ba4ec4d343af316cf5817dda1a0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c11ba64b-62d6-446b-bc3c-9f2a53d3366a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"c11ba64b-62d6-446b-bc3c-9f2a53d3366a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Draft","message":"Quote# Q-1022796 status changed to Draft.","modifiedAt":"2025-08-21T10:19:10.078Z"},"publishedAt":"2025-08-21T20:31:14.000Z","csn":"5103159758"}
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 20:31:16
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:36]  Provided signature: sha256=b1bb1ee1ee6dc7913cf65ba10b6c18f1506f3fd615a57afaad063ef5ee5f65f2
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:37]  Calculated signature: sha256=b1bb1ee1ee6dc7913cf65ba10b6c18f1506f3fd615a57afaad063ef5ee5f65f2
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5fdd587716b4445b\n    [X-B3-Traceid] => 68a78211e3cfef0fc18fc973cc6c4d37\n    [B3] => 68a78211e3cfef0fc18fc973cc6c4d37-5fdd587716b4445b-1\n    [Traceparent] => 00-68a78211e3cfef0fc18fc973cc6c4d37-5fdd587716b4445b-01\n    [X-Amzn-Trace-Id] => Root=1-68a78211-e3cfef0fc18fc973cc6c4d37;Parent=5fdd587716b4445b;Sampled=1\n    [X-Adsk-Signature] => sha256=b1bb1ee1ee6dc7913cf65ba10b6c18f1506f3fd615a57afaad063ef5ee5f65f2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c11ba64b-62d6-446b-bc3c-9f2a53d3366a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"c11ba64b-62d6-446b-bc3c-9f2a53d3366a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Draft","message":"Quote# Q-1022796 status changed to Draft.","modifiedAt":"2025-08-21T10:19:10.078Z"},"publishedAt":"2025-08-21T20:31:14.000Z","csn":"5103159758"}
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 20:31:16
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:36]  Provided signature: sha256=52f43f2391d4e356f56c18d1dd33e4556c1bfd6584a73bcce537ff9115fbf267
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:37]  Calculated signature: sha256=52f43f2391d4e356f56c18d1dd33e4556c1bfd6584a73bcce537ff9115fbf267
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0d0d9c901c8c12f9\n    [X-B3-Traceid] => 68a78212886406cbb3af1fc8f86ec6dc\n    [B3] => 68a78212886406cbb3af1fc8f86ec6dc-0d0d9c901c8c12f9-1\n    [Traceparent] => 00-68a78212886406cbb3af1fc8f86ec6dc-0d0d9c901c8c12f9-01\n    [X-Amzn-Trace-Id] => Root=1-68a78212-886406cbb3af1fc8f86ec6dc;Parent=0d0d9c901c8c12f9;Sampled=1\n    [X-Adsk-Signature] => sha256=52f43f2391d4e356f56c18d1dd33e4556c1bfd6584a73bcce537ff9115fbf267\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 12e0ca00-bbbc-46f5-a664-758ccc3a396e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"12e0ca00-bbbc-46f5-a664-758ccc3a396e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Quoted","message":"Quote# Q-1022796 status changed to Quoted.","modifiedAt":"2025-08-21T10:19:54.809Z"},"publishedAt":"2025-08-21T20:31:14.000Z","csn":"5103159758"}
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 20:31:16
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:36]  Provided signature: sha256=3ddbe4abf31095a60751b8ad77ef0e16604cb1ca232198a1c74c9039f649033f
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:37]  Calculated signature: sha256=52f43f2391d4e356f56c18d1dd33e4556c1bfd6584a73bcce537ff9115fbf267
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d25c70b05d76953f\n    [X-B3-Traceid] => 68a78212886406cbb3af1fc8f86ec6dc\n    [B3] => 68a78212886406cbb3af1fc8f86ec6dc-d25c70b05d76953f-1\n    [Traceparent] => 00-68a78212886406cbb3af1fc8f86ec6dc-d25c70b05d76953f-01\n    [X-Amzn-Trace-Id] => Root=1-68a78212-886406cbb3af1fc8f86ec6dc;Parent=d25c70b05d76953f;Sampled=1\n    [X-Adsk-Signature] => sha256=3ddbe4abf31095a60751b8ad77ef0e16604cb1ca232198a1c74c9039f649033f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 12e0ca00-bbbc-46f5-a664-758ccc3a396e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 20:31:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"12e0ca00-bbbc-46f5-a664-758ccc3a396e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Quoted","message":"Quote# Q-1022796 status changed to Quoted.","modifiedAt":"2025-08-21T10:19:54.809Z"},"publishedAt":"2025-08-21T20:31:14.000Z","csn":"5103159758"}
[webhook] [2025-08-21 22:21:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 22:21:39
[webhook] [2025-08-21 22:21:39] [adwsapi_v2.php:36]  Provided signature: sha256=8540574ced3ad553e1efff27050ee176914322737d491b29a7e202248946a741
[webhook] [2025-08-21 22:21:39] [adwsapi_v2.php:37]  Calculated signature: sha256=73262a5543d27102708bc26f3bfd025a530d75031c43a8fc26178e81f515d8a3
[webhook] [2025-08-21 22:21:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 230e6ed428a70928\n    [X-B3-Traceid] => 68a79bf1056b62077b6cc7be4892aae5\n    [B3] => 68a79bf1056b62077b6cc7be4892aae5-230e6ed428a70928-1\n    [Traceparent] => 00-68a79bf1056b62077b6cc7be4892aae5-230e6ed428a70928-01\n    [X-Amzn-Trace-Id] => Root=1-68a79bf1-056b62077b6cc7be4892aae5;Parent=230e6ed428a70928;Sampled=1\n    [X-Adsk-Signature] => sha256=8540574ced3ad553e1efff27050ee176914322737d491b29a7e202248946a741\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755814897734-69270257497660-9033787120-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 22:21:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755814897734-69270257497660-9033787120-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-21T22:21:37.734Z"},"publishedAt":"2025-08-21T22:21:37.000Z","csn":"5103159758"}
[webhook] [2025-08-21 22:21:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 22:21:40
[webhook] [2025-08-21 22:21:40] [adwsapi_v2.php:36]  Provided signature: sha256=73262a5543d27102708bc26f3bfd025a530d75031c43a8fc26178e81f515d8a3
[webhook] [2025-08-21 22:21:40] [adwsapi_v2.php:37]  Calculated signature: sha256=73262a5543d27102708bc26f3bfd025a530d75031c43a8fc26178e81f515d8a3
[webhook] [2025-08-21 22:21:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4a62791d615f318a\n    [X-B3-Traceid] => 68a79bf1056b62077b6cc7be4892aae5\n    [B3] => 68a79bf1056b62077b6cc7be4892aae5-4a62791d615f318a-1\n    [Traceparent] => 00-68a79bf1056b62077b6cc7be4892aae5-4a62791d615f318a-01\n    [X-Amzn-Trace-Id] => Root=1-68a79bf1-056b62077b6cc7be4892aae5;Parent=4a62791d615f318a;Sampled=1\n    [X-Adsk-Signature] => sha256=73262a5543d27102708bc26f3bfd025a530d75031c43a8fc26178e81f515d8a3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755814897734-69270257497660-9033787120-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 22:21:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755814897734-69270257497660-9033787120-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69270257497660","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-21T22:21:37.734Z"},"publishedAt":"2025-08-21T22:21:37.000Z","csn":"5103159758"}
[webhook] [2025-08-21 23:00:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 23:00:26
[webhook] [2025-08-21 23:00:26] [adwsapi_v2.php:36]  Provided signature: sha256=c2c9f4800e0b7888ca70fe7dd44807d3c4f05db2cd7423fd681a4e06dd3fbe12
[webhook] [2025-08-21 23:00:26] [adwsapi_v2.php:37]  Calculated signature: sha256=b5bb372a48710608deb40dbea5cd4edcfb1e92b0b45758c6504f28d8def3abb5
[webhook] [2025-08-21 23:00:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9f82acb08e4b3ec9\n    [X-B3-Traceid] => 68a7a5085daf96682681a8cd0c2dcb12\n    [B3] => 68a7a5085daf96682681a8cd0c2dcb12-9f82acb08e4b3ec9-1\n    [Traceparent] => 00-68a7a5085daf96682681a8cd0c2dcb12-9f82acb08e4b3ec9-01\n    [X-Amzn-Trace-Id] => Root=1-68a7a508-5daf96682681a8cd0c2dcb12;Parent=9f82acb08e4b3ec9;Sampled=1\n    [X-Adsk-Signature] => sha256=c2c9f4800e0b7888ca70fe7dd44807d3c4f05db2cd7423fd681a4e06dd3fbe12\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b92dc446-6b68-432c-9bff-e46689ae2fa6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-21 23:00:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"b92dc446-6b68-432c-9bff-e46689ae2fa6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-948896","transactionId":"1af4e7b7-5fab-56e2-9663-874e34da2e9d","quoteStatus":"Expired","message":"Quote# Q-948896 status changed to Expired.","modifiedAt":"2025-08-21T23:00:24.414Z"},"publishedAt":"2025-08-21T23:00:24.000Z","csn":"5103159758"}
[webhook] [2025-08-21 23:00:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-21 23:00:27
[webhook] [2025-08-21 23:00:27] [adwsapi_v2.php:36]  Provided signature: sha256=b5bb372a48710608deb40dbea5cd4edcfb1e92b0b45758c6504f28d8def3abb5
[webhook] [2025-08-21 23:00:27] [adwsapi_v2.php:37]  Calculated signature: sha256=b5bb372a48710608deb40dbea5cd4edcfb1e92b0b45758c6504f28d8def3abb5
[webhook] [2025-08-21 23:00:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7c6044d403baec8d\n    [X-B3-Traceid] => 68a7a5085daf96682681a8cd0c2dcb12\n    [B3] => 68a7a5085daf96682681a8cd0c2dcb12-7c6044d403baec8d-1\n    [Traceparent] => 00-68a7a5085daf96682681a8cd0c2dcb12-7c6044d403baec8d-01\n    [X-Amzn-Trace-Id] => Root=1-68a7a508-5daf96682681a8cd0c2dcb12;Parent=7c6044d403baec8d;Sampled=1\n    [X-Adsk-Signature] => sha256=b5bb372a48710608deb40dbea5cd4edcfb1e92b0b45758c6504f28d8def3abb5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b92dc446-6b68-432c-9bff-e46689ae2fa6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-21 23:00:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"b92dc446-6b68-432c-9bff-e46689ae2fa6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-948896","transactionId":"1af4e7b7-5fab-56e2-9663-874e34da2e9d","quoteStatus":"Expired","message":"Quote# Q-948896 status changed to Expired.","modifiedAt":"2025-08-21T23:00:24.414Z"},"publishedAt":"2025-08-21T23:00:24.000Z","csn":"5103159758"}
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 00:01:02
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:36]  Provided signature: sha256=7d3f85fb88a49a8383473db28601f3ef9b803ff8be09029010f787026ba8ca89
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:37]  Calculated signature: sha256=6c3f85a100c40c9b9e5d0ea846548eade746684b5743dadd18511c0a7a26718e
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 97ee61f5ebdcdcc6\n    [X-B3-Traceid] => 68a7b33b05d9a9d37a3dac633e7df8a6\n    [B3] => 68a7b33b05d9a9d37a3dac633e7df8a6-97ee61f5ebdcdcc6-1\n    [Traceparent] => 00-68a7b33b05d9a9d37a3dac633e7df8a6-97ee61f5ebdcdcc6-01\n    [X-Amzn-Trace-Id] => Root=1-68a7b33b-05d9a9d37a3dac633e7df8a6;Parent=97ee61f5ebdcdcc6;Sampled=1\n    [X-Adsk-Signature] => sha256=7d3f85fb88a49a8383473db28601f3ef9b803ff8be09029010f787026ba8ca89\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69565d6d-b419-4dc5-9e4d-8cece470d7a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"69565d6d-b419-4dc5-9e4d-8cece470d7a6","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-22T00:00:59Z"},"publishedAt":"2025-08-22T00:00:59.000Z","country":"GB"}
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 00:01:02
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:36]  Provided signature: sha256=6c3f85a100c40c9b9e5d0ea846548eade746684b5743dadd18511c0a7a26718e
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:37]  Calculated signature: sha256=6c3f85a100c40c9b9e5d0ea846548eade746684b5743dadd18511c0a7a26718e
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 22c31a7dc5459bff\n    [X-B3-Traceid] => 68a7b33b05d9a9d37a3dac633e7df8a6\n    [B3] => 68a7b33b05d9a9d37a3dac633e7df8a6-22c31a7dc5459bff-1\n    [Traceparent] => 00-68a7b33b05d9a9d37a3dac633e7df8a6-22c31a7dc5459bff-01\n    [X-Amzn-Trace-Id] => Root=1-68a7b33b-05d9a9d37a3dac633e7df8a6;Parent=22c31a7dc5459bff;Sampled=1\n    [X-Adsk-Signature] => sha256=6c3f85a100c40c9b9e5d0ea846548eade746684b5743dadd18511c0a7a26718e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69565d6d-b419-4dc5-9e4d-8cece470d7a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 00:01:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"69565d6d-b419-4dc5-9e4d-8cece470d7a6","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-22T00:00:59Z"},"publishedAt":"2025-08-22T00:00:59.000Z","country":"GB"}
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 07:07:32
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 07:07:32
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:36]  Provided signature: sha256=7dfd4da3fac9f7266b1d70555ea24e99e88bc6df27e4a26751ae75ec79a7d275
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:37]  Calculated signature: sha256=84bc2024816f6323e16f2391450e9e4fb40307fd7a4ebc8a84ce399061389265
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:36]  Provided signature: sha256=84bc2024816f6323e16f2391450e9e4fb40307fd7a4ebc8a84ce399061389265
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:37]  Calculated signature: sha256=84bc2024816f6323e16f2391450e9e4fb40307fd7a4ebc8a84ce399061389265
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8f2f94fa9f26d7a7\n    [X-B3-Traceid] => 68a817315b5f17cc10ee079a06f6168d\n    [B3] => 68a817315b5f17cc10ee079a06f6168d-8f2f94fa9f26d7a7-1\n    [Traceparent] => 00-68a817315b5f17cc10ee079a06f6168d-8f2f94fa9f26d7a7-01\n    [X-Amzn-Trace-Id] => Root=1-68a81731-5b5f17cc10ee079a06f6168d;Parent=8f2f94fa9f26d7a7;Sampled=1\n    [X-Adsk-Signature] => sha256=84bc2024816f6323e16f2391450e9e4fb40307fd7a4ebc8a84ce399061389265\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 55b335a6-e3d1-4902-8776-0555ddfcc900\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"55b335a6-e3d1-4902-8776-0555ddfcc900","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59680488697967","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-22T06:42:27.000+0000"},"publishedAt":"2025-08-22T07:07:30.000Z","csn":"5103159758"}
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 537ff54977d59ab1\n    [X-B3-Traceid] => 68a817315b5f17cc10ee079a06f6168d\n    [B3] => 68a817315b5f17cc10ee079a06f6168d-537ff54977d59ab1-1\n    [Traceparent] => 00-68a817315b5f17cc10ee079a06f6168d-537ff54977d59ab1-01\n    [X-Amzn-Trace-Id] => Root=1-68a81731-5b5f17cc10ee079a06f6168d;Parent=537ff54977d59ab1;Sampled=1\n    [X-Adsk-Signature] => sha256=7dfd4da3fac9f7266b1d70555ea24e99e88bc6df27e4a26751ae75ec79a7d275\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 55b335a6-e3d1-4902-8776-0555ddfcc900\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 07:07:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"55b335a6-e3d1-4902-8776-0555ddfcc900","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59680488697967","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-22T06:42:27.000+0000"},"publishedAt":"2025-08-22T07:07:30.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:35:20
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:36]  Provided signature: sha256=1a25bfde5a71fc497885869bdec331d00a261b8ffb08b73b6bcb1ecb785e6e30
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:37]  Calculated signature: sha256=e40a84d14d5edf4c96acdeedfc6bd9efd0f00a3dcb6ecfc86b7e49b40fab20d4
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b7840f6bdc9da2ff\n    [X-B3-Traceid] => 68a847e6d9295634359ffe40f7307853\n    [B3] => 68a847e6d9295634359ffe40f7307853-b7840f6bdc9da2ff-1\n    [Traceparent] => 00-68a847e6d9295634359ffe40f7307853-b7840f6bdc9da2ff-01\n    [X-Amzn-Trace-Id] => Root=1-68a847e6-d9295634359ffe40f7307853;Parent=b7840f6bdc9da2ff;Sampled=1\n    [X-Adsk-Signature] => sha256=1a25bfde5a71fc497885869bdec331d00a261b8ffb08b73b6bcb1ecb785e6e30\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c594debb-b173-4f7c-8e69-ce4d1155282c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"c594debb-b173-4f7c-8e69-ce4d1155282c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1000885","transactionId":"736ccc27-2ca1-5661-bb01-ad3160d0c043","quoteStatus":"Order Submitted","message":"Quote# Q-1000885 status changed to Order Submitted.","modifiedAt":"2025-08-22T10:35:17.913Z"},"publishedAt":"2025-08-22T10:35:18.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:35:20
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:36]  Provided signature: sha256=e40a84d14d5edf4c96acdeedfc6bd9efd0f00a3dcb6ecfc86b7e49b40fab20d4
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:37]  Calculated signature: sha256=e40a84d14d5edf4c96acdeedfc6bd9efd0f00a3dcb6ecfc86b7e49b40fab20d4
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1f5d38feeb507c69\n    [X-B3-Traceid] => 68a847e6d9295634359ffe40f7307853\n    [B3] => 68a847e6d9295634359ffe40f7307853-1f5d38feeb507c69-1\n    [Traceparent] => 00-68a847e6d9295634359ffe40f7307853-1f5d38feeb507c69-01\n    [X-Amzn-Trace-Id] => Root=1-68a847e6-d9295634359ffe40f7307853;Parent=1f5d38feeb507c69;Sampled=1\n    [X-Adsk-Signature] => sha256=e40a84d14d5edf4c96acdeedfc6bd9efd0f00a3dcb6ecfc86b7e49b40fab20d4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c594debb-b173-4f7c-8e69-ce4d1155282c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:35:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"c594debb-b173-4f7c-8e69-ce4d1155282c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1000885","transactionId":"736ccc27-2ca1-5661-bb01-ad3160d0c043","quoteStatus":"Order Submitted","message":"Quote# Q-1000885 status changed to Order Submitted.","modifiedAt":"2025-08-22T10:35:17.913Z"},"publishedAt":"2025-08-22T10:35:18.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:35:24
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:36]  Provided signature: sha256=448e2a90c50c4b4e9de1a5a9f4f93b2d9fd4cb7236b13570c783b99f9889f61d
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:37]  Calculated signature: sha256=286917e7a1dcf75c4a7bcb5751c6a1936ef1114fe0fc8171921e9d18c3e461d7
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b92c184f56ee67b4\n    [X-B3-Traceid] => 68a847e9874ad2c90a9ddea6ec84c33a\n    [B3] => 68a847e9874ad2c90a9ddea6ec84c33a-b92c184f56ee67b4-1\n    [Traceparent] => 00-68a847e9874ad2c90a9ddea6ec84c33a-b92c184f56ee67b4-01\n    [X-Amzn-Trace-Id] => Root=1-68a847e9-874ad2c90a9ddea6ec84c33a;Parent=b92c184f56ee67b4;Sampled=1\n    [X-Adsk-Signature] => sha256=448e2a90c50c4b4e9de1a5a9f4f93b2d9fd4cb7236b13570c783b99f9889f61d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6a0f7376-92f2-4dc2-b940-5f621cba5112\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"6a0f7376-92f2-4dc2-b940-5f621cba5112","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1000885","transactionId":"736ccc27-2ca1-5661-bb01-ad3160d0c043","quoteStatus":"Ordered","message":"Quote# Q-1000885 status changed to Ordered.","modifiedAt":"2025-08-22T10:35:21.040Z"},"publishedAt":"2025-08-22T10:35:21.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:35:24
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:36]  Provided signature: sha256=286917e7a1dcf75c4a7bcb5751c6a1936ef1114fe0fc8171921e9d18c3e461d7
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:37]  Calculated signature: sha256=286917e7a1dcf75c4a7bcb5751c6a1936ef1114fe0fc8171921e9d18c3e461d7
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7c1adbfe3c68c202\n    [X-B3-Traceid] => 68a847e9874ad2c90a9ddea6ec84c33a\n    [B3] => 68a847e9874ad2c90a9ddea6ec84c33a-7c1adbfe3c68c202-1\n    [Traceparent] => 00-68a847e9874ad2c90a9ddea6ec84c33a-7c1adbfe3c68c202-01\n    [X-Amzn-Trace-Id] => Root=1-68a847e9-874ad2c90a9ddea6ec84c33a;Parent=7c1adbfe3c68c202;Sampled=1\n    [X-Adsk-Signature] => sha256=286917e7a1dcf75c4a7bcb5751c6a1936ef1114fe0fc8171921e9d18c3e461d7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6a0f7376-92f2-4dc2-b940-5f621cba5112\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:35:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"6a0f7376-92f2-4dc2-b940-5f621cba5112","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1000885","transactionId":"736ccc27-2ca1-5661-bb01-ad3160d0c043","quoteStatus":"Ordered","message":"Quote# Q-1000885 status changed to Ordered.","modifiedAt":"2025-08-22T10:35:21.040Z"},"publishedAt":"2025-08-22T10:35:21.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:53:26
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:36]  Provided signature: sha256=002bd8c3859268df775518d62a3185ddbeafed88a4cbffd840cfc43e31671b52
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:37]  Calculated signature: sha256=002bd8c3859268df775518d62a3185ddbeafed88a4cbffd840cfc43e31671b52
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d3eac41b88c6fca8\n    [X-B3-Traceid] => 68a84c2452b2b255d747214f462b0f40\n    [B3] => 68a84c2452b2b255d747214f462b0f40-d3eac41b88c6fca8-1\n    [Traceparent] => 00-68a84c2452b2b255d747214f462b0f40-d3eac41b88c6fca8-01\n    [X-Amzn-Trace-Id] => Root=1-68a84c24-52b2b255d747214f462b0f40;Parent=d3eac41b88c6fca8;Sampled=1\n    [X-Adsk-Signature] => sha256=002bd8c3859268df775518d62a3185ddbeafed88a4cbffd840cfc43e31671b52\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 88198dfb-81cf-401d-8257-2da9b280dadb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"88198dfb-81cf-401d-8257-2da9b280dadb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Order Submitted","message":"Quote# Q-1022796 status changed to Order Submitted.","modifiedAt":"2025-08-22T10:53:23.746Z"},"publishedAt":"2025-08-22T10:53:24.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:53:26
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:36]  Provided signature: sha256=f04bc616d69c04e9403434d009ad10c13be39aa6c0518e34caed97a1740b1b22
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:37]  Calculated signature: sha256=002bd8c3859268df775518d62a3185ddbeafed88a4cbffd840cfc43e31671b52
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b43956e31d989082\n    [X-B3-Traceid] => 68a84c2452b2b255d747214f462b0f40\n    [B3] => 68a84c2452b2b255d747214f462b0f40-b43956e31d989082-1\n    [Traceparent] => 00-68a84c2452b2b255d747214f462b0f40-b43956e31d989082-01\n    [X-Amzn-Trace-Id] => Root=1-68a84c24-52b2b255d747214f462b0f40;Parent=b43956e31d989082;Sampled=1\n    [X-Adsk-Signature] => sha256=f04bc616d69c04e9403434d009ad10c13be39aa6c0518e34caed97a1740b1b22\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 88198dfb-81cf-401d-8257-2da9b280dadb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:53:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"88198dfb-81cf-401d-8257-2da9b280dadb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Order Submitted","message":"Quote# Q-1022796 status changed to Order Submitted.","modifiedAt":"2025-08-22T10:53:23.746Z"},"publishedAt":"2025-08-22T10:53:24.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:53:30
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:36]  Provided signature: sha256=8579aa40c2233cda2ad3b3b5f6b9ffb71c62077c56002e6b8f2c77d01e5a7e34
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:37]  Calculated signature: sha256=8579aa40c2233cda2ad3b3b5f6b9ffb71c62077c56002e6b8f2c77d01e5a7e34
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bf52389853335805\n    [X-B3-Traceid] => 68a84c2718d3ef89ea750e6de627aeff\n    [B3] => 68a84c2718d3ef89ea750e6de627aeff-bf52389853335805-1\n    [Traceparent] => 00-68a84c2718d3ef89ea750e6de627aeff-bf52389853335805-01\n    [X-Amzn-Trace-Id] => Root=1-68a84c27-18d3ef89ea750e6de627aeff;Parent=bf52389853335805;Sampled=1\n    [X-Adsk-Signature] => sha256=8579aa40c2233cda2ad3b3b5f6b9ffb71c62077c56002e6b8f2c77d01e5a7e34\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1afa4d03-94fa-4af5-9a70-ba69055079b2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1afa4d03-94fa-4af5-9a70-ba69055079b2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Ordered","message":"Quote# Q-1022796 status changed to Ordered.","modifiedAt":"2025-08-22T10:53:27.281Z"},"publishedAt":"2025-08-22T10:53:27.000Z","csn":"5103159758"}
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 10:53:30
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:36]  Provided signature: sha256=004d36dfc166b2e269ac5e33136f9f9973bc399a348c1094acb368a593c4ba22
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:37]  Calculated signature: sha256=8579aa40c2233cda2ad3b3b5f6b9ffb71c62077c56002e6b8f2c77d01e5a7e34
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 905e225d7db34574\n    [X-B3-Traceid] => 68a84c2718d3ef89ea750e6de627aeff\n    [B3] => 68a84c2718d3ef89ea750e6de627aeff-905e225d7db34574-1\n    [Traceparent] => 00-68a84c2718d3ef89ea750e6de627aeff-905e225d7db34574-01\n    [X-Amzn-Trace-Id] => Root=1-68a84c27-18d3ef89ea750e6de627aeff;Parent=905e225d7db34574;Sampled=1\n    [X-Adsk-Signature] => sha256=004d36dfc166b2e269ac5e33136f9f9973bc399a348c1094acb368a593c4ba22\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1afa4d03-94fa-4af5-9a70-ba69055079b2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 10:53:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1afa4d03-94fa-4af5-9a70-ba69055079b2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022796","transactionId":"1f9e4acd-e8fe-5e2e-9c45-2bed1c32ca2e","quoteStatus":"Ordered","message":"Quote# Q-1022796 status changed to Ordered.","modifiedAt":"2025-08-22T10:53:27.281Z"},"publishedAt":"2025-08-22T10:53:27.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:08:37
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:36]  Provided signature: sha256=ec8e42b55ab48d45860df450705e648f2829eff7f12774867014692162e02175
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:37]  Calculated signature: sha256=5261d75aa8ad4026fcd8473cc8555bc2f9ed88ab64b68c12bc542905e5d24230
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 80ff5c6728090194\n    [X-B3-Traceid] => 68a84fb36fc5d0f628489efb20953441\n    [B3] => 68a84fb36fc5d0f628489efb20953441-80ff5c6728090194-1\n    [Traceparent] => 00-68a84fb36fc5d0f628489efb20953441-80ff5c6728090194-01\n    [X-Amzn-Trace-Id] => Root=1-68a84fb3-6fc5d0f628489efb20953441;Parent=80ff5c6728090194;Sampled=1\n    [X-Adsk-Signature] => sha256=ec8e42b55ab48d45860df450705e648f2829eff7f12774867014692162e02175\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2b7c16f1-b790-4742-bc46-d722095a96d1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"2b7c16f1-b790-4742-bc46-d722095a96d1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","status":"Active","quantity":1,"endDate":"2028-10-15","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-22T10:53:31.000+0000"},"publishedAt":"2025-08-22T11:08:35.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:08:37
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:36]  Provided signature: sha256=5261d75aa8ad4026fcd8473cc8555bc2f9ed88ab64b68c12bc542905e5d24230
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:37]  Calculated signature: sha256=5261d75aa8ad4026fcd8473cc8555bc2f9ed88ab64b68c12bc542905e5d24230
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7b2e3bcb4fb1b3b9\n    [X-B3-Traceid] => 68a84fb36fc5d0f628489efb20953441\n    [B3] => 68a84fb36fc5d0f628489efb20953441-7b2e3bcb4fb1b3b9-1\n    [Traceparent] => 00-68a84fb36fc5d0f628489efb20953441-7b2e3bcb4fb1b3b9-01\n    [X-Amzn-Trace-Id] => Root=1-68a84fb3-6fc5d0f628489efb20953441;Parent=7b2e3bcb4fb1b3b9;Sampled=1\n    [X-Adsk-Signature] => sha256=5261d75aa8ad4026fcd8473cc8555bc2f9ed88ab64b68c12bc542905e5d24230\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2b7c16f1-b790-4742-bc46-d722095a96d1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:08:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"2b7c16f1-b790-4742-bc46-d722095a96d1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","status":"Active","quantity":1,"endDate":"2028-10-15","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-22T10:53:31.000+0000"},"publishedAt":"2025-08-22T11:08:35.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:10:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:10:32
[webhook] [2025-08-22 11:10:32] [adwsapi_v2.php:36]  Provided signature: sha256=4a04763b5732b075b2f25dda2bf0af74e3a4e25a68d371f1391e868808a0aed3
[webhook] [2025-08-22 11:10:32] [adwsapi_v2.php:37]  Calculated signature: sha256=c7b6e92e2f71f9212c87798e8d67ddf50c6ede53c37bdaddb4a80c8423b07044
[webhook] [2025-08-22 11:10:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5da53cf52c7eeea3\n    [X-B3-Traceid] => 68a850260f94e54a22ecf4412456e79b\n    [B3] => 68a850260f94e54a22ecf4412456e79b-5da53cf52c7eeea3-1\n    [Traceparent] => 00-68a850260f94e54a22ecf4412456e79b-5da53cf52c7eeea3-01\n    [X-Amzn-Trace-Id] => Root=1-68a85026-0f94e54a22ecf4412456e79b;Parent=5da53cf52c7eeea3;Sampled=1\n    [X-Adsk-Signature] => sha256=4a04763b5732b075b2f25dda2bf0af74e3a4e25a68d371f1391e868808a0aed3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 04908ece-2e0f-4c73-b420-8a49b0b44da0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:10:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"04908ece-2e0f-4c73-b420-8a49b0b44da0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","status":"Active","quantity":1,"endDate":"2026-08-25","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-22T10:35:28.000+0000"},"publishedAt":"2025-08-22T11:10:30.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:10:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:10:33
[webhook] [2025-08-22 11:10:33] [adwsapi_v2.php:36]  Provided signature: sha256=c7b6e92e2f71f9212c87798e8d67ddf50c6ede53c37bdaddb4a80c8423b07044
[webhook] [2025-08-22 11:10:33] [adwsapi_v2.php:37]  Calculated signature: sha256=c7b6e92e2f71f9212c87798e8d67ddf50c6ede53c37bdaddb4a80c8423b07044
[webhook] [2025-08-22 11:10:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6ecb71470a7df21f\n    [X-B3-Traceid] => 68a850260f94e54a22ecf4412456e79b\n    [B3] => 68a850260f94e54a22ecf4412456e79b-6ecb71470a7df21f-1\n    [Traceparent] => 00-68a850260f94e54a22ecf4412456e79b-6ecb71470a7df21f-01\n    [X-Amzn-Trace-Id] => Root=1-68a85026-0f94e54a22ecf4412456e79b;Parent=6ecb71470a7df21f;Sampled=1\n    [X-Adsk-Signature] => sha256=c7b6e92e2f71f9212c87798e8d67ddf50c6ede53c37bdaddb4a80c8423b07044\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 04908ece-2e0f-4c73-b420-8a49b0b44da0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:10:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"04908ece-2e0f-4c73-b420-8a49b0b44da0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","status":"Active","quantity":1,"endDate":"2026-08-25","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-22T10:35:28.000+0000"},"publishedAt":"2025-08-22T11:10:30.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:37:08
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:36]  Provided signature: sha256=fc1113c7221b0be5d9665fe2af5251e298e628d1eee0a8d9dc5fa67c9b035284
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:37]  Calculated signature: sha256=853d6fc20e6ae200b42ac0fe86aa56b177693a112c89f0c1eb99506f21927f39
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 88de064e234833bb\n    [X-B3-Traceid] => 68a8566110ef6abc10f086080f21f6a6\n    [B3] => 68a8566110ef6abc10f086080f21f6a6-88de064e234833bb-1\n    [Traceparent] => 00-68a8566110ef6abc10f086080f21f6a6-88de064e234833bb-01\n    [X-Amzn-Trace-Id] => Root=1-68a85661-10ef6abc10f086080f21f6a6;Parent=88de064e234833bb;Sampled=1\n    [X-Adsk-Signature] => sha256=fc1113c7221b0be5d9665fe2af5251e298e628d1eee0a8d9dc5fa67c9b035284\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 84b15514-bddc-480b-980e-882ace41582f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"84b15514-bddc-480b-980e-882ace41582f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-22T11:16:55.000+0000"},"publishedAt":"2025-08-22T11:37:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:37:08
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:36]  Provided signature: sha256=853d6fc20e6ae200b42ac0fe86aa56b177693a112c89f0c1eb99506f21927f39
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:37]  Calculated signature: sha256=853d6fc20e6ae200b42ac0fe86aa56b177693a112c89f0c1eb99506f21927f39
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2e572178b53c9f89\n    [X-B3-Traceid] => 68a8566110ef6abc10f086080f21f6a6\n    [B3] => 68a8566110ef6abc10f086080f21f6a6-2e572178b53c9f89-1\n    [Traceparent] => 00-68a8566110ef6abc10f086080f21f6a6-2e572178b53c9f89-01\n    [X-Amzn-Trace-Id] => Root=1-68a85661-10ef6abc10f086080f21f6a6;Parent=2e572178b53c9f89;Sampled=1\n    [X-Adsk-Signature] => sha256=853d6fc20e6ae200b42ac0fe86aa56b177693a112c89f0c1eb99506f21927f39\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 84b15514-bddc-480b-980e-882ace41582f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:37:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"84b15514-bddc-480b-980e-882ace41582f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-22T11:16:55.000+0000"},"publishedAt":"2025-08-22T11:37:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:37:17
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:36]  Provided signature: sha256=78e498c7d9c601d5073865761ae4143c7281244b6c4b6d2f7c0e01b282c2d728
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:37]  Calculated signature: sha256=78e498c7d9c601d5073865761ae4143c7281244b6c4b6d2f7c0e01b282c2d728
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2af67cbbdea36b55\n    [X-B3-Traceid] => 68a8566a74bb845970616279130e6e11\n    [B3] => 68a8566a74bb845970616279130e6e11-2af67cbbdea36b55-1\n    [Traceparent] => 00-68a8566a74bb845970616279130e6e11-2af67cbbdea36b55-01\n    [X-Amzn-Trace-Id] => Root=1-68a8566a-74bb845970616279130e6e11;Parent=2af67cbbdea36b55;Sampled=1\n    [X-Adsk-Signature] => sha256=78e498c7d9c601d5073865761ae4143c7281244b6c4b6d2f7c0e01b282c2d728\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c6764620-cc34-421e-ae4c-d24d8e0640ab\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"c6764620-cc34-421e-ae4c-d24d8e0640ab","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-22T11:17:08.000+0000"},"publishedAt":"2025-08-22T11:37:14.000Z","csn":"5103159758"}
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 11:37:17
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:36]  Provided signature: sha256=c1216020aa753b52278dfcc810ceb4f2d8a8382cc1095f872d780788729e3b75
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:37]  Calculated signature: sha256=78e498c7d9c601d5073865761ae4143c7281244b6c4b6d2f7c0e01b282c2d728
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => aec3ec2007d9420d\n    [X-B3-Traceid] => 68a8566a74bb845970616279130e6e11\n    [B3] => 68a8566a74bb845970616279130e6e11-aec3ec2007d9420d-1\n    [Traceparent] => 00-68a8566a74bb845970616279130e6e11-aec3ec2007d9420d-01\n    [X-Amzn-Trace-Id] => Root=1-68a8566a-74bb845970616279130e6e11;Parent=aec3ec2007d9420d;Sampled=1\n    [X-Adsk-Signature] => sha256=c1216020aa753b52278dfcc810ceb4f2d8a8382cc1095f872d780788729e3b75\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c6764620-cc34-421e-ae4c-d24d8e0640ab\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 11:37:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"c6764620-cc34-421e-ae4c-d24d8e0640ab","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-22T11:17:08.000+0000"},"publishedAt":"2025-08-22T11:37:14.000Z","csn":"5103159758"}
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 12:50:18
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:36]  Provided signature: sha256=91f0f6d3baa3fb553acf498fb0355e3b6d05018e2ad11a325a5b14a812a139ad
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:37]  Calculated signature: sha256=0fb54454ea400068de1424be3ed69b14eebfa9420013829013d847c11827b163
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e528a7d0e6c841e7\n    [X-B3-Traceid] => 68a867873213421914b8c83d663500f6\n    [B3] => 68a867873213421914b8c83d663500f6-e528a7d0e6c841e7-1\n    [Traceparent] => 00-68a867873213421914b8c83d663500f6-e528a7d0e6c841e7-01\n    [X-Amzn-Trace-Id] => Root=1-68a86787-3213421914b8c83d663500f6;Parent=e528a7d0e6c841e7;Sampled=1\n    [X-Adsk-Signature] => sha256=91f0f6d3baa3fb553acf498fb0355e3b6d05018e2ad11a325a5b14a812a139ad\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755867015944-59680488697967-9033697472-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755867015944-59680488697967-9033697472-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59680488697967","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T12:50:15.944Z"},"publishedAt":"2025-08-22T12:50:16.000Z","csn":"5103159758"}
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 12:50:18
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:36]  Provided signature: sha256=0fb54454ea400068de1424be3ed69b14eebfa9420013829013d847c11827b163
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:37]  Calculated signature: sha256=0fb54454ea400068de1424be3ed69b14eebfa9420013829013d847c11827b163
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b9a6b675ad5447c7\n    [X-B3-Traceid] => 68a867873213421914b8c83d663500f6\n    [B3] => 68a867873213421914b8c83d663500f6-b9a6b675ad5447c7-1\n    [Traceparent] => 00-68a867873213421914b8c83d663500f6-b9a6b675ad5447c7-01\n    [X-Amzn-Trace-Id] => Root=1-68a86787-3213421914b8c83d663500f6;Parent=b9a6b675ad5447c7;Sampled=1\n    [X-Adsk-Signature] => sha256=0fb54454ea400068de1424be3ed69b14eebfa9420013829013d847c11827b163\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755867015944-59680488697967-9033697472-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 12:50:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755867015944-59680488697967-9033697472-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59680488697967","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T12:50:15.944Z"},"publishedAt":"2025-08-22T12:50:16.000Z","csn":"5103159758"}
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 13:46:08
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:36]  Provided signature: sha256=a744c55de205f23d345ec7009d2b314be8b1864682511edbe3f2e607c90c5698
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:37]  Calculated signature: sha256=1153169d9f8a1049900a79730c11843704eea56664aa34bba9f64c2558a5c642
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5f72182fcbc4571c\n    [X-B3-Traceid] => 68a8749eeaa13b03b14402048842120f\n    [B3] => 68a8749eeaa13b03b14402048842120f-5f72182fcbc4571c-1\n    [Traceparent] => 00-68a8749eeaa13b03b14402048842120f-5f72182fcbc4571c-01\n    [X-Amzn-Trace-Id] => Root=1-68a8749e-eaa13b03b14402048842120f;Parent=5f72182fcbc4571c;Sampled=1\n    [X-Adsk-Signature] => sha256=a744c55de205f23d345ec7009d2b314be8b1864682511edbe3f2e607c90c5698\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 08b815a0-cf42-4166-b79f-42869ad70347\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"08b815a0-cf42-4166-b79f-42869ad70347","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026344","transactionId":"caaa866f-9e83-5421-88fe-e74de8489ba6","quoteStatus":"Draft","message":"Quote# Q-1026344 status changed to Draft.","modifiedAt":"2025-08-22T13:46:06.271Z"},"publishedAt":"2025-08-22T13:46:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 13:46:08
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:36]  Provided signature: sha256=1153169d9f8a1049900a79730c11843704eea56664aa34bba9f64c2558a5c642
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:37]  Calculated signature: sha256=1153169d9f8a1049900a79730c11843704eea56664aa34bba9f64c2558a5c642
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3530e55ff6913d53\n    [X-B3-Traceid] => 68a8749eeaa13b03b14402048842120f\n    [B3] => 68a8749eeaa13b03b14402048842120f-3530e55ff6913d53-1\n    [Traceparent] => 00-68a8749eeaa13b03b14402048842120f-3530e55ff6913d53-01\n    [X-Amzn-Trace-Id] => Root=1-68a8749e-eaa13b03b14402048842120f;Parent=3530e55ff6913d53;Sampled=1\n    [X-Adsk-Signature] => sha256=1153169d9f8a1049900a79730c11843704eea56664aa34bba9f64c2558a5c642\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 08b815a0-cf42-4166-b79f-42869ad70347\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 13:46:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"08b815a0-cf42-4166-b79f-42869ad70347","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026344","transactionId":"caaa866f-9e83-5421-88fe-e74de8489ba6","quoteStatus":"Draft","message":"Quote# Q-1026344 status changed to Draft.","modifiedAt":"2025-08-22T13:46:06.271Z"},"publishedAt":"2025-08-22T13:46:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 13:47:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 13:47:30
[webhook] [2025-08-22 13:47:30] [adwsapi_v2.php:36]  Provided signature: sha256=7e17821d6ebb230afb10237740503b282a024aff143b5785b139c3f51f798b2e
[webhook] [2025-08-22 13:47:30] [adwsapi_v2.php:37]  Calculated signature: sha256=bd8099384d2c5ad515ececa616ea44f0a0fc5d39c70587fee49ce8220c8d76da
[webhook] [2025-08-22 13:47:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => abaf45459131b3dd\n    [X-B3-Traceid] => 68a874f040b24fc26020fa2db2b673b9\n    [B3] => 68a874f040b24fc26020fa2db2b673b9-abaf45459131b3dd-1\n    [Traceparent] => 00-68a874f040b24fc26020fa2db2b673b9-abaf45459131b3dd-01\n    [X-Amzn-Trace-Id] => Root=1-68a874f0-40b24fc26020fa2db2b673b9;Parent=abaf45459131b3dd;Sampled=1\n    [X-Adsk-Signature] => sha256=7e17821d6ebb230afb10237740503b282a024aff143b5785b139c3f51f798b2e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0e64d591-7867-42c0-8262-5b2df3be5e62\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 13:47:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"0e64d591-7867-42c0-8262-5b2df3be5e62","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026344","transactionId":"caaa866f-9e83-5421-88fe-e74de8489ba6","quoteStatus":"Quoted","message":"Quote# Q-1026344 status changed to Quoted.","modifiedAt":"2025-08-22T13:47:28.013Z"},"publishedAt":"2025-08-22T13:47:28.000Z","csn":"5103159758"}
[webhook] [2025-08-22 13:47:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 13:47:31
[webhook] [2025-08-22 13:47:31] [adwsapi_v2.php:36]  Provided signature: sha256=bd8099384d2c5ad515ececa616ea44f0a0fc5d39c70587fee49ce8220c8d76da
[webhook] [2025-08-22 13:47:31] [adwsapi_v2.php:37]  Calculated signature: sha256=bd8099384d2c5ad515ececa616ea44f0a0fc5d39c70587fee49ce8220c8d76da
[webhook] [2025-08-22 13:47:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3601bb0a5216588c\n    [X-B3-Traceid] => 68a874f040b24fc26020fa2db2b673b9\n    [B3] => 68a874f040b24fc26020fa2db2b673b9-3601bb0a5216588c-1\n    [Traceparent] => 00-68a874f040b24fc26020fa2db2b673b9-3601bb0a5216588c-01\n    [X-Amzn-Trace-Id] => Root=1-68a874f0-40b24fc26020fa2db2b673b9;Parent=3601bb0a5216588c;Sampled=1\n    [X-Adsk-Signature] => sha256=bd8099384d2c5ad515ececa616ea44f0a0fc5d39c70587fee49ce8220c8d76da\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0e64d591-7867-42c0-8262-5b2df3be5e62\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 13:47:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"0e64d591-7867-42c0-8262-5b2df3be5e62","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026344","transactionId":"caaa866f-9e83-5421-88fe-e74de8489ba6","quoteStatus":"Quoted","message":"Quote# Q-1026344 status changed to Quoted.","modifiedAt":"2025-08-22T13:47:28.013Z"},"publishedAt":"2025-08-22T13:47:28.000Z","csn":"5103159758"}
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 14:25:22
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 14:25:22
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:36]  Provided signature: sha256=c54ecc8bb38390cbcf241a779ab17431aca8e2daea3df608442790f70b4c9f6f
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:37]  Calculated signature: sha256=c54ecc8bb38390cbcf241a779ab17431aca8e2daea3df608442790f70b4c9f6f
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 992cfd83a8df3582\n    [X-B3-Traceid] => 68a87dcfe190b4ba363e22586fc7c8fd\n    [B3] => 68a87dcfe190b4ba363e22586fc7c8fd-992cfd83a8df3582-1\n    [Traceparent] => 00-68a87dcfe190b4ba363e22586fc7c8fd-992cfd83a8df3582-01\n    [X-Amzn-Trace-Id] => Root=1-68a87dcf-e190b4ba363e22586fc7c8fd;Parent=992cfd83a8df3582;Sampled=1\n    [X-Adsk-Signature] => sha256=c54ecc8bb38390cbcf241a779ab17431aca8e2daea3df608442790f70b4c9f6f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6b7f8c2d-7c25-491a-913a-eeed3aa060b5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"6b7f8c2d-7c25-491a-913a-eeed3aa060b5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Draft","message":"Quote# Q-1026459 status changed to Draft.","modifiedAt":"2025-08-22T14:25:19.243Z"},"publishedAt":"2025-08-22T14:25:19.000Z","csn":"5103159758"}
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:36]  Provided signature: sha256=7247117fc923a6cb75b7c1173f31e6a0c452946435b6c1ffaab27b3592a72899
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:37]  Calculated signature: sha256=c54ecc8bb38390cbcf241a779ab17431aca8e2daea3df608442790f70b4c9f6f
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7ccfae66bcea5027\n    [X-B3-Traceid] => 68a87dcfe190b4ba363e22586fc7c8fd\n    [B3] => 68a87dcfe190b4ba363e22586fc7c8fd-7ccfae66bcea5027-1\n    [Traceparent] => 00-68a87dcfe190b4ba363e22586fc7c8fd-7ccfae66bcea5027-01\n    [X-Amzn-Trace-Id] => Root=1-68a87dcf-e190b4ba363e22586fc7c8fd;Parent=7ccfae66bcea5027;Sampled=1\n    [X-Adsk-Signature] => sha256=7247117fc923a6cb75b7c1173f31e6a0c452946435b6c1ffaab27b3592a72899\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6b7f8c2d-7c25-491a-913a-eeed3aa060b5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 14:25:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"6b7f8c2d-7c25-491a-913a-eeed3aa060b5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Draft","message":"Quote# Q-1026459 status changed to Draft.","modifiedAt":"2025-08-22T14:25:19.243Z"},"publishedAt":"2025-08-22T14:25:19.000Z","csn":"5103159758"}
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 14:43:53
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:36]  Provided signature: sha256=70278361d3447e220d1c4afba55ac3a4d321dc5b7b3538801cbef74f98dc0472
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:37]  Calculated signature: sha256=56ccdad41dfd89292af172da71b44b436dc5350a10a0786ac4272f8378416c59
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 720610af0ad06ebc\n    [X-B3-Traceid] => 68a88226b0373782b9334539ee4062aa\n    [B3] => 68a88226b0373782b9334539ee4062aa-720610af0ad06ebc-1\n    [Traceparent] => 00-68a88226b0373782b9334539ee4062aa-720610af0ad06ebc-01\n    [X-Amzn-Trace-Id] => Root=1-68a88226-b0373782b9334539ee4062aa;Parent=720610af0ad06ebc;Sampled=1\n    [X-Adsk-Signature] => sha256=70278361d3447e220d1c4afba55ac3a4d321dc5b7b3538801cbef74f98dc0472\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b6e5032b-6601-4e14-9287-646f18c3a069\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"b6e5032b-6601-4e14-9287-646f18c3a069","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Quoted","message":"Quote# Q-1026459 status changed to Quoted.","modifiedAt":"2025-08-22T14:43:50.754Z"},"publishedAt":"2025-08-22T14:43:50.000Z","csn":"5103159758"}
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 14:43:53
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:36]  Provided signature: sha256=56ccdad41dfd89292af172da71b44b436dc5350a10a0786ac4272f8378416c59
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:37]  Calculated signature: sha256=56ccdad41dfd89292af172da71b44b436dc5350a10a0786ac4272f8378416c59
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 28e788934ceeb1a9\n    [X-B3-Traceid] => 68a88226b0373782b9334539ee4062aa\n    [B3] => 68a88226b0373782b9334539ee4062aa-28e788934ceeb1a9-1\n    [Traceparent] => 00-68a88226b0373782b9334539ee4062aa-28e788934ceeb1a9-01\n    [X-Amzn-Trace-Id] => Root=1-68a88226-b0373782b9334539ee4062aa;Parent=28e788934ceeb1a9;Sampled=1\n    [X-Adsk-Signature] => sha256=56ccdad41dfd89292af172da71b44b436dc5350a10a0786ac4272f8378416c59\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b6e5032b-6601-4e14-9287-646f18c3a069\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 14:43:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"b6e5032b-6601-4e14-9287-646f18c3a069","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Quoted","message":"Quote# Q-1026459 status changed to Quoted.","modifiedAt":"2025-08-22T14:43:50.754Z"},"publishedAt":"2025-08-22T14:43:50.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:02:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:02:10
[webhook] [2025-08-22 15:02:10] [adwsapi_v2.php:36]  Provided signature: sha256=2667b5a303b89dacbc3409350b36db92814fbc725745377a994ecca0e1309df8
[webhook] [2025-08-22 15:02:10] [adwsapi_v2.php:37]  Calculated signature: sha256=2667b5a303b89dacbc3409350b36db92814fbc725745377a994ecca0e1309df8
[webhook] [2025-08-22 15:02:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b4c4b1d58565e412\n    [X-B3-Traceid] => 68a8866f3281379bbab1c3c2cd9a9034\n    [B3] => 68a8866f3281379bbab1c3c2cd9a9034-b4c4b1d58565e412-1\n    [Traceparent] => 00-68a8866f3281379bbab1c3c2cd9a9034-b4c4b1d58565e412-01\n    [X-Amzn-Trace-Id] => Root=1-68a8866f-3281379bbab1c3c2cd9a9034;Parent=b4c4b1d58565e412;Sampled=1\n    [X-Adsk-Signature] => sha256=2667b5a303b89dacbc3409350b36db92814fbc725745377a994ecca0e1309df8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c690e6b7-b3dc-424d-8561-ef90e9ff1c7d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:02:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"c690e6b7-b3dc-424d-8561-ef90e9ff1c7d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Draft","message":"Quote# Q-1026553 status changed to Draft.","modifiedAt":"2025-08-22T15:02:06.615Z"},"publishedAt":"2025-08-22T15:02:07.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:02:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:02:14
[webhook] [2025-08-22 15:02:14] [adwsapi_v2.php:36]  Provided signature: sha256=5829a2f1eb9a1d063cf35f7382dd0cb74b8f66f79ad3ab27fa5af9272f5542fb
[webhook] [2025-08-22 15:02:14] [adwsapi_v2.php:37]  Calculated signature: sha256=2667b5a303b89dacbc3409350b36db92814fbc725745377a994ecca0e1309df8
[webhook] [2025-08-22 15:02:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 43c57480bcb2c858\n    [X-B3-Traceid] => 68a8866f3281379bbab1c3c2cd9a9034\n    [B3] => 68a8866f3281379bbab1c3c2cd9a9034-43c57480bcb2c858-1\n    [Traceparent] => 00-68a8866f3281379bbab1c3c2cd9a9034-43c57480bcb2c858-01\n    [X-Amzn-Trace-Id] => Root=1-68a8866f-3281379bbab1c3c2cd9a9034;Parent=43c57480bcb2c858;Sampled=1\n    [X-Adsk-Signature] => sha256=5829a2f1eb9a1d063cf35f7382dd0cb74b8f66f79ad3ab27fa5af9272f5542fb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c690e6b7-b3dc-424d-8561-ef90e9ff1c7d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:02:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"c690e6b7-b3dc-424d-8561-ef90e9ff1c7d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Draft","message":"Quote# Q-1026553 status changed to Draft.","modifiedAt":"2025-08-22T15:02:06.615Z"},"publishedAt":"2025-08-22T15:02:07.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:03:43
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:36]  Provided signature: sha256=8203a490bd8f1e58d7fac97eb6db24d11a487921538baf90064d77cf13457bd6
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:37]  Calculated signature: sha256=8203a490bd8f1e58d7fac97eb6db24d11a487921538baf90064d77cf13457bd6
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4bc00ab9d307bc65\n    [X-B3-Traceid] => 68a886cca5f135206f87d42bba66d430\n    [B3] => 68a886cca5f135206f87d42bba66d430-4bc00ab9d307bc65-1\n    [Traceparent] => 00-68a886cca5f135206f87d42bba66d430-4bc00ab9d307bc65-01\n    [X-Amzn-Trace-Id] => Root=1-68a886cc-a5f135206f87d42bba66d430;Parent=4bc00ab9d307bc65;Sampled=1\n    [X-Adsk-Signature] => sha256=8203a490bd8f1e58d7fac97eb6db24d11a487921538baf90064d77cf13457bd6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 23c32155-bbf7-40fa-8a26-d40b5b2f1804\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"23c32155-bbf7-40fa-8a26-d40b5b2f1804","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Quoted","message":"Quote# Q-1026553 status changed to Quoted.","modifiedAt":"2025-08-22T15:03:40.137Z"},"publishedAt":"2025-08-22T15:03:40.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:03:43
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:36]  Provided signature: sha256=ac5cf3b21f8470a83b76602749870a58f2de359047353c62fa7e9d2687f61713
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:37]  Calculated signature: sha256=8203a490bd8f1e58d7fac97eb6db24d11a487921538baf90064d77cf13457bd6
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2f786f6a375db9f4\n    [X-B3-Traceid] => 68a886cca5f135206f87d42bba66d430\n    [B3] => 68a886cca5f135206f87d42bba66d430-2f786f6a375db9f4-1\n    [Traceparent] => 00-68a886cca5f135206f87d42bba66d430-2f786f6a375db9f4-01\n    [X-Amzn-Trace-Id] => Root=1-68a886cc-a5f135206f87d42bba66d430;Parent=2f786f6a375db9f4;Sampled=1\n    [X-Adsk-Signature] => sha256=ac5cf3b21f8470a83b76602749870a58f2de359047353c62fa7e9d2687f61713\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 23c32155-bbf7-40fa-8a26-d40b5b2f1804\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:03:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"23c32155-bbf7-40fa-8a26-d40b5b2f1804","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Quoted","message":"Quote# Q-1026553 status changed to Quoted.","modifiedAt":"2025-08-22T15:03:40.137Z"},"publishedAt":"2025-08-22T15:03:40.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:28:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:28:36
[webhook] [2025-08-22 15:28:36] [adwsapi_v2.php:36]  Provided signature: sha256=4f9dd737f4ecc1a3b5d3785936487a0fac3f4f0024f67f97259fe1c1eb0ff312
[webhook] [2025-08-22 15:28:36] [adwsapi_v2.php:37]  Calculated signature: sha256=fa4a925335bc3895323b4198ba6198a59f5d3e30ddf96a74dcb4a9edfc887ee0
[webhook] [2025-08-22 15:28:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 723737c5661ae072\n    [X-B3-Traceid] => 68a88ca2d15912ecf5ad4c51559398b8\n    [B3] => 68a88ca2d15912ecf5ad4c51559398b8-723737c5661ae072-1\n    [Traceparent] => 00-68a88ca2d15912ecf5ad4c51559398b8-723737c5661ae072-01\n    [X-Amzn-Trace-Id] => Root=1-68a88ca2-d15912ecf5ad4c51559398b8;Parent=723737c5661ae072;Sampled=1\n    [X-Adsk-Signature] => sha256=4f9dd737f4ecc1a3b5d3785936487a0fac3f4f0024f67f97259fe1c1eb0ff312\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9c3cd97a-4c10-4430-9b0d-45c8fa8db27a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:28:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"9c3cd97a-4c10-4430-9b0d-45c8fa8db27a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Order Submitted","message":"Quote# Q-1022321 status changed to Order Submitted.","modifiedAt":"2025-08-22T15:28:34.144Z"},"publishedAt":"2025-08-22T15:28:34.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:28:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:28:37
[webhook] [2025-08-22 15:28:37] [adwsapi_v2.php:36]  Provided signature: sha256=fa4a925335bc3895323b4198ba6198a59f5d3e30ddf96a74dcb4a9edfc887ee0
[webhook] [2025-08-22 15:28:37] [adwsapi_v2.php:37]  Calculated signature: sha256=fa4a925335bc3895323b4198ba6198a59f5d3e30ddf96a74dcb4a9edfc887ee0
[webhook] [2025-08-22 15:28:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ebb05e726d414802\n    [X-B3-Traceid] => 68a88ca2d15912ecf5ad4c51559398b8\n    [B3] => 68a88ca2d15912ecf5ad4c51559398b8-ebb05e726d414802-1\n    [Traceparent] => 00-68a88ca2d15912ecf5ad4c51559398b8-ebb05e726d414802-01\n    [X-Amzn-Trace-Id] => Root=1-68a88ca2-d15912ecf5ad4c51559398b8;Parent=ebb05e726d414802;Sampled=1\n    [X-Adsk-Signature] => sha256=fa4a925335bc3895323b4198ba6198a59f5d3e30ddf96a74dcb4a9edfc887ee0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9c3cd97a-4c10-4430-9b0d-45c8fa8db27a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:28:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"9c3cd97a-4c10-4430-9b0d-45c8fa8db27a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Order Submitted","message":"Quote# Q-1022321 status changed to Order Submitted.","modifiedAt":"2025-08-22T15:28:34.144Z"},"publishedAt":"2025-08-22T15:28:34.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:28:38
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:36]  Provided signature: sha256=86e2ac3d3103e33e6d4e36ef78812fdf247588038d6c22c97333a1aa9e806efd
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:37]  Calculated signature: sha256=86e2ac3d3103e33e6d4e36ef78812fdf247588038d6c22c97333a1aa9e806efd
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bf672745c7adfb35\n    [X-B3-Traceid] => 68a88ca3b5cae1bc9de477dade0865ae\n    [B3] => 68a88ca3b5cae1bc9de477dade0865ae-bf672745c7adfb35-1\n    [Traceparent] => 00-68a88ca3b5cae1bc9de477dade0865ae-bf672745c7adfb35-01\n    [X-Amzn-Trace-Id] => Root=1-68a88ca3-b5cae1bc9de477dade0865ae;Parent=bf672745c7adfb35;Sampled=1\n    [X-Adsk-Signature] => sha256=86e2ac3d3103e33e6d4e36ef78812fdf247588038d6c22c97333a1aa9e806efd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => eb642089-0246-43c0-9583-05ac3e34c7f8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"eb642089-0246-43c0-9583-05ac3e34c7f8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Ordered","message":"Quote# Q-1022321 status changed to Ordered.","modifiedAt":"2025-08-22T15:28:35.049Z"},"publishedAt":"2025-08-22T15:28:35.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:28:38
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:36]  Provided signature: sha256=e3556ded552f5294f4059de1e381f0b3459e7c41c28c7a9c040f647edcc3e6ed
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:37]  Calculated signature: sha256=86e2ac3d3103e33e6d4e36ef78812fdf247588038d6c22c97333a1aa9e806efd
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 151a09085b1a6b75\n    [X-B3-Traceid] => 68a88ca3b5cae1bc9de477dade0865ae\n    [B3] => 68a88ca3b5cae1bc9de477dade0865ae-151a09085b1a6b75-1\n    [Traceparent] => 00-68a88ca3b5cae1bc9de477dade0865ae-151a09085b1a6b75-01\n    [X-Amzn-Trace-Id] => Root=1-68a88ca3-b5cae1bc9de477dade0865ae;Parent=151a09085b1a6b75;Sampled=1\n    [X-Adsk-Signature] => sha256=e3556ded552f5294f4059de1e381f0b3459e7c41c28c7a9c040f647edcc3e6ed\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => eb642089-0246-43c0-9583-05ac3e34c7f8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:28:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"eb642089-0246-43c0-9583-05ac3e34c7f8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1022321","transactionId":"77668ff3-50f2-5e19-b3b0-eed3860784df","quoteStatus":"Ordered","message":"Quote# Q-1022321 status changed to Ordered.","modifiedAt":"2025-08-22T15:28:35.049Z"},"publishedAt":"2025-08-22T15:28:35.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:43:48
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:36]  Provided signature: sha256=46a2780f785fa67b62a19a94e94dc91ace3caf2e372f6324bef3f74ccef0a70d
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:37]  Calculated signature: sha256=40b03fd22c3fa2790779de32a63b401b0f0ad97557fa2eee91b31134d59f4746
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d046b1c1799f194b\n    [X-B3-Traceid] => 68a890325c86b5550aa28892001161c0\n    [B3] => 68a890325c86b5550aa28892001161c0-d046b1c1799f194b-1\n    [Traceparent] => 00-68a890325c86b5550aa28892001161c0-d046b1c1799f194b-01\n    [X-Amzn-Trace-Id] => Root=1-68a89032-5c86b5550aa28892001161c0;Parent=d046b1c1799f194b;Sampled=1\n    [X-Adsk-Signature] => sha256=46a2780f785fa67b62a19a94e94dc91ace3caf2e372f6324bef3f74ccef0a70d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8367a4d5-c9ef-4604-942f-4a26f06ce103\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"8367a4d5-c9ef-4604-942f-4a26f06ce103","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57199375151109","status":"Active","quantity":2,"endDate":"2028-10-24","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-22T15:28:43.000+0000"},"publishedAt":"2025-08-22T15:43:46.000Z","csn":"5103159758"}
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 15:43:48
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:36]  Provided signature: sha256=40b03fd22c3fa2790779de32a63b401b0f0ad97557fa2eee91b31134d59f4746
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:37]  Calculated signature: sha256=40b03fd22c3fa2790779de32a63b401b0f0ad97557fa2eee91b31134d59f4746
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 03aa1dd248baa414\n    [X-B3-Traceid] => 68a890325c86b5550aa28892001161c0\n    [B3] => 68a890325c86b5550aa28892001161c0-03aa1dd248baa414-1\n    [Traceparent] => 00-68a890325c86b5550aa28892001161c0-03aa1dd248baa414-01\n    [X-Amzn-Trace-Id] => Root=1-68a89032-5c86b5550aa28892001161c0;Parent=03aa1dd248baa414;Sampled=1\n    [X-Adsk-Signature] => sha256=40b03fd22c3fa2790779de32a63b401b0f0ad97557fa2eee91b31134d59f4746\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8367a4d5-c9ef-4604-942f-4a26f06ce103\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 15:43:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"8367a4d5-c9ef-4604-942f-4a26f06ce103","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57199375151109","status":"Active","quantity":2,"endDate":"2028-10-24","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-22T15:28:43.000+0000"},"publishedAt":"2025-08-22T15:43:46.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:05:12
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:36]  Provided signature: sha256=24fdaedcc35f6a95e81778cad250d7d1efe42de16d2df0ed709a05bb1fec1708
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:37]  Calculated signature: sha256=005d14a99621d83b7c0dfbc5368a0da03c6b1dbcb8c02cfdaef260a7ba1895fa
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1950849e0e4ba44d\n    [X-B3-Traceid] => 68a8953611ace27214afa7d55893a2c5\n    [B3] => 68a8953611ace27214afa7d55893a2c5-1950849e0e4ba44d-1\n    [Traceparent] => 00-68a8953611ace27214afa7d55893a2c5-1950849e0e4ba44d-01\n    [X-Amzn-Trace-Id] => Root=1-68a89536-11ace27214afa7d55893a2c5;Parent=1950849e0e4ba44d;Sampled=1\n    [X-Adsk-Signature] => sha256=24fdaedcc35f6a95e81778cad250d7d1efe42de16d2df0ed709a05bb1fec1708\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755878710349-57122874418375-9033790641-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755878710349-57122874418375-9033790641-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T16:05:10.349Z"},"publishedAt":"2025-08-22T16:05:10.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:05:12
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:36]  Provided signature: sha256=005d14a99621d83b7c0dfbc5368a0da03c6b1dbcb8c02cfdaef260a7ba1895fa
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:37]  Calculated signature: sha256=005d14a99621d83b7c0dfbc5368a0da03c6b1dbcb8c02cfdaef260a7ba1895fa
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8f5370315b3c27cf\n    [X-B3-Traceid] => 68a8953611ace27214afa7d55893a2c5\n    [B3] => 68a8953611ace27214afa7d55893a2c5-8f5370315b3c27cf-1\n    [Traceparent] => 00-68a8953611ace27214afa7d55893a2c5-8f5370315b3c27cf-01\n    [X-Amzn-Trace-Id] => Root=1-68a89536-11ace27214afa7d55893a2c5;Parent=8f5370315b3c27cf;Sampled=1\n    [X-Adsk-Signature] => sha256=005d14a99621d83b7c0dfbc5368a0da03c6b1dbcb8c02cfdaef260a7ba1895fa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755878710349-57122874418375-9033790641-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:05:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755878710349-57122874418375-9033790641-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T16:05:10.349Z"},"publishedAt":"2025-08-22T16:05:10.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:05:14
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:36]  Provided signature: sha256=4a7af65a01286caa33b91172e8a3e9724d57a09d998881ada70deb0fe658870f
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:37]  Calculated signature: sha256=4a7af65a01286caa33b91172e8a3e9724d57a09d998881ada70deb0fe658870f
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8091a7175512a6ce\n    [X-B3-Traceid] => 68a895383b3b63764a5254b308dfd587\n    [B3] => 68a895383b3b63764a5254b308dfd587-8091a7175512a6ce-1\n    [Traceparent] => 00-68a895383b3b63764a5254b308dfd587-8091a7175512a6ce-01\n    [X-Amzn-Trace-Id] => Root=1-68a89538-3b3b63764a5254b308dfd587;Parent=8091a7175512a6ce;Sampled=1\n    [X-Adsk-Signature] => sha256=4a7af65a01286caa33b91172e8a3e9724d57a09d998881ada70deb0fe658870f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755878712210-59843928037733-9033790576-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755878712210-59843928037733-9033790576-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T16:05:12.210Z"},"publishedAt":"2025-08-22T16:05:12.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:05:14
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:36]  Provided signature: sha256=d9102eef2bf747239c8a3571c2c8fb5204d63f66a259009ff39bcd19264c6741
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:37]  Calculated signature: sha256=4a7af65a01286caa33b91172e8a3e9724d57a09d998881ada70deb0fe658870f
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6ec8c80776b5a105\n    [X-B3-Traceid] => 68a895383b3b63764a5254b308dfd587\n    [B3] => 68a895383b3b63764a5254b308dfd587-6ec8c80776b5a105-1\n    [Traceparent] => 00-68a895383b3b63764a5254b308dfd587-6ec8c80776b5a105-01\n    [X-Amzn-Trace-Id] => Root=1-68a89538-3b3b63764a5254b308dfd587;Parent=6ec8c80776b5a105;Sampled=1\n    [X-Adsk-Signature] => sha256=d9102eef2bf747239c8a3571c2c8fb5204d63f66a259009ff39bcd19264c6741\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755878712210-59843928037733-9033790576-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:05:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755878712210-59843928037733-9033790576-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T16:05:12.210Z"},"publishedAt":"2025-08-22T16:05:12.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:35:28
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:36]  Provided signature: sha256=3fb438a2fb46c21105f33aa9adfb756d96176900249e033c6e2bd935f9feb984
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:37]  Calculated signature: sha256=8d1c31eb0a07bbeb1e10e3ba5b2455b84b976679db0dae7885aa1a6d8c9fb231
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4a09bfc720ab6b3f\n    [X-B3-Traceid] => 68a89c4deb28fcdbb847344b65ab5319\n    [B3] => 68a89c4deb28fcdbb847344b65ab5319-4a09bfc720ab6b3f-1\n    [Traceparent] => 00-68a89c4deb28fcdbb847344b65ab5319-4a09bfc720ab6b3f-01\n    [X-Amzn-Trace-Id] => Root=1-68a89c4d-eb28fcdbb847344b65ab5319;Parent=4a09bfc720ab6b3f;Sampled=1\n    [X-Adsk-Signature] => sha256=3fb438a2fb46c21105f33aa9adfb756d96176900249e033c6e2bd935f9feb984\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5e7d71ae-21b0-47c1-a47d-38f3f876a195\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"5e7d71ae-21b0-47c1-a47d-38f3f876a195","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Order Submitted","message":"Quote# Q-1026553 status changed to Order Submitted.","modifiedAt":"2025-08-22T16:35:25.480Z"},"publishedAt":"2025-08-22T16:35:25.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:35:28
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:36]  Provided signature: sha256=8d1c31eb0a07bbeb1e10e3ba5b2455b84b976679db0dae7885aa1a6d8c9fb231
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:37]  Calculated signature: sha256=8d1c31eb0a07bbeb1e10e3ba5b2455b84b976679db0dae7885aa1a6d8c9fb231
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bc69c58e70682604\n    [X-B3-Traceid] => 68a89c4deb28fcdbb847344b65ab5319\n    [B3] => 68a89c4deb28fcdbb847344b65ab5319-bc69c58e70682604-1\n    [Traceparent] => 00-68a89c4deb28fcdbb847344b65ab5319-bc69c58e70682604-01\n    [X-Amzn-Trace-Id] => Root=1-68a89c4d-eb28fcdbb847344b65ab5319;Parent=bc69c58e70682604;Sampled=1\n    [X-Adsk-Signature] => sha256=8d1c31eb0a07bbeb1e10e3ba5b2455b84b976679db0dae7885aa1a6d8c9fb231\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5e7d71ae-21b0-47c1-a47d-38f3f876a195\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:35:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"5e7d71ae-21b0-47c1-a47d-38f3f876a195","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Order Submitted","message":"Quote# Q-1026553 status changed to Order Submitted.","modifiedAt":"2025-08-22T16:35:25.480Z"},"publishedAt":"2025-08-22T16:35:25.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:35:29
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:36]  Provided signature: sha256=0f9567e60d4f23a261b51e87ff55142aa5e51e2cc3b342b8ca0ff9f4d4d5fdc1
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:37]  Calculated signature: sha256=0f9567e60d4f23a261b51e87ff55142aa5e51e2cc3b342b8ca0ff9f4d4d5fdc1
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ea6937a0daab0bbc\n    [X-B3-Traceid] => 68a89c4fc5684ba5e916d81e59cc4c20\n    [B3] => 68a89c4fc5684ba5e916d81e59cc4c20-ea6937a0daab0bbc-1\n    [Traceparent] => 00-68a89c4fc5684ba5e916d81e59cc4c20-ea6937a0daab0bbc-01\n    [X-Amzn-Trace-Id] => Root=1-68a89c4f-c5684ba5e916d81e59cc4c20;Parent=ea6937a0daab0bbc;Sampled=1\n    [X-Adsk-Signature] => sha256=0f9567e60d4f23a261b51e87ff55142aa5e51e2cc3b342b8ca0ff9f4d4d5fdc1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8db659cb-e508-4e61-85ff-b46a112ba2b7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"8db659cb-e508-4e61-85ff-b46a112ba2b7","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Ordered","message":"Quote# Q-1026553 status changed to Ordered.","modifiedAt":"2025-08-22T16:35:26.746Z"},"publishedAt":"2025-08-22T16:35:27.000Z","csn":"5103159758"}
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 16:35:29
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:36]  Provided signature: sha256=a8f06ba163a9623edf5a0cedf5f6aa132bd3fce49dbaf93244ecc7a3d8883476
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:37]  Calculated signature: sha256=0f9567e60d4f23a261b51e87ff55142aa5e51e2cc3b342b8ca0ff9f4d4d5fdc1
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0f651339dd6feb93\n    [X-B3-Traceid] => 68a89c4fc5684ba5e916d81e59cc4c20\n    [B3] => 68a89c4fc5684ba5e916d81e59cc4c20-0f651339dd6feb93-1\n    [Traceparent] => 00-68a89c4fc5684ba5e916d81e59cc4c20-0f651339dd6feb93-01\n    [X-Amzn-Trace-Id] => Root=1-68a89c4f-c5684ba5e916d81e59cc4c20;Parent=0f651339dd6feb93;Sampled=1\n    [X-Adsk-Signature] => sha256=a8f06ba163a9623edf5a0cedf5f6aa132bd3fce49dbaf93244ecc7a3d8883476\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8db659cb-e508-4e61-85ff-b46a112ba2b7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 16:35:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"8db659cb-e508-4e61-85ff-b46a112ba2b7","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026553","transactionId":"d729811b-1d41-5abd-b7e6-e03995e73c81","quoteStatus":"Ordered","message":"Quote# Q-1026553 status changed to Ordered.","modifiedAt":"2025-08-22T16:35:26.746Z"},"publishedAt":"2025-08-22T16:35:27.000Z","csn":"5103159758"}
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 19:40:27
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:36]  Provided signature: sha256=bb010df46293bcc697823361b76b955fa01529ad437a8baed5e0627ee5d9c152
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:37]  Calculated signature: sha256=edfa7fe302de6266f47ebd84b4f2374e52d078ff60f02e204a8cd3e63ff61e8d
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 488e9a73d0e94c4f\n    [X-B3-Traceid] => 68a8c7a825334bad230ca684056f8b28\n    [B3] => 68a8c7a825334bad230ca684056f8b28-488e9a73d0e94c4f-1\n    [Traceparent] => 00-68a8c7a825334bad230ca684056f8b28-488e9a73d0e94c4f-01\n    [X-Amzn-Trace-Id] => Root=1-68a8c7a8-25334bad230ca684056f8b28;Parent=488e9a73d0e94c4f;Sampled=1\n    [X-Adsk-Signature] => sha256=bb010df46293bcc697823361b76b955fa01529ad437a8baed5e0627ee5d9c152\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 058afd33-8cd0-4552-9721-eda087be46d8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"058afd33-8cd0-4552-9721-eda087be46d8","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57199375151109","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-22T19:10:15.000+0000"},"publishedAt":"2025-08-22T19:40:24.000Z","csn":"5103159758"}
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 19:40:27
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:36]  Provided signature: sha256=edfa7fe302de6266f47ebd84b4f2374e52d078ff60f02e204a8cd3e63ff61e8d
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:37]  Calculated signature: sha256=edfa7fe302de6266f47ebd84b4f2374e52d078ff60f02e204a8cd3e63ff61e8d
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a231aae5af1e3f7c\n    [X-B3-Traceid] => 68a8c7a825334bad230ca684056f8b28\n    [B3] => 68a8c7a825334bad230ca684056f8b28-a231aae5af1e3f7c-1\n    [Traceparent] => 00-68a8c7a825334bad230ca684056f8b28-a231aae5af1e3f7c-01\n    [X-Amzn-Trace-Id] => Root=1-68a8c7a8-25334bad230ca684056f8b28;Parent=a231aae5af1e3f7c;Sampled=1\n    [X-Adsk-Signature] => sha256=edfa7fe302de6266f47ebd84b4f2374e52d078ff60f02e204a8cd3e63ff61e8d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 058afd33-8cd0-4552-9721-eda087be46d8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 19:40:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"058afd33-8cd0-4552-9721-eda087be46d8","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57199375151109","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-22T19:10:15.000+0000"},"publishedAt":"2025-08-22T19:40:24.000Z","csn":"5103159758"}
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 20:27:09
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:36]  Provided signature: sha256=dd899a0f775a231973c24a478ee7e86d32ddd0c755602bc4f09214f0a3c467b2
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:37]  Calculated signature: sha256=d0952dd3c640038cae8f5281a9457c2fc04c00ea80b6e075916e43e4cae3331e
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1e1c80216152115b\n    [X-B3-Traceid] => 68a8d29a4c796ddf15a43ce43ad5f80f\n    [B3] => 68a8d29a4c796ddf15a43ce43ad5f80f-1e1c80216152115b-1\n    [Traceparent] => 00-68a8d29a4c796ddf15a43ce43ad5f80f-1e1c80216152115b-01\n    [X-Amzn-Trace-Id] => Root=1-68a8d29a-4c796ddf15a43ce43ad5f80f;Parent=1e1c80216152115b;Sampled=1\n    [X-Adsk-Signature] => sha256=dd899a0f775a231973c24a478ee7e86d32ddd0c755602bc4f09214f0a3c467b2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755894426690-59843928037733-9033790576-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755894426690-59843928037733-9033790576-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T20:27:06.690Z"},"publishedAt":"2025-08-22T20:27:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 20:27:09
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:36]  Provided signature: sha256=d0952dd3c640038cae8f5281a9457c2fc04c00ea80b6e075916e43e4cae3331e
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:37]  Calculated signature: sha256=d0952dd3c640038cae8f5281a9457c2fc04c00ea80b6e075916e43e4cae3331e
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e3a4758c32662d7e\n    [X-B3-Traceid] => 68a8d29a4c796ddf15a43ce43ad5f80f\n    [B3] => 68a8d29a4c796ddf15a43ce43ad5f80f-e3a4758c32662d7e-1\n    [Traceparent] => 00-68a8d29a4c796ddf15a43ce43ad5f80f-e3a4758c32662d7e-01\n    [X-Amzn-Trace-Id] => Root=1-68a8d29a-4c796ddf15a43ce43ad5f80f;Parent=e3a4758c32662d7e;Sampled=1\n    [X-Adsk-Signature] => sha256=d0952dd3c640038cae8f5281a9457c2fc04c00ea80b6e075916e43e4cae3331e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755894426690-59843928037733-9033790576-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 20:27:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755894426690-59843928037733-9033790576-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59843928037733","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T20:27:06.690Z"},"publishedAt":"2025-08-22T20:27:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 20:29:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 20:29:46
[webhook] [2025-08-22 20:29:46] [adwsapi_v2.php:36]  Provided signature: sha256=16cb6130a985e8b6668004030cdcb101a1adb3570ada553f06079971c39055dd
[webhook] [2025-08-22 20:29:46] [adwsapi_v2.php:37]  Calculated signature: sha256=16cb6130a985e8b6668004030cdcb101a1adb3570ada553f06079971c39055dd
[webhook] [2025-08-22 20:29:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4fc0fc407f79d917\n    [X-B3-Traceid] => 68a8d3383f765c557c0942c3790fbff9\n    [B3] => 68a8d3383f765c557c0942c3790fbff9-4fc0fc407f79d917-1\n    [Traceparent] => 00-68a8d3383f765c557c0942c3790fbff9-4fc0fc407f79d917-01\n    [X-Amzn-Trace-Id] => Root=1-68a8d338-3f765c557c0942c3790fbff9;Parent=4fc0fc407f79d917;Sampled=1\n    [X-Adsk-Signature] => sha256=16cb6130a985e8b6668004030cdcb101a1adb3570ada553f06079971c39055dd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755894584537-57122874418375-9033790641-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 20:29:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755894584537-57122874418375-9033790641-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T20:29:44.537Z"},"publishedAt":"2025-08-22T20:29:44.000Z","csn":"5103159758"}
[webhook] [2025-08-22 20:29:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 20:29:47
[webhook] [2025-08-22 20:29:47] [adwsapi_v2.php:36]  Provided signature: sha256=45eeffa317dfd5ff7f7fb580b961c7a2bcac31bc14050182cbb418ebdfe96fd7
[webhook] [2025-08-22 20:29:47] [adwsapi_v2.php:37]  Calculated signature: sha256=16cb6130a985e8b6668004030cdcb101a1adb3570ada553f06079971c39055dd
[webhook] [2025-08-22 20:29:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0464a79f1b1a8166\n    [X-B3-Traceid] => 68a8d3383f765c557c0942c3790fbff9\n    [B3] => 68a8d3383f765c557c0942c3790fbff9-0464a79f1b1a8166-1\n    [Traceparent] => 00-68a8d3383f765c557c0942c3790fbff9-0464a79f1b1a8166-01\n    [X-Amzn-Trace-Id] => Root=1-68a8d338-3f765c557c0942c3790fbff9;Parent=0464a79f1b1a8166;Sampled=1\n    [X-Adsk-Signature] => sha256=45eeffa317dfd5ff7f7fb580b961c7a2bcac31bc14050182cbb418ebdfe96fd7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755894584537-57122874418375-9033790641-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 20:29:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755894584537-57122874418375-9033790641-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57122874418375","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T20:29:44.537Z"},"publishedAt":"2025-08-22T20:29:44.000Z","csn":"5103159758"}
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 22:53:09
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:36]  Provided signature: sha256=8030af25103a7a899cdb8843a5273c7c23d0fb610e31e8fd9141d9e63b24fe01
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:37]  Calculated signature: sha256=c4035f6c8e1619b74c5a866cc80183be41e15c0fef33a31001f25b5318e9d6fb
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 28349f6977776459\n    [X-B3-Traceid] => 68a8f4d24f9acda636624cbe75f9102e\n    [B3] => 68a8f4d24f9acda636624cbe75f9102e-28349f6977776459-1\n    [Traceparent] => 00-68a8f4d24f9acda636624cbe75f9102e-28349f6977776459-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f4d2-4f9acda636624cbe75f9102e;Parent=28349f6977776459;Sampled=1\n    [X-Adsk-Signature] => sha256=8030af25103a7a899cdb8843a5273c7c23d0fb610e31e8fd9141d9e63b24fe01\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903186734-75588052271494-9033791156-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903186734-75588052271494-9033791156-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75588052271494","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T22:53:06.734Z"},"publishedAt":"2025-08-22T22:53:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 22:53:09
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:36]  Provided signature: sha256=c4035f6c8e1619b74c5a866cc80183be41e15c0fef33a31001f25b5318e9d6fb
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:37]  Calculated signature: sha256=c4035f6c8e1619b74c5a866cc80183be41e15c0fef33a31001f25b5318e9d6fb
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => acba672fd6195eaf\n    [X-B3-Traceid] => 68a8f4d24f9acda636624cbe75f9102e\n    [B3] => 68a8f4d24f9acda636624cbe75f9102e-acba672fd6195eaf-1\n    [Traceparent] => 00-68a8f4d24f9acda636624cbe75f9102e-acba672fd6195eaf-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f4d2-4f9acda636624cbe75f9102e;Parent=acba672fd6195eaf;Sampled=1\n    [X-Adsk-Signature] => sha256=c4035f6c8e1619b74c5a866cc80183be41e15c0fef33a31001f25b5318e9d6fb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903186734-75588052271494-9033791156-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 22:53:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903186734-75588052271494-9033791156-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75588052271494","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T22:53:06.734Z"},"publishedAt":"2025-08-22T22:53:06.000Z","csn":"5103159758"}
[webhook] [2025-08-22 22:54:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 22:54:21
[webhook] [2025-08-22 22:54:21] [adwsapi_v2.php:36]  Provided signature: sha256=c2e9a4d9cc3ff73db0db63729d5d6b730cfd03084438f28fcdcfdf269ae39eb8
[webhook] [2025-08-22 22:54:21] [adwsapi_v2.php:37]  Calculated signature: sha256=ac71a11319d4af7164e7470cedaf07a9d73510a1d9799a6de8f94a26787a9811
[webhook] [2025-08-22 22:54:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5b6933d8c136a3cf\n    [X-B3-Traceid] => 68a8f51b35e7f51a1a13d4a4492f72a9\n    [B3] => 68a8f51b35e7f51a1a13d4a4492f72a9-5b6933d8c136a3cf-1\n    [Traceparent] => 00-68a8f51b35e7f51a1a13d4a4492f72a9-5b6933d8c136a3cf-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f51b-35e7f51a1a13d4a4492f72a9;Parent=5b6933d8c136a3cf;Sampled=1\n    [X-Adsk-Signature] => sha256=c2e9a4d9cc3ff73db0db63729d5d6b730cfd03084438f28fcdcfdf269ae39eb8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903259789-57199375151109-9033791146-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 22:54:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903259789-57199375151109-9033791146-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57199375151109","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T22:54:19.789Z"},"publishedAt":"2025-08-22T22:54:19.000Z","csn":"5103159758"}
[webhook] [2025-08-22 22:54:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 22:54:22
[webhook] [2025-08-22 22:54:22] [adwsapi_v2.php:36]  Provided signature: sha256=ac71a11319d4af7164e7470cedaf07a9d73510a1d9799a6de8f94a26787a9811
[webhook] [2025-08-22 22:54:22] [adwsapi_v2.php:37]  Calculated signature: sha256=ac71a11319d4af7164e7470cedaf07a9d73510a1d9799a6de8f94a26787a9811
[webhook] [2025-08-22 22:54:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a98c962f1182b776\n    [X-B3-Traceid] => 68a8f51b35e7f51a1a13d4a4492f72a9\n    [B3] => 68a8f51b35e7f51a1a13d4a4492f72a9-a98c962f1182b776-1\n    [Traceparent] => 00-68a8f51b35e7f51a1a13d4a4492f72a9-a98c962f1182b776-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f51b-35e7f51a1a13d4a4492f72a9;Parent=a98c962f1182b776;Sampled=1\n    [X-Adsk-Signature] => sha256=ac71a11319d4af7164e7470cedaf07a9d73510a1d9799a6de8f94a26787a9811\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903259789-57199375151109-9033791146-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 22:54:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903259789-57199375151109-9033791146-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57199375151109","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-22T22:54:19.789Z"},"publishedAt":"2025-08-22T22:54:19.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:00:31
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:36]  Provided signature: sha256=c4be14db79c057f20312cebe3d8066a6e36f4182daff5c2cb507c9f83638c042
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:37]  Calculated signature: sha256=c414c11995676fe2791c43545296242e6c87f081cee2f4e83c2b92f7b7cbc1cc
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3a9d1fb0f9518993\n    [X-B3-Traceid] => 68a8f68a061801d12c221df1d16018a1\n    [B3] => 68a8f68a061801d12c221df1d16018a1-3a9d1fb0f9518993-1\n    [Traceparent] => 00-68a8f68a061801d12c221df1d16018a1-3a9d1fb0f9518993-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f68a-061801d12c221df1d16018a1;Parent=3a9d1fb0f9518993;Sampled=1\n    [X-Adsk-Signature] => sha256=c4be14db79c057f20312cebe3d8066a6e36f4182daff5c2cb507c9f83638c042\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 57afb165-fca4-4e20-962c-1faf453a35ff\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"57afb165-fca4-4e20-962c-1faf453a35ff","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-953277","transactionId":"23196f48-7025-5f32-8824-0fb371ed20df","quoteStatus":"Expired","message":"Quote# Q-953277 status changed to Expired.","modifiedAt":"2025-08-22T23:00:26.219Z"},"publishedAt":"2025-08-22T23:00:29.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:00:31
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:36]  Provided signature: sha256=c414c11995676fe2791c43545296242e6c87f081cee2f4e83c2b92f7b7cbc1cc
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:37]  Calculated signature: sha256=c414c11995676fe2791c43545296242e6c87f081cee2f4e83c2b92f7b7cbc1cc
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ce4b1e49a18089a5\n    [X-B3-Traceid] => 68a8f68a061801d12c221df1d16018a1\n    [B3] => 68a8f68a061801d12c221df1d16018a1-ce4b1e49a18089a5-1\n    [Traceparent] => 00-68a8f68a061801d12c221df1d16018a1-ce4b1e49a18089a5-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f68a-061801d12c221df1d16018a1;Parent=ce4b1e49a18089a5;Sampled=1\n    [X-Adsk-Signature] => sha256=c414c11995676fe2791c43545296242e6c87f081cee2f4e83c2b92f7b7cbc1cc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 57afb165-fca4-4e20-962c-1faf453a35ff\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:00:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"57afb165-fca4-4e20-962c-1faf453a35ff","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-953277","transactionId":"23196f48-7025-5f32-8824-0fb371ed20df","quoteStatus":"Expired","message":"Quote# Q-953277 status changed to Expired.","modifiedAt":"2025-08-22T23:00:26.219Z"},"publishedAt":"2025-08-22T23:00:29.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:00:40
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:36]  Provided signature: sha256=db44b711908330b5ea9cac50b09993333885f0b262409a6c9a1f91b595b47570
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:37]  Calculated signature: sha256=128d8bbe6546ca8aef6156391233a474f8c821bea09cebfedc16af965ca1ed2b
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3ffcdba79f2f0bec\n    [X-B3-Traceid] => 68a8f693e2757247c2bef4933d913ece\n    [B3] => 68a8f693e2757247c2bef4933d913ece-3ffcdba79f2f0bec-1\n    [Traceparent] => 00-68a8f693e2757247c2bef4933d913ece-3ffcdba79f2f0bec-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f693-e2757247c2bef4933d913ece;Parent=3ffcdba79f2f0bec;Sampled=1\n    [X-Adsk-Signature] => sha256=db44b711908330b5ea9cac50b09993333885f0b262409a6c9a1f91b595b47570\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => efcb5cb4-cfc1-4c07-b047-557e86214720\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"efcb5cb4-cfc1-4c07-b047-557e86214720","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-952055","transactionId":"e0c020a0-2506-5b89-ac84-405a8f94c054","quoteStatus":"Expired","message":"Quote# Q-952055 status changed to Expired.","modifiedAt":"2025-08-22T23:00:34.977Z"},"publishedAt":"2025-08-22T23:00:35.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:00:40
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:36]  Provided signature: sha256=128d8bbe6546ca8aef6156391233a474f8c821bea09cebfedc16af965ca1ed2b
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:37]  Calculated signature: sha256=128d8bbe6546ca8aef6156391233a474f8c821bea09cebfedc16af965ca1ed2b
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e4d05b494eafc457\n    [X-B3-Traceid] => 68a8f693e2757247c2bef4933d913ece\n    [B3] => 68a8f693e2757247c2bef4933d913ece-e4d05b494eafc457-1\n    [Traceparent] => 00-68a8f693e2757247c2bef4933d913ece-e4d05b494eafc457-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f693-e2757247c2bef4933d913ece;Parent=e4d05b494eafc457;Sampled=1\n    [X-Adsk-Signature] => sha256=128d8bbe6546ca8aef6156391233a474f8c821bea09cebfedc16af965ca1ed2b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => efcb5cb4-cfc1-4c07-b047-557e86214720\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:00:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"efcb5cb4-cfc1-4c07-b047-557e86214720","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-952055","transactionId":"e0c020a0-2506-5b89-ac84-405a8f94c054","quoteStatus":"Expired","message":"Quote# Q-952055 status changed to Expired.","modifiedAt":"2025-08-22T23:00:34.977Z"},"publishedAt":"2025-08-22T23:00:35.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:02:45
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:36]  Provided signature: sha256=c63c6929bd23faa35712a2d07629671c196e814bf7fae34fbcdc64b967f71de0
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:37]  Calculated signature: sha256=3af8f29c3699e4a8be4fa0b7ba470a04764d1b48fce70336d49b6b28ef282821
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 721d70514ba3a92c\n    [X-B3-Traceid] => 68a8f713181ad27f5d42387963ad8bb7\n    [B3] => 68a8f713181ad27f5d42387963ad8bb7-721d70514ba3a92c-1\n    [Traceparent] => 00-68a8f713181ad27f5d42387963ad8bb7-721d70514ba3a92c-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f713-181ad27f5d42387963ad8bb7;Parent=721d70514ba3a92c;Sampled=1\n    [X-Adsk-Signature] => sha256=c63c6929bd23faa35712a2d07629671c196e814bf7fae34fbcdc64b967f71de0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903763432-574-96089708\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903763432-574-96089708","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96089708","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-22T23:02:43.432Z"},"publishedAt":"2025-08-22T23:02:43.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:02:45
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:36]  Provided signature: sha256=3af8f29c3699e4a8be4fa0b7ba470a04764d1b48fce70336d49b6b28ef282821
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:37]  Calculated signature: sha256=3af8f29c3699e4a8be4fa0b7ba470a04764d1b48fce70336d49b6b28ef282821
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 33f8fb8f553a19ec\n    [X-B3-Traceid] => 68a8f713181ad27f5d42387963ad8bb7\n    [B3] => 68a8f713181ad27f5d42387963ad8bb7-33f8fb8f553a19ec-1\n    [Traceparent] => 00-68a8f713181ad27f5d42387963ad8bb7-33f8fb8f553a19ec-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f713-181ad27f5d42387963ad8bb7;Parent=33f8fb8f553a19ec;Sampled=1\n    [X-Adsk-Signature] => sha256=3af8f29c3699e4a8be4fa0b7ba470a04764d1b48fce70336d49b6b28ef282821\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903763432-574-96089708\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:02:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903763432-574-96089708","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96089708","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-22T23:02:43.432Z"},"publishedAt":"2025-08-22T23:02:43.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:03:16
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:36]  Provided signature: sha256=683849392dd665d0dbf7a57102751ef0d480f10495fa96e55de6e0c3229641e9
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:37]  Calculated signature: sha256=da11b33cc7599034f30d05814be91eccc6897c84fbb5e4e16984c1630b136cd6
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2c6e5dcf1d031448\n    [X-B3-Traceid] => 68a8f73202705b2d400132cd78f2ed0b\n    [B3] => 68a8f73202705b2d400132cd78f2ed0b-2c6e5dcf1d031448-1\n    [Traceparent] => 00-68a8f73202705b2d400132cd78f2ed0b-2c6e5dcf1d031448-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f732-02705b2d400132cd78f2ed0b;Parent=2c6e5dcf1d031448;Sampled=1\n    [X-Adsk-Signature] => sha256=683849392dd665d0dbf7a57102751ef0d480f10495fa96e55de6e0c3229641e9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903794389-574-96087431\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903794389-574-96087431","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96087431","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-22T23:03:14.389Z"},"publishedAt":"2025-08-22T23:03:14.000Z","csn":"5103159758"}
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-22 23:03:16
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:36]  Provided signature: sha256=da11b33cc7599034f30d05814be91eccc6897c84fbb5e4e16984c1630b136cd6
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:37]  Calculated signature: sha256=da11b33cc7599034f30d05814be91eccc6897c84fbb5e4e16984c1630b136cd6
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 888bf82b39fd46c5\n    [X-B3-Traceid] => 68a8f73202705b2d400132cd78f2ed0b\n    [B3] => 68a8f73202705b2d400132cd78f2ed0b-888bf82b39fd46c5-1\n    [Traceparent] => 00-68a8f73202705b2d400132cd78f2ed0b-888bf82b39fd46c5-01\n    [X-Amzn-Trace-Id] => Root=1-68a8f732-02705b2d400132cd78f2ed0b;Parent=888bf82b39fd46c5;Sampled=1\n    [X-Adsk-Signature] => sha256=da11b33cc7599034f30d05814be91eccc6897c84fbb5e4e16984c1630b136cd6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755903794389-574-96087431\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-22 23:03:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755903794389-574-96087431","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96087431","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-22T23:03:14.389Z"},"publishedAt":"2025-08-22T23:03:14.000Z","csn":"5103159758"}
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 00:00:58
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:36]  Provided signature: sha256=4d144da52d6b016f16d14d88ab2839d9464f7efa9a76924ad5b88e3691324158
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:37]  Calculated signature: sha256=4d144da52d6b016f16d14d88ab2839d9464f7efa9a76924ad5b88e3691324158
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b42af8f13596f648\n    [X-B3-Traceid] => 68a904b824d517691c5f4a965b586f48\n    [B3] => 68a904b824d517691c5f4a965b586f48-b42af8f13596f648-1\n    [Traceparent] => 00-68a904b824d517691c5f4a965b586f48-b42af8f13596f648-01\n    [X-Amzn-Trace-Id] => Root=1-68a904b8-24d517691c5f4a965b586f48;Parent=b42af8f13596f648;Sampled=1\n    [X-Adsk-Signature] => sha256=4d144da52d6b016f16d14d88ab2839d9464f7efa9a76924ad5b88e3691324158\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4790f5b2-e6c2-4e32-a524-f73718d4053d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"4790f5b2-e6c2-4e32-a524-f73718d4053d","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-23T00:00:56Z"},"publishedAt":"2025-08-23T00:00:56.000Z","country":"GB"}
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 00:00:58
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:36]  Provided signature: sha256=e971771b1b7114049214263f2ebbc0f0f50bcc0d5a73df7106a0b8d9c89d0c64
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:37]  Calculated signature: sha256=4d144da52d6b016f16d14d88ab2839d9464f7efa9a76924ad5b88e3691324158
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d13ce5b764f9841a\n    [X-B3-Traceid] => 68a904b824d517691c5f4a965b586f48\n    [B3] => 68a904b824d517691c5f4a965b586f48-d13ce5b764f9841a-1\n    [Traceparent] => 00-68a904b824d517691c5f4a965b586f48-d13ce5b764f9841a-01\n    [X-Amzn-Trace-Id] => Root=1-68a904b8-24d517691c5f4a965b586f48;Parent=d13ce5b764f9841a;Sampled=1\n    [X-Adsk-Signature] => sha256=e971771b1b7114049214263f2ebbc0f0f50bcc0d5a73df7106a0b8d9c89d0c64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4790f5b2-e6c2-4e32-a524-f73718d4053d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-23 00:00:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"4790f5b2-e6c2-4e32-a524-f73718d4053d","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-23T00:00:56Z"},"publishedAt":"2025-08-23T00:00:56.000Z","country":"GB"}
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 14:37:52
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:36]  Provided signature: sha256=af6d53fe934a2680f98c0766831486780c6e469a5a8ab336524071fbac372a19
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:37]  Calculated signature: sha256=af6d53fe934a2680f98c0766831486780c6e469a5a8ab336524071fbac372a19
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9711114e0fb6efef\n    [X-B3-Traceid] => 68a9d23d00fd62f855fe0c86134b3ae5\n    [B3] => 68a9d23d00fd62f855fe0c86134b3ae5-9711114e0fb6efef-1\n    [Traceparent] => 00-68a9d23d00fd62f855fe0c86134b3ae5-9711114e0fb6efef-01\n    [X-Amzn-Trace-Id] => Root=1-68a9d23d-00fd62f855fe0c86134b3ae5;Parent=9711114e0fb6efef;Sampled=1\n    [X-Adsk-Signature] => sha256=af6d53fe934a2680f98c0766831486780c6e469a5a8ab336524071fbac372a19\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => abf942da-b04c-458e-9521-d90c42584f41\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"abf942da-b04c-458e-9521-d90c42584f41","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59921156018881","status":"Active","quantity":1,"endDate":"2026-09-03","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-23T14:02:46.000+0000"},"publishedAt":"2025-08-23T14:37:49.000Z","csn":"5103159758"}
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 14:37:52
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:36]  Provided signature: sha256=da2cf51cd221b9dd7e4d1e2583f632770f572785d2bd438283a5c92d963575ee
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:37]  Calculated signature: sha256=af6d53fe934a2680f98c0766831486780c6e469a5a8ab336524071fbac372a19
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => eaae007e99f7e703\n    [X-B3-Traceid] => 68a9d23d00fd62f855fe0c86134b3ae5\n    [B3] => 68a9d23d00fd62f855fe0c86134b3ae5-eaae007e99f7e703-1\n    [Traceparent] => 00-68a9d23d00fd62f855fe0c86134b3ae5-eaae007e99f7e703-01\n    [X-Amzn-Trace-Id] => Root=1-68a9d23d-00fd62f855fe0c86134b3ae5;Parent=eaae007e99f7e703;Sampled=1\n    [X-Adsk-Signature] => sha256=da2cf51cd221b9dd7e4d1e2583f632770f572785d2bd438283a5c92d963575ee\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => abf942da-b04c-458e-9521-d90c42584f41\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-23 14:37:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"abf942da-b04c-458e-9521-d90c42584f41","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59921156018881","status":"Active","quantity":1,"endDate":"2026-09-03","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-23T14:02:46.000+0000"},"publishedAt":"2025-08-23T14:37:49.000Z","csn":"5103159758"}
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 15:39:47
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:36]  Provided signature: sha256=d7aab58e435108d86c63b54a3ea986590dff634aabad7ec1341fdd56d6089dc8
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:37]  Calculated signature: sha256=d7aab58e435108d86c63b54a3ea986590dff634aabad7ec1341fdd56d6089dc8
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f557d93914636792\n    [X-B3-Traceid] => 68a9e0c058ffdb2b05451578542c1e70\n    [B3] => 68a9e0c058ffdb2b05451578542c1e70-f557d93914636792-1\n    [Traceparent] => 00-68a9e0c058ffdb2b05451578542c1e70-f557d93914636792-01\n    [X-Amzn-Trace-Id] => Root=1-68a9e0c0-58ffdb2b05451578542c1e70;Parent=f557d93914636792;Sampled=1\n    [X-Adsk-Signature] => sha256=d7aab58e435108d86c63b54a3ea986590dff634aabad7ec1341fdd56d6089dc8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 903860b0-4192-4a80-84d9-59beecf7f397\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"903860b0-4192-4a80-84d9-59beecf7f397","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59921156018881","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-23T15:04:28.000+0000"},"publishedAt":"2025-08-23T15:39:44.000Z","csn":"5103159758"}
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 15:39:47
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:36]  Provided signature: sha256=7b5d8a0a359a1bb4555ec570fc616ee75c7fa6e1b4f936ffdaba837f67f5b0de
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:37]  Calculated signature: sha256=d7aab58e435108d86c63b54a3ea986590dff634aabad7ec1341fdd56d6089dc8
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 14195525c484559b\n    [X-B3-Traceid] => 68a9e0c058ffdb2b05451578542c1e70\n    [B3] => 68a9e0c058ffdb2b05451578542c1e70-14195525c484559b-1\n    [Traceparent] => 00-68a9e0c058ffdb2b05451578542c1e70-14195525c484559b-01\n    [X-Amzn-Trace-Id] => Root=1-68a9e0c0-58ffdb2b05451578542c1e70;Parent=14195525c484559b;Sampled=1\n    [X-Adsk-Signature] => sha256=7b5d8a0a359a1bb4555ec570fc616ee75c7fa6e1b4f936ffdaba837f67f5b0de\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 903860b0-4192-4a80-84d9-59beecf7f397\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-23 15:39:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"903860b0-4192-4a80-84d9-59beecf7f397","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59921156018881","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-23T15:04:28.000+0000"},"publishedAt":"2025-08-23T15:39:44.000Z","csn":"5103159758"}
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 20:14:00
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:36]  Provided signature: sha256=d76c965de91304a0da3ce8f133dd9ce4527d095c221cbe8baafe6255f6524512
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:37]  Calculated signature: sha256=d76c965de91304a0da3ce8f133dd9ce4527d095c221cbe8baafe6255f6524512
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a0695ae8b8525ea0\n    [X-B3-Traceid] => 68aa2106217603683db58c0a1cf8c77c\n    [B3] => 68aa2106217603683db58c0a1cf8c77c-a0695ae8b8525ea0-1\n    [Traceparent] => 00-68aa2106217603683db58c0a1cf8c77c-a0695ae8b8525ea0-01\n    [X-Amzn-Trace-Id] => Root=1-68aa2106-217603683db58c0a1cf8c77c;Parent=a0695ae8b8525ea0;Sampled=1\n    [X-Adsk-Signature] => sha256=d76c965de91304a0da3ce8f133dd9ce4527d095c221cbe8baafe6255f6524512\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755980038038-59921156018881-9033792602-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755980038038-59921156018881-9033792602-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59921156018881","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-23T20:13:58.038Z"},"publishedAt":"2025-08-23T20:13:58.000Z","csn":"5103159758"}
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 20:14:00
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:36]  Provided signature: sha256=a70cb6b3458bed56d9f0c1689711f4f245ec5f77ee9a07bfc79865ceaad53a97
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:37]  Calculated signature: sha256=d76c965de91304a0da3ce8f133dd9ce4527d095c221cbe8baafe6255f6524512
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3fda41b6098a13a8\n    [X-B3-Traceid] => 68aa2106217603683db58c0a1cf8c77c\n    [B3] => 68aa2106217603683db58c0a1cf8c77c-3fda41b6098a13a8-1\n    [Traceparent] => 00-68aa2106217603683db58c0a1cf8c77c-3fda41b6098a13a8-01\n    [X-Amzn-Trace-Id] => Root=1-68aa2106-217603683db58c0a1cf8c77c;Parent=3fda41b6098a13a8;Sampled=1\n    [X-Adsk-Signature] => sha256=a70cb6b3458bed56d9f0c1689711f4f245ec5f77ee9a07bfc79865ceaad53a97\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755980038038-59921156018881-9033792602-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-23 20:14:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755980038038-59921156018881-9033792602-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59921156018881","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-23T20:13:58.038Z"},"publishedAt":"2025-08-23T20:13:58.000Z","csn":"5103159758"}
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 23:04:19
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:36]  Provided signature: sha256=c42bc0ca7112da7fbaa23390ba4177f053ce9b8c4a4f83ef82a7a5d530faee1d
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:37]  Calculated signature: sha256=fcb55295f4dd01ab14c9d9fb95211ce211f63d15d0425f73427a77e78be083bd
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7247ce8d8a0ed99f\n    [X-B3-Traceid] => 68aa48f11d59d2c2322301253d4b497a\n    [B3] => 68aa48f11d59d2c2322301253d4b497a-7247ce8d8a0ed99f-1\n    [Traceparent] => 00-68aa48f11d59d2c2322301253d4b497a-7247ce8d8a0ed99f-01\n    [X-Amzn-Trace-Id] => Root=1-68aa48f1-1d59d2c2322301253d4b497a;Parent=7247ce8d8a0ed99f;Sampled=1\n    [X-Adsk-Signature] => sha256=c42bc0ca7112da7fbaa23390ba4177f053ce9b8c4a4f83ef82a7a5d530faee1d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755990257642-574-96315974\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755990257642-574-96315974","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96315974","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-23T23:04:17.642Z"},"publishedAt":"2025-08-23T23:04:17.000Z","csn":"5103159758"}
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-23 23:04:19
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:36]  Provided signature: sha256=fcb55295f4dd01ab14c9d9fb95211ce211f63d15d0425f73427a77e78be083bd
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:37]  Calculated signature: sha256=fcb55295f4dd01ab14c9d9fb95211ce211f63d15d0425f73427a77e78be083bd
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c50134916ab66bb0\n    [X-B3-Traceid] => 68aa48f11d59d2c2322301253d4b497a\n    [B3] => 68aa48f11d59d2c2322301253d4b497a-c50134916ab66bb0-1\n    [Traceparent] => 00-68aa48f11d59d2c2322301253d4b497a-c50134916ab66bb0-01\n    [X-Amzn-Trace-Id] => Root=1-68aa48f1-1d59d2c2322301253d4b497a;Parent=c50134916ab66bb0;Sampled=1\n    [X-Adsk-Signature] => sha256=fcb55295f4dd01ab14c9d9fb95211ce211f63d15d0425f73427a77e78be083bd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755990257642-574-96315974\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-23 23:04:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755990257642-574-96315974","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96315974","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-23T23:04:17.642Z"},"publishedAt":"2025-08-23T23:04:17.000Z","csn":"5103159758"}
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 00:00:46
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=6e7e63d65cb5020b93a7c9ddb774a2ff9ebc1b261aaf1229f4e922d93a1b1b81
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=c3d09377459d697d530e35708148aecd0f4350016e42c26c1a1b8327a0fada2a
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7897b024188bee91\n    [X-B3-Traceid] => 68aa562b712d20166ce707b4756e165d\n    [B3] => 68aa562b712d20166ce707b4756e165d-7897b024188bee91-1\n    [Traceparent] => 00-68aa562b712d20166ce707b4756e165d-7897b024188bee91-01\n    [X-Amzn-Trace-Id] => Root=1-68aa562b-712d20166ce707b4756e165d;Parent=7897b024188bee91;Sampled=1\n    [X-Adsk-Signature] => sha256=6e7e63d65cb5020b93a7c9ddb774a2ff9ebc1b261aaf1229f4e922d93a1b1b81\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d6da3d18-4abd-499c-b0a5-389f8dfbb879\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"d6da3d18-4abd-499c-b0a5-389f8dfbb879","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-24T00:00:43Z"},"publishedAt":"2025-08-24T00:00:43.000Z","country":"GB"}
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 00:00:46
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=c3d09377459d697d530e35708148aecd0f4350016e42c26c1a1b8327a0fada2a
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=c3d09377459d697d530e35708148aecd0f4350016e42c26c1a1b8327a0fada2a
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2e64301982e2185e\n    [X-B3-Traceid] => 68aa562b712d20166ce707b4756e165d\n    [B3] => 68aa562b712d20166ce707b4756e165d-2e64301982e2185e-1\n    [Traceparent] => 00-68aa562b712d20166ce707b4756e165d-2e64301982e2185e-01\n    [X-Amzn-Trace-Id] => Root=1-68aa562b-712d20166ce707b4756e165d;Parent=2e64301982e2185e;Sampled=1\n    [X-Adsk-Signature] => sha256=c3d09377459d697d530e35708148aecd0f4350016e42c26c1a1b8327a0fada2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d6da3d18-4abd-499c-b0a5-389f8dfbb879\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-24 00:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"d6da3d18-4abd-499c-b0a5-389f8dfbb879","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-24T00:00:43Z"},"publishedAt":"2025-08-24T00:00:43.000Z","country":"GB"}
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:00:42
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:36]  Provided signature: sha256=c70a71a18fe9a1fb6892224c1445fe66fc1c24216d43c3b2f96ad98b9ff7556b
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:37]  Calculated signature: sha256=4f78dfb1c108471d464b99088315f10a5e419f3ada96bb28eed646c1bc6db030
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 21acae5270f8b56c\n    [X-B3-Traceid] => 68ab999808234b7bc7acc6c0a6f0d562\n    [B3] => 68ab999808234b7bc7acc6c0a6f0d562-21acae5270f8b56c-1\n    [Traceparent] => 00-68ab999808234b7bc7acc6c0a6f0d562-21acae5270f8b56c-01\n    [X-Amzn-Trace-Id] => Root=1-68ab9998-08234b7bc7acc6c0a6f0d562;Parent=21acae5270f8b56c;Sampled=1\n    [X-Adsk-Signature] => sha256=c70a71a18fe9a1fb6892224c1445fe66fc1c24216d43c3b2f96ad98b9ff7556b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86c48622-9d7c-4b25-adc6-743205c0561d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"86c48622-9d7c-4b25-adc6-743205c0561d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-959019","transactionId":"cd676e8a-08b1-554b-9744-0e7776c6b8dd","quoteStatus":"Expired","message":"Quote# Q-959019 status changed to Expired.","modifiedAt":"2025-08-24T23:00:39.283Z"},"publishedAt":"2025-08-24T23:00:40.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:00:42
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:36]  Provided signature: sha256=4f78dfb1c108471d464b99088315f10a5e419f3ada96bb28eed646c1bc6db030
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:37]  Calculated signature: sha256=4f78dfb1c108471d464b99088315f10a5e419f3ada96bb28eed646c1bc6db030
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4c9faf5550fd3774\n    [X-B3-Traceid] => 68ab999808234b7bc7acc6c0a6f0d562\n    [B3] => 68ab999808234b7bc7acc6c0a6f0d562-4c9faf5550fd3774-1\n    [Traceparent] => 00-68ab999808234b7bc7acc6c0a6f0d562-4c9faf5550fd3774-01\n    [X-Amzn-Trace-Id] => Root=1-68ab9998-08234b7bc7acc6c0a6f0d562;Parent=4c9faf5550fd3774;Sampled=1\n    [X-Adsk-Signature] => sha256=4f78dfb1c108471d464b99088315f10a5e419f3ada96bb28eed646c1bc6db030\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86c48622-9d7c-4b25-adc6-743205c0561d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:00:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"86c48622-9d7c-4b25-adc6-743205c0561d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-959019","transactionId":"cd676e8a-08b1-554b-9744-0e7776c6b8dd","quoteStatus":"Expired","message":"Quote# Q-959019 status changed to Expired.","modifiedAt":"2025-08-24T23:00:39.283Z"},"publishedAt":"2025-08-24T23:00:40.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:01:44
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:36]  Provided signature: sha256=7ab2d087a550add285170d3248bdda9b7c44ec9924213905295363d85766410e
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:37]  Calculated signature: sha256=5f1bb4e4775666947d55bbeb1e6b5ab2695b5d7c2bb5d38bc148a9a143ee1ad0
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ff5b2ae23b4cd28a\n    [X-B3-Traceid] => 68ab99d61ea3adb573a56f82260e3bcc\n    [B3] => 68ab99d61ea3adb573a56f82260e3bcc-ff5b2ae23b4cd28a-1\n    [Traceparent] => 00-68ab99d61ea3adb573a56f82260e3bcc-ff5b2ae23b4cd28a-01\n    [X-Amzn-Trace-Id] => Root=1-68ab99d6-1ea3adb573a56f82260e3bcc;Parent=ff5b2ae23b4cd28a;Sampled=1\n    [X-Adsk-Signature] => sha256=7ab2d087a550add285170d3248bdda9b7c44ec9924213905295363d85766410e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756076502424-574-96427129\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756076502424-574-96427129","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96427129","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-24T23:01:42.424Z"},"publishedAt":"2025-08-24T23:01:42.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:01:44
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:36]  Provided signature: sha256=5f1bb4e4775666947d55bbeb1e6b5ab2695b5d7c2bb5d38bc148a9a143ee1ad0
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:37]  Calculated signature: sha256=5f1bb4e4775666947d55bbeb1e6b5ab2695b5d7c2bb5d38bc148a9a143ee1ad0
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3f49da89f356370e\n    [X-B3-Traceid] => 68ab99d61ea3adb573a56f82260e3bcc\n    [B3] => 68ab99d61ea3adb573a56f82260e3bcc-3f49da89f356370e-1\n    [Traceparent] => 00-68ab99d61ea3adb573a56f82260e3bcc-3f49da89f356370e-01\n    [X-Amzn-Trace-Id] => Root=1-68ab99d6-1ea3adb573a56f82260e3bcc;Parent=3f49da89f356370e;Sampled=1\n    [X-Adsk-Signature] => sha256=5f1bb4e4775666947d55bbeb1e6b5ab2695b5d7c2bb5d38bc148a9a143ee1ad0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756076502424-574-96427129\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:01:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756076502424-574-96427129","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-96427129","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-24T23:01:42.424Z"},"publishedAt":"2025-08-24T23:01:42.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:03:47
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:36]  Provided signature: sha256=bc877f4391b4e9d6291ae052f532ced1d5a84906a3802a49f8f50c9830dc1c64
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:37]  Calculated signature: sha256=bc877f4391b4e9d6291ae052f532ced1d5a84906a3802a49f8f50c9830dc1c64
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f5e4b1a0a4dc2bba\n    [X-B3-Traceid] => 68ab9a5004a7781e6e9d180b5e06f212\n    [B3] => 68ab9a5004a7781e6e9d180b5e06f212-f5e4b1a0a4dc2bba-1\n    [Traceparent] => 00-68ab9a5004a7781e6e9d180b5e06f212-f5e4b1a0a4dc2bba-01\n    [X-Amzn-Trace-Id] => Root=1-68ab9a50-04a7781e6e9d180b5e06f212;Parent=f5e4b1a0a4dc2bba;Sampled=1\n    [X-Adsk-Signature] => sha256=bc877f4391b4e9d6291ae052f532ced1d5a84906a3802a49f8f50c9830dc1c64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756076624915-574-13998608\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756076624915-574-13998608","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-13998608","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-24T23:03:44.915Z"},"publishedAt":"2025-08-24T23:03:44.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:03:47
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:36]  Provided signature: sha256=cffe27b66a12d504ba6db85f0c0dccd64e6aeeb704f640fcb614411f4c847635
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:37]  Calculated signature: sha256=bc877f4391b4e9d6291ae052f532ced1d5a84906a3802a49f8f50c9830dc1c64
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => eb2aaa72fd5125f1\n    [X-B3-Traceid] => 68ab9a5004a7781e6e9d180b5e06f212\n    [B3] => 68ab9a5004a7781e6e9d180b5e06f212-eb2aaa72fd5125f1-1\n    [Traceparent] => 00-68ab9a5004a7781e6e9d180b5e06f212-eb2aaa72fd5125f1-01\n    [X-Amzn-Trace-Id] => Root=1-68ab9a50-04a7781e6e9d180b5e06f212;Parent=eb2aaa72fd5125f1;Sampled=1\n    [X-Adsk-Signature] => sha256=cffe27b66a12d504ba6db85f0c0dccd64e6aeeb704f640fcb614411f4c847635\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756076624915-574-13998608\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:03:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756076624915-574-13998608","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-13998608","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-24T23:03:44.915Z"},"publishedAt":"2025-08-24T23:03:44.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:40:17
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:36]  Provided signature: sha256=ced9f8f869143a63e0743e5d7a4afe7b9cf00371911a3473948faacd66a375a1
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:37]  Calculated signature: sha256=ced9f8f869143a63e0743e5d7a4afe7b9cf00371911a3473948faacd66a375a1
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b54e4814f31b79c5\n    [X-B3-Traceid] => 68aba2df538ff609312e7dda42c1d6c6\n    [B3] => 68aba2df538ff609312e7dda42c1d6c6-b54e4814f31b79c5-1\n    [Traceparent] => 00-68aba2df538ff609312e7dda42c1d6c6-b54e4814f31b79c5-01\n    [X-Amzn-Trace-Id] => Root=1-68aba2df-538ff609312e7dda42c1d6c6;Parent=b54e4814f31b79c5;Sampled=1\n    [X-Adsk-Signature] => sha256=ced9f8f869143a63e0743e5d7a4afe7b9cf00371911a3473948faacd66a375a1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6ffba838-92db-46fb-bb86-4572d1bffe2d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"6ffba838-92db-46fb-bb86-4572d1bffe2d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75266597996116","quantity":1,"endDate":"2026-08-24","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-24T23:05:13.000+0000"},"publishedAt":"2025-08-24T23:40:15.000Z","csn":"5103159758"}
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-24 23:40:17
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:36]  Provided signature: sha256=b9d8919667a309e2328225cbc6eaca114b4102fa3c3c3f06c34a95f15e238ee5
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:37]  Calculated signature: sha256=ced9f8f869143a63e0743e5d7a4afe7b9cf00371911a3473948faacd66a375a1
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0cb15f1da601a5ce\n    [X-B3-Traceid] => 68aba2df538ff609312e7dda42c1d6c6\n    [B3] => 68aba2df538ff609312e7dda42c1d6c6-0cb15f1da601a5ce-1\n    [Traceparent] => 00-68aba2df538ff609312e7dda42c1d6c6-0cb15f1da601a5ce-01\n    [X-Amzn-Trace-Id] => Root=1-68aba2df-538ff609312e7dda42c1d6c6;Parent=0cb15f1da601a5ce;Sampled=1\n    [X-Adsk-Signature] => sha256=b9d8919667a309e2328225cbc6eaca114b4102fa3c3c3f06c34a95f15e238ee5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6ffba838-92db-46fb-bb86-4572d1bffe2d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-24 23:40:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"6ffba838-92db-46fb-bb86-4572d1bffe2d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75266597996116","quantity":1,"endDate":"2026-08-24","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-24T23:05:13.000+0000"},"publishedAt":"2025-08-24T23:40:15.000Z","csn":"5103159758"}
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 00:01:04
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:36]  Provided signature: sha256=ca896f6ba3f92d9604bfb8138c851943935f294db2b4e10fc261e4ca47b55273
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:37]  Calculated signature: sha256=bb79ee609e04b022526778e3f244aa4b40e6fa9d15e1165c5e02b9f70b9f118d
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a1e0f99b55fbe99a\n    [X-B3-Traceid] => 68aba7bd5d1c35b54604b5022062d3fc\n    [B3] => 68aba7bd5d1c35b54604b5022062d3fc-a1e0f99b55fbe99a-1\n    [Traceparent] => 00-68aba7bd5d1c35b54604b5022062d3fc-a1e0f99b55fbe99a-01\n    [X-Amzn-Trace-Id] => Root=1-68aba7bd-5d1c35b54604b5022062d3fc;Parent=a1e0f99b55fbe99a;Sampled=1\n    [X-Adsk-Signature] => sha256=ca896f6ba3f92d9604bfb8138c851943935f294db2b4e10fc261e4ca47b55273\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6a8ca06b-0ccf-4245-814b-4a2f03c96034\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"6a8ca06b-0ccf-4245-814b-4a2f03c96034","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-25T00:01:01Z"},"publishedAt":"2025-08-25T00:01:01.000Z","country":"GB"}
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 00:01:04
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:36]  Provided signature: sha256=bb79ee609e04b022526778e3f244aa4b40e6fa9d15e1165c5e02b9f70b9f118d
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:37]  Calculated signature: sha256=bb79ee609e04b022526778e3f244aa4b40e6fa9d15e1165c5e02b9f70b9f118d
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cea6811a0a3f0cfa\n    [X-B3-Traceid] => 68aba7bd5d1c35b54604b5022062d3fc\n    [B3] => 68aba7bd5d1c35b54604b5022062d3fc-cea6811a0a3f0cfa-1\n    [Traceparent] => 00-68aba7bd5d1c35b54604b5022062d3fc-cea6811a0a3f0cfa-01\n    [X-Amzn-Trace-Id] => Root=1-68aba7bd-5d1c35b54604b5022062d3fc;Parent=cea6811a0a3f0cfa;Sampled=1\n    [X-Adsk-Signature] => sha256=bb79ee609e04b022526778e3f244aa4b40e6fa9d15e1165c5e02b9f70b9f118d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6a8ca06b-0ccf-4245-814b-4a2f03c96034\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 00:01:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"6a8ca06b-0ccf-4245-814b-4a2f03c96034","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-25T00:01:01Z"},"publishedAt":"2025-08-25T00:01:01.000Z","country":"GB"}
[webhook] [2025-08-25 03:37:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 03:37:41
[webhook] [2025-08-25 03:37:41] [adwsapi_v2.php:36]  Provided signature: sha256=043a92f5a3e905c271a89f0b3c9c41d6999d14c4fe6b6738f9a09d4fccb9b7aa
[webhook] [2025-08-25 03:37:41] [adwsapi_v2.php:37]  Calculated signature: sha256=9a0f46bf3d6119fb9eaff26c1a8e2ffc51fa7c135d22033ac7e306644781b154
[webhook] [2025-08-25 03:37:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e0bbaf8393c345a0\n    [X-B3-Traceid] => 68abda83061fc2c2126223fe2bc73f86\n    [B3] => 68abda83061fc2c2126223fe2bc73f86-e0bbaf8393c345a0-1\n    [Traceparent] => 00-68abda83061fc2c2126223fe2bc73f86-e0bbaf8393c345a0-01\n    [X-Amzn-Trace-Id] => Root=1-68abda83-061fc2c2126223fe2bc73f86;Parent=e0bbaf8393c345a0;Sampled=1\n    [X-Adsk-Signature] => sha256=043a92f5a3e905c271a89f0b3c9c41d6999d14c4fe6b6738f9a09d4fccb9b7aa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 91120408-0ddc-48b1-a389-c519976d7758\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 03:37:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"91120408-0ddc-48b1-a389-c519976d7758","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75266597996116","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-25T03:07:35.000+0000"},"publishedAt":"2025-08-25T03:37:39.000Z","csn":"5103159758"}
[webhook] [2025-08-25 03:37:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 03:37:42
[webhook] [2025-08-25 03:37:42] [adwsapi_v2.php:36]  Provided signature: sha256=9a0f46bf3d6119fb9eaff26c1a8e2ffc51fa7c135d22033ac7e306644781b154
[webhook] [2025-08-25 03:37:42] [adwsapi_v2.php:37]  Calculated signature: sha256=9a0f46bf3d6119fb9eaff26c1a8e2ffc51fa7c135d22033ac7e306644781b154
[webhook] [2025-08-25 03:37:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 85489326b1d662f0\n    [X-B3-Traceid] => 68abda83061fc2c2126223fe2bc73f86\n    [B3] => 68abda83061fc2c2126223fe2bc73f86-85489326b1d662f0-1\n    [Traceparent] => 00-68abda83061fc2c2126223fe2bc73f86-85489326b1d662f0-01\n    [X-Amzn-Trace-Id] => Root=1-68abda83-061fc2c2126223fe2bc73f86;Parent=85489326b1d662f0;Sampled=1\n    [X-Adsk-Signature] => sha256=9a0f46bf3d6119fb9eaff26c1a8e2ffc51fa7c135d22033ac7e306644781b154\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 91120408-0ddc-48b1-a389-c519976d7758\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 03:37:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"91120408-0ddc-48b1-a389-c519976d7758","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75266597996116","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-25T03:07:35.000+0000"},"publishedAt":"2025-08-25T03:37:39.000Z","csn":"5103159758"}
[webhook] [2025-08-25 06:24:57] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 06:24:57
[webhook] [2025-08-25 06:24:57] [adwsapi_v2.php:36]  Provided signature: sha256=4d336fc5b976cad133845195bba696ae3a20543c4b797140ffb6e765c83c5a92
[webhook] [2025-08-25 06:24:57] [adwsapi_v2.php:37]  Calculated signature: sha256=4d336fc5b976cad133845195bba696ae3a20543c4b797140ffb6e765c83c5a92
[webhook] [2025-08-25 06:24:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 74236cae02052fcf\n    [X-B3-Traceid] => 68ac01b7735e9830692261ec6b1181c3\n    [B3] => 68ac01b7735e9830692261ec6b1181c3-74236cae02052fcf-1\n    [Traceparent] => 00-68ac01b7735e9830692261ec6b1181c3-74236cae02052fcf-01\n    [X-Amzn-Trace-Id] => Root=1-68ac01b7-735e9830692261ec6b1181c3;Parent=74236cae02052fcf;Sampled=1\n    [X-Adsk-Signature] => sha256=4d336fc5b976cad133845195bba696ae3a20543c4b797140ffb6e765c83c5a92\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756103095770-75266597996116-9033795111-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 06:24:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756103095770-75266597996116-9033795111-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75266597996116","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-25T06:24:55.770Z"},"publishedAt":"2025-08-25T06:24:55.000Z","csn":"5103159758"}
[webhook] [2025-08-25 06:24:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 06:24:58
[webhook] [2025-08-25 06:24:58] [adwsapi_v2.php:36]  Provided signature: sha256=73f3e7bd4ec81308dfe5ee301f6bf96a0fbae1ee805658e9d9f9c78ff4381bc5
[webhook] [2025-08-25 06:24:58] [adwsapi_v2.php:37]  Calculated signature: sha256=4d336fc5b976cad133845195bba696ae3a20543c4b797140ffb6e765c83c5a92
[webhook] [2025-08-25 06:24:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 12da18e178183611\n    [X-B3-Traceid] => 68ac01b7735e9830692261ec6b1181c3\n    [B3] => 68ac01b7735e9830692261ec6b1181c3-12da18e178183611-1\n    [Traceparent] => 00-68ac01b7735e9830692261ec6b1181c3-12da18e178183611-01\n    [X-Amzn-Trace-Id] => Root=1-68ac01b7-735e9830692261ec6b1181c3;Parent=12da18e178183611;Sampled=1\n    [X-Adsk-Signature] => sha256=73f3e7bd4ec81308dfe5ee301f6bf96a0fbae1ee805658e9d9f9c78ff4381bc5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756103095770-75266597996116-9033795111-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 06:24:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756103095770-75266597996116-9033795111-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75266597996116","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-25T06:24:55.770Z"},"publishedAt":"2025-08-25T06:24:55.000Z","csn":"5103159758"}
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 16:12:11
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:36]  Provided signature: sha256=9009f00a7279b100ab536d1f4d5d598d9677e487f3e4f5a4fb416ac135556df6
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:37]  Calculated signature: sha256=9009f00a7279b100ab536d1f4d5d598d9677e487f3e4f5a4fb416ac135556df6
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 66ee1da69631aa06\n    [X-B3-Traceid] => 68ac8b597627083434cf4372056849f3\n    [B3] => 68ac8b597627083434cf4372056849f3-66ee1da69631aa06-1\n    [Traceparent] => 00-68ac8b597627083434cf4372056849f3-66ee1da69631aa06-01\n    [X-Amzn-Trace-Id] => Root=1-68ac8b59-7627083434cf4372056849f3;Parent=66ee1da69631aa06;Sampled=1\n    [X-Adsk-Signature] => sha256=9009f00a7279b100ab536d1f4d5d598d9677e487f3e4f5a4fb416ac135556df6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b29a3300-3660-4d5b-90c9-c72147c1fd75\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"b29a3300-3660-4d5b-90c9-c72147c1fd75","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"64811964452952","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-25T15:57:06.000+0000"},"publishedAt":"2025-08-25T16:12:09.000Z","csn":"5103159758"}
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 16:12:11
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:36]  Provided signature: sha256=4be842e13d545246f55ed7cd1c7d2e571c3d317ad88ab3bbb8496a191553d19c
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:37]  Calculated signature: sha256=9009f00a7279b100ab536d1f4d5d598d9677e487f3e4f5a4fb416ac135556df6
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1e8bac5695b0a85a\n    [X-B3-Traceid] => 68ac8b597627083434cf4372056849f3\n    [B3] => 68ac8b597627083434cf4372056849f3-1e8bac5695b0a85a-1\n    [Traceparent] => 00-68ac8b597627083434cf4372056849f3-1e8bac5695b0a85a-01\n    [X-Amzn-Trace-Id] => Root=1-68ac8b59-7627083434cf4372056849f3;Parent=1e8bac5695b0a85a;Sampled=1\n    [X-Adsk-Signature] => sha256=4be842e13d545246f55ed7cd1c7d2e571c3d317ad88ab3bbb8496a191553d19c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b29a3300-3660-4d5b-90c9-c72147c1fd75\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 16:12:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"b29a3300-3660-4d5b-90c9-c72147c1fd75","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"64811964452952","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-25T15:57:06.000+0000"},"publishedAt":"2025-08-25T16:12:09.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:01:06
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:36]  Provided signature: sha256=cf9204e562b5e16cc4cb6b2e26fdbe34c3c89d2cd609c3ae79c8a1d2059ccf3c
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:37]  Calculated signature: sha256=cde3327c57a3304b6067531b5a7a52e1a6eadadddf6ea7fb65fb8c722328a0e4
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 03181eeabb0f439e\n    [X-B3-Traceid] => 68aceb304e104ba24b5a50e91f75008b\n    [B3] => 68aceb304e104ba24b5a50e91f75008b-03181eeabb0f439e-1\n    [Traceparent] => 00-68aceb304e104ba24b5a50e91f75008b-03181eeabb0f439e-01\n    [X-Amzn-Trace-Id] => Root=1-68aceb30-4e104ba24b5a50e91f75008b;Parent=03181eeabb0f439e;Sampled=1\n    [X-Adsk-Signature] => sha256=cf9204e562b5e16cc4cb6b2e26fdbe34c3c89d2cd609c3ae79c8a1d2059ccf3c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756162864454-572-35848309\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756162864454-572-35848309","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-35848309","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:01:04.454Z"},"publishedAt":"2025-08-25T23:01:04.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:01:06
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:36]  Provided signature: sha256=cde3327c57a3304b6067531b5a7a52e1a6eadadddf6ea7fb65fb8c722328a0e4
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:37]  Calculated signature: sha256=cde3327c57a3304b6067531b5a7a52e1a6eadadddf6ea7fb65fb8c722328a0e4
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1a3c8423b8be1124\n    [X-B3-Traceid] => 68aceb304e104ba24b5a50e91f75008b\n    [B3] => 68aceb304e104ba24b5a50e91f75008b-1a3c8423b8be1124-1\n    [Traceparent] => 00-68aceb304e104ba24b5a50e91f75008b-1a3c8423b8be1124-01\n    [X-Amzn-Trace-Id] => Root=1-68aceb30-4e104ba24b5a50e91f75008b;Parent=1a3c8423b8be1124;Sampled=1\n    [X-Adsk-Signature] => sha256=cde3327c57a3304b6067531b5a7a52e1a6eadadddf6ea7fb65fb8c722328a0e4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756162864454-572-35848309\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:01:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756162864454-572-35848309","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-35848309","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:01:04.454Z"},"publishedAt":"2025-08-25T23:01:04.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:01:34
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:36]  Provided signature: sha256=a39dcba5eb5ae4e49cb2d6d59eb0411a38f07e997e99d4eaa0c018ae5ab93ad5
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:37]  Calculated signature: sha256=03901efd5a8a73dad2d2fed7fa705d7ac43c4e5d7a9ef6d13cc2e0ebfa6c731f
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8dd706abb6487f48\n    [X-B3-Traceid] => 68aceb4c4985b6f37793c73c70c04812\n    [B3] => 68aceb4c4985b6f37793c73c70c04812-8dd706abb6487f48-1\n    [Traceparent] => 00-68aceb4c4985b6f37793c73c70c04812-8dd706abb6487f48-01\n    [X-Amzn-Trace-Id] => Root=1-68aceb4c-4985b6f37793c73c70c04812;Parent=8dd706abb6487f48;Sampled=1\n    [X-Adsk-Signature] => sha256=a39dcba5eb5ae4e49cb2d6d59eb0411a38f07e997e99d4eaa0c018ae5ab93ad5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756162892516-573-21728743\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756162892516-573-21728743","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"573-21728743","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:01:32.516Z"},"publishedAt":"2025-08-25T23:01:32.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:01:34
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:36]  Provided signature: sha256=03901efd5a8a73dad2d2fed7fa705d7ac43c4e5d7a9ef6d13cc2e0ebfa6c731f
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:37]  Calculated signature: sha256=03901efd5a8a73dad2d2fed7fa705d7ac43c4e5d7a9ef6d13cc2e0ebfa6c731f
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => df07152de3b4d33e\n    [X-B3-Traceid] => 68aceb4c4985b6f37793c73c70c04812\n    [B3] => 68aceb4c4985b6f37793c73c70c04812-df07152de3b4d33e-1\n    [Traceparent] => 00-68aceb4c4985b6f37793c73c70c04812-df07152de3b4d33e-01\n    [X-Amzn-Trace-Id] => Root=1-68aceb4c-4985b6f37793c73c70c04812;Parent=df07152de3b4d33e;Sampled=1\n    [X-Adsk-Signature] => sha256=03901efd5a8a73dad2d2fed7fa705d7ac43c4e5d7a9ef6d13cc2e0ebfa6c731f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756162892516-573-21728743\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:01:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756162892516-573-21728743","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"573-21728743","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:01:32.516Z"},"publishedAt":"2025-08-25T23:01:32.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:03:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:03:33
[webhook] [2025-08-25 23:03:33] [adwsapi_v2.php:36]  Provided signature: sha256=18712a876d2190a461178dca53c3744d88115e8287f2cb8e08ae6869f1db42a8
[webhook] [2025-08-25 23:03:33] [adwsapi_v2.php:37]  Calculated signature: sha256=d5dab668a5a7f0d145486b00c6ea6d6404cd2947507cefa83447858f3c57c7d4
[webhook] [2025-08-25 23:03:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e54d3ca0b20dcb13\n    [X-B3-Traceid] => 68acebc378e4b2571a01d46c38e4fbd3\n    [B3] => 68acebc378e4b2571a01d46c38e4fbd3-e54d3ca0b20dcb13-1\n    [Traceparent] => 00-68acebc378e4b2571a01d46c38e4fbd3-e54d3ca0b20dcb13-01\n    [X-Amzn-Trace-Id] => Root=1-68acebc3-78e4b2571a01d46c38e4fbd3;Parent=e54d3ca0b20dcb13;Sampled=1\n    [X-Adsk-Signature] => sha256=18712a876d2190a461178dca53c3744d88115e8287f2cb8e08ae6869f1db42a8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756163011884-574-14338009\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:03:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756163011884-574-14338009","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-14338009","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:03:31.884Z"},"publishedAt":"2025-08-25T23:03:31.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:03:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:03:34
[webhook] [2025-08-25 23:03:34] [adwsapi_v2.php:36]  Provided signature: sha256=d5dab668a5a7f0d145486b00c6ea6d6404cd2947507cefa83447858f3c57c7d4
[webhook] [2025-08-25 23:03:34] [adwsapi_v2.php:37]  Calculated signature: sha256=d5dab668a5a7f0d145486b00c6ea6d6404cd2947507cefa83447858f3c57c7d4
[webhook] [2025-08-25 23:03:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0f04e8cb24579082\n    [X-B3-Traceid] => 68acebc378e4b2571a01d46c38e4fbd3\n    [B3] => 68acebc378e4b2571a01d46c38e4fbd3-0f04e8cb24579082-1\n    [Traceparent] => 00-68acebc378e4b2571a01d46c38e4fbd3-0f04e8cb24579082-01\n    [X-Amzn-Trace-Id] => Root=1-68acebc3-78e4b2571a01d46c38e4fbd3;Parent=0f04e8cb24579082;Sampled=1\n    [X-Adsk-Signature] => sha256=d5dab668a5a7f0d145486b00c6ea6d6404cd2947507cefa83447858f3c57c7d4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756163011884-574-14338009\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:03:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756163011884-574-14338009","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-14338009","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:03:31.884Z"},"publishedAt":"2025-08-25T23:03:31.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:03:41
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:36]  Provided signature: sha256=07bd9a0532373cb6d310c7cd6afb4e5db56339bba7a9497756bcb045b615aa52
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:37]  Calculated signature: sha256=07bd9a0532373cb6d310c7cd6afb4e5db56339bba7a9497756bcb045b615aa52
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 59b76d7da0cd64cc\n    [X-B3-Traceid] => 68acebcb3a8b02e35a614486777e0eec\n    [B3] => 68acebcb3a8b02e35a614486777e0eec-59b76d7da0cd64cc-1\n    [Traceparent] => 00-68acebcb3a8b02e35a614486777e0eec-59b76d7da0cd64cc-01\n    [X-Amzn-Trace-Id] => Root=1-68acebcb-3a8b02e35a614486777e0eec;Parent=59b76d7da0cd64cc;Sampled=1\n    [X-Adsk-Signature] => sha256=07bd9a0532373cb6d310c7cd6afb4e5db56339bba7a9497756bcb045b615aa52\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756163019147-561-69965787\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756163019147-561-69965787","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"561-69965787","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:03:39.147Z"},"publishedAt":"2025-08-25T23:03:39.000Z","csn":"5103159758"}
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-25 23:03:41
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:36]  Provided signature: sha256=f68d47995a797db975c83205789b5855c9f3745680de6ebdf699171eb5f9c9de
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:37]  Calculated signature: sha256=07bd9a0532373cb6d310c7cd6afb4e5db56339bba7a9497756bcb045b615aa52
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9ef9dd3901186dee\n    [X-B3-Traceid] => 68acebcb3a8b02e35a614486777e0eec\n    [B3] => 68acebcb3a8b02e35a614486777e0eec-9ef9dd3901186dee-1\n    [Traceparent] => 00-68acebcb3a8b02e35a614486777e0eec-9ef9dd3901186dee-01\n    [X-Amzn-Trace-Id] => Root=1-68acebcb-3a8b02e35a614486777e0eec;Parent=9ef9dd3901186dee;Sampled=1\n    [X-Adsk-Signature] => sha256=f68d47995a797db975c83205789b5855c9f3745680de6ebdf699171eb5f9c9de\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756163019147-561-69965787\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-25 23:03:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756163019147-561-69965787","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"561-69965787","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-25T23:03:39.147Z"},"publishedAt":"2025-08-25T23:03:39.000Z","csn":"5103159758"}
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 00:01:07
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:36]  Provided signature: sha256=2402199ce33f71e0def2787bd5451980155308266f037a15ba396fde6ffaa190
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:37]  Calculated signature: sha256=676284f6a11c68205db982f8aa532a756f61737c8fac623af74889d1a797dd6e
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 53f9d62650ac99b4\n    [X-B3-Traceid] => 68acf94001ce321d3d50a22b73327ada\n    [B3] => 68acf94001ce321d3d50a22b73327ada-53f9d62650ac99b4-1\n    [Traceparent] => 00-68acf94001ce321d3d50a22b73327ada-53f9d62650ac99b4-01\n    [X-Amzn-Trace-Id] => Root=1-68acf940-01ce321d3d50a22b73327ada;Parent=53f9d62650ac99b4;Sampled=1\n    [X-Adsk-Signature] => sha256=2402199ce33f71e0def2787bd5451980155308266f037a15ba396fde6ffaa190\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 64a13f17-d9c5-4e37-8bca-89824c98fdb1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"64a13f17-d9c5-4e37-8bca-89824c98fdb1","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-26T00:01:04Z"},"publishedAt":"2025-08-26T00:01:05.000Z","country":"GB"}
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 00:01:07
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:36]  Provided signature: sha256=676284f6a11c68205db982f8aa532a756f61737c8fac623af74889d1a797dd6e
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:37]  Calculated signature: sha256=676284f6a11c68205db982f8aa532a756f61737c8fac623af74889d1a797dd6e
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4005b72b7e9d7eb4\n    [X-B3-Traceid] => 68acf94001ce321d3d50a22b73327ada\n    [B3] => 68acf94001ce321d3d50a22b73327ada-4005b72b7e9d7eb4-1\n    [Traceparent] => 00-68acf94001ce321d3d50a22b73327ada-4005b72b7e9d7eb4-01\n    [X-Amzn-Trace-Id] => Root=1-68acf940-01ce321d3d50a22b73327ada;Parent=4005b72b7e9d7eb4;Sampled=1\n    [X-Adsk-Signature] => sha256=676284f6a11c68205db982f8aa532a756f61737c8fac623af74889d1a797dd6e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 64a13f17-d9c5-4e37-8bca-89824c98fdb1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 00:01:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"64a13f17-d9c5-4e37-8bca-89824c98fdb1","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-26T00:01:04Z"},"publishedAt":"2025-08-26T00:01:05.000Z","country":"GB"}
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 00:06:06
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:36]  Provided signature: sha256=2d800f977adce087cae42d6046af6a3074df1aa90905ecbaf8dee136cd3e7ef6
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:37]  Calculated signature: sha256=2d800f977adce087cae42d6046af6a3074df1aa90905ecbaf8dee136cd3e7ef6
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 203e38a440858daa\n    [X-B3-Traceid] => 68acfa6b55f5134540bdcca8640fbf8c\n    [B3] => 68acfa6b55f5134540bdcca8640fbf8c-203e38a440858daa-1\n    [Traceparent] => 00-68acfa6b55f5134540bdcca8640fbf8c-203e38a440858daa-01\n    [X-Amzn-Trace-Id] => Root=1-68acfa6b-55f5134540bdcca8640fbf8c;Parent=203e38a440858daa;Sampled=1\n    [X-Adsk-Signature] => sha256=2d800f977adce087cae42d6046af6a3074df1aa90905ecbaf8dee136cd3e7ef6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 27aa3ac0-2e30-4c15-a748-0ad4502289d3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"27aa3ac0-2e30-4c15-a748-0ad4502289d3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74700029815254","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-25T23:41:01.000+0000"},"publishedAt":"2025-08-26T00:06:03.000Z","csn":"5103159758"}
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 00:06:06
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:36]  Provided signature: sha256=4cc0fe04c888ec0b7abbee091773998d470b08d1445079bcef6d2ecf3976e297
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:37]  Calculated signature: sha256=2d800f977adce087cae42d6046af6a3074df1aa90905ecbaf8dee136cd3e7ef6
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6ca31597be930fb6\n    [X-B3-Traceid] => 68acfa6b55f5134540bdcca8640fbf8c\n    [B3] => 68acfa6b55f5134540bdcca8640fbf8c-6ca31597be930fb6-1\n    [Traceparent] => 00-68acfa6b55f5134540bdcca8640fbf8c-6ca31597be930fb6-01\n    [X-Amzn-Trace-Id] => Root=1-68acfa6b-55f5134540bdcca8640fbf8c;Parent=6ca31597be930fb6;Sampled=1\n    [X-Adsk-Signature] => sha256=4cc0fe04c888ec0b7abbee091773998d470b08d1445079bcef6d2ecf3976e297\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 27aa3ac0-2e30-4c15-a748-0ad4502289d3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 00:06:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"27aa3ac0-2e30-4c15-a748-0ad4502289d3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74700029815254","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-25T23:41:01.000+0000"},"publishedAt":"2025-08-26T00:06:03.000Z","csn":"5103159758"}
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 07:11:05
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:36]  Provided signature: sha256=b11c86e0dcfdef2e2a252e83ac87d19ce9db6868551cad5ade0b6be6f03fca23
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:37]  Calculated signature: sha256=b11c86e0dcfdef2e2a252e83ac87d19ce9db6868551cad5ade0b6be6f03fca23
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d4e9102428b6424b\n    [X-B3-Traceid] => 68ad5e071c07247e3ca7ae497273e24b\n    [B3] => 68ad5e071c07247e3ca7ae497273e24b-d4e9102428b6424b-1\n    [Traceparent] => 00-68ad5e071c07247e3ca7ae497273e24b-d4e9102428b6424b-01\n    [X-Amzn-Trace-Id] => Root=1-68ad5e07-1c07247e3ca7ae497273e24b;Parent=d4e9102428b6424b;Sampled=1\n    [X-Adsk-Signature] => sha256=b11c86e0dcfdef2e2a252e83ac87d19ce9db6868551cad5ade0b6be6f03fca23\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9ce724be-4c12-45e5-97f4-bb9d5a4f98c6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"9ce724be-4c12-45e5-97f4-bb9d5a4f98c6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69107177208594","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-26T06:41:01.000+0000"},"publishedAt":"2025-08-26T07:11:03.000Z","csn":"5103159758"}
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 07:11:05
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:36]  Provided signature: sha256=3f5edb1bfbd6ca3fac3969baede9a3dad413f95842fd318eb1515dab37eda376
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:37]  Calculated signature: sha256=b11c86e0dcfdef2e2a252e83ac87d19ce9db6868551cad5ade0b6be6f03fca23
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2eeb9c08700dc3dc\n    [X-B3-Traceid] => 68ad5e071c07247e3ca7ae497273e24b\n    [B3] => 68ad5e071c07247e3ca7ae497273e24b-2eeb9c08700dc3dc-1\n    [Traceparent] => 00-68ad5e071c07247e3ca7ae497273e24b-2eeb9c08700dc3dc-01\n    [X-Amzn-Trace-Id] => Root=1-68ad5e07-1c07247e3ca7ae497273e24b;Parent=2eeb9c08700dc3dc;Sampled=1\n    [X-Adsk-Signature] => sha256=3f5edb1bfbd6ca3fac3969baede9a3dad413f95842fd318eb1515dab37eda376\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9ce724be-4c12-45e5-97f4-bb9d5a4f98c6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 07:11:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"9ce724be-4c12-45e5-97f4-bb9d5a4f98c6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69107177208594","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-26T06:41:01.000+0000"},"publishedAt":"2025-08-26T07:11:03.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:07:22
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=4cc83d2afa4f8ed2fc4dc431de74dc0fab82e51998dfcec77768f75e60523f1b
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=7df51197543149732d75970f421f620bb7ff4346327d5fd3bf029fa07cbfdb77
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 75de76961aceca9c\n    [X-B3-Traceid] => 68ad6b38e2fcfca72d3884cd0112fef1\n    [B3] => 68ad6b38e2fcfca72d3884cd0112fef1-75de76961aceca9c-1\n    [Traceparent] => 00-68ad6b38e2fcfca72d3884cd0112fef1-75de76961aceca9c-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6b38-e2fcfca72d3884cd0112fef1;Parent=75de76961aceca9c;Sampled=1\n    [X-Adsk-Signature] => sha256=4cc83d2afa4f8ed2fc4dc431de74dc0fab82e51998dfcec77768f75e60523f1b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a19a5a1b-52d5-4f3d-96f1-8c0b29776f52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"a19a5a1b-52d5-4f3d-96f1-8c0b29776f52","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032677","transactionId":"a42b41ce-c374-589b-be9b-20e9ae07a967","quoteStatus":"Draft","message":"Quote# Q-1032677 status changed to Draft.","modifiedAt":"2025-08-26T08:07:19.944Z"},"publishedAt":"2025-08-26T08:07:20.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:07:22
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=7df51197543149732d75970f421f620bb7ff4346327d5fd3bf029fa07cbfdb77
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=7df51197543149732d75970f421f620bb7ff4346327d5fd3bf029fa07cbfdb77
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 05cb2d31bf6272a2\n    [X-B3-Traceid] => 68ad6b38e2fcfca72d3884cd0112fef1\n    [B3] => 68ad6b38e2fcfca72d3884cd0112fef1-05cb2d31bf6272a2-1\n    [Traceparent] => 00-68ad6b38e2fcfca72d3884cd0112fef1-05cb2d31bf6272a2-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6b38-e2fcfca72d3884cd0112fef1;Parent=05cb2d31bf6272a2;Sampled=1\n    [X-Adsk-Signature] => sha256=7df51197543149732d75970f421f620bb7ff4346327d5fd3bf029fa07cbfdb77\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a19a5a1b-52d5-4f3d-96f1-8c0b29776f52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"a19a5a1b-52d5-4f3d-96f1-8c0b29776f52","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032677","transactionId":"a42b41ce-c374-589b-be9b-20e9ae07a967","quoteStatus":"Draft","message":"Quote# Q-1032677 status changed to Draft.","modifiedAt":"2025-08-26T08:07:19.944Z"},"publishedAt":"2025-08-26T08:07:20.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:08:31
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:36]  Provided signature: sha256=6533f0c8bf7ef18fa7b0f9de9ec407eeaab02fe50c488422af70c8165c195949
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:37]  Calculated signature: sha256=69e276ef37bf2080646b74b993c5e43a30092b28b237ffbceb7bd9c38986a3d0
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f1b23a16471aa625\n    [X-B3-Traceid] => 68ad6b7d5d22f96664b9910f44e247b6\n    [B3] => 68ad6b7d5d22f96664b9910f44e247b6-f1b23a16471aa625-1\n    [Traceparent] => 00-68ad6b7d5d22f96664b9910f44e247b6-f1b23a16471aa625-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6b7d-5d22f96664b9910f44e247b6;Parent=f1b23a16471aa625;Sampled=1\n    [X-Adsk-Signature] => sha256=6533f0c8bf7ef18fa7b0f9de9ec407eeaab02fe50c488422af70c8165c195949\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b0842017-91b2-49b8-af14-47b94fa58741\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"b0842017-91b2-49b8-af14-47b94fa58741","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-961925","transactionId":"dd1ff252-5881-529d-9dc6-93536e239200","quoteStatus":"Order Submitted","message":"Quote# Q-961925 status changed to Order Submitted.","modifiedAt":"2025-08-26T08:08:28.936Z"},"publishedAt":"2025-08-26T08:08:29.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:08:31
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:36]  Provided signature: sha256=69e276ef37bf2080646b74b993c5e43a30092b28b237ffbceb7bd9c38986a3d0
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:37]  Calculated signature: sha256=69e276ef37bf2080646b74b993c5e43a30092b28b237ffbceb7bd9c38986a3d0
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3f783e9758d98eeb\n    [X-B3-Traceid] => 68ad6b7d5d22f96664b9910f44e247b6\n    [B3] => 68ad6b7d5d22f96664b9910f44e247b6-3f783e9758d98eeb-1\n    [Traceparent] => 00-68ad6b7d5d22f96664b9910f44e247b6-3f783e9758d98eeb-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6b7d-5d22f96664b9910f44e247b6;Parent=3f783e9758d98eeb;Sampled=1\n    [X-Adsk-Signature] => sha256=69e276ef37bf2080646b74b993c5e43a30092b28b237ffbceb7bd9c38986a3d0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b0842017-91b2-49b8-af14-47b94fa58741\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:08:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"b0842017-91b2-49b8-af14-47b94fa58741","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-961925","transactionId":"dd1ff252-5881-529d-9dc6-93536e239200","quoteStatus":"Order Submitted","message":"Quote# Q-961925 status changed to Order Submitted.","modifiedAt":"2025-08-26T08:08:28.936Z"},"publishedAt":"2025-08-26T08:08:29.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:08:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:08:33
[webhook] [2025-08-26 08:08:33] [adwsapi_v2.php:36]  Provided signature: sha256=6c5ab19193395b7a11de793ea8a0551aa8845e59b0e900357eb06ad30dc9fe04
[webhook] [2025-08-26 08:08:33] [adwsapi_v2.php:37]  Calculated signature: sha256=6de1381219b832f54e01a15c2874031c7a66f6253eb85b50bf3f60a01b067a81
[webhook] [2025-08-26 08:08:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2a477a60c144ad2b\n    [X-B3-Traceid] => 68ad6b7fe21c51088c0896218d27ddc0\n    [B3] => 68ad6b7fe21c51088c0896218d27ddc0-2a477a60c144ad2b-1\n    [Traceparent] => 00-68ad6b7fe21c51088c0896218d27ddc0-2a477a60c144ad2b-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6b7f-e21c51088c0896218d27ddc0;Parent=2a477a60c144ad2b;Sampled=1\n    [X-Adsk-Signature] => sha256=6c5ab19193395b7a11de793ea8a0551aa8845e59b0e900357eb06ad30dc9fe04\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8944bb3f-5b8e-4b0b-b362-d15ad98f6230\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:08:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"8944bb3f-5b8e-4b0b-b362-d15ad98f6230","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-961925","transactionId":"dd1ff252-5881-529d-9dc6-93536e239200","quoteStatus":"Ordered","message":"Quote# Q-961925 status changed to Ordered.","modifiedAt":"2025-08-26T08:08:31.456Z"},"publishedAt":"2025-08-26T08:08:32.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:08:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:08:34
[webhook] [2025-08-26 08:08:34] [adwsapi_v2.php:36]  Provided signature: sha256=6de1381219b832f54e01a15c2874031c7a66f6253eb85b50bf3f60a01b067a81
[webhook] [2025-08-26 08:08:34] [adwsapi_v2.php:37]  Calculated signature: sha256=6de1381219b832f54e01a15c2874031c7a66f6253eb85b50bf3f60a01b067a81
[webhook] [2025-08-26 08:08:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a0517dc012ed5c22\n    [X-B3-Traceid] => 68ad6b7fe21c51088c0896218d27ddc0\n    [B3] => 68ad6b7fe21c51088c0896218d27ddc0-a0517dc012ed5c22-1\n    [Traceparent] => 00-68ad6b7fe21c51088c0896218d27ddc0-a0517dc012ed5c22-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6b7f-e21c51088c0896218d27ddc0;Parent=a0517dc012ed5c22;Sampled=1\n    [X-Adsk-Signature] => sha256=6de1381219b832f54e01a15c2874031c7a66f6253eb85b50bf3f60a01b067a81\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8944bb3f-5b8e-4b0b-b362-d15ad98f6230\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:08:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"8944bb3f-5b8e-4b0b-b362-d15ad98f6230","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-961925","transactionId":"dd1ff252-5881-529d-9dc6-93536e239200","quoteStatus":"Ordered","message":"Quote# Q-961925 status changed to Ordered.","modifiedAt":"2025-08-26T08:08:31.456Z"},"publishedAt":"2025-08-26T08:08:32.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:09:22
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:36]  Provided signature: sha256=8561e0771dbd62d842b720020de527e47cf60b32aa61e62ca087e92d1a1a60b7
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:37]  Calculated signature: sha256=a2f15d1894404879ef35969df3595fc2cc70a2166c0e5d8ecf582486545820e4
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7c2a0718c24e5c13\n    [X-B3-Traceid] => 68ad6bafae40d5ba2eae8fecdb41331d\n    [B3] => 68ad6bafae40d5ba2eae8fecdb41331d-7c2a0718c24e5c13-1\n    [Traceparent] => 00-68ad6bafae40d5ba2eae8fecdb41331d-7c2a0718c24e5c13-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6baf-ae40d5ba2eae8fecdb41331d;Parent=7c2a0718c24e5c13;Sampled=1\n    [X-Adsk-Signature] => sha256=8561e0771dbd62d842b720020de527e47cf60b32aa61e62ca087e92d1a1a60b7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e1e436b3-40c9-41d0-84f8-56fb06da0bfc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"e1e436b3-40c9-41d0-84f8-56fb06da0bfc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032677","transactionId":"a42b41ce-c374-589b-be9b-20e9ae07a967","quoteStatus":"Quoted","message":"Quote# Q-1032677 status changed to Quoted.","modifiedAt":"2025-08-26T08:09:19.172Z"},"publishedAt":"2025-08-26T08:09:19.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:09:22
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:36]  Provided signature: sha256=a2f15d1894404879ef35969df3595fc2cc70a2166c0e5d8ecf582486545820e4
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:37]  Calculated signature: sha256=a2f15d1894404879ef35969df3595fc2cc70a2166c0e5d8ecf582486545820e4
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b45785360b410ff3\n    [X-B3-Traceid] => 68ad6bafae40d5ba2eae8fecdb41331d\n    [B3] => 68ad6bafae40d5ba2eae8fecdb41331d-b45785360b410ff3-1\n    [Traceparent] => 00-68ad6bafae40d5ba2eae8fecdb41331d-b45785360b410ff3-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6baf-ae40d5ba2eae8fecdb41331d;Parent=b45785360b410ff3;Sampled=1\n    [X-Adsk-Signature] => sha256=a2f15d1894404879ef35969df3595fc2cc70a2166c0e5d8ecf582486545820e4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e1e436b3-40c9-41d0-84f8-56fb06da0bfc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:09:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"e1e436b3-40c9-41d0-84f8-56fb06da0bfc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032677","transactionId":"a42b41ce-c374-589b-be9b-20e9ae07a967","quoteStatus":"Quoted","message":"Quote# Q-1032677 status changed to Quoted.","modifiedAt":"2025-08-26T08:09:19.172Z"},"publishedAt":"2025-08-26T08:09:19.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:16:16
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:36]  Provided signature: sha256=846fef9317c8920d1b57cf4c4018059a10eff4730d2c391086f02da7b30cc92f
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:37]  Calculated signature: sha256=ca650a4703fc6fd47e28a61000aa8ffb7c7e0044099596a96e3aabd55f524fc3
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 686ac4556bdec65c\n    [X-B3-Traceid] => 68ad6d4ead612ba0eb39fc8a81f3fa7f\n    [B3] => 68ad6d4ead612ba0eb39fc8a81f3fa7f-686ac4556bdec65c-1\n    [Traceparent] => 00-68ad6d4ead612ba0eb39fc8a81f3fa7f-686ac4556bdec65c-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6d4e-ad612ba0eb39fc8a81f3fa7f;Parent=686ac4556bdec65c;Sampled=1\n    [X-Adsk-Signature] => sha256=846fef9317c8920d1b57cf4c4018059a10eff4730d2c391086f02da7b30cc92f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7392d28b-1817-4626-8bc7-f9d96c9b4c0a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"7392d28b-1817-4626-8bc7-f9d96c9b4c0a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032722","transactionId":"db063b03-ca44-5119-9d6a-5a24a53ab1c5","quoteStatus":"Draft","message":"Quote# Q-1032722 status changed to Draft.","modifiedAt":"2025-08-26T08:16:13.839Z"},"publishedAt":"2025-08-26T08:16:14.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:16:16
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:36]  Provided signature: sha256=ca650a4703fc6fd47e28a61000aa8ffb7c7e0044099596a96e3aabd55f524fc3
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:37]  Calculated signature: sha256=ca650a4703fc6fd47e28a61000aa8ffb7c7e0044099596a96e3aabd55f524fc3
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a6af46c4c12072d5\n    [X-B3-Traceid] => 68ad6d4ead612ba0eb39fc8a81f3fa7f\n    [B3] => 68ad6d4ead612ba0eb39fc8a81f3fa7f-a6af46c4c12072d5-1\n    [Traceparent] => 00-68ad6d4ead612ba0eb39fc8a81f3fa7f-a6af46c4c12072d5-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6d4e-ad612ba0eb39fc8a81f3fa7f;Parent=a6af46c4c12072d5;Sampled=1\n    [X-Adsk-Signature] => sha256=ca650a4703fc6fd47e28a61000aa8ffb7c7e0044099596a96e3aabd55f524fc3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7392d28b-1817-4626-8bc7-f9d96c9b4c0a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:16:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"7392d28b-1817-4626-8bc7-f9d96c9b4c0a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032722","transactionId":"db063b03-ca44-5119-9d6a-5a24a53ab1c5","quoteStatus":"Draft","message":"Quote# Q-1032722 status changed to Draft.","modifiedAt":"2025-08-26T08:16:13.839Z"},"publishedAt":"2025-08-26T08:16:14.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:17:12
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:36]  Provided signature: sha256=04307bf5797ea27c2982451b12cde621ff12ad0f9fcda59e3f992906e0ba7961
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:37]  Calculated signature: sha256=04307bf5797ea27c2982451b12cde621ff12ad0f9fcda59e3f992906e0ba7961
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ed4703ecfbd4ea8c\n    [X-B3-Traceid] => 68ad6d8564a383d04faee84a6801ce25\n    [B3] => 68ad6d8564a383d04faee84a6801ce25-ed4703ecfbd4ea8c-1\n    [Traceparent] => 00-68ad6d8564a383d04faee84a6801ce25-ed4703ecfbd4ea8c-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6d85-64a383d04faee84a6801ce25;Parent=ed4703ecfbd4ea8c;Sampled=1\n    [X-Adsk-Signature] => sha256=04307bf5797ea27c2982451b12cde621ff12ad0f9fcda59e3f992906e0ba7961\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 239fd3b5-0302-40d2-a549-822e312cac25\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"239fd3b5-0302-40d2-a549-822e312cac25","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032722","transactionId":"db063b03-ca44-5119-9d6a-5a24a53ab1c5","quoteStatus":"Quoted","message":"Quote# Q-1032722 status changed to Quoted.","modifiedAt":"2025-08-26T08:17:09.630Z"},"publishedAt":"2025-08-26T08:17:09.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:17:12
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:36]  Provided signature: sha256=9b5e7552b93e435718f731518efbf5392d2e59b667133fd0524aa47ee60fcff0
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:37]  Calculated signature: sha256=04307bf5797ea27c2982451b12cde621ff12ad0f9fcda59e3f992906e0ba7961
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8877a6f3848ca97d\n    [X-B3-Traceid] => 68ad6d8564a383d04faee84a6801ce25\n    [B3] => 68ad6d8564a383d04faee84a6801ce25-8877a6f3848ca97d-1\n    [Traceparent] => 00-68ad6d8564a383d04faee84a6801ce25-8877a6f3848ca97d-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6d85-64a383d04faee84a6801ce25;Parent=8877a6f3848ca97d;Sampled=1\n    [X-Adsk-Signature] => sha256=9b5e7552b93e435718f731518efbf5392d2e59b667133fd0524aa47ee60fcff0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 239fd3b5-0302-40d2-a549-822e312cac25\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:17:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"239fd3b5-0302-40d2-a549-822e312cac25","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032722","transactionId":"db063b03-ca44-5119-9d6a-5a24a53ab1c5","quoteStatus":"Quoted","message":"Quote# Q-1032722 status changed to Quoted.","modifiedAt":"2025-08-26T08:17:09.630Z"},"publishedAt":"2025-08-26T08:17:09.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:19:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:19:41
[webhook] [2025-08-26 08:19:41] [adwsapi_v2.php:36]  Provided signature: sha256=3923abeabd639e569bf525aef9e9e15e5862cdb329fa1a023190bac204fefc71
[webhook] [2025-08-26 08:19:41] [adwsapi_v2.php:37]  Calculated signature: sha256=3923abeabd639e569bf525aef9e9e15e5862cdb329fa1a023190bac204fefc71
[webhook] [2025-08-26 08:19:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b1f0822369429171\n    [X-B3-Traceid] => 68ad6e1b3e757654fa55e5be483641ff\n    [B3] => 68ad6e1b3e757654fa55e5be483641ff-b1f0822369429171-1\n    [Traceparent] => 00-68ad6e1b3e757654fa55e5be483641ff-b1f0822369429171-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6e1b-3e757654fa55e5be483641ff;Parent=b1f0822369429171;Sampled=1\n    [X-Adsk-Signature] => sha256=3923abeabd639e569bf525aef9e9e15e5862cdb329fa1a023190bac204fefc71\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6e63a643-46c4-4cbd-a32f-f5fd629cc9c6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:19:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"6e63a643-46c4-4cbd-a32f-f5fd629cc9c6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Draft","message":"Quote# Q-1032741 status changed to Draft.","modifiedAt":"2025-08-26T08:19:39.325Z"},"publishedAt":"2025-08-26T08:19:39.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:19:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:19:42
[webhook] [2025-08-26 08:19:42] [adwsapi_v2.php:36]  Provided signature: sha256=02810a09acb7c3b6f47cc9429e16a1066be424dff978eb2347be706fa3706ad1
[webhook] [2025-08-26 08:19:42] [adwsapi_v2.php:37]  Calculated signature: sha256=3923abeabd639e569bf525aef9e9e15e5862cdb329fa1a023190bac204fefc71
[webhook] [2025-08-26 08:19:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6dc9569b4a4773bb\n    [X-B3-Traceid] => 68ad6e1b3e757654fa55e5be483641ff\n    [B3] => 68ad6e1b3e757654fa55e5be483641ff-6dc9569b4a4773bb-1\n    [Traceparent] => 00-68ad6e1b3e757654fa55e5be483641ff-6dc9569b4a4773bb-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6e1b-3e757654fa55e5be483641ff;Parent=6dc9569b4a4773bb;Sampled=1\n    [X-Adsk-Signature] => sha256=02810a09acb7c3b6f47cc9429e16a1066be424dff978eb2347be706fa3706ad1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6e63a643-46c4-4cbd-a32f-f5fd629cc9c6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:19:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"6e63a643-46c4-4cbd-a32f-f5fd629cc9c6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Draft","message":"Quote# Q-1032741 status changed to Draft.","modifiedAt":"2025-08-26T08:19:39.325Z"},"publishedAt":"2025-08-26T08:19:39.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:23:07
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:36]  Provided signature: sha256=257919a99941b2c319df5e674e04b166c42d50e3c18faba09e7044b42d097e1b
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:37]  Calculated signature: sha256=7c7974aa7d30978fa38bb53a309c292aa783c574ae155d791f151a90a4a9b3fe
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4f1002fdacb63bcd\n    [X-B3-Traceid] => 68ad6ee8a29e67516a1cebff9448f2bd\n    [B3] => 68ad6ee8a29e67516a1cebff9448f2bd-4f1002fdacb63bcd-1\n    [Traceparent] => 00-68ad6ee8a29e67516a1cebff9448f2bd-4f1002fdacb63bcd-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6ee8-a29e67516a1cebff9448f2bd;Parent=4f1002fdacb63bcd;Sampled=1\n    [X-Adsk-Signature] => sha256=257919a99941b2c319df5e674e04b166c42d50e3c18faba09e7044b42d097e1b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dee151c5-4d10-40de-812b-2e7b7ff5cece\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"dee151c5-4d10-40de-812b-2e7b7ff5cece","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Quoted","message":"Quote# Q-1032741 status changed to Quoted.","modifiedAt":"2025-08-26T08:23:04.346Z"},"publishedAt":"2025-08-26T08:23:04.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:23:07
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:36]  Provided signature: sha256=7c7974aa7d30978fa38bb53a309c292aa783c574ae155d791f151a90a4a9b3fe
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:37]  Calculated signature: sha256=7c7974aa7d30978fa38bb53a309c292aa783c574ae155d791f151a90a4a9b3fe
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 63eae8adf90fb176\n    [X-B3-Traceid] => 68ad6ee8a29e67516a1cebff9448f2bd\n    [B3] => 68ad6ee8a29e67516a1cebff9448f2bd-63eae8adf90fb176-1\n    [Traceparent] => 00-68ad6ee8a29e67516a1cebff9448f2bd-63eae8adf90fb176-01\n    [X-Amzn-Trace-Id] => Root=1-68ad6ee8-a29e67516a1cebff9448f2bd;Parent=63eae8adf90fb176;Sampled=1\n    [X-Adsk-Signature] => sha256=7c7974aa7d30978fa38bb53a309c292aa783c574ae155d791f151a90a4a9b3fe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dee151c5-4d10-40de-812b-2e7b7ff5cece\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:23:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"dee151c5-4d10-40de-812b-2e7b7ff5cece","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Quoted","message":"Quote# Q-1032741 status changed to Quoted.","modifiedAt":"2025-08-26T08:23:04.346Z"},"publishedAt":"2025-08-26T08:23:04.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:38:41
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:36]  Provided signature: sha256=f307247bd7b0c373e24a4a77b1fb811fa2bf3ebbe5735982a31fd3e17bfb08f4
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:37]  Calculated signature: sha256=ee307f753cc649af0dfdb720c50b0de2a6e9614d677f43a263df6ab2810df64d
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b6c5d691a8e53099\n    [X-B3-Traceid] => 68ad728f2bfa07376cdbfbe11d39455b\n    [B3] => 68ad728f2bfa07376cdbfbe11d39455b-b6c5d691a8e53099-1\n    [Traceparent] => 00-68ad728f2bfa07376cdbfbe11d39455b-b6c5d691a8e53099-01\n    [X-Amzn-Trace-Id] => Root=1-68ad728f-2bfa07376cdbfbe11d39455b;Parent=b6c5d691a8e53099;Sampled=1\n    [X-Adsk-Signature] => sha256=f307247bd7b0c373e24a4a77b1fb811fa2bf3ebbe5735982a31fd3e17bfb08f4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e042ca48-b3d4-4580-9e58-59afb107f7cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"e042ca48-b3d4-4580-9e58-59afb107f7cc","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63275261587012","status":"Active","quantity":1,"endDate":"2026-09-26","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T08:08:37.000+0000"},"publishedAt":"2025-08-26T08:38:39.000Z","csn":"5103159758"}
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 08:38:41
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:36]  Provided signature: sha256=ee307f753cc649af0dfdb720c50b0de2a6e9614d677f43a263df6ab2810df64d
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:37]  Calculated signature: sha256=ee307f753cc649af0dfdb720c50b0de2a6e9614d677f43a263df6ab2810df64d
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => feedbe33a63644aa\n    [X-B3-Traceid] => 68ad728f2bfa07376cdbfbe11d39455b\n    [B3] => 68ad728f2bfa07376cdbfbe11d39455b-feedbe33a63644aa-1\n    [Traceparent] => 00-68ad728f2bfa07376cdbfbe11d39455b-feedbe33a63644aa-01\n    [X-Amzn-Trace-Id] => Root=1-68ad728f-2bfa07376cdbfbe11d39455b;Parent=feedbe33a63644aa;Sampled=1\n    [X-Adsk-Signature] => sha256=ee307f753cc649af0dfdb720c50b0de2a6e9614d677f43a263df6ab2810df64d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e042ca48-b3d4-4580-9e58-59afb107f7cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 08:38:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"e042ca48-b3d4-4580-9e58-59afb107f7cc","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63275261587012","status":"Active","quantity":1,"endDate":"2026-09-26","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T08:08:37.000+0000"},"publishedAt":"2025-08-26T08:38:39.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:09:47
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:36]  Provided signature: sha256=62391dbb3a8ff2bd22f5ed262497ffa14262bd439abedb644f0edc4a6a60ab29
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:37]  Calculated signature: sha256=2540c2231bcfe9b72f5cfadd4c199d1fe5c5dce7629175c32a6c9d690b86304d
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 96052a3ef032b5d5\n    [X-B3-Traceid] => 68ad79d8329db23710237b645cd05432\n    [B3] => 68ad79d8329db23710237b645cd05432-96052a3ef032b5d5-1\n    [Traceparent] => 00-68ad79d8329db23710237b645cd05432-96052a3ef032b5d5-01\n    [X-Amzn-Trace-Id] => Root=1-68ad79d8-329db23710237b645cd05432;Parent=96052a3ef032b5d5;Sampled=1\n    [X-Adsk-Signature] => sha256=62391dbb3a8ff2bd22f5ed262497ffa14262bd439abedb644f0edc4a6a60ab29\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2d689897-0db8-4985-8ae2-9648a00cfaa2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"2d689897-0db8-4985-8ae2-9648a00cfaa2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65331798033507","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-26T08:44:42.000+0000"},"publishedAt":"2025-08-26T09:09:44.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:09:47
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:36]  Provided signature: sha256=2540c2231bcfe9b72f5cfadd4c199d1fe5c5dce7629175c32a6c9d690b86304d
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:37]  Calculated signature: sha256=2540c2231bcfe9b72f5cfadd4c199d1fe5c5dce7629175c32a6c9d690b86304d
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d6f5f620bebb5b2e\n    [X-B3-Traceid] => 68ad79d8329db23710237b645cd05432\n    [B3] => 68ad79d8329db23710237b645cd05432-d6f5f620bebb5b2e-1\n    [Traceparent] => 00-68ad79d8329db23710237b645cd05432-d6f5f620bebb5b2e-01\n    [X-Amzn-Trace-Id] => Root=1-68ad79d8-329db23710237b645cd05432;Parent=d6f5f620bebb5b2e;Sampled=1\n    [X-Adsk-Signature] => sha256=2540c2231bcfe9b72f5cfadd4c199d1fe5c5dce7629175c32a6c9d690b86304d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2d689897-0db8-4985-8ae2-9648a00cfaa2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:09:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"2d689897-0db8-4985-8ae2-9648a00cfaa2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65331798033507","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-26T08:44:42.000+0000"},"publishedAt":"2025-08-26T09:09:44.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:26:02
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:36]  Provided signature: sha256=8141ca83793407e6266de8e47c84e324abfed3a3ba47304e0e6e3f9a4cc81b02
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:37]  Calculated signature: sha256=1dd12d13478ec897e35baf7e4aad5bbe7d45839de6e32f8c067f8e2df7ce0a2d
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9dccc2027db1213d\n    [X-B3-Traceid] => 68ad7da8f25ea780d7a708a78715148d\n    [B3] => 68ad7da8f25ea780d7a708a78715148d-9dccc2027db1213d-1\n    [Traceparent] => 00-68ad7da8f25ea780d7a708a78715148d-9dccc2027db1213d-01\n    [X-Amzn-Trace-Id] => Root=1-68ad7da8-f25ea780d7a708a78715148d;Parent=9dccc2027db1213d;Sampled=1\n    [X-Adsk-Signature] => sha256=8141ca83793407e6266de8e47c84e324abfed3a3ba47304e0e6e3f9a4cc81b02\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3cedaf40-3cd8-4e19-9a05-b921d9d85c34\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"3cedaf40-3cd8-4e19-9a05-b921d9d85c34","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Order Submitted","message":"Quote# Q-1032741 status changed to Order Submitted.","modifiedAt":"2025-08-26T09:25:59.923Z"},"publishedAt":"2025-08-26T09:26:00.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:26:02
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:36]  Provided signature: sha256=1dd12d13478ec897e35baf7e4aad5bbe7d45839de6e32f8c067f8e2df7ce0a2d
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:37]  Calculated signature: sha256=1dd12d13478ec897e35baf7e4aad5bbe7d45839de6e32f8c067f8e2df7ce0a2d
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 66bd87fbbb435301\n    [X-B3-Traceid] => 68ad7da8f25ea780d7a708a78715148d\n    [B3] => 68ad7da8f25ea780d7a708a78715148d-66bd87fbbb435301-1\n    [Traceparent] => 00-68ad7da8f25ea780d7a708a78715148d-66bd87fbbb435301-01\n    [X-Amzn-Trace-Id] => Root=1-68ad7da8-f25ea780d7a708a78715148d;Parent=66bd87fbbb435301;Sampled=1\n    [X-Adsk-Signature] => sha256=1dd12d13478ec897e35baf7e4aad5bbe7d45839de6e32f8c067f8e2df7ce0a2d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3cedaf40-3cd8-4e19-9a05-b921d9d85c34\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:26:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"3cedaf40-3cd8-4e19-9a05-b921d9d85c34","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Order Submitted","message":"Quote# Q-1032741 status changed to Order Submitted.","modifiedAt":"2025-08-26T09:25:59.923Z"},"publishedAt":"2025-08-26T09:26:00.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:26:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:26:11
[webhook] [2025-08-26 09:26:11] [adwsapi_v2.php:36]  Provided signature: sha256=a0e45eda9638372760c9e6161bd6410281ddcf9979ea9d5f803fc5d093865d82
[webhook] [2025-08-26 09:26:11] [adwsapi_v2.php:37]  Calculated signature: sha256=53da378d35ec5c2f5693b38a4742a63e19ae3e8b0cdc88108f4e30fc13325038
[webhook] [2025-08-26 09:26:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 22bc8366f8302782\n    [X-B3-Traceid] => 68ad7db1908d2887b325075c38771d04\n    [B3] => 68ad7db1908d2887b325075c38771d04-22bc8366f8302782-1\n    [Traceparent] => 00-68ad7db1908d2887b325075c38771d04-22bc8366f8302782-01\n    [X-Amzn-Trace-Id] => Root=1-68ad7db1-908d2887b325075c38771d04;Parent=22bc8366f8302782;Sampled=1\n    [X-Adsk-Signature] => sha256=a0e45eda9638372760c9e6161bd6410281ddcf9979ea9d5f803fc5d093865d82\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69be2570-cb13-4b3b-94f0-9687e8524a06\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:26:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"69be2570-cb13-4b3b-94f0-9687e8524a06","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Ordered","message":"Quote# Q-1032741 status changed to Ordered.","modifiedAt":"2025-08-26T09:26:09.197Z"},"publishedAt":"2025-08-26T09:26:09.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:26:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:26:12
[webhook] [2025-08-26 09:26:12] [adwsapi_v2.php:36]  Provided signature: sha256=53da378d35ec5c2f5693b38a4742a63e19ae3e8b0cdc88108f4e30fc13325038
[webhook] [2025-08-26 09:26:12] [adwsapi_v2.php:37]  Calculated signature: sha256=53da378d35ec5c2f5693b38a4742a63e19ae3e8b0cdc88108f4e30fc13325038
[webhook] [2025-08-26 09:26:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 832d0bd228afadba\n    [X-B3-Traceid] => 68ad7db1908d2887b325075c38771d04\n    [B3] => 68ad7db1908d2887b325075c38771d04-832d0bd228afadba-1\n    [Traceparent] => 00-68ad7db1908d2887b325075c38771d04-832d0bd228afadba-01\n    [X-Amzn-Trace-Id] => Root=1-68ad7db1-908d2887b325075c38771d04;Parent=832d0bd228afadba;Sampled=1\n    [X-Adsk-Signature] => sha256=53da378d35ec5c2f5693b38a4742a63e19ae3e8b0cdc88108f4e30fc13325038\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69be2570-cb13-4b3b-94f0-9687e8524a06\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:26:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"69be2570-cb13-4b3b-94f0-9687e8524a06","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1032741","transactionId":"6af76946-d54c-5fd1-8190-0861a5059251","quoteStatus":"Ordered","message":"Quote# Q-1032741 status changed to Ordered.","modifiedAt":"2025-08-26T09:26:09.197Z"},"publishedAt":"2025-08-26T09:26:09.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:37:37
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:36]  Provided signature: sha256=a33b3f56a9cb5924f370cbd0c298a11c557e6effba7f61749d055bebe6397d36
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:37]  Calculated signature: sha256=a33b3f56a9cb5924f370cbd0c298a11c557e6effba7f61749d055bebe6397d36
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8cb2a9cb650f72d5\n    [X-B3-Traceid] => 68ad805e64a04766b3947f59e15315ff\n    [B3] => 68ad805e64a04766b3947f59e15315ff-8cb2a9cb650f72d5-1\n    [Traceparent] => 00-68ad805e64a04766b3947f59e15315ff-8cb2a9cb650f72d5-01\n    [X-Amzn-Trace-Id] => Root=1-68ad805e-64a04766b3947f59e15315ff;Parent=8cb2a9cb650f72d5;Sampled=1\n    [X-Adsk-Signature] => sha256=a33b3f56a9cb5924f370cbd0c298a11c557e6effba7f61749d055bebe6397d36\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 33624f20-3ebd-422f-971f-e4d6b67ac6fc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"33624f20-3ebd-422f-971f-e4d6b67ac6fc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033073","transactionId":"2661f3d4-ef22-5658-ad05-92f36db5fba7","quoteStatus":"Draft","message":"Quote# Q-1033073 status changed to Draft.","modifiedAt":"2025-08-26T09:37:34.643Z"},"publishedAt":"2025-08-26T09:37:34.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:37:37
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:36]  Provided signature: sha256=3a976a6517203a24a2fbe156ce0158b152bf3c98fb08d8702675e8649005e46d
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:37]  Calculated signature: sha256=a33b3f56a9cb5924f370cbd0c298a11c557e6effba7f61749d055bebe6397d36
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 97575865319dd1a4\n    [X-B3-Traceid] => 68ad805e64a04766b3947f59e15315ff\n    [B3] => 68ad805e64a04766b3947f59e15315ff-97575865319dd1a4-1\n    [Traceparent] => 00-68ad805e64a04766b3947f59e15315ff-97575865319dd1a4-01\n    [X-Amzn-Trace-Id] => Root=1-68ad805e-64a04766b3947f59e15315ff;Parent=97575865319dd1a4;Sampled=1\n    [X-Adsk-Signature] => sha256=3a976a6517203a24a2fbe156ce0158b152bf3c98fb08d8702675e8649005e46d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 33624f20-3ebd-422f-971f-e4d6b67ac6fc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:37:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"33624f20-3ebd-422f-971f-e4d6b67ac6fc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033073","transactionId":"2661f3d4-ef22-5658-ad05-92f36db5fba7","quoteStatus":"Draft","message":"Quote# Q-1033073 status changed to Draft.","modifiedAt":"2025-08-26T09:37:34.643Z"},"publishedAt":"2025-08-26T09:37:34.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:39:05
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:36]  Provided signature: sha256=dcb1d5d1c944121f1ac66b66c097705c4badec7a163e2d781f5b215be2c4d86f
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:37]  Calculated signature: sha256=dcb1d5d1c944121f1ac66b66c097705c4badec7a163e2d781f5b215be2c4d86f
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f034f90b4959c605\n    [X-B3-Traceid] => 68ad80b6dc46cca144d3e659b9862373\n    [B3] => 68ad80b6dc46cca144d3e659b9862373-f034f90b4959c605-1\n    [Traceparent] => 00-68ad80b6dc46cca144d3e659b9862373-f034f90b4959c605-01\n    [X-Amzn-Trace-Id] => Root=1-68ad80b6-dc46cca144d3e659b9862373;Parent=f034f90b4959c605;Sampled=1\n    [X-Adsk-Signature] => sha256=dcb1d5d1c944121f1ac66b66c097705c4badec7a163e2d781f5b215be2c4d86f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 94f739fe-b373-4a1a-9047-4d75c0126463\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"94f739fe-b373-4a1a-9047-4d75c0126463","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033073","transactionId":"2661f3d4-ef22-5658-ad05-92f36db5fba7","quoteStatus":"Quoted","message":"Quote# Q-1033073 status changed to Quoted.","modifiedAt":"2025-08-26T09:39:02.492Z"},"publishedAt":"2025-08-26T09:39:02.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:39:05
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:36]  Provided signature: sha256=f7a1989ca0c2e0684c8f7fe2437513e0e942ce0f22da984df555bb8836cfc51d
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:37]  Calculated signature: sha256=dcb1d5d1c944121f1ac66b66c097705c4badec7a163e2d781f5b215be2c4d86f
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4dfa8af83c02339b\n    [X-B3-Traceid] => 68ad80b6dc46cca144d3e659b9862373\n    [B3] => 68ad80b6dc46cca144d3e659b9862373-4dfa8af83c02339b-1\n    [Traceparent] => 00-68ad80b6dc46cca144d3e659b9862373-4dfa8af83c02339b-01\n    [X-Amzn-Trace-Id] => Root=1-68ad80b6-dc46cca144d3e659b9862373;Parent=4dfa8af83c02339b;Sampled=1\n    [X-Adsk-Signature] => sha256=f7a1989ca0c2e0684c8f7fe2437513e0e942ce0f22da984df555bb8836cfc51d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 94f739fe-b373-4a1a-9047-4d75c0126463\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:39:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"94f739fe-b373-4a1a-9047-4d75c0126463","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033073","transactionId":"2661f3d4-ef22-5658-ad05-92f36db5fba7","quoteStatus":"Quoted","message":"Quote# Q-1033073 status changed to Quoted.","modifiedAt":"2025-08-26T09:39:02.492Z"},"publishedAt":"2025-08-26T09:39:02.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:41:19
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:36]  Provided signature: sha256=c57e00527fa3daf9b708f6ce303b3bdd4295c3a15574c33c27e1e8f001f5cfd6
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:37]  Calculated signature: sha256=1e7875b50d2091b2f8b16fbb775b3dea709614ee5efed8ddf24c5e4df498789b
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fdfe570cc0c8ca38\n    [X-B3-Traceid] => 68ad813d52ddd7885c54f6d579298bf6\n    [B3] => 68ad813d52ddd7885c54f6d579298bf6-fdfe570cc0c8ca38-1\n    [Traceparent] => 00-68ad813d52ddd7885c54f6d579298bf6-fdfe570cc0c8ca38-01\n    [X-Amzn-Trace-Id] => Root=1-68ad813d-52ddd7885c54f6d579298bf6;Parent=fdfe570cc0c8ca38;Sampled=1\n    [X-Adsk-Signature] => sha256=c57e00527fa3daf9b708f6ce303b3bdd4295c3a15574c33c27e1e8f001f5cfd6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8e154c79-dd82-4d40-8091-e00da0307355\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"8e154c79-dd82-4d40-8091-e00da0307355","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527130055219","status":"Active","quantity":1,"endDate":"2026-09-01","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T09:26:14.000+0000"},"publishedAt":"2025-08-26T09:41:17.000Z","csn":"5103159758"}
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 09:41:19
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:36]  Provided signature: sha256=1e7875b50d2091b2f8b16fbb775b3dea709614ee5efed8ddf24c5e4df498789b
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:37]  Calculated signature: sha256=1e7875b50d2091b2f8b16fbb775b3dea709614ee5efed8ddf24c5e4df498789b
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 28dc9f158e0936af\n    [X-B3-Traceid] => 68ad813d52ddd7885c54f6d579298bf6\n    [B3] => 68ad813d52ddd7885c54f6d579298bf6-28dc9f158e0936af-1\n    [Traceparent] => 00-68ad813d52ddd7885c54f6d579298bf6-28dc9f158e0936af-01\n    [X-Amzn-Trace-Id] => Root=1-68ad813d-52ddd7885c54f6d579298bf6;Parent=28dc9f158e0936af;Sampled=1\n    [X-Adsk-Signature] => sha256=1e7875b50d2091b2f8b16fbb775b3dea709614ee5efed8ddf24c5e4df498789b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8e154c79-dd82-4d40-8091-e00da0307355\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 09:41:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"8e154c79-dd82-4d40-8091-e00da0307355","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527130055219","status":"Active","quantity":1,"endDate":"2026-09-01","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T09:26:14.000+0000"},"publishedAt":"2025-08-26T09:41:17.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:08:20
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:36]  Provided signature: sha256=7d86eccd47f7167e9858bbd812b2925340c23a5bc8b5b0d7c93ea1d0e844a547
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:37]  Calculated signature: sha256=7d86eccd47f7167e9858bbd812b2925340c23a5bc8b5b0d7c93ea1d0e844a547
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 22662b41eb4c5081\n    [X-B3-Traceid] => 68ad95a250b95b67ab3cb04b36e1c18c\n    [B3] => 68ad95a250b95b67ab3cb04b36e1c18c-22662b41eb4c5081-1\n    [Traceparent] => 00-68ad95a250b95b67ab3cb04b36e1c18c-22662b41eb4c5081-01\n    [X-Amzn-Trace-Id] => Root=1-68ad95a2-50b95b67ab3cb04b36e1c18c;Parent=22662b41eb4c5081;Sampled=1\n    [X-Adsk-Signature] => sha256=7d86eccd47f7167e9858bbd812b2925340c23a5bc8b5b0d7c93ea1d0e844a547\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 09924806-dd6c-4e9e-a7b3-c854cc75cd47\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"09924806-dd6c-4e9e-a7b3-c854cc75cd47","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Draft","message":"Quote# Q-1033420 status changed to Draft.","modifiedAt":"2025-08-26T11:08:18.217Z"},"publishedAt":"2025-08-26T11:08:18.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:08:20
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:36]  Provided signature: sha256=0ec18aa19b4dbdd451c0d74096f86e1d420cd1cb2a9d842aa250f74a184f7b02
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:37]  Calculated signature: sha256=7d86eccd47f7167e9858bbd812b2925340c23a5bc8b5b0d7c93ea1d0e844a547
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5d4d19412a95a3b9\n    [X-B3-Traceid] => 68ad95a250b95b67ab3cb04b36e1c18c\n    [B3] => 68ad95a250b95b67ab3cb04b36e1c18c-5d4d19412a95a3b9-1\n    [Traceparent] => 00-68ad95a250b95b67ab3cb04b36e1c18c-5d4d19412a95a3b9-01\n    [X-Amzn-Trace-Id] => Root=1-68ad95a2-50b95b67ab3cb04b36e1c18c;Parent=5d4d19412a95a3b9;Sampled=1\n    [X-Adsk-Signature] => sha256=0ec18aa19b4dbdd451c0d74096f86e1d420cd1cb2a9d842aa250f74a184f7b02\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 09924806-dd6c-4e9e-a7b3-c854cc75cd47\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:08:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"09924806-dd6c-4e9e-a7b3-c854cc75cd47","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Draft","message":"Quote# Q-1033420 status changed to Draft.","modifiedAt":"2025-08-26T11:08:18.217Z"},"publishedAt":"2025-08-26T11:08:18.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:09:50
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:36]  Provided signature: sha256=9fdac682d6ad98258e7da65b0c1dc829c3b4e9b41ff777ce77e6b58414d28e78
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:37]  Calculated signature: sha256=31145fb0581c1b78a1df7948ad9dc320c4a5fa7825d074c2e01db9a978133ca2
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a11920e5fe62526b\n    [X-B3-Traceid] => 68ad95fccde433f3141c45450f4d0694\n    [B3] => 68ad95fccde433f3141c45450f4d0694-a11920e5fe62526b-1\n    [Traceparent] => 00-68ad95fccde433f3141c45450f4d0694-a11920e5fe62526b-01\n    [X-Amzn-Trace-Id] => Root=1-68ad95fc-cde433f3141c45450f4d0694;Parent=a11920e5fe62526b;Sampled=1\n    [X-Adsk-Signature] => sha256=9fdac682d6ad98258e7da65b0c1dc829c3b4e9b41ff777ce77e6b58414d28e78\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 32dcbad2-3bb7-48e1-a4bc-ab06f7d2b1bb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"32dcbad2-3bb7-48e1-a4bc-ab06f7d2b1bb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Quoted","message":"Quote# Q-1033420 status changed to Quoted.","modifiedAt":"2025-08-26T11:09:48.152Z"},"publishedAt":"2025-08-26T11:09:48.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:09:50
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:36]  Provided signature: sha256=31145fb0581c1b78a1df7948ad9dc320c4a5fa7825d074c2e01db9a978133ca2
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:37]  Calculated signature: sha256=31145fb0581c1b78a1df7948ad9dc320c4a5fa7825d074c2e01db9a978133ca2
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6c93c4a690a92cdb\n    [X-B3-Traceid] => 68ad95fccde433f3141c45450f4d0694\n    [B3] => 68ad95fccde433f3141c45450f4d0694-6c93c4a690a92cdb-1\n    [Traceparent] => 00-68ad95fccde433f3141c45450f4d0694-6c93c4a690a92cdb-01\n    [X-Amzn-Trace-Id] => Root=1-68ad95fc-cde433f3141c45450f4d0694;Parent=6c93c4a690a92cdb;Sampled=1\n    [X-Adsk-Signature] => sha256=31145fb0581c1b78a1df7948ad9dc320c4a5fa7825d074c2e01db9a978133ca2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 32dcbad2-3bb7-48e1-a4bc-ab06f7d2b1bb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:09:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"32dcbad2-3bb7-48e1-a4bc-ab06f7d2b1bb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Quoted","message":"Quote# Q-1033420 status changed to Quoted.","modifiedAt":"2025-08-26T11:09:48.152Z"},"publishedAt":"2025-08-26T11:09:48.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:32:47
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:36]  Provided signature: sha256=f44c75d9d79065af9c8eda720b254444bdd849ea141e5fe4b6ca90b2f156ca4c
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:37]  Calculated signature: sha256=9b6acd1707546bf8666fbe0171fb774c9a48e1e5d47aad51785a8f9631cdb3cc
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => eaacc7104998049e\n    [X-B3-Traceid] => 68ad9b5cdfe505aa0da5d870fa9b7006\n    [B3] => 68ad9b5cdfe505aa0da5d870fa9b7006-eaacc7104998049e-1\n    [Traceparent] => 00-68ad9b5cdfe505aa0da5d870fa9b7006-eaacc7104998049e-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b5c-dfe505aa0da5d870fa9b7006;Parent=eaacc7104998049e;Sampled=1\n    [X-Adsk-Signature] => sha256=f44c75d9d79065af9c8eda720b254444bdd849ea141e5fe4b6ca90b2f156ca4c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4de2cb63-2b74-4dc9-91e3-0a486e116104\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"4de2cb63-2b74-4dc9-91e3-0a486e116104","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Order Submitted","message":"Quote# Q-1033420 status changed to Order Submitted.","modifiedAt":"2025-08-26T11:32:44.402Z"},"publishedAt":"2025-08-26T11:32:44.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:32:47
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:36]  Provided signature: sha256=9b6acd1707546bf8666fbe0171fb774c9a48e1e5d47aad51785a8f9631cdb3cc
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:37]  Calculated signature: sha256=9b6acd1707546bf8666fbe0171fb774c9a48e1e5d47aad51785a8f9631cdb3cc
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6a2c688b54cb4d58\n    [X-B3-Traceid] => 68ad9b5cdfe505aa0da5d870fa9b7006\n    [B3] => 68ad9b5cdfe505aa0da5d870fa9b7006-6a2c688b54cb4d58-1\n    [Traceparent] => 00-68ad9b5cdfe505aa0da5d870fa9b7006-6a2c688b54cb4d58-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b5c-dfe505aa0da5d870fa9b7006;Parent=6a2c688b54cb4d58;Sampled=1\n    [X-Adsk-Signature] => sha256=9b6acd1707546bf8666fbe0171fb774c9a48e1e5d47aad51785a8f9631cdb3cc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4de2cb63-2b74-4dc9-91e3-0a486e116104\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:32:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"4de2cb63-2b74-4dc9-91e3-0a486e116104","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Order Submitted","message":"Quote# Q-1033420 status changed to Order Submitted.","modifiedAt":"2025-08-26T11:32:44.402Z"},"publishedAt":"2025-08-26T11:32:44.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:32:48
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:36]  Provided signature: sha256=6899978713eee32770288a548305d9d049caa738630c1c2cd97009890ed3582f
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:37]  Calculated signature: sha256=6899978713eee32770288a548305d9d049caa738630c1c2cd97009890ed3582f
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1a03e467fcdfb8e4\n    [X-B3-Traceid] => 68ad9b5d10b072c68a5fc16350535989\n    [B3] => 68ad9b5d10b072c68a5fc16350535989-1a03e467fcdfb8e4-1\n    [Traceparent] => 00-68ad9b5d10b072c68a5fc16350535989-1a03e467fcdfb8e4-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b5d-10b072c68a5fc16350535989;Parent=1a03e467fcdfb8e4;Sampled=1\n    [X-Adsk-Signature] => sha256=6899978713eee32770288a548305d9d049caa738630c1c2cd97009890ed3582f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5b4b377b-ab0a-429c-87c1-34927666ec32\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"5b4b377b-ab0a-429c-87c1-34927666ec32","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Ordered","message":"Quote# Q-1033420 status changed to Ordered.","modifiedAt":"2025-08-26T11:32:45.386Z"},"publishedAt":"2025-08-26T11:32:45.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:32:48
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:36]  Provided signature: sha256=38d8e706e019ec1efab2c8f61b84ef983521a1b278aa085313a825a9ed5904a7
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:37]  Calculated signature: sha256=6899978713eee32770288a548305d9d049caa738630c1c2cd97009890ed3582f
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 431cfb1a9d47c67d\n    [X-B3-Traceid] => 68ad9b5d10b072c68a5fc16350535989\n    [B3] => 68ad9b5d10b072c68a5fc16350535989-431cfb1a9d47c67d-1\n    [Traceparent] => 00-68ad9b5d10b072c68a5fc16350535989-431cfb1a9d47c67d-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b5d-10b072c68a5fc16350535989;Parent=431cfb1a9d47c67d;Sampled=1\n    [X-Adsk-Signature] => sha256=38d8e706e019ec1efab2c8f61b84ef983521a1b278aa085313a825a9ed5904a7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5b4b377b-ab0a-429c-87c1-34927666ec32\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:32:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"5b4b377b-ab0a-429c-87c1-34927666ec32","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033420","transactionId":"2cc8e8f1-2ad9-5632-b9e9-89d6bd017ab1","quoteStatus":"Ordered","message":"Quote# Q-1033420 status changed to Ordered.","modifiedAt":"2025-08-26T11:32:45.386Z"},"publishedAt":"2025-08-26T11:32:45.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:33:18
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:36]  Provided signature: sha256=7a205ae249a24b2ca4389bd9338a27e9a1332fd8773d4ba8ccdcd94c8b3bbebe
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:37]  Calculated signature: sha256=9e0a1a2c92a0119b3f6d6f7e3af63a3ccec172bd597d425d7fdaf2e092b8a44d
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => dc68d45d32a77cf8\n    [X-B3-Traceid] => 68ad9b7c50ada19abb1530ea19aa267b\n    [B3] => 68ad9b7c50ada19abb1530ea19aa267b-dc68d45d32a77cf8-1\n    [Traceparent] => 00-68ad9b7c50ada19abb1530ea19aa267b-dc68d45d32a77cf8-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b7c-50ada19abb1530ea19aa267b;Parent=dc68d45d32a77cf8;Sampled=1\n    [X-Adsk-Signature] => sha256=7a205ae249a24b2ca4389bd9338a27e9a1332fd8773d4ba8ccdcd94c8b3bbebe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4f7ff48e-30c1-4948-86a3-46873a23b3df\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"4f7ff48e-30c1-4948-86a3-46873a23b3df","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Order Submitted","message":"Quote# Q-1026459 status changed to Order Submitted.","modifiedAt":"2025-08-26T11:33:16.279Z"},"publishedAt":"2025-08-26T11:33:16.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:33:18
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:36]  Provided signature: sha256=9e0a1a2c92a0119b3f6d6f7e3af63a3ccec172bd597d425d7fdaf2e092b8a44d
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:37]  Calculated signature: sha256=9e0a1a2c92a0119b3f6d6f7e3af63a3ccec172bd597d425d7fdaf2e092b8a44d
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cad29d0b2e43829f\n    [X-B3-Traceid] => 68ad9b7c50ada19abb1530ea19aa267b\n    [B3] => 68ad9b7c50ada19abb1530ea19aa267b-cad29d0b2e43829f-1\n    [Traceparent] => 00-68ad9b7c50ada19abb1530ea19aa267b-cad29d0b2e43829f-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b7c-50ada19abb1530ea19aa267b;Parent=cad29d0b2e43829f;Sampled=1\n    [X-Adsk-Signature] => sha256=9e0a1a2c92a0119b3f6d6f7e3af63a3ccec172bd597d425d7fdaf2e092b8a44d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4f7ff48e-30c1-4948-86a3-46873a23b3df\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:33:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"4f7ff48e-30c1-4948-86a3-46873a23b3df","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Order Submitted","message":"Quote# Q-1026459 status changed to Order Submitted.","modifiedAt":"2025-08-26T11:33:16.279Z"},"publishedAt":"2025-08-26T11:33:16.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:33:21
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:36]  Provided signature: sha256=c8a707e3434cc34da2fe45b1acc5dc6eec05386ee73a8f32fb352e33fd5d5fbc
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:37]  Calculated signature: sha256=c8a707e3434cc34da2fe45b1acc5dc6eec05386ee73a8f32fb352e33fd5d5fbc
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 534fc2fa0ea4f85f\n    [X-B3-Traceid] => 68ad9b7ee7d20c51915ebf4fe884b369\n    [B3] => 68ad9b7ee7d20c51915ebf4fe884b369-534fc2fa0ea4f85f-1\n    [Traceparent] => 00-68ad9b7ee7d20c51915ebf4fe884b369-534fc2fa0ea4f85f-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b7e-e7d20c51915ebf4fe884b369;Parent=534fc2fa0ea4f85f;Sampled=1\n    [X-Adsk-Signature] => sha256=c8a707e3434cc34da2fe45b1acc5dc6eec05386ee73a8f32fb352e33fd5d5fbc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df713b9d-506b-4bdb-9f15-69a3344be84a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"df713b9d-506b-4bdb-9f15-69a3344be84a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Ordered","message":"Quote# Q-1026459 status changed to Ordered.","modifiedAt":"2025-08-26T11:33:18.375Z"},"publishedAt":"2025-08-26T11:33:18.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:33:21
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:36]  Provided signature: sha256=6e7fd14684af597c03b97f2ea4fe5c6eda6f8a29d470bfbc77376e0eba2e2923
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:37]  Calculated signature: sha256=c8a707e3434cc34da2fe45b1acc5dc6eec05386ee73a8f32fb352e33fd5d5fbc
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 26c1455b929aa077\n    [X-B3-Traceid] => 68ad9b7ee7d20c51915ebf4fe884b369\n    [B3] => 68ad9b7ee7d20c51915ebf4fe884b369-26c1455b929aa077-1\n    [Traceparent] => 00-68ad9b7ee7d20c51915ebf4fe884b369-26c1455b929aa077-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9b7e-e7d20c51915ebf4fe884b369;Parent=26c1455b929aa077;Sampled=1\n    [X-Adsk-Signature] => sha256=6e7fd14684af597c03b97f2ea4fe5c6eda6f8a29d470bfbc77376e0eba2e2923\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df713b9d-506b-4bdb-9f15-69a3344be84a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:33:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"df713b9d-506b-4bdb-9f15-69a3344be84a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1026459","transactionId":"1058a0d1-c955-562b-8f04-c1f349568f45","quoteStatus":"Ordered","message":"Quote# Q-1026459 status changed to Ordered.","modifiedAt":"2025-08-26T11:33:18.375Z"},"publishedAt":"2025-08-26T11:33:18.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:36:26
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:36]  Provided signature: sha256=12cfa1ee1a391f331435498a6c3f8cfbdc164a1bcb916c28346875e3d2b188f0
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:37]  Calculated signature: sha256=12cfa1ee1a391f331435498a6c3f8cfbdc164a1bcb916c28346875e3d2b188f0
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bd280e1bf92f0805\n    [X-B3-Traceid] => 68ad9c382bd807e87d7cc82404fafe02\n    [B3] => 68ad9c382bd807e87d7cc82404fafe02-bd280e1bf92f0805-1\n    [Traceparent] => 00-68ad9c382bd807e87d7cc82404fafe02-bd280e1bf92f0805-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9c38-2bd807e87d7cc82404fafe02;Parent=bd280e1bf92f0805;Sampled=1\n    [X-Adsk-Signature] => sha256=12cfa1ee1a391f331435498a6c3f8cfbdc164a1bcb916c28346875e3d2b188f0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 282fa9e3-e036-4480-abb0-cd40a024e363\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"282fa9e3-e036-4480-abb0-cd40a024e363","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63275261587012","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-26T11:21:14.000+0000"},"publishedAt":"2025-08-26T11:36:24.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:36:26
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:36]  Provided signature: sha256=3529b6b930fa3f9b0455740a81b106f8dd737478af8a278e62bb38bca43923f1
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:37]  Calculated signature: sha256=12cfa1ee1a391f331435498a6c3f8cfbdc164a1bcb916c28346875e3d2b188f0
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d9b65434e00ceba1\n    [X-B3-Traceid] => 68ad9c382bd807e87d7cc82404fafe02\n    [B3] => 68ad9c382bd807e87d7cc82404fafe02-d9b65434e00ceba1-1\n    [Traceparent] => 00-68ad9c382bd807e87d7cc82404fafe02-d9b65434e00ceba1-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9c38-2bd807e87d7cc82404fafe02;Parent=d9b65434e00ceba1;Sampled=1\n    [X-Adsk-Signature] => sha256=3529b6b930fa3f9b0455740a81b106f8dd737478af8a278e62bb38bca43923f1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 282fa9e3-e036-4480-abb0-cd40a024e363\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:36:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"282fa9e3-e036-4480-abb0-cd40a024e363","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63275261587012","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-26T11:21:14.000+0000"},"publishedAt":"2025-08-26T11:36:24.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:39:00
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:36]  Provided signature: sha256=d0aa6b041726b2a41ff2f153654b3794603b1ed8b3512f5e61c4ebbba094e481
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:37]  Calculated signature: sha256=40be57c024ae0458816d688dd75919ffd3c8218b044bbea9d5ffc80f61c6cad8
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 19fb0e21b9541b4c\n    [X-B3-Traceid] => 68ad9cd26575d5c60d6cec6b62a1ac01\n    [B3] => 68ad9cd26575d5c60d6cec6b62a1ac01-19fb0e21b9541b4c-1\n    [Traceparent] => 00-68ad9cd26575d5c60d6cec6b62a1ac01-19fb0e21b9541b4c-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9cd2-6575d5c60d6cec6b62a1ac01;Parent=19fb0e21b9541b4c;Sampled=1\n    [X-Adsk-Signature] => sha256=d0aa6b041726b2a41ff2f153654b3794603b1ed8b3512f5e61c4ebbba094e481\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3a406d77-f958-43a8-aec1-bba3a80fd1d5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"3a406d77-f958-43a8-aec1-bba3a80fd1d5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527130055219","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-26T11:23:52.000+0000"},"publishedAt":"2025-08-26T11:38:58.000Z","csn":"5103159758"}
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 11:39:00
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:36]  Provided signature: sha256=40be57c024ae0458816d688dd75919ffd3c8218b044bbea9d5ffc80f61c6cad8
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:37]  Calculated signature: sha256=40be57c024ae0458816d688dd75919ffd3c8218b044bbea9d5ffc80f61c6cad8
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8f95d330ca2a83ca\n    [X-B3-Traceid] => 68ad9cd26575d5c60d6cec6b62a1ac01\n    [B3] => 68ad9cd26575d5c60d6cec6b62a1ac01-8f95d330ca2a83ca-1\n    [Traceparent] => 00-68ad9cd26575d5c60d6cec6b62a1ac01-8f95d330ca2a83ca-01\n    [X-Amzn-Trace-Id] => Root=1-68ad9cd2-6575d5c60d6cec6b62a1ac01;Parent=8f95d330ca2a83ca;Sampled=1\n    [X-Adsk-Signature] => sha256=40be57c024ae0458816d688dd75919ffd3c8218b044bbea9d5ffc80f61c6cad8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3a406d77-f958-43a8-aec1-bba3a80fd1d5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 11:39:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"3a406d77-f958-43a8-aec1-bba3a80fd1d5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527130055219","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-26T11:23:52.000+0000"},"publishedAt":"2025-08-26T11:38:58.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:08:28
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:36]  Provided signature: sha256=f342a27fada18f79047deb8e797181a4b721a979e62421b823cba8d3ce35132d
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:37]  Calculated signature: sha256=f342a27fada18f79047deb8e797181a4b721a979e62421b823cba8d3ce35132d
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 045b9b3c932f4256\n    [X-B3-Traceid] => 68ada3b9388d1d5c4712274e3c55c42d\n    [B3] => 68ada3b9388d1d5c4712274e3c55c42d-045b9b3c932f4256-1\n    [Traceparent] => 00-68ada3b9388d1d5c4712274e3c55c42d-045b9b3c932f4256-01\n    [X-Amzn-Trace-Id] => Root=1-68ada3b9-388d1d5c4712274e3c55c42d;Parent=045b9b3c932f4256;Sampled=1\n    [X-Adsk-Signature] => sha256=f342a27fada18f79047deb8e797181a4b721a979e62421b823cba8d3ce35132d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b77b04d1-8cb0-406f-9209-8b87d2921411\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"b77b04d1-8cb0-406f-9209-8b87d2921411","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"60008181759135","status":"Active","quantity":1,"endDate":"2028-09-13","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T11:33:23.000+0000"},"publishedAt":"2025-08-26T12:08:25.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:08:28
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:36]  Provided signature: sha256=390b10a6b4f66e6d8f21d462e40bc57c8f05f52f1281c6ed9652a1ed5aa39384
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:37]  Calculated signature: sha256=f342a27fada18f79047deb8e797181a4b721a979e62421b823cba8d3ce35132d
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 108d952005c9a273\n    [X-B3-Traceid] => 68ada3b9388d1d5c4712274e3c55c42d\n    [B3] => 68ada3b9388d1d5c4712274e3c55c42d-108d952005c9a273-1\n    [Traceparent] => 00-68ada3b9388d1d5c4712274e3c55c42d-108d952005c9a273-01\n    [X-Amzn-Trace-Id] => Root=1-68ada3b9-388d1d5c4712274e3c55c42d;Parent=108d952005c9a273;Sampled=1\n    [X-Adsk-Signature] => sha256=390b10a6b4f66e6d8f21d462e40bc57c8f05f52f1281c6ed9652a1ed5aa39384\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b77b04d1-8cb0-406f-9209-8b87d2921411\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:08:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"b77b04d1-8cb0-406f-9209-8b87d2921411","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"60008181759135","status":"Active","quantity":1,"endDate":"2028-09-13","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T11:33:23.000+0000"},"publishedAt":"2025-08-26T12:08:25.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:08:31
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:36]  Provided signature: sha256=ddbfa8a221e56f3529afd31a381ac055202d53b42171fb73da883eebf3f6868b
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:37]  Calculated signature: sha256=0102ee6a45fdb70cd6b4232160cb1d4f47339786b92afd7c539eb8095a933036
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 75acc508441c07ed\n    [X-B3-Traceid] => 68ada3bd2218d05126781f585bb676d6\n    [B3] => 68ada3bd2218d05126781f585bb676d6-75acc508441c07ed-1\n    [Traceparent] => 00-68ada3bd2218d05126781f585bb676d6-75acc508441c07ed-01\n    [X-Amzn-Trace-Id] => Root=1-68ada3bd-2218d05126781f585bb676d6;Parent=75acc508441c07ed;Sampled=1\n    [X-Adsk-Signature] => sha256=ddbfa8a221e56f3529afd31a381ac055202d53b42171fb73da883eebf3f6868b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b17e1e12-b679-4383-a3ca-9ec97b4d162d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"b17e1e12-b679-4383-a3ca-9ec97b4d162d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"60008181733933","status":"Active","quantity":1,"endDate":"2028-09-13","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T11:33:26.000+0000"},"publishedAt":"2025-08-26T12:08:29.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:08:31
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:36]  Provided signature: sha256=0102ee6a45fdb70cd6b4232160cb1d4f47339786b92afd7c539eb8095a933036
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:37]  Calculated signature: sha256=0102ee6a45fdb70cd6b4232160cb1d4f47339786b92afd7c539eb8095a933036
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 92ab2da209e4a785\n    [X-B3-Traceid] => 68ada3bd2218d05126781f585bb676d6\n    [B3] => 68ada3bd2218d05126781f585bb676d6-92ab2da209e4a785-1\n    [Traceparent] => 00-68ada3bd2218d05126781f585bb676d6-92ab2da209e4a785-01\n    [X-Amzn-Trace-Id] => Root=1-68ada3bd-2218d05126781f585bb676d6;Parent=92ab2da209e4a785;Sampled=1\n    [X-Adsk-Signature] => sha256=0102ee6a45fdb70cd6b4232160cb1d4f47339786b92afd7c539eb8095a933036\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b17e1e12-b679-4383-a3ca-9ec97b4d162d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:08:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"b17e1e12-b679-4383-a3ca-9ec97b4d162d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"60008181733933","status":"Active","quantity":1,"endDate":"2028-09-13","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T11:33:26.000+0000"},"publishedAt":"2025-08-26T12:08:29.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:09:43
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:36]  Provided signature: sha256=0a12cb7cfdcaa3beb813e669a3b24503f3b1bc8df76501777fdd33fb7f695df4
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:37]  Calculated signature: sha256=8f81a620c76d447f081f5c0a00e3a57a0b61fe4f678caa0fc2a0678b5f2799ba
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7e6e323e25be9bcd\n    [X-B3-Traceid] => 68ada4052a62dced35671c237c65a5f8\n    [B3] => 68ada4052a62dced35671c237c65a5f8-7e6e323e25be9bcd-1\n    [Traceparent] => 00-68ada4052a62dced35671c237c65a5f8-7e6e323e25be9bcd-01\n    [X-Amzn-Trace-Id] => Root=1-68ada405-2a62dced35671c237c65a5f8;Parent=7e6e323e25be9bcd;Sampled=1\n    [X-Adsk-Signature] => sha256=0a12cb7cfdcaa3beb813e669a3b24503f3b1bc8df76501777fdd33fb7f695df4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 44c4bf96-0539-419a-a3ba-f01bfcfbe9e5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"44c4bf96-0539-419a-a3ba-f01bfcfbe9e5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75620796179516","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-26T11:39:39.000+0000"},"publishedAt":"2025-08-26T12:09:41.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:09:43
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:36]  Provided signature: sha256=8f81a620c76d447f081f5c0a00e3a57a0b61fe4f678caa0fc2a0678b5f2799ba
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:37]  Calculated signature: sha256=8f81a620c76d447f081f5c0a00e3a57a0b61fe4f678caa0fc2a0678b5f2799ba
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 09927574b6fb6f6f\n    [X-B3-Traceid] => 68ada4052a62dced35671c237c65a5f8\n    [B3] => 68ada4052a62dced35671c237c65a5f8-09927574b6fb6f6f-1\n    [Traceparent] => 00-68ada4052a62dced35671c237c65a5f8-09927574b6fb6f6f-01\n    [X-Amzn-Trace-Id] => Root=1-68ada405-2a62dced35671c237c65a5f8;Parent=09927574b6fb6f6f;Sampled=1\n    [X-Adsk-Signature] => sha256=8f81a620c76d447f081f5c0a00e3a57a0b61fe4f678caa0fc2a0678b5f2799ba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 44c4bf96-0539-419a-a3ba-f01bfcfbe9e5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:09:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"44c4bf96-0539-419a-a3ba-f01bfcfbe9e5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75620796179516","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-26T11:39:39.000+0000"},"publishedAt":"2025-08-26T12:09:41.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:12:46
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:36]  Provided signature: sha256=50d5f410ce90d1ba04c4709b67658823109d50d52c58204f240d6411094df1e5
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:37]  Calculated signature: sha256=06efee1a87b96007729e5e9b23c29abbc56b1ac08c26511e3bd35efe11ef5368
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bd780323e1280012\n    [X-B3-Traceid] => 68ada4bb295f272e5afafab759325aa5\n    [B3] => 68ada4bb295f272e5afafab759325aa5-bd780323e1280012-1\n    [Traceparent] => 00-68ada4bb295f272e5afafab759325aa5-bd780323e1280012-01\n    [X-Amzn-Trace-Id] => Root=1-68ada4bb-295f272e5afafab759325aa5;Parent=bd780323e1280012;Sampled=1\n    [X-Adsk-Signature] => sha256=50d5f410ce90d1ba04c4709b67658823109d50d52c58204f240d6411094df1e5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756210363792-69107177208594-9033725615-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756210363792-69107177208594-9033725615-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69107177208594","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-26T12:12:43.792Z"},"publishedAt":"2025-08-26T12:12:43.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:12:46
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:36]  Provided signature: sha256=06efee1a87b96007729e5e9b23c29abbc56b1ac08c26511e3bd35efe11ef5368
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:37]  Calculated signature: sha256=06efee1a87b96007729e5e9b23c29abbc56b1ac08c26511e3bd35efe11ef5368
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 77a17fdad51532e2\n    [X-B3-Traceid] => 68ada4bb295f272e5afafab759325aa5\n    [B3] => 68ada4bb295f272e5afafab759325aa5-77a17fdad51532e2-1\n    [Traceparent] => 00-68ada4bb295f272e5afafab759325aa5-77a17fdad51532e2-01\n    [X-Amzn-Trace-Id] => Root=1-68ada4bb-295f272e5afafab759325aa5;Parent=77a17fdad51532e2;Sampled=1\n    [X-Adsk-Signature] => sha256=06efee1a87b96007729e5e9b23c29abbc56b1ac08c26511e3bd35efe11ef5368\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756210363792-69107177208594-9033725615-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:12:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756210363792-69107177208594-9033725615-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69107177208594","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-26T12:12:43.792Z"},"publishedAt":"2025-08-26T12:12:43.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:33:42
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:36]  Provided signature: sha256=fddff7483ded35b66a6b80ef572eaaeefb0ad87309eb49c8c15704f89912918e
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:37]  Calculated signature: sha256=397726e79725f995f57506e2e7156bd3732c9e9cba266d517122f9ff26ab8349
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 44542615dd722936\n    [X-B3-Traceid] => 68ada9a4f889290cb2ebcba55c7181b1\n    [B3] => 68ada9a4f889290cb2ebcba55c7181b1-44542615dd722936-1\n    [Traceparent] => 00-68ada9a4f889290cb2ebcba55c7181b1-44542615dd722936-01\n    [X-Amzn-Trace-Id] => Root=1-68ada9a4-f889290cb2ebcba55c7181b1;Parent=44542615dd722936;Sampled=1\n    [X-Adsk-Signature] => sha256=fddff7483ded35b66a6b80ef572eaaeefb0ad87309eb49c8c15704f89912918e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ece61737-251c-4819-9a7e-898ea7647462\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"ece61737-251c-4819-9a7e-898ea7647462","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033698","transactionId":"702811ea-549c-5fd2-8c9c-70cdff438630","quoteStatus":"Draft","message":"Quote# Q-1033698 status changed to Draft.","modifiedAt":"2025-08-26T12:33:39.992Z"},"publishedAt":"2025-08-26T12:33:40.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:33:42
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:36]  Provided signature: sha256=397726e79725f995f57506e2e7156bd3732c9e9cba266d517122f9ff26ab8349
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:37]  Calculated signature: sha256=397726e79725f995f57506e2e7156bd3732c9e9cba266d517122f9ff26ab8349
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 186fdf10bc9d9e1c\n    [X-B3-Traceid] => 68ada9a4f889290cb2ebcba55c7181b1\n    [B3] => 68ada9a4f889290cb2ebcba55c7181b1-186fdf10bc9d9e1c-1\n    [Traceparent] => 00-68ada9a4f889290cb2ebcba55c7181b1-186fdf10bc9d9e1c-01\n    [X-Amzn-Trace-Id] => Root=1-68ada9a4-f889290cb2ebcba55c7181b1;Parent=186fdf10bc9d9e1c;Sampled=1\n    [X-Adsk-Signature] => sha256=397726e79725f995f57506e2e7156bd3732c9e9cba266d517122f9ff26ab8349\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ece61737-251c-4819-9a7e-898ea7647462\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:33:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"ece61737-251c-4819-9a7e-898ea7647462","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033698","transactionId":"702811ea-549c-5fd2-8c9c-70cdff438630","quoteStatus":"Draft","message":"Quote# Q-1033698 status changed to Draft.","modifiedAt":"2025-08-26T12:33:39.992Z"},"publishedAt":"2025-08-26T12:33:40.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:34:13
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:36]  Provided signature: sha256=8d3e5be0d5889328776bd2756d0f4379be9fa38a5f93d169ba45960c2654cfd5
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:37]  Calculated signature: sha256=8d3e5be0d5889328776bd2756d0f4379be9fa38a5f93d169ba45960c2654cfd5
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b94528b1ba634570\n    [X-B3-Traceid] => 68ada9c3a248e297751a14a1d2d17a42\n    [B3] => 68ada9c3a248e297751a14a1d2d17a42-b94528b1ba634570-1\n    [Traceparent] => 00-68ada9c3a248e297751a14a1d2d17a42-b94528b1ba634570-01\n    [X-Amzn-Trace-Id] => Root=1-68ada9c3-a248e297751a14a1d2d17a42;Parent=b94528b1ba634570;Sampled=1\n    [X-Adsk-Signature] => sha256=8d3e5be0d5889328776bd2756d0f4379be9fa38a5f93d169ba45960c2654cfd5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 45c442b7-5c15-4cff-9d27-9b6d8fb3b657\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"45c442b7-5c15-4cff-9d27-9b6d8fb3b657","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033698","transactionId":"702811ea-549c-5fd2-8c9c-70cdff438630","quoteStatus":"Quoted","message":"Quote# Q-1033698 status changed to Quoted.","modifiedAt":"2025-08-26T12:34:10.995Z"},"publishedAt":"2025-08-26T12:34:11.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:34:13
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:36]  Provided signature: sha256=d4e4e686dd630e8ceccfed371fa4ecb7efc22a4495aa9cf24925b90a8131e3c5
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:37]  Calculated signature: sha256=8d3e5be0d5889328776bd2756d0f4379be9fa38a5f93d169ba45960c2654cfd5
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => aec8e4f6b1b873e5\n    [X-B3-Traceid] => 68ada9c3a248e297751a14a1d2d17a42\n    [B3] => 68ada9c3a248e297751a14a1d2d17a42-aec8e4f6b1b873e5-1\n    [Traceparent] => 00-68ada9c3a248e297751a14a1d2d17a42-aec8e4f6b1b873e5-01\n    [X-Amzn-Trace-Id] => Root=1-68ada9c3-a248e297751a14a1d2d17a42;Parent=aec8e4f6b1b873e5;Sampled=1\n    [X-Adsk-Signature] => sha256=d4e4e686dd630e8ceccfed371fa4ecb7efc22a4495aa9cf24925b90a8131e3c5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 45c442b7-5c15-4cff-9d27-9b6d8fb3b657\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:34:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"45c442b7-5c15-4cff-9d27-9b6d8fb3b657","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033698","transactionId":"702811ea-549c-5fd2-8c9c-70cdff438630","quoteStatus":"Quoted","message":"Quote# Q-1033698 status changed to Quoted.","modifiedAt":"2025-08-26T12:34:10.995Z"},"publishedAt":"2025-08-26T12:34:11.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:38:38
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:36]  Provided signature: sha256=f1575a34b4de0c5ceaada227205148b0b6ea0946d9bedab8585576d7bff907fc
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:37]  Calculated signature: sha256=081926b36b275ce39033a9724fa11d931a07fe82994cacdf15d92455127e6630
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b5d46609fccfe765\n    [X-B3-Traceid] => 68adaacc485104390ab6094b0a0c4be6\n    [B3] => 68adaacc485104390ab6094b0a0c4be6-b5d46609fccfe765-1\n    [Traceparent] => 00-68adaacc485104390ab6094b0a0c4be6-b5d46609fccfe765-01\n    [X-Amzn-Trace-Id] => Root=1-68adaacc-485104390ab6094b0a0c4be6;Parent=b5d46609fccfe765;Sampled=1\n    [X-Adsk-Signature] => sha256=f1575a34b4de0c5ceaada227205148b0b6ea0946d9bedab8585576d7bff907fc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2099b0d8-f9d3-4869-a6c4-4eff4db35309\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"2099b0d8-f9d3-4869-a6c4-4eff4db35309","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Draft","message":"Quote# Q-1033723 status changed to Draft.","modifiedAt":"2025-08-26T12:38:36.397Z"},"publishedAt":"2025-08-26T12:38:36.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:38:38
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:36]  Provided signature: sha256=081926b36b275ce39033a9724fa11d931a07fe82994cacdf15d92455127e6630
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:37]  Calculated signature: sha256=081926b36b275ce39033a9724fa11d931a07fe82994cacdf15d92455127e6630
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c13aa2a6732591bd\n    [X-B3-Traceid] => 68adaacc485104390ab6094b0a0c4be6\n    [B3] => 68adaacc485104390ab6094b0a0c4be6-c13aa2a6732591bd-1\n    [Traceparent] => 00-68adaacc485104390ab6094b0a0c4be6-c13aa2a6732591bd-01\n    [X-Amzn-Trace-Id] => Root=1-68adaacc-485104390ab6094b0a0c4be6;Parent=c13aa2a6732591bd;Sampled=1\n    [X-Adsk-Signature] => sha256=081926b36b275ce39033a9724fa11d931a07fe82994cacdf15d92455127e6630\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2099b0d8-f9d3-4869-a6c4-4eff4db35309\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:38:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"2099b0d8-f9d3-4869-a6c4-4eff4db35309","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Draft","message":"Quote# Q-1033723 status changed to Draft.","modifiedAt":"2025-08-26T12:38:36.397Z"},"publishedAt":"2025-08-26T12:38:36.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:39:16
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:36]  Provided signature: sha256=3dc44ea317717f2a5bb86edef8ec560eb158b76836f51a9c437bec243047bd12
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:37]  Calculated signature: sha256=3dc44ea317717f2a5bb86edef8ec560eb158b76836f51a9c437bec243047bd12
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4200c2a1c6443bf1\n    [X-B3-Traceid] => 68adaaf1987ab2ca0f5344734f451791\n    [B3] => 68adaaf1987ab2ca0f5344734f451791-4200c2a1c6443bf1-1\n    [Traceparent] => 00-68adaaf1987ab2ca0f5344734f451791-4200c2a1c6443bf1-01\n    [X-Amzn-Trace-Id] => Root=1-68adaaf1-987ab2ca0f5344734f451791;Parent=4200c2a1c6443bf1;Sampled=1\n    [X-Adsk-Signature] => sha256=3dc44ea317717f2a5bb86edef8ec560eb158b76836f51a9c437bec243047bd12\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1ba8d03b-5c4a-4d76-83df-ee3be5991a6d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1ba8d03b-5c4a-4d76-83df-ee3be5991a6d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Quoted","message":"Quote# Q-1033723 status changed to Quoted.","modifiedAt":"2025-08-26T12:39:13.407Z"},"publishedAt":"2025-08-26T12:39:13.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:39:16
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:36]  Provided signature: sha256=7419bbb78bbb753ad80915d86351745cd733524f03cff1d18e909c819412d437
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:37]  Calculated signature: sha256=3dc44ea317717f2a5bb86edef8ec560eb158b76836f51a9c437bec243047bd12
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6dbfd93c8cb57704\n    [X-B3-Traceid] => 68adaaf1987ab2ca0f5344734f451791\n    [B3] => 68adaaf1987ab2ca0f5344734f451791-6dbfd93c8cb57704-1\n    [Traceparent] => 00-68adaaf1987ab2ca0f5344734f451791-6dbfd93c8cb57704-01\n    [X-Amzn-Trace-Id] => Root=1-68adaaf1-987ab2ca0f5344734f451791;Parent=6dbfd93c8cb57704;Sampled=1\n    [X-Adsk-Signature] => sha256=7419bbb78bbb753ad80915d86351745cd733524f03cff1d18e909c819412d437\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1ba8d03b-5c4a-4d76-83df-ee3be5991a6d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:39:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1ba8d03b-5c4a-4d76-83df-ee3be5991a6d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Quoted","message":"Quote# Q-1033723 status changed to Quoted.","modifiedAt":"2025-08-26T12:39:13.407Z"},"publishedAt":"2025-08-26T12:39:13.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:56:42
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:36]  Provided signature: sha256=52c0ba0e7d27218966f0ac951d8517960b349d209e1c28e06d806d3fe1336f7e
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:37]  Calculated signature: sha256=52c0ba0e7d27218966f0ac951d8517960b349d209e1c28e06d806d3fe1336f7e
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 54ee50a370d4c003\n    [X-B3-Traceid] => 68adaf07cce2ce84f02f6db2228bbe26\n    [B3] => 68adaf07cce2ce84f02f6db2228bbe26-54ee50a370d4c003-1\n    [Traceparent] => 00-68adaf07cce2ce84f02f6db2228bbe26-54ee50a370d4c003-01\n    [X-Amzn-Trace-Id] => Root=1-68adaf07-cce2ce84f02f6db2228bbe26;Parent=54ee50a370d4c003;Sampled=1\n    [X-Adsk-Signature] => sha256=52c0ba0e7d27218966f0ac951d8517960b349d209e1c28e06d806d3fe1336f7e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 06d85189-e61c-4159-9782-5ef72d6644fc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"06d85189-e61c-4159-9782-5ef72d6644fc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Order Submitted","message":"Quote# Q-1033723 status changed to Order Submitted.","modifiedAt":"2025-08-26T12:56:39.653Z"},"publishedAt":"2025-08-26T12:56:40.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:56:42
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:36]  Provided signature: sha256=0bd25a5427e9e58634a29066e315641cc1a38d7252809ddf460a06493bc62eb1
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:37]  Calculated signature: sha256=52c0ba0e7d27218966f0ac951d8517960b349d209e1c28e06d806d3fe1336f7e
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 62558bdfc7fa9c19\n    [X-B3-Traceid] => 68adaf07cce2ce84f02f6db2228bbe26\n    [B3] => 68adaf07cce2ce84f02f6db2228bbe26-62558bdfc7fa9c19-1\n    [Traceparent] => 00-68adaf07cce2ce84f02f6db2228bbe26-62558bdfc7fa9c19-01\n    [X-Amzn-Trace-Id] => Root=1-68adaf07-cce2ce84f02f6db2228bbe26;Parent=62558bdfc7fa9c19;Sampled=1\n    [X-Adsk-Signature] => sha256=0bd25a5427e9e58634a29066e315641cc1a38d7252809ddf460a06493bc62eb1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 06d85189-e61c-4159-9782-5ef72d6644fc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:56:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"06d85189-e61c-4159-9782-5ef72d6644fc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Order Submitted","message":"Quote# Q-1033723 status changed to Order Submitted.","modifiedAt":"2025-08-26T12:56:39.653Z"},"publishedAt":"2025-08-26T12:56:40.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:56:44
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:36]  Provided signature: sha256=2e35cd3f5974de2e8d45970b4257bb980fcc689818eb73473f2b6af773120d92
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:37]  Calculated signature: sha256=25cd4e1877b4152c89fed333ec27098b43de7fc4295926b4dd826a6e830f436d
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a1a884426daff928\n    [X-B3-Traceid] => 68adaf09dbebc2f5e02df7ed0b7666f1\n    [B3] => 68adaf09dbebc2f5e02df7ed0b7666f1-a1a884426daff928-1\n    [Traceparent] => 00-68adaf09dbebc2f5e02df7ed0b7666f1-a1a884426daff928-01\n    [X-Amzn-Trace-Id] => Root=1-68adaf09-dbebc2f5e02df7ed0b7666f1;Parent=a1a884426daff928;Sampled=1\n    [X-Adsk-Signature] => sha256=2e35cd3f5974de2e8d45970b4257bb980fcc689818eb73473f2b6af773120d92\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5fb2dfb2-fe76-473b-8aeb-5f1d81c2d965\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"5fb2dfb2-fe76-473b-8aeb-5f1d81c2d965","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Ordered","message":"Quote# Q-1033723 status changed to Ordered.","modifiedAt":"2025-08-26T12:56:41.467Z"},"publishedAt":"2025-08-26T12:56:41.000Z","csn":"5103159758"}
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 12:56:44
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:36]  Provided signature: sha256=25cd4e1877b4152c89fed333ec27098b43de7fc4295926b4dd826a6e830f436d
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:37]  Calculated signature: sha256=25cd4e1877b4152c89fed333ec27098b43de7fc4295926b4dd826a6e830f436d
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3adeee77b90e4d42\n    [X-B3-Traceid] => 68adaf09dbebc2f5e02df7ed0b7666f1\n    [B3] => 68adaf09dbebc2f5e02df7ed0b7666f1-3adeee77b90e4d42-1\n    [Traceparent] => 00-68adaf09dbebc2f5e02df7ed0b7666f1-3adeee77b90e4d42-01\n    [X-Amzn-Trace-Id] => Root=1-68adaf09-dbebc2f5e02df7ed0b7666f1;Parent=3adeee77b90e4d42;Sampled=1\n    [X-Adsk-Signature] => sha256=25cd4e1877b4152c89fed333ec27098b43de7fc4295926b4dd826a6e830f436d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5fb2dfb2-fe76-473b-8aeb-5f1d81c2d965\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 12:56:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"5fb2dfb2-fe76-473b-8aeb-5f1d81c2d965","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033723","transactionId":"449bac05-63d0-514e-94fb-cd22ced4cc51","quoteStatus":"Ordered","message":"Quote# Q-1033723 status changed to Ordered.","modifiedAt":"2025-08-26T12:56:41.467Z"},"publishedAt":"2025-08-26T12:56:41.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:06:07
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:36]  Provided signature: sha256=6eb87913ce01924cf81696716c3c7347ee06cd5ac6dcc8c7f84db53a70a84c40
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:37]  Calculated signature: sha256=d837bf3ec065631c09f3628e2253864874cb1b1c1bd3603ae5ea9639933fc7fa
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f2b10f8923c0bb7b\n    [X-B3-Traceid] => 68adb13d0dae134b429ae0cd3b2d5493\n    [B3] => 68adb13d0dae134b429ae0cd3b2d5493-f2b10f8923c0bb7b-1\n    [Traceparent] => 00-68adb13d0dae134b429ae0cd3b2d5493-f2b10f8923c0bb7b-01\n    [X-Amzn-Trace-Id] => Root=1-68adb13d-0dae134b429ae0cd3b2d5493;Parent=f2b10f8923c0bb7b;Sampled=1\n    [X-Adsk-Signature] => sha256=6eb87913ce01924cf81696716c3c7347ee06cd5ac6dcc8c7f84db53a70a84c40\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 58e4cb72-b8fb-46c6-9efc-a8a7f7901c2b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"58e4cb72-b8fb-46c6-9efc-a8a7f7901c2b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033852","transactionId":"42058ff8-1b2c-5fd8-b41a-eedfa8463c93","quoteStatus":"Draft","message":"Quote# Q-1033852 status changed to Draft.","modifiedAt":"2025-08-26T13:06:04.898Z"},"publishedAt":"2025-08-26T13:06:05.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:06:07
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:36]  Provided signature: sha256=d837bf3ec065631c09f3628e2253864874cb1b1c1bd3603ae5ea9639933fc7fa
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:37]  Calculated signature: sha256=d837bf3ec065631c09f3628e2253864874cb1b1c1bd3603ae5ea9639933fc7fa
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 347e10581f463cb1\n    [X-B3-Traceid] => 68adb13d0dae134b429ae0cd3b2d5493\n    [B3] => 68adb13d0dae134b429ae0cd3b2d5493-347e10581f463cb1-1\n    [Traceparent] => 00-68adb13d0dae134b429ae0cd3b2d5493-347e10581f463cb1-01\n    [X-Amzn-Trace-Id] => Root=1-68adb13d-0dae134b429ae0cd3b2d5493;Parent=347e10581f463cb1;Sampled=1\n    [X-Adsk-Signature] => sha256=d837bf3ec065631c09f3628e2253864874cb1b1c1bd3603ae5ea9639933fc7fa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 58e4cb72-b8fb-46c6-9efc-a8a7f7901c2b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:06:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"58e4cb72-b8fb-46c6-9efc-a8a7f7901c2b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033852","transactionId":"42058ff8-1b2c-5fd8-b41a-eedfa8463c93","quoteStatus":"Draft","message":"Quote# Q-1033852 status changed to Draft.","modifiedAt":"2025-08-26T13:06:04.898Z"},"publishedAt":"2025-08-26T13:06:05.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:07:21
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:36]  Provided signature: sha256=fc7392c3c49ff83d50f53235b8815ba0a5cc18075838f031e0b23516f972b778
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:37]  Calculated signature: sha256=201051dc0016ce1b995adfe78eaf7dbe79ebf88e37165fbdc186525afbf131d8
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ca39fcac9c4a7f37\n    [X-B3-Traceid] => 68adb186581523c3b4c1bc7769cdd338\n    [B3] => 68adb186581523c3b4c1bc7769cdd338-ca39fcac9c4a7f37-1\n    [Traceparent] => 00-68adb186581523c3b4c1bc7769cdd338-ca39fcac9c4a7f37-01\n    [X-Amzn-Trace-Id] => Root=1-68adb186-581523c3b4c1bc7769cdd338;Parent=ca39fcac9c4a7f37;Sampled=1\n    [X-Adsk-Signature] => sha256=fc7392c3c49ff83d50f53235b8815ba0a5cc18075838f031e0b23516f972b778\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 19f4d71f-3308-41e6-baa2-5d1d33b0898e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"19f4d71f-3308-41e6-baa2-5d1d33b0898e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033852","transactionId":"42058ff8-1b2c-5fd8-b41a-eedfa8463c93","quoteStatus":"Quoted","message":"Quote# Q-1033852 status changed to Quoted.","modifiedAt":"2025-08-26T13:07:18.457Z"},"publishedAt":"2025-08-26T13:07:18.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:07:21
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:36]  Provided signature: sha256=201051dc0016ce1b995adfe78eaf7dbe79ebf88e37165fbdc186525afbf131d8
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:37]  Calculated signature: sha256=201051dc0016ce1b995adfe78eaf7dbe79ebf88e37165fbdc186525afbf131d8
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b61983d9a174b906\n    [X-B3-Traceid] => 68adb186581523c3b4c1bc7769cdd338\n    [B3] => 68adb186581523c3b4c1bc7769cdd338-b61983d9a174b906-1\n    [Traceparent] => 00-68adb186581523c3b4c1bc7769cdd338-b61983d9a174b906-01\n    [X-Amzn-Trace-Id] => Root=1-68adb186-581523c3b4c1bc7769cdd338;Parent=b61983d9a174b906;Sampled=1\n    [X-Adsk-Signature] => sha256=201051dc0016ce1b995adfe78eaf7dbe79ebf88e37165fbdc186525afbf131d8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 19f4d71f-3308-41e6-baa2-5d1d33b0898e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:07:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"19f4d71f-3308-41e6-baa2-5d1d33b0898e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033852","transactionId":"42058ff8-1b2c-5fd8-b41a-eedfa8463c93","quoteStatus":"Quoted","message":"Quote# Q-1033852 status changed to Quoted.","modifiedAt":"2025-08-26T13:07:18.457Z"},"publishedAt":"2025-08-26T13:07:18.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:11:53
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:36]  Provided signature: sha256=e5a9ec52005fd4c34960d18fb83929d97f258e15064ca605001aeef95f9e82fd
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:37]  Calculated signature: sha256=ed5d61050e40a83f5a710c7081675d2a3c39bb97ce26d57d810900fb9359ce61
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1fde4641db9dd6d9\n    [X-B3-Traceid] => 68adb2976e1909f741de642738657486\n    [B3] => 68adb2976e1909f741de642738657486-1fde4641db9dd6d9-1\n    [Traceparent] => 00-68adb2976e1909f741de642738657486-1fde4641db9dd6d9-01\n    [X-Amzn-Trace-Id] => Root=1-68adb297-6e1909f741de642738657486;Parent=1fde4641db9dd6d9;Sampled=1\n    [X-Adsk-Signature] => sha256=e5a9ec52005fd4c34960d18fb83929d97f258e15064ca605001aeef95f9e82fd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 87945ffd-f3e7-498b-9ca4-f23ea6299af1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"87945ffd-f3e7-498b-9ca4-f23ea6299af1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56717693784427","status":"Active","quantity":1,"endDate":"2026-08-29","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T12:56:48.000+0000"},"publishedAt":"2025-08-26T13:11:51.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:11:53
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:36]  Provided signature: sha256=ed5d61050e40a83f5a710c7081675d2a3c39bb97ce26d57d810900fb9359ce61
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:37]  Calculated signature: sha256=ed5d61050e40a83f5a710c7081675d2a3c39bb97ce26d57d810900fb9359ce61
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7b8be7c6b492f698\n    [X-B3-Traceid] => 68adb2976e1909f741de642738657486\n    [B3] => 68adb2976e1909f741de642738657486-7b8be7c6b492f698-1\n    [Traceparent] => 00-68adb2976e1909f741de642738657486-7b8be7c6b492f698-01\n    [X-Amzn-Trace-Id] => Root=1-68adb297-6e1909f741de642738657486;Parent=7b8be7c6b492f698;Sampled=1\n    [X-Adsk-Signature] => sha256=ed5d61050e40a83f5a710c7081675d2a3c39bb97ce26d57d810900fb9359ce61\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 87945ffd-f3e7-498b-9ca4-f23ea6299af1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:11:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"87945ffd-f3e7-498b-9ca4-f23ea6299af1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56717693784427","status":"Active","quantity":1,"endDate":"2026-08-29","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-26T12:56:48.000+0000"},"publishedAt":"2025-08-26T13:11:51.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:12:48
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:36]  Provided signature: sha256=493f20ebe27e95781420178a4d8542d1c044a940b7cb2e91fb7aa0b5ce718e7b
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:37]  Calculated signature: sha256=493f20ebe27e95781420178a4d8542d1c044a940b7cb2e91fb7aa0b5ce718e7b
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7764754b21618235\n    [X-B3-Traceid] => 68adb2ce3cda0f9e083da7851a57069a\n    [B3] => 68adb2ce3cda0f9e083da7851a57069a-7764754b21618235-1\n    [Traceparent] => 00-68adb2ce3cda0f9e083da7851a57069a-7764754b21618235-01\n    [X-Amzn-Trace-Id] => Root=1-68adb2ce-3cda0f9e083da7851a57069a;Parent=7764754b21618235;Sampled=1\n    [X-Adsk-Signature] => sha256=493f20ebe27e95781420178a4d8542d1c044a940b7cb2e91fb7aa0b5ce718e7b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4bf0754b-1bd6-462e-afe0-fa5a744fb6b0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"4bf0754b-1bd6-462e-afe0-fa5a744fb6b0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Draft","message":"Quote# Q-1033885 status changed to Draft.","modifiedAt":"2025-08-26T13:12:46.021Z"},"publishedAt":"2025-08-26T13:12:46.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:12:48
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:36]  Provided signature: sha256=d1b5a8d1a490cdc0b9e7bfa8c35f3740a486add8ff88d3ac62b91c5c22e97d38
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:37]  Calculated signature: sha256=493f20ebe27e95781420178a4d8542d1c044a940b7cb2e91fb7aa0b5ce718e7b
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c01bad047c18ae93\n    [X-B3-Traceid] => 68adb2ce3cda0f9e083da7851a57069a\n    [B3] => 68adb2ce3cda0f9e083da7851a57069a-c01bad047c18ae93-1\n    [Traceparent] => 00-68adb2ce3cda0f9e083da7851a57069a-c01bad047c18ae93-01\n    [X-Amzn-Trace-Id] => Root=1-68adb2ce-3cda0f9e083da7851a57069a;Parent=c01bad047c18ae93;Sampled=1\n    [X-Adsk-Signature] => sha256=d1b5a8d1a490cdc0b9e7bfa8c35f3740a486add8ff88d3ac62b91c5c22e97d38\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4bf0754b-1bd6-462e-afe0-fa5a744fb6b0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:12:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"4bf0754b-1bd6-462e-afe0-fa5a744fb6b0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Draft","message":"Quote# Q-1033885 status changed to Draft.","modifiedAt":"2025-08-26T13:12:46.021Z"},"publishedAt":"2025-08-26T13:12:46.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:14:31
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:36]  Provided signature: sha256=c79692e8e8176240a25dd7d80f2d554c58acf57d1f213839f59cbbbcdfad5e83
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:37]  Calculated signature: sha256=2a71a5d86eaf7f2167f43e5fd03575e0fe253fe830263a82fd0c168d784495b0
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 319b6b67c44f4228\n    [X-B3-Traceid] => 68adb33561ef5e47a933711312bb8941\n    [B3] => 68adb33561ef5e47a933711312bb8941-319b6b67c44f4228-1\n    [Traceparent] => 00-68adb33561ef5e47a933711312bb8941-319b6b67c44f4228-01\n    [X-Amzn-Trace-Id] => Root=1-68adb335-61ef5e47a933711312bb8941;Parent=319b6b67c44f4228;Sampled=1\n    [X-Adsk-Signature] => sha256=c79692e8e8176240a25dd7d80f2d554c58acf57d1f213839f59cbbbcdfad5e83\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f198157b-7751-4a5a-86df-fb775ebb89ab\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"f198157b-7751-4a5a-86df-fb775ebb89ab","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Quoted","message":"Quote# Q-1033885 status changed to Quoted.","modifiedAt":"2025-08-26T13:14:29.102Z"},"publishedAt":"2025-08-26T13:14:29.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:14:31
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:36]  Provided signature: sha256=2a71a5d86eaf7f2167f43e5fd03575e0fe253fe830263a82fd0c168d784495b0
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:37]  Calculated signature: sha256=2a71a5d86eaf7f2167f43e5fd03575e0fe253fe830263a82fd0c168d784495b0
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 951095cdaf11c127\n    [X-B3-Traceid] => 68adb33561ef5e47a933711312bb8941\n    [B3] => 68adb33561ef5e47a933711312bb8941-951095cdaf11c127-1\n    [Traceparent] => 00-68adb33561ef5e47a933711312bb8941-951095cdaf11c127-01\n    [X-Amzn-Trace-Id] => Root=1-68adb335-61ef5e47a933711312bb8941;Parent=951095cdaf11c127;Sampled=1\n    [X-Adsk-Signature] => sha256=2a71a5d86eaf7f2167f43e5fd03575e0fe253fe830263a82fd0c168d784495b0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f198157b-7751-4a5a-86df-fb775ebb89ab\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:14:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"f198157b-7751-4a5a-86df-fb775ebb89ab","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Quoted","message":"Quote# Q-1033885 status changed to Quoted.","modifiedAt":"2025-08-26T13:14:29.102Z"},"publishedAt":"2025-08-26T13:14:29.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:16:43
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:36]  Provided signature: sha256=89fe713116d6a8598d327c6960d74bf2ad36bec6bc1e8b815d03acd0245c4edd
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:37]  Calculated signature: sha256=89fe713116d6a8598d327c6960d74bf2ad36bec6bc1e8b815d03acd0245c4edd
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 250a0ddafd463b05\n    [X-B3-Traceid] => 68adb3b84b64eeba45ec01c3ccb4c46d\n    [B3] => 68adb3b84b64eeba45ec01c3ccb4c46d-250a0ddafd463b05-1\n    [Traceparent] => 00-68adb3b84b64eeba45ec01c3ccb4c46d-250a0ddafd463b05-01\n    [X-Amzn-Trace-Id] => Root=1-68adb3b8-4b64eeba45ec01c3ccb4c46d;Parent=250a0ddafd463b05;Sampled=1\n    [X-Adsk-Signature] => sha256=89fe713116d6a8598d327c6960d74bf2ad36bec6bc1e8b815d03acd0245c4edd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 96c895b3-94c5-4b27-b7bd-03f45880abdb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"96c895b3-94c5-4b27-b7bd-03f45880abdb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033910","transactionId":"c941b554-0e63-5eee-a076-d473bb908f73","quoteStatus":"Draft","message":"Quote# Q-1033910 status changed to Draft.","modifiedAt":"2025-08-26T13:16:40.444Z"},"publishedAt":"2025-08-26T13:16:40.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:16:43
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:36]  Provided signature: sha256=ae9438430a5771c8c7abac5cfd89bd1ddca5daf4463379bfe0414ecd333eeb3b
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:37]  Calculated signature: sha256=89fe713116d6a8598d327c6960d74bf2ad36bec6bc1e8b815d03acd0245c4edd
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8304ffb03538567e\n    [X-B3-Traceid] => 68adb3b84b64eeba45ec01c3ccb4c46d\n    [B3] => 68adb3b84b64eeba45ec01c3ccb4c46d-8304ffb03538567e-1\n    [Traceparent] => 00-68adb3b84b64eeba45ec01c3ccb4c46d-8304ffb03538567e-01\n    [X-Amzn-Trace-Id] => Root=1-68adb3b8-4b64eeba45ec01c3ccb4c46d;Parent=8304ffb03538567e;Sampled=1\n    [X-Adsk-Signature] => sha256=ae9438430a5771c8c7abac5cfd89bd1ddca5daf4463379bfe0414ecd333eeb3b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 96c895b3-94c5-4b27-b7bd-03f45880abdb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:16:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"96c895b3-94c5-4b27-b7bd-03f45880abdb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033910","transactionId":"c941b554-0e63-5eee-a076-d473bb908f73","quoteStatus":"Draft","message":"Quote# Q-1033910 status changed to Draft.","modifiedAt":"2025-08-26T13:16:40.444Z"},"publishedAt":"2025-08-26T13:16:40.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:18:19
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:36]  Provided signature: sha256=3f3a3508db7a182579a5a4b225c0d853b2693c1d59cac225548b43b2d7d01d0d
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:37]  Calculated signature: sha256=882406c479a936eb8c08b43915f603e965ca4235a8c5da091dae273f57dead10
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 818d98b9f69e485d\n    [X-B3-Traceid] => 68adb419a93a5416a10172260fc39c1c\n    [B3] => 68adb419a93a5416a10172260fc39c1c-818d98b9f69e485d-1\n    [Traceparent] => 00-68adb419a93a5416a10172260fc39c1c-818d98b9f69e485d-01\n    [X-Amzn-Trace-Id] => Root=1-68adb419-a93a5416a10172260fc39c1c;Parent=818d98b9f69e485d;Sampled=1\n    [X-Adsk-Signature] => sha256=3f3a3508db7a182579a5a4b225c0d853b2693c1d59cac225548b43b2d7d01d0d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a06aea0e-8054-4280-b6da-1a4f6d499ba7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"a06aea0e-8054-4280-b6da-1a4f6d499ba7","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033910","transactionId":"c941b554-0e63-5eee-a076-d473bb908f73","quoteStatus":"Quoted","message":"Quote# Q-1033910 status changed to Quoted.","modifiedAt":"2025-08-26T13:18:17.347Z"},"publishedAt":"2025-08-26T13:18:17.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:18:19
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:36]  Provided signature: sha256=882406c479a936eb8c08b43915f603e965ca4235a8c5da091dae273f57dead10
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:37]  Calculated signature: sha256=882406c479a936eb8c08b43915f603e965ca4235a8c5da091dae273f57dead10
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 564701e43a208d6a\n    [X-B3-Traceid] => 68adb419a93a5416a10172260fc39c1c\n    [B3] => 68adb419a93a5416a10172260fc39c1c-564701e43a208d6a-1\n    [Traceparent] => 00-68adb419a93a5416a10172260fc39c1c-564701e43a208d6a-01\n    [X-Amzn-Trace-Id] => Root=1-68adb419-a93a5416a10172260fc39c1c;Parent=564701e43a208d6a;Sampled=1\n    [X-Adsk-Signature] => sha256=882406c479a936eb8c08b43915f603e965ca4235a8c5da091dae273f57dead10\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a06aea0e-8054-4280-b6da-1a4f6d499ba7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:18:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"a06aea0e-8054-4280-b6da-1a4f6d499ba7","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033910","transactionId":"c941b554-0e63-5eee-a076-d473bb908f73","quoteStatus":"Quoted","message":"Quote# Q-1033910 status changed to Quoted.","modifiedAt":"2025-08-26T13:18:17.347Z"},"publishedAt":"2025-08-26T13:18:17.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:18:54
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:36]  Provided signature: sha256=44bb734b32d5e7e914c493a24a5183b50758b0ba73073ff4e6f0311398ae6587
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:37]  Calculated signature: sha256=44bb734b32d5e7e914c493a24a5183b50758b0ba73073ff4e6f0311398ae6587
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e256bac999b887f4\n    [X-B3-Traceid] => 68adb43ca45f29c7b1bdcca4e8698a84\n    [B3] => 68adb43ca45f29c7b1bdcca4e8698a84-e256bac999b887f4-1\n    [Traceparent] => 00-68adb43ca45f29c7b1bdcca4e8698a84-e256bac999b887f4-01\n    [X-Amzn-Trace-Id] => Root=1-68adb43c-a45f29c7b1bdcca4e8698a84;Parent=e256bac999b887f4;Sampled=1\n    [X-Adsk-Signature] => sha256=44bb734b32d5e7e914c493a24a5183b50758b0ba73073ff4e6f0311398ae6587\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1c2911b7-3009-4822-8e3a-5c29e46ecb9e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"1c2911b7-3009-4822-8e3a-5c29e46ecb9e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-970058","transactionId":"95306435-d28f-594a-8f67-0531cfc7e636","quoteStatus":"Cancelled","message":"Quote# Q-970058 status changed to Cancelled.","modifiedAt":"2025-08-26T13:18:52.012Z"},"publishedAt":"2025-08-26T13:18:52.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:18:54
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:36]  Provided signature: sha256=6a9a16be4d8a5d88492a8bb9d1ea065b65def7c197046da1366db695c92ecfd2
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:37]  Calculated signature: sha256=44bb734b32d5e7e914c493a24a5183b50758b0ba73073ff4e6f0311398ae6587
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5130b11816b30185\n    [X-B3-Traceid] => 68adb43ca45f29c7b1bdcca4e8698a84\n    [B3] => 68adb43ca45f29c7b1bdcca4e8698a84-5130b11816b30185-1\n    [Traceparent] => 00-68adb43ca45f29c7b1bdcca4e8698a84-5130b11816b30185-01\n    [X-Amzn-Trace-Id] => Root=1-68adb43c-a45f29c7b1bdcca4e8698a84;Parent=5130b11816b30185;Sampled=1\n    [X-Adsk-Signature] => sha256=6a9a16be4d8a5d88492a8bb9d1ea065b65def7c197046da1366db695c92ecfd2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1c2911b7-3009-4822-8e3a-5c29e46ecb9e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:18:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"1c2911b7-3009-4822-8e3a-5c29e46ecb9e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-970058","transactionId":"95306435-d28f-594a-8f67-0531cfc7e636","quoteStatus":"Cancelled","message":"Quote# Q-970058 status changed to Cancelled.","modifiedAt":"2025-08-26T13:18:52.012Z"},"publishedAt":"2025-08-26T13:18:52.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:19:26
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:36]  Provided signature: sha256=a5c61bb6a6ce1ab61ef97298646c6d9baad585b9118ab398d4454d5abab3de77
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:37]  Calculated signature: sha256=5cc5c2c01b4172753207e242e21aa613e40d733313dbdda044a26d3d1077a34d
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 72588559b7589b53\n    [X-B3-Traceid] => 68adb45b1b0e5cc7029f7f10a4364a3b\n    [B3] => 68adb45b1b0e5cc7029f7f10a4364a3b-72588559b7589b53-1\n    [Traceparent] => 00-68adb45b1b0e5cc7029f7f10a4364a3b-72588559b7589b53-01\n    [X-Amzn-Trace-Id] => Root=1-68adb45b-1b0e5cc7029f7f10a4364a3b;Parent=72588559b7589b53;Sampled=1\n    [X-Adsk-Signature] => sha256=a5c61bb6a6ce1ab61ef97298646c6d9baad585b9118ab398d4454d5abab3de77\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b0f91f64-237b-4542-bc4c-71d30eff7e19\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"b0f91f64-237b-4542-bc4c-71d30eff7e19","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1016569","transactionId":"d6707371-1813-5bfb-a51c-126f9599ff84","quoteStatus":"Cancelled","message":"Quote# Q-1016569 status changed to Cancelled.","modifiedAt":"2025-08-26T13:19:23.827Z"},"publishedAt":"2025-08-26T13:19:24.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:19:26
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:36]  Provided signature: sha256=5cc5c2c01b4172753207e242e21aa613e40d733313dbdda044a26d3d1077a34d
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:37]  Calculated signature: sha256=5cc5c2c01b4172753207e242e21aa613e40d733313dbdda044a26d3d1077a34d
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 66229deb73bc9512\n    [X-B3-Traceid] => 68adb45b1b0e5cc7029f7f10a4364a3b\n    [B3] => 68adb45b1b0e5cc7029f7f10a4364a3b-66229deb73bc9512-1\n    [Traceparent] => 00-68adb45b1b0e5cc7029f7f10a4364a3b-66229deb73bc9512-01\n    [X-Amzn-Trace-Id] => Root=1-68adb45b-1b0e5cc7029f7f10a4364a3b;Parent=66229deb73bc9512;Sampled=1\n    [X-Adsk-Signature] => sha256=5cc5c2c01b4172753207e242e21aa613e40d733313dbdda044a26d3d1077a34d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b0f91f64-237b-4542-bc4c-71d30eff7e19\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:19:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"b0f91f64-237b-4542-bc4c-71d30eff7e19","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1016569","transactionId":"d6707371-1813-5bfb-a51c-126f9599ff84","quoteStatus":"Cancelled","message":"Quote# Q-1016569 status changed to Cancelled.","modifiedAt":"2025-08-26T13:19:23.827Z"},"publishedAt":"2025-08-26T13:19:24.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:37:28
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:36]  Provided signature: sha256=6ee1620eede1cfbebd425465373acad54d3f54ce1c25748fe4c8c62d87b123b0
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:37]  Calculated signature: sha256=16164e9297a8036783c984668a2f52f71b583f65ac27d28c5088c700a7b569ca
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c4f57b8458b340dc\n    [X-B3-Traceid] => 68adb895f1fc25d0405ead76f315aa57\n    [B3] => 68adb895f1fc25d0405ead76f315aa57-c4f57b8458b340dc-1\n    [Traceparent] => 00-68adb895f1fc25d0405ead76f315aa57-c4f57b8458b340dc-01\n    [X-Amzn-Trace-Id] => Root=1-68adb895-f1fc25d0405ead76f315aa57;Parent=c4f57b8458b340dc;Sampled=1\n    [X-Adsk-Signature] => sha256=6ee1620eede1cfbebd425465373acad54d3f54ce1c25748fe4c8c62d87b123b0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a29aa68b-6b7c-47d9-8f31-d85fd804bb02\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"a29aa68b-6b7c-47d9-8f31-d85fd804bb02","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Order Submitted","message":"Quote# Q-1033885 status changed to Order Submitted.","modifiedAt":"2025-08-26T13:37:25.596Z"},"publishedAt":"2025-08-26T13:37:25.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:37:28
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:36]  Provided signature: sha256=16164e9297a8036783c984668a2f52f71b583f65ac27d28c5088c700a7b569ca
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:37]  Calculated signature: sha256=16164e9297a8036783c984668a2f52f71b583f65ac27d28c5088c700a7b569ca
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2a065d02f104640f\n    [X-B3-Traceid] => 68adb895f1fc25d0405ead76f315aa57\n    [B3] => 68adb895f1fc25d0405ead76f315aa57-2a065d02f104640f-1\n    [Traceparent] => 00-68adb895f1fc25d0405ead76f315aa57-2a065d02f104640f-01\n    [X-Amzn-Trace-Id] => Root=1-68adb895-f1fc25d0405ead76f315aa57;Parent=2a065d02f104640f;Sampled=1\n    [X-Adsk-Signature] => sha256=16164e9297a8036783c984668a2f52f71b583f65ac27d28c5088c700a7b569ca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a29aa68b-6b7c-47d9-8f31-d85fd804bb02\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:37:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"a29aa68b-6b7c-47d9-8f31-d85fd804bb02","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Order Submitted","message":"Quote# Q-1033885 status changed to Order Submitted.","modifiedAt":"2025-08-26T13:37:25.596Z"},"publishedAt":"2025-08-26T13:37:25.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:37:29
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:36]  Provided signature: sha256=dd56d19593fedce65f1d5ae1be786b1075a0f4c56d7faa8a604491102ceacf30
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:37]  Calculated signature: sha256=3e69016bb8092c0b6824c53d5818f2996c6c9c81ebfcf814580f64d9517f581b
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 61ef6e276b6404e9\n    [X-B3-Traceid] => 68adb896dfce429e3a559054d3225422\n    [B3] => 68adb896dfce429e3a559054d3225422-61ef6e276b6404e9-1\n    [Traceparent] => 00-68adb896dfce429e3a559054d3225422-61ef6e276b6404e9-01\n    [X-Amzn-Trace-Id] => Root=1-68adb896-dfce429e3a559054d3225422;Parent=61ef6e276b6404e9;Sampled=1\n    [X-Adsk-Signature] => sha256=dd56d19593fedce65f1d5ae1be786b1075a0f4c56d7faa8a604491102ceacf30\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 30603e2d-0d29-43b1-9c02-bf7b14be5338\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"30603e2d-0d29-43b1-9c02-bf7b14be5338","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Ordered","message":"Quote# Q-1033885 status changed to Ordered.","modifiedAt":"2025-08-26T13:37:26.823Z"},"publishedAt":"2025-08-26T13:37:27.000Z","csn":"5103159758"}
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-26 13:37:29
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:36]  Provided signature: sha256=3e69016bb8092c0b6824c53d5818f2996c6c9c81ebfcf814580f64d9517f581b
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:37]  Calculated signature: sha256=3e69016bb8092c0b6824c53d5818f2996c6c9c81ebfcf814580f64d9517f581b
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3472cb6e93ed4fdf\n    [X-B3-Traceid] => 68adb896dfce429e3a559054d3225422\n    [B3] => 68adb896dfce429e3a559054d3225422-3472cb6e93ed4fdf-1\n    [Traceparent] => 00-68adb896dfce429e3a559054d3225422-3472cb6e93ed4fdf-01\n    [X-Amzn-Trace-Id] => Root=1-68adb896-dfce429e3a559054d3225422;Parent=3472cb6e93ed4fdf;Sampled=1\n    [X-Adsk-Signature] => sha256=3e69016bb8092c0b6824c53d5818f2996c6c9c81ebfcf814580f64d9517f581b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 30603e2d-0d29-43b1-9c02-bf7b14be5338\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-26 13:37:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"30603e2d-0d29-43b1-9c02-bf7b14be5338","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1033885","transactionId":"5ff62418-6ece-5793-8ccd-453fdbcd23fb","quoteStatus":"Ordered","message":"Quote# Q-1033885 status changed to Ordered.","modifiedAt":"2025-08-26T13:37:26.823Z"},"publishedAt":"2025-08-26T13:37:27.000Z","csn":"5103159758"}
