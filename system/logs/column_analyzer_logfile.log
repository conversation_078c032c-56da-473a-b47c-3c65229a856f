[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 09:06:35] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketechup_data
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 09:39:40] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 09:43:47] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 09:52:29] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 10:01:14] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 10:03:33] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:614] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:625] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:625] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:625] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:625] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:610] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:625] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:528] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:532] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:593] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:597] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:600] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:614] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 10:53:49] [data_table_generator.class.php:625] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:619] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:630] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:615] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:630] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:630] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:630] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:615] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:630] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:533] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:605] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 11:06:30] [data_table_generator.class.php:630] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:619] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:630] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:615] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:630] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:630] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:630] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:615] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:630] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:533] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:537] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:598] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:602] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:605] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:619] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 11:08:10] [data_table_generator.class.php:630] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:631] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:642] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:642] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:642] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:642] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:534] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:617] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 12:35:01] [data_table_generator.class.php:642] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:631] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:642] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:642] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:642] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:642] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:534] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:617] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":25.5,"reasoning":"Low confidence - added context prefix (score: 25.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"email":{"score":90.25,"matches":19,"total":20,"percentage":0.95},"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":4,"matches":1,"total":20,"percentage":0.05,"type":"product_name"},"uk_locations":{"score":35,"matches":14,"total":20,"percentage":0.7,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":25.5}}}
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_contact_email (confidence: 25.5%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-20 12:36:23] [data_table_generator.class.php:642] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:631] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:642] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_3, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_3
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_3
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_address_3: {"original_name":"end_customer_address_3","suggested_name":"end_customer_address_3","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_3 (confidence: 0%, suggested: end_customer_address_3)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_3
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:642] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:642] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:534] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:617] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-21 21:56:28] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:631] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:642] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:631] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:642] Using standard formatting for vendor_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:642] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:642] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:534] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:617] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":3.5,"reasoning":"Confidence too low to suggest changes (score: 3.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":7,"pattern_matches":{"first_names":{"score":7,"matches":2,"total":20,"percentage":0.1,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":3.5}}}
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_contact_name (confidence: 3.5%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-22 13:44:35] [data_table_generator.class.php:642] Using standard formatting for end_customer_contact_name
