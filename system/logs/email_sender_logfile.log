[email_sender] [2025-08-20 11:00:02] [send_email.php:11]  autodesk_api initialized.
[email_sender] [2025-08-20 11:00:02] [send_email.php:18]  Settings retrieved from database. Rules: -20,-15,-10,0,1,3,5,10,15,30,60,90, Settings days: ,true,true,true,true,true and time: 11
[email_sender] [2025-08-20 11:00:02] [send_email.php:38]  Settings retrieved from database.
[email_sender] [2025-08-20 11:00:02] [send_email.php:39]  Valid send time detected. Proceeding with email processing.
[email_sender] [2025-08-20 11:00:02] [send_email.php:51]  3767 renewable subscriptions retrieved.
[email_sender] [2025-08-20 11:00:02] [send_email.php:58]  Email template loaded successfully.
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-36033345: id 5 for customer: RIC<PERSON>RDSON ARCHITECTURE Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  570-48836366: id 20 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-01 with d/r: 41 and last sent: 19 (2025-08-01) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  570-67307146: id 28 for customer: THORNE RAINWATER SYSTEMS End date is 2025-11-02 with d/r: 73 and last sent: 17 (2025-08-03) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  564-89454291: id 29 for customer: IRD DESIGN Ltd End date is 2025-10-14 with d/r: 54 and last sent: 6 (2025-08-14) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:87]  575-00642075: id 43 for customer: IAN TITLEY DESIGN End date is 2025-08-26 with d/r: 5 and last sent: 5 (2025-08-15) so 10 (5 + 5 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:02] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-90800535: id 53 for customer: BARRIER ARCHITECTURAL SERVICES End date is 2025-06-25 with d/r: 56 and last sent: 2 (2025-08-18) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-21267569: id 75 for customer: apdesign End date is 2025-09-03 with d/r: 13 and last sent: 2 (2025-08-18) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:87]  572-48896886: id 101 for customer: DSB PROPERTY DESIGNS End date is 2025-09-20 with d/r: 30 and last sent: 30 (2025-07-21) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:02] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-37654136: id 103 for customer: Barden Chapman End date is 2025-11-13 with d/r: 84 and last sent: 6 (2025-08-14) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  567-63524427: id 104 for customer: CT GLASS End date is 2025-11-05 with d/r: 76 and last sent: 14 (2025-08-06) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-32890446: id 106 for customer: VKE Contractors End date is 2025-10-15 with d/r: 55 and last sent: 5 (2025-08-15) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-94161683: id 107 for customer: ADR Consulting End date is 2025-07-10 with d/r: 41 and last sent: 11 (2025-08-09) so 52 (41 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  574-97991797: id 117 for customer: ATELIERS DE FRANCE End date is 2025-07-31 with d/r: 20 and last sent: 5 (2025-08-15) so 25 (20 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  569-59178474: id 125 for customer: 360 Prism Ltd End date is 2025-07-07 with d/r: 44 and last sent: 14 (2025-08-06) so 58 (44 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:95]  564-87864877: id 127 for customer: Robert Brown Smith End date is 2025-10-08 with d/r: 48 and last sent: 12 (2025-08-08) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:02] [send_email.php:87]  570-41940359: id 135 for customer: ADAMS DESIGN ASSOCIATES Ltd End date is 2025-09-20 with d/r: 30 and last sent: 30 (2025-07-21) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:02] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-40188932: id 137 for customer: EDM London End date is 2025-10-30 with d/r: 70 and last sent: 20 (2025-07-31) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-96087431: id 152 for customer: Civils & Construction Solutions End date is 2025-07-23 with d/r: 28 and last sent: 1 (2025-08-19) so 29 (28 + 1 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  573-25195702: id 181 for customer: VKE Contractors End date is 2025-08-17 with d/r: 3 and last sent: 2 (2025-08-18) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-48568175: id 185 for customer: LITAC ENGINEERING End date is 2025-09-19 with d/r: 29 and last sent: 1 (2025-08-19) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-47469602: id 187 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-72954823: id 198 for customer: CISTEC Ltd End date is 2025-11-11 with d/r: 82 and last sent: 8 (2025-08-12) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-18644809: id 201 for customer: GASTECH ENGINEERING Ltd End date is 2025-08-16 with d/r: 4 and last sent: 1 (2025-08-19) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  563-47081948: id 207 for customer: Tristan Plant End date is 2025-11-09 with d/r: 80 and last sent: 10 (2025-08-10) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-60126176: id 210 for customer: CROSSOVER AV Ltd End date is 2025-10-21 with d/r: 61 and last sent: 29 (2025-07-22) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  575-01683044: id 222 for customer: JCP Consulting Ltd End date is 2025-09-06 with d/r: 16 and last sent: 14 (2025-08-06) so 30 (16 + 14 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  569-55575321: id 227 for customer: JEL Renewables Ltd End date is 2025-06-30 with d/r: 51 and last sent: 5 (2025-08-15) so 56 (51 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-33202457: id 242 for customer: Promack Ltd End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-54042242: id 243 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  569-99919365: id 244 for customer: REALISATION BY DESIGN Ltd End date is 2025-09-03 with d/r: 13 and last sent: 2 (2025-08-18) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-58045471: id 261 for customer: BOWER EDLESTON End date is 2025-10-27 with d/r: 67 and last sent: 23 (2025-07-28) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-93279082: id 263 for customer: Blue Aardvark Joinery End date is 2025-07-04 with d/r: 47 and last sent: 1 (2025-08-19) so 48 (47 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  569-91563509: id 295 for customer: Concept Eight Architects End date is 2025-08-23 with d/r: 2 and last sent: 1 (2025-08-19) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  : id 308 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-09-19 with d/r: 29 and last sent: 1 (2025-08-19) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-60446078: id 325 for customer: Leech Mechanical Services Ltd End date is 2025-10-21 with d/r: 61 and last sent: 29 (2025-07-22) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  : id 332 for customer: K BAVA ARCHITECTS Ltd End date is 2025-10-02 with d/r: 42 and last sent: 18 (2025-08-02) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  561-95896659: id 343 for customer: Davies Mr End date is 2025-10-24 with d/r: 64 and last sent: 26 (2025-07-25) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-12556332: id 352 for customer: Whitaker Lianne End date is 2025-06-24 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-27481734: id 353 for customer: Glent Engineering Ltd End date is 2025-06-10 with d/r: 71 and last sent: 11 (2025-08-09) so 82 (71 + 11 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-41393046: id 367 for customer: TUFCOT ENGINEERING Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  563-47765403: id 368 for customer: AMALGAM MODELMAKERS Ltd End date is 2025-11-12 with d/r: 83 and last sent: 7 (2025-08-13) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-45667281: id 369 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-09-06 with d/r: 16 and last sent: 14 (2025-08-06) so 30 (16 + 14 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-98994263: id 380 for customer: NGP Architecture Ltd End date is 2025-08-11 with d/r: 9 and last sent: 1 (2025-08-19) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  : id 399 for customer: STRENGER End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-51541127: id 419 for customer: O'MAC CONSTRUCTION End date is 2025-10-03 with d/r: 43 and last sent: 17 (2025-08-03) so 60 (43 + 17 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-31780884: id 426 for customer: J W CONTRACT SERVICES Ltd End date is 2025-10-08 with d/r: 48 and last sent: 12 (2025-08-08) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  567-64336356: id 448 for customer: Bay Building Services End date is 2025-11-06 with d/r: 77 and last sent: 13 (2025-08-07) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-40116973: id 472 for customer: Aecor Marine Ltd End date is 2025-10-30 with d/r: 70 and last sent: 20 (2025-07-31) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  : id 476 for customer: Blue Aardvark Joinery End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-59689919: id 477 for customer: NIC Ltd End date is 2025-11-02 with d/r: 73 and last sent: 17 (2025-08-03) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  567-56252692: id 478 for customer: Cornerstone Projects Ltd End date is 2025-10-24 with d/r: 64 and last sent: 26 (2025-07-25) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-31811921: id 481 for customer: CAMB MACHINE KNIVES End date is 2025-07-19 with d/r: 32 and last sent: 2 (2025-08-18) so 34 (32 + 2 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  572-95952873: id 492 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-12406719: id 502 for customer: Project Marble Ltd End date is 2025-07-16 with d/r: 35 and last sent: 5 (2025-08-15) so 40 (35 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-00229171: id 503 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-09-03 with d/r: 13 and last sent: 2 (2025-08-18) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-85203338: id 511 for customer: ATELIERS DE FRANCE End date is 2025-06-02 with d/r: 79 and last sent: 3 (2025-08-17) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-27210206: id 522 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 38 and last sent: 22 (2025-07-29) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  : id 523 for customer: HEWITSON Ltd End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  566-96027273: id 525 for customer: Frame Development End date is 2025-07-30 with d/r: 21 and last sent: 6 (2025-08-14) so 27 (21 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  567-64333386: id 534 for customer: Bay Building Services End date is 2025-11-06 with d/r: 77 and last sent: 13 (2025-08-07) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-40524951: id 537 for customer: Manor Construction (South Yorkshire) Ltd End date is 2025-09-16 with d/r: 26 and last sent: 4 (2025-08-16) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-65006068: id 556 for customer: McAuliffe Group End date is 2025-10-28 with d/r: 68 and last sent: 22 (2025-07-29) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  574-25454704: id 557 for customer: EVOLVE INTEGRATED SOLUTIONS End date is 2025-09-20 with d/r: 30 and last sent: 30 (2025-07-21) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  572-55764387: id 559 for customer: Scomac Services End date is 2025-10-20 with d/r: 60 and last sent: 30 (2025-07-21) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-04847152: id 566 for customer: ALLAN MCGOVERN End date is 2025-06-06 with d/r: 75 and last sent: 15 (2025-08-05) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  563-44078413: id 586 for customer: Ground Condition Consultants L End date is 2025-11-01 with d/r: 72 and last sent: 18 (2025-08-02) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  564-98116688: id 595 for customer: Peter Cogill End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  : id 622 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-34812927: id 623 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  573-27543694: id 634 for customer: BRU Fabrication End date is 2025-08-31 with d/r: 10 and last sent: 5 (2025-08-15) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-36183822: id 636 for customer: Murwell Consulting Engineers Ltd End date is 2025-10-11 with d/r: 51 and last sent: 9 (2025-08-11) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-94161782: id 660 for customer: ADR Consulting End date is 2025-07-10 with d/r: 41 and last sent: 11 (2025-08-09) so 52 (41 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-35853527: id 662 for customer: Utility Consultancy & Engineer End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  564-79249198: id 685 for customer: TURNBULL SURVEYING End date is 2025-09-16 with d/r: 26 and last sent: 4 (2025-08-16) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  572-55812392: id 687 for customer: Hewer FM Ltd End date is 2025-10-20 with d/r: 60 and last sent: 30 (2025-07-21) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  573-31816149: id 697 for customer: All Design (Scotland) Ltd End date is 2025-09-20 with d/r: 30 and last sent: 30 (2025-07-21) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-24998408: id 698 for customer: ZinCo Green Roof Systems End date is 2025-09-18 with d/r: 28 and last sent: 2 (2025-08-18) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  574-83243839: id 699 for customer: HALLAM PARTNERSHIP Ltd End date is 2025-05-22 with d/r: 90 and last sent: 2 (2025-08-18) so 92 (90 + 2 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  574-19310149: id 704 for customer: ADR Consulting End date is 2025-08-21 with d/r: 0 and last sent: 1 (2025-08-19) so 1 (0 + 1 is above rule 0: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-95439412: id 705 for customer: ADF Paris End date is 2025-07-18 with d/r: 33 and last sent: 3 (2025-08-17) so 36 (33 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  569-67099416: id 716 for customer: COVENTRY CONSTRUCTION Ltd End date is 2025-07-20 with d/r: 31 and last sent: 1 (2025-08-19) so 32 (31 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-27784286: id 719 for customer: Clive Martin End date is 2025-10-02 with d/r: 42 and last sent: 18 (2025-08-02) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  777-11777898: id 737 for customer: NARRACOTT S End date is 2025-10-15 with d/r: 55 and last sent: 5 (2025-08-15) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-37459245: id 742 for customer: Southern Aluminium Upvc End date is 2025-11-12 with d/r: 83 and last sent: 7 (2025-08-13) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-16408490: id 762 for customer: One Design Architectural Services End date is 2025-06-30 with d/r: 51 and last sent: 5 (2025-08-15) so 56 (51 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-13128704: id 785 for customer: Workbox UK Ltd End date is 2025-06-15 with d/r: 66 and last sent: 6 (2025-08-14) so 72 (66 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  573-19751230: id 789 for customer: DLG Architects Leeds End date is 2025-07-17 with d/r: 34 and last sent: 4 (2025-08-16) so 38 (34 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  574-23764132: id 795 for customer: Geoffrey Robinson Ltd End date is 2025-09-12 with d/r: 22 and last sent: 8 (2025-08-12) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-38101832: id 796 for customer: STS STORAGE SYSTEMS End date is 2025-09-13 with d/r: 23 and last sent: 7 (2025-08-13) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  564-46205357: id 802 for customer: JAMES MACKINTOSH ARCHITECTS End date is 2025-06-13 with d/r: 68 and last sent: 8 (2025-08-12) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  569-82407105: id 823 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-38111433: id 840 for customer: TIM HB Ltd End date is 2025-09-13 with d/r: 23 and last sent: 7 (2025-08-13) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  570-39779536: id 841 for customer: PLACEFIRST CONSTRUCTION End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:95]  567-37574551: id 842 for customer: AMW DESIGN Ltd End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:03] [send_email.php:87]  573-23630636: id 846 for customer: GRK CIVILS Ltd End date is 2025-08-07 with d/r: 13 and last sent: 3 (2025-08-17) so 16 (13 + 3 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 859 for customer: SeAH Wind Ltd End date is 2025-10-22 with d/r: 62 and last sent: 28 (2025-07-23) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-27210404: id 860 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 38 and last sent: 22 (2025-07-29) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  571-92575619: id 872 for customer: IAIN MACRAE End date is 2025-05-25 with d/r: 87 and last sent: 3 (2025-08-17) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-49274889: id 875 for customer: NIC Ltd End date is 2025-09-22 with d/r: 32 and last sent: 28 (2025-07-23) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  569-57473650: id 876 for customer: Surrey Tech Services Ltd End date is 2025-07-05 with d/r: 46 and last sent: 16 (2025-08-04) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  572-55701536: id 891 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-10-20 with d/r: 60 and last sent: 30 (2025-07-21) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-06550095: id 892 for customer: ark architecture + design End date is 2025-06-15 with d/r: 66 and last sent: 6 (2025-08-14) so 72 (66 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  567-38296113: id 917 for customer: Wensley & Lawz Ltd End date is 2025-09-25 with d/r: 35 and last sent: 25 (2025-07-26) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-51548253: id 922 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-10-03 with d/r: 43 and last sent: 17 (2025-08-03) so 60 (43 + 17 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-40455681: id 938 for customer: GREENCOLD End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  567-60486446: id 956 for customer: NARRACOTT S End date is 2025-10-30 with d/r: 70 and last sent: 20 (2025-07-31) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-30772311: id 957 for customer: TCS CAD & BIM Solutions Ltd End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  571-76923876: id 959 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 24 and last sent: 6 (2025-08-14) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-46238294: id 968 for customer: Furness Green Partnership End date is 2025-09-08 with d/r: 18 and last sent: 12 (2025-08-08) so 30 (18 + 12 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 971 for customer: Cladtech Systems Industrial Roofing & Cladding End date is 2025-09-22 with d/r: 32 and last sent: 28 (2025-07-23) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  574-21713967: id 976 for customer: Rkb Electrical Ltd End date is 2025-09-05 with d/r: 15 and last sent: 15 (2025-08-05) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  569-38279528: id 984 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-04 with d/r: 77 and last sent: 1 (2025-08-19) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  569-33468427: id 988 for customer: CUTLER & MACLEAN Ltd End date is 2025-05-28 with d/r: 84 and last sent: 8 (2025-08-12) so 92 (84 + 8 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-36040471: id 991 for customer: Truck-Lite Europe Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  569-71004061: id 993 for customer: John Woodvine End date is 2025-08-05 with d/r: 15 and last sent: 2 (2025-08-18) so 17 (15 + 2 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  555-79670111: id 1006 for customer: Mark Thornton End date is 2025-10-01 with d/r: 41 and last sent: 19 (2025-08-01) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  570-61955320: id 1010 for customer: EMILY ESTATE UK Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-27277512: id 1022 for customer: Broad Planning and Architecture End date is 2025-09-28 with d/r: 38 and last sent: 22 (2025-07-29) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-38345113: id 1040 for customer: Spirotech SRD Group Ltd End date is 2025-11-16 with d/r: 87 and last sent: 3 (2025-08-17) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-34733842: id 1051 for customer: SeAH Wind Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  564-79253653: id 1070 for customer: Philip Bingham Associates End date is 2025-09-16 with d/r: 26 and last sent: 4 (2025-08-16) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1075 for customer: MQM Ltd End date is 2025-10-28 with d/r: 68 and last sent: 22 (2025-07-29) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  572-13383998: id 1082 for customer: EDM-London End date is 2025-06-27 with d/r: 54 and last sent: 8 (2025-08-12) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  777-13196806: id 1083 for customer: BLACKFRIARS STAGING Ltd End date is 2025-09-06 with d/r: 16 and last sent: 14 (2025-08-06) so 30 (16 + 14 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-31115373: id 1085 for customer: Syntegon Telstar UK Ltd End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-38587739: id 1092 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-23 with d/r: 63 and last sent: 27 (2025-07-24) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  570-59618709: id 1120 for customer: JOHN FOWKES ARCHITECTS End date is 2025-10-20 with d/r: 60 and last sent: 30 (2025-07-21) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  564-84641410: id 1121 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 40 and last sent: 20 (2025-07-31) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-37286526: id 1125 for customer: AIR HANDLING SYSTEMS End date is 2025-11-10 with d/r: 81 and last sent: 9 (2025-08-11) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  563-39096670: id 1126 for customer: COOLSPOT Ltd End date is 2025-10-19 with d/r: 59 and last sent: 1 (2025-08-19) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-57898459: id 1144 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  571-61094664: id 1146 for customer: FRANK H DALE Ltd End date is 2025-10-14 with d/r: 54 and last sent: 6 (2025-08-14) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-36056534: id 1147 for customer: Bauer Consult End date is 2025-10-11 with d/r: 51 and last sent: 9 (2025-08-11) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-50153433: id 1158 for customer: IMPC Ltd End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-92526838: id 1161 for customer: Chris Hinchliff End date is 2025-06-30 with d/r: 51 and last sent: 5 (2025-08-15) so 56 (51 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-46580467: id 1162 for customer: Architectural Designs Derby End date is 2025-09-09 with d/r: 19 and last sent: 11 (2025-08-09) so 30 (19 + 11 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  567-58754007: id 1176 for customer: Electrical Automation Solution End date is 2025-10-28 with d/r: 68 and last sent: 22 (2025-07-29) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1178 for customer: HERMANTES STUDIO End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-36391678: id 1181 for customer: Daniela Favero End date is 2025-10-12 with d/r: 52 and last sent: 8 (2025-08-12) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-37910746: id 1183 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  567-65874005: id 1184 for customer: Anchorpoint Interiors End date is 2025-11-10 with d/r: 81 and last sent: 9 (2025-08-11) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-36350405: id 1197 for customer: DOWEN FARMER ARCHITECTS Ltd End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  563-24896167: id 1211 for customer: INTELECT MECHICAL End date is 2025-09-17 with d/r: 27 and last sent: 3 (2025-08-17) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1216 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-51912398: id 1246 for customer: C BARLEY SERVICES End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  570-64821075: id 1251 for customer: ALEXANDER WATERWORTH End date is 2025-10-28 with d/r: 68 and last sent: 22 (2025-07-29) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-97316361: id 1278 for customer: Arkitectonic End date is 2025-07-30 with d/r: 21 and last sent: 6 (2025-08-14) so 27 (21 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-32020515: id 1292 for customer: Abbey Joinery Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-85170872: id 1294 for customer: Igloo Design and Consultancy Ltd End date is 2025-06-09 with d/r: 72 and last sent: 12 (2025-08-08) so 84 (72 + 12 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-21464638: id 1296 for customer: HGCE Ltd End date is 2025-09-04 with d/r: 14 and last sent: 1 (2025-08-19) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-62802431: id 1297 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  565-04736147: id 1298 for customer: B-12 Development Ltd End date is 2025-11-14 with d/r: 85 and last sent: 5 (2025-08-15) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-60416726: id 1312 for customer: Nuckey James End date is 2025-11-07 with d/r: 78 and last sent: 12 (2025-08-08) so 90 (78 + 12 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  570-74943521: id 1313 for customer: COLSEC Ltd End date is 2025-11-17 with d/r: 88 and last sent: 2 (2025-08-18) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  564-87293072: id 1359 for customer: TANDEM ARCHITECTS Ltd End date is 2025-10-07 with d/r: 47 and last sent: 13 (2025-08-07) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-93532668: id 1363 for customer: HGCE Ltd End date is 2025-07-07 with d/r: 44 and last sent: 14 (2025-08-06) so 58 (44 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  566-87318554: id 1364 for customer: JAMESDODDRELL:ARCHITECT Ltd End date is 2025-07-17 with d/r: 34 and last sent: 4 (2025-08-16) so 38 (34 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  575-01066402: id 1365 for customer: southeast design solutions Ltd End date is 2025-08-28 with d/r: 7 and last sent: 3 (2025-08-17) so 10 (7 + 3 is below rule 10: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1402 for customer: Simon Farr End date is 2025-10-27 with d/r: 67 and last sent: 23 (2025-07-28) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-94018361: id 1404 for customer: SP Joinery Design solutions End date is 2025-07-09 with d/r: 42 and last sent: 12 (2025-08-08) so 54 (42 + 12 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-36255356: id 1419 for customer: HYDROMAX Inc. Ltd End date is 2025-11-01 with d/r: 72 and last sent: 18 (2025-08-02) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  567-21993480: id 1431 for customer: ALMA SHEET METAL LTD End date is 2025-09-03 with d/r: 13 and last sent: 2 (2025-08-18) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-94376298: id 1434 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-34367222: id 1435 for customer: Design Coalition End date is 2025-10-23 with d/r: 63 and last sent: 27 (2025-07-24) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1456 for customer: Cladtech Systems Industrial Roofing & Cladding End date is 2025-09-22 with d/r: 32 and last sent: 28 (2025-07-23) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  575-01669285: id 1467 for customer: SeAH Wind Ltd End date is 2025-09-02 with d/r: 12 and last sent: 3 (2025-08-17) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-32724853: id 1470 for customer: HMA Ventilation Ltd End date is 2025-10-12 with d/r: 52 and last sent: 8 (2025-08-12) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  570-38071148: id 1474 for customer: BRODIE PLANNING ASSOCIATES Ltd End date is 2025-09-13 with d/r: 23 and last sent: 7 (2025-08-13) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1491 for customer: Abbey Joinery Ltd End date is 2025-10-03 with d/r: 43 and last sent: 17 (2025-08-03) so 60 (43 + 17 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-05573266: id 1496 for customer: ATELIERS DE FRANCE End date is 2025-06-11 with d/r: 70 and last sent: 10 (2025-08-10) so 80 (70 + 10 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-14186398: id 1498 for customer: RUSHMON Ltd End date is 2025-06-22 with d/r: 59 and last sent: 1 (2025-08-19) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-19399186: id 1508 for customer: Roger Betts End date is 2025-07-07 with d/r: 44 and last sent: 14 (2025-08-06) so 58 (44 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-33205526: id 1512 for customer: JDW Architects End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-32125658: id 1520 for customer: DLG Architects Leeds End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  569-93809751: id 1527 for customer: CPI Group End date is 2025-08-25 with d/r: 4 and last sent: 1 (2025-08-19) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-25275550: id 1542 for customer: Kevin Judson End date is 2025-09-19 with d/r: 29 and last sent: 1 (2025-08-19) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-36941115: id 1545 for customer: James M Brown Ltd End date is 2025-10-16 with d/r: 56 and last sent: 4 (2025-08-16) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  569-10991547: id 1552 for customer: Micam Ltd End date is 2025-05-22 with d/r: 90 and last sent: 2 (2025-08-18) so 92 (90 + 2 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-94152181: id 1553 for customer: Artium Construction Ltd End date is 2025-07-10 with d/r: 41 and last sent: 11 (2025-08-09) so 52 (41 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1554 for customer: ATELIERS DE FRANCE End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  567-47838438: id 1571 for customer: FURNESS GREEN PARTNERSHIP End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-98883704: id 1587 for customer: STORTFORD HOLDINGS Ltd End date is 2025-08-08 with d/r: 12 and last sent: 2 (2025-08-18) so 14 (12 + 2 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  570-48504685: id 1607 for customer: IRELAND ALBRECHT LANDSCAPE Ltd End date is 2025-09-30 with d/r: 40 and last sent: 20 (2025-07-31) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-06066877: id 1611 for customer: VKE Contractors End date is 2025-06-13 with d/r: 68 and last sent: 8 (2025-08-12) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  564-89630277: id 1650 for customer: KOHA ARCHITECTS Ltd End date is 2025-10-14 with d/r: 54 and last sent: 6 (2025-08-14) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  575-00807173: id 1653 for customer: ARDERN & DRUGGAN Ltd End date is 2025-08-27 with d/r: 6 and last sent: 4 (2025-08-16) so 10 (6 + 4 is below rule 10: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  563-37468456: id 1673 for customer: GM Steel Newark Ltd End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-98881031: id 1676 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-32016852: id 1707 for customer: J W CONTRACT SERVICES Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1708 for customer: JAWDESIGN End date is 2025-10-16 with d/r: 56 and last sent: 4 (2025-08-16) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-29853680: id 1709 for customer: ECS BATH Ltd End date is 2025-09-12 with d/r: 22 and last sent: 8 (2025-08-12) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  572-54600981: id 1710 for customer: CONSULTANCY 26 SERVICES LTD End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-61569683: id 1717 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-33673007: id 1725 for customer: Kevin Judson End date is 2025-09-28 with d/r: 38 and last sent: 22 (2025-07-29) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-04208338: id 1739 for customer: L & Architects End date is 2025-06-04 with d/r: 77 and last sent: 1 (2025-08-19) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-14195902: id 1776 for customer: Thermal Earth Ltd End date is 2025-06-22 with d/r: 59 and last sent: 1 (2025-08-19) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  : id 1777 for customer: PCC CONSULTANTS Ltd End date is 2025-11-01 with d/r: 72 and last sent: 18 (2025-08-02) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  573-35824229: id 1782 for customer: VKE Contractors End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:95]  574-27569896: id 1788 for customer: AP4 LLP End date is 2025-10-01 with d/r: 41 and last sent: 19 (2025-08-01) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:04] [send_email.php:87]  572-11448156: id 1804 for customer: ADF Paris End date is 2025-06-23 with d/r: 58 and last sent: 4 (2025-08-16) so 62 (58 + 4 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 1814 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-24035957: id 1816 for customer: PREMIER CAD Ltd End date is 2025-08-09 with d/r: 11 and last sent: 1 (2025-08-19) so 12 (11 + 1 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 1829 for customer: ARTFORM ARCHITECTS End date is 2025-10-06 with d/r: 46 and last sent: 14 (2025-08-06) so 60 (46 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-96089708: id 1874 for customer: V4 ARCHITECTS Ltd End date is 2025-07-23 with d/r: 28 and last sent: 1 (2025-08-19) so 29 (28 + 1 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  574-94938079: id 1889 for customer: Wiveliscombe Joinery Ltd End date is 2025-07-21 with d/r: 30 and last sent: 1 (2025-08-19) so 31 (30 + 1 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-35472356: id 1893 for customer: LIFESTYLE INTERIORS Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-04006645: id 1904 for customer: Jameson Builders End date is 2025-10-21 with d/r: 61 and last sent: 29 (2025-07-22) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-48342359: id 1907 for customer: DAVID HALLAM Ltd End date is 2025-09-30 with d/r: 40 and last sent: 20 (2025-07-31) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  564-82289555: id 1908 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-59716681: id 1922 for customer: McAuliffe Group End date is 2025-10-29 with d/r: 69 and last sent: 21 (2025-07-30) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  564-88436286: id 1926 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-12577755: id 1942 for customer: ATELIERS DE FRANCE End date is 2025-07-17 with d/r: 34 and last sent: 4 (2025-08-16) so 38 (34 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-30137158: id 1944 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  564-93155733: id 1955 for customer: MRM ELECTRICAL SOLUTIONS Ltd End date is 2025-10-23 with d/r: 63 and last sent: 27 (2025-07-24) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  563-46766006: id 1972 for customer: BSBA TEES Ltd End date is 2025-11-10 with d/r: 81 and last sent: 9 (2025-08-11) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  575-01512206: id 1982 for customer: Ramsay McMichael Consulting End date is 2025-09-01 with d/r: 11 and last sent: 4 (2025-08-16) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-50418836: id 1989 for customer: Elevation One Building Design Ltd End date is 2025-10-16 with d/r: 56 and last sent: 4 (2025-08-16) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  569-91701485: id 1991 for customer: Eyeking Ltd End date is 2025-08-23 with d/r: 2 and last sent: 1 (2025-08-19) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 1992 for customer: VKE Contractors End date is 2025-10-01 with d/r: 41 and last sent: 19 (2025-08-01) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-33245480: id 2001 for customer: MAX BUSTON Ltd End date is 2025-09-16 with d/r: 26 and last sent: 4 (2025-08-16) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-59027997: id 2003 for customer: RUSSELL JONES Ltd End date is 2025-10-19 with d/r: 59 and last sent: 1 (2025-08-19) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2007 for customer: Shields (Driffield) Ltd End date is 2025-09-30 with d/r: 40 and last sent: 20 (2025-07-31) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2008 for customer: George Everett End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-64043212: id 2020 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-85627465: id 2040 for customer: COHANIM ARCHITECTURE End date is 2025-06-04 with d/r: 77 and last sent: 1 (2025-08-19) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-00538372: id 2054 for customer: JEL Renewables Ltd End date is 2025-05-25 with d/r: 87 and last sent: 3 (2025-08-17) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-58721541: id 2055 for customer: Dendra Consulting Ltd End date is 2025-10-28 with d/r: 68 and last sent: 22 (2025-07-29) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-37196454: id 2069 for customer: COWAL DESIGN CONSULTANTS End date is 2025-11-08 with d/r: 79 and last sent: 11 (2025-08-09) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  574-96315974: id 2074 for customer: Stair Formwork End date is 2025-07-24 with d/r: 27 and last sent: 4 (2025-08-16) so 31 (27 + 4 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  567-27391729: id 2078 for customer: CUBIC BUILDING SURVEYING End date is 2025-09-05 with d/r: 15 and last sent: 15 (2025-08-05) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2094 for customer: AGW ELECTRICAL SERVICES Ltd End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-40734292: id 2115 for customer: John Farah End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  564-86383747: id 2120 for customer: KT Fabrications Ltd End date is 2025-10-03 with d/r: 43 and last sent: 17 (2025-08-03) so 60 (43 + 17 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-33329619: id 2123 for customer: Aughton Automation Ltd End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2136 for customer: Washington Waterjet Ltd End date is 2025-10-02 with d/r: 42 and last sent: 18 (2025-08-02) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  571-91785365: id 2138 for customer: HIGHALL DEVELOPMENTS End date is 2025-05-23 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-83903340: id 2140 for customer: CHESTERFORD SURVEYS Ltd End date is 2025-06-10 with d/r: 71 and last sent: 11 (2025-08-09) so 82 (71 + 11 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  563-35958127: id 2142 for customer: IRD DESIGN Ltd End date is 2025-10-15 with d/r: 55 and last sent: 5 (2025-08-15) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  573-15237562: id 2143 for customer: TFG STAGE TECHNOLOGY Ltd End date is 2025-06-27 with d/r: 54 and last sent: 8 (2025-08-12) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  564-89015021: id 2151 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-19349768: id 2153 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 24 and last sent: 6 (2025-08-14) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-36391381: id 2158 for customer: Aecor Marine Ltd End date is 2025-10-12 with d/r: 52 and last sent: 8 (2025-08-12) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2164 for customer: JAWDESIGN End date is 2025-10-16 with d/r: 56 and last sent: 4 (2025-08-16) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-96427129: id 2165 for customer: GREENMAN ENVIRONMENTAL MANAGEMENT End date is 2025-07-25 with d/r: 26 and last sent: 3 (2025-08-17) so 29 (26 + 3 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  563-37300685: id 2180 for customer: RC DONKIN & PARTNER LTD End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-38046292: id 2187 for customer: Claire Raw End date is 2025-11-15 with d/r: 86 and last sent: 4 (2025-08-16) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-92722818: id 2198 for customer: Roadcraft Crane and Plant Hire End date is 2025-07-01 with d/r: 50 and last sent: 4 (2025-08-16) so 54 (50 + 4 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-35106697: id 2202 for customer: ARA Architects End date is 2025-10-26 with d/r: 66 and last sent: 24 (2025-07-27) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-37286823: id 2204 for customer: Keir Townsend Ltd End date is 2025-11-09 with d/r: 80 and last sent: 10 (2025-08-10) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-34540958: id 2209 for customer: Stefan Martin End date is 2025-10-03 with d/r: 43 and last sent: 17 (2025-08-03) so 60 (43 + 17 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-32477432: id 2223 for customer: R NUTTALL & Co. End date is 2025-09-22 with d/r: 32 and last sent: 28 (2025-07-23) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-43813746: id 2224 for customer: Designer Metals Ltd End date is 2025-09-22 with d/r: 32 and last sent: 28 (2025-07-23) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-35831355: id 2225 for customer: VKE Contractors End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2226 for customer: JMC Packaging End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-13762865: id 2227 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 24 and last sent: 6 (2025-08-14) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2242 for customer: PAMTAR Ltd End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-32027641: id 2264 for customer: Abbey Joinery Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-20988838: id 2268 for customer: BEAVERDENT Ltd End date is 2025-09-02 with d/r: 12 and last sent: 3 (2025-08-17) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  573-10547316: id 2269 for customer: HANNAH LAWSON STUDIO End date is 2025-06-05 with d/r: 76 and last sent: 16 (2025-08-04) so 92 (76 + 16 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2284 for customer: BIMSynergy End date is 2025-11-02 with d/r: 73 and last sent: 17 (2025-08-03) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  575-02157156: id 2286 for customer: Coast Consulting Engineers End date is 2025-09-04 with d/r: 14 and last sent: 1 (2025-08-19) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-42465462: id 2289 for customer: Utility Consultancy & Engineer End date is 2025-11-10 with d/r: 81 and last sent: 9 (2025-08-11) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  572-54671851: id 2290 for customer: Carson Powell Construction Ltd End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2301 for customer: lydia rowe End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2315 for customer: YORKSHIRE CHOICE HOMES CONSTRUCTION Ltd End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-38076789: id 2319 for customer: THUNDERBOLT & MAINTENANCE End date is 2025-09-13 with d/r: 23 and last sent: 7 (2025-08-13) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  563-35400673: id 2329 for customer: DMC ARCHITECTURE DESIGN Ltd End date is 2025-10-12 with d/r: 52 and last sent: 8 (2025-08-12) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2330 for customer: FRANK H DALE Ltd End date is 2025-10-14 with d/r: 54 and last sent: 6 (2025-08-14) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-86477996: id 2331 for customer: ALU-FIX UK Ltd End date is 2025-06-10 with d/r: 71 and last sent: 11 (2025-08-09) so 82 (71 + 11 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-93544051: id 2332 for customer: Beardmax Limitet End date is 2025-07-07 with d/r: 44 and last sent: 14 (2025-08-06) so 58 (44 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-47135979: id 2336 for customer: Karen Gardner Architects Ltd End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2350 for customer: Anthony Stuchberry End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-60486347: id 2352 for customer: NARRACOTT S End date is 2025-10-30 with d/r: 70 and last sent: 20 (2025-07-31) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  572-42802219: id 2413 for customer: VKE Contractors End date is 2025-08-22 with d/r: 1 and last sent: 2 (2025-08-18) so 3 (1 + 2 is above rule 1: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-51983637: id 2428 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-33205625: id 2460 for customer: JDW Architects End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-66746724: id 2476 for customer: ADS End date is 2025-11-01 with d/r: 72 and last sent: 18 (2025-08-02) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  562-94469544: id 2480 for customer: Solus Homes End date is 2025-06-27 with d/r: 54 and last sent: 8 (2025-08-12) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  573-10577998: id 2497 for customer: EDM London End date is 2025-06-05 with d/r: 76 and last sent: 16 (2025-08-04) so 92 (76 + 16 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  570-50497145: id 2500 for customer: Peak Circuit Ltd End date is 2025-10-05 with d/r: 45 and last sent: 15 (2025-08-05) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  572-43806564: id 2514 for customer: APPROVED ELECTRICAL INSTALLATI End date is 2025-08-26 with d/r: 5 and last sent: 5 (2025-08-15) so 10 (5 + 5 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-27265931: id 2521 for customer: ORIGIN DESIGN STUDIO Ltd End date is 2025-09-28 with d/r: 38 and last sent: 22 (2025-07-29) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2524 for customer: LINROC Ltd End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  567-28774572: id 2527 for customer: INTELECT MECHICAL End date is 2025-08-03 with d/r: 17 and last sent: 2 (2025-08-18) so 19 (17 + 2 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2539 for customer: SeAH Wind Ltd End date is 2025-09-23 with d/r: 33 and last sent: 27 (2025-07-24) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2558 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-41179223: id 2608 for customer: DAVID SALISBURY JOINERY Ltd End date is 2025-11-03 with d/r: 74 and last sent: 16 (2025-08-04) so 90 (74 + 16 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-40383526: id 2609 for customer: Treveth Holdings LLP End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-05983438: id 2625 for customer: BARRIER ARCHITECTURAL SERVICES End date is 2025-06-13 with d/r: 68 and last sent: 8 (2025-08-12) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  563-43882136: id 2626 for customer: Adam Langsbury Chartered Build End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  : id 2646 for customer: BIMSynergy End date is 2025-11-02 with d/r: 73 and last sent: 17 (2025-08-03) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-17937204: id 2670 for customer: Kevin Mitchell Consulting Engineers Ltd End date is 2025-08-13 with d/r: 7 and last sent: 2 (2025-08-18) so 9 (7 + 2 is below rule 10: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  574-11224705: id 2671 for customer: MELIUS HOMES Ltd End date is 2025-07-10 with d/r: 41 and last sent: 11 (2025-08-09) so 52 (41 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  573-64915024: id 2683 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-04 with d/r: 77 and last sent: 1 (2025-08-19) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  564-84641509: id 2688 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 40 and last sent: 20 (2025-07-31) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:95]  566-56786617: id 2708 for customer: A & M Architectural Partnership End date is 2025-05-30 with d/r: 82 and last sent: 6 (2025-08-14) so 88 (82 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:05] [send_email.php:87]  572-13359749: id 2709 for customer: Castle Masonry Products Ltd End date is 2025-06-27 with d/r: 54 and last sent: 8 (2025-08-12) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  574-95603322: id 2712 for customer: Wiveliscombe Joinery Ltd End date is 2025-07-21 with d/r: 30 and last sent: 1 (2025-08-19) so 31 (30 + 1 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  560-07789113: id 2768 for customer: Surveying Solutions Ltd End date is 2025-10-22 with d/r: 62 and last sent: 28 (2025-07-23) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-63567482: id 2769 for customer: Bay Building Services End date is 2025-11-05 with d/r: 76 and last sent: 14 (2025-08-06) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  563-48124598: id 2783 for customer: Mark Architecture Ltd End date is 2025-11-13 with d/r: 84 and last sent: 6 (2025-08-14) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  572-75412629: id 2799 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-07 with d/r: 17 and last sent: 13 (2025-08-07) so 30 (17 + 13 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-13068029: id 2803 for customer: EDM London End date is 2025-06-15 with d/r: 66 and last sent: 6 (2025-08-14) so 72 (66 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 2804 for customer: BWM End date is 2025-09-24 with d/r: 34 and last sent: 26 (2025-07-25) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-85174040: id 2809 for customer: ATELIERS DE FRANCE End date is 2025-06-02 with d/r: 79 and last sent: 3 (2025-08-17) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  564-81177817: id 2814 for customer: E & M Design Partnership End date is 2025-09-20 with d/r: 30 and last sent: 30 (2025-07-21) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-09838098: id 2841 for customer: JAMES MACKINTOSH ARCHITECTS End date is 2025-07-02 with d/r: 49 and last sent: 3 (2025-08-17) so 52 (49 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-26001394: id 2869 for customer: MPR Architectural Designs Ltd End date is 2025-08-23 with d/r: 2 and last sent: 1 (2025-08-19) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-33568852: id 2871 for customer: ORIGIN DESIGN STUDIO Ltd End date is 2025-10-18 with d/r: 58 and last sent: 2 (2025-08-18) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-38325416: id 2879 for customer: Kevin Judson End date is 2025-11-16 with d/r: 87 and last sent: 3 (2025-08-17) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  572-55743009: id 2884 for customer: AIR HANDLING SYSTEMS End date is 2025-11-11 with d/r: 82 and last sent: 8 (2025-08-12) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-36239916: id 2898 for customer: PCC CONSULTANTS Ltd End date is 2025-11-01 with d/r: 72 and last sent: 18 (2025-08-02) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  572-43389365: id 2942 for customer: LOWRY LIGHTING SOLUTIONS Ltd End date is 2025-08-24 with d/r: 3 and last sent: 2 (2025-08-18) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  560-14529128: id 2987 for customer: HATTRELL DS ONE End date is 2025-11-15 with d/r: 86 and last sent: 4 (2025-08-16) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  574-18378058: id 2990 for customer: Designteam End date is 2025-08-15 with d/r: 5 and last sent: 2 (2025-08-18) so 7 (5 + 2 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  575-01538336: id 3005 for customer: MO Construction Ltd End date is 2025-09-01 with d/r: 11 and last sent: 4 (2025-08-16) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-59716582: id 3008 for customer: McAuliffe Group End date is 2025-10-29 with d/r: 69 and last sent: 21 (2025-07-30) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-39310653: id 3013 for customer: Squire Associates (Aberdeen) Ltd End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  572-55398361: id 3021 for customer: Adib Nouri Zina End date is 2025-10-19 with d/r: 59 and last sent: 1 (2025-08-19) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  : id 3032 for customer: VKE Contractors End date is 2025-10-20 with d/r: 60 and last sent: 30 (2025-07-21) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  570-38101733: id 3035 for customer: STS STORAGE SYSTEMS End date is 2025-09-13 with d/r: 23 and last sent: 7 (2025-08-13) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 3041 for customer: Alex Grey End date is 2025-09-17 with d/r: 27 and last sent: 3 (2025-08-17) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-32179603: id 3070 for customer: Kevin Judson End date is 2025-09-21 with d/r: 31 and last sent: 29 (2025-07-22) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-32239155: id 3107 for customer: S2CARCHITECTS End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-35549263: id 3124 for customer: Byjc Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  570-40726176: id 3127 for customer: IGUANA DEVELOPMENTS Ltd End date is 2025-09-16 with d/r: 26 and last sent: 4 (2025-08-16) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 3129 for customer: Truck-Lite Europe Ltd End date is 2025-10-22 with d/r: 62 and last sent: 28 (2025-07-23) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-25997508: id 3138 for customer: Epoch Architecture End date is 2025-09-21 with d/r: 31 and last sent: 29 (2025-07-22) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  570-44772660: id 3139 for customer: Constantine Design Ltd End date is 2025-09-23 with d/r: 33 and last sent: 27 (2025-07-24) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-17141007: id 3140 for customer: GIGANT Ltd End date is 2025-08-29 with d/r: 8 and last sent: 2 (2025-08-18) so 10 (8 + 2 is below rule 10: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-36191146: id 3141 for customer: CSL Associates Ltd End date is 2025-10-11 with d/r: 51 and last sent: 9 (2025-08-11) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-84381313: id 3146 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-30188430: id 3164 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 24 and last sent: 6 (2025-08-14) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  565-00640173: id 3171 for customer: POWERCOM SYSTEMS End date is 2025-11-07 with d/r: 78 and last sent: 12 (2025-08-08) so 90 (78 + 12 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-34398097: id 3185 for customer: Premo Fabrications Ltd End date is 2025-09-18 with d/r: 28 and last sent: 2 (2025-08-18) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  566-59603476: id 3208 for customer: NIGHTINGALE JOINERY Ltd End date is 2025-06-03 with d/r: 78 and last sent: 2 (2025-08-18) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 3239 for customer: M & C ROOFING CONTRACTORS Ltd End date is 2025-11-03 with d/r: 74 and last sent: 16 (2025-08-04) so 90 (74 + 16 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-55456804: id 3243 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  569-73908915: id 3252 for customer: PDR GROUP SERVICES End date is 2025-08-02 with d/r: 18 and last sent: 3 (2025-08-17) so 21 (18 + 3 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  574-88124028: id 3257 for customer: JMC Packaging End date is 2025-06-23 with d/r: 58 and last sent: 4 (2025-08-16) so 62 (58 + 4 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-38596120: id 3277 for customer: ORANGE KEY Ltd End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-06801505: id 3283 for customer: EDM London End date is 2025-06-18 with d/r: 63 and last sent: 3 (2025-08-17) so 66 (63 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-43598976: id 3327 for customer: Shear Stress Ltd End date is 2025-11-16 with d/r: 87 and last sent: 3 (2025-08-17) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  570-38455782: id 3340 for customer: MACHINES AND CONTROLS Ltd End date is 2025-09-13 with d/r: 23 and last sent: 7 (2025-08-13) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  565-04538979: id 3351 for customer: D & G UTILITIES Ltd End date is 2025-11-14 with d/r: 85 and last sent: 5 (2025-08-15) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-19330566: id 3356 for customer: EDM London End date is 2025-07-14 with d/r: 37 and last sent: 7 (2025-08-13) so 44 (37 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  574-25816572: id 3366 for customer: JB Surveying Ltd End date is 2025-09-20 with d/r: 30 and last sent: 30 (2025-07-21) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-36777968: id 3367 for customer: RD NAIRN Ltd End date is 2025-11-06 with d/r: 77 and last sent: 13 (2025-08-07) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-60577508: id 3376 for customer: ECS BATH Ltd End date is 2025-10-30 with d/r: 70 and last sent: 20 (2025-07-31) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  570-81465285: id 3394 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-13069019: id 3396 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-15 with d/r: 66 and last sent: 6 (2025-08-14) so 72 (66 + 6 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  574-04439554: id 3410 for customer: ECODEV GROUP End date is 2025-06-05 with d/r: 76 and last sent: 16 (2025-08-04) so 92 (76 + 16 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-49530691: id 3412 for customer: TYLER PARKES End date is 2025-10-15 with d/r: 55 and last sent: 5 (2025-08-15) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  573-26160653: id 3419 for customer: POTTER COWAN End date is 2025-08-24 with d/r: 3 and last sent: 2 (2025-08-18) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-17611758: id 3430 for customer: METRICAB End date is 2025-08-09 with d/r: 11 and last sent: 1 (2025-08-19) so 12 (11 + 1 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-53479680: id 3442 for customer: McAuliffe Group End date is 2025-10-21 with d/r: 61 and last sent: 29 (2025-07-22) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 3448 for customer: SHERRINGTON LIFTING SERVICES Ltd End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  567-01043658: id 3451 for customer: Elevation One Building Design Ltd End date is 2025-08-12 with d/r: 8 and last sent: 3 (2025-08-17) so 11 (8 + 3 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-36777671: id 3452 for customer: V4 ARCHITECTS Ltd End date is 2025-11-06 with d/r: 77 and last sent: 13 (2025-08-07) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  575-01297717: id 3453 for customer: Advanced Water Treatment UK Ltd End date is 2025-08-29 with d/r: 8 and last sent: 2 (2025-08-18) so 10 (8 + 2 is below rule 10: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  565-05805127: id 3472 for customer: SL PLASTICS End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-41227921: id 3477 for customer: Verdi Systems Ltd End date is 2025-11-03 with d/r: 74 and last sent: 16 (2025-08-04) so 90 (74 + 16 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 3487 for customer: SALT & WHITE ARCHITECTS End date is 2025-10-10 with d/r: 50 and last sent: 10 (2025-08-10) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-27766965: id 3491 for customer: STRUCTURE WORKSHOP End date is 2025-10-02 with d/r: 42 and last sent: 18 (2025-08-02) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  : id 3493 for customer: Martyn Lowther End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  572-61258943: id 3500 for customer: STRUCTURAL DESIGN SERVICES Ltd End date is 2025-11-10 with d/r: 81 and last sent: 9 (2025-08-11) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  561-85235864: id 3522 for customer: LOCKWOODS CONSTRUCTION End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  574-98419488: id 3544 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  567-41993197: id 3556 for customer: Lazzeri Creative Interiors End date is 2025-10-01 with d/r: 41 and last sent: 19 (2025-08-01) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:95]  573-33212652: id 3558 for customer: M & E Design Ltd End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:06] [send_email.php:87]  574-21063077: id 3564 for customer: KOHA ARCHITECTS Ltd End date is 2025-08-31 with d/r: 10 and last sent: 5 (2025-08-15) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  567-53543424: id 3572 for customer: Jameson Builders End date is 2025-10-21 with d/r: 61 and last sent: 29 (2025-07-22) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-06243754: id 3582 for customer: ALEXANDER WATERWORTH End date is 2025-06-14 with d/r: 67 and last sent: 7 (2025-08-13) so 74 (67 + 7 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-09401796: id 3598 for customer: ATELIERS DE FRANCE End date is 2025-06-29 with d/r: 52 and last sent: 6 (2025-08-14) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-93530292: id 3602 for customer: HGCE Ltd End date is 2025-07-07 with d/r: 44 and last sent: 14 (2025-08-06) so 58 (44 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  567-32215203: id 3617 for customer: MICHAEL VAUGHAN RACKING SERVIC End date is 2025-09-15 with d/r: 25 and last sent: 5 (2025-08-15) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-34734040: id 3618 for customer: HEATING DESIGN SOLUTIONS End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-27210503: id 3631 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 38 and last sent: 22 (2025-07-29) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  566-63440025: id 3649 for customer: Hammond Design End date is 2025-06-04 with d/r: 77 and last sent: 1 (2025-08-19) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-92527234: id 3650 for customer: STRENGER End date is 2025-06-30 with d/r: 51 and last sent: 5 (2025-08-15) so 56 (51 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-22571554: id 3693 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 3695 for customer: Truck-Lite Europe Ltd End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-91270489: id 3699 for customer: DURATA End date is 2025-06-26 with d/r: 55 and last sent: 1 (2025-08-19) so 56 (55 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  560-02732245: id 3718 for customer: FERNBALLOT Ltd End date is 2025-10-11 with d/r: 51 and last sent: 9 (2025-08-11) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-33477126: id 3724 for customer: Glent Engineering Ltd End date is 2025-09-27 with d/r: 37 and last sent: 23 (2025-07-28) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-36759459: id 3729 for customer: OVNI Consulting Engineers Ltd End date is 2025-11-06 with d/r: 77 and last sent: 13 (2025-08-07) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  569-97731818: id 3734 for customer: NORTHALLERTON DRAUGHTING End date is 2025-08-31 with d/r: 10 and last sent: 5 (2025-08-15) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  573-20718161: id 3736 for customer: KITCHEN ARCHITECTURE Ltd End date is 2025-09-05 with d/r: 15 and last sent: 15 (2025-08-05) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 3747 for customer: KMD CONSULTING End date is 2025-10-06 with d/r: 46 and last sent: 14 (2025-08-06) so 60 (46 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  567-57622669: id 3748 for customer: CUMBRIA WASTE GROUP End date is 2025-10-27 with d/r: 67 and last sent: 23 (2025-07-28) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 3751 for customer: Sarah Darlow Darlow End date is 2025-11-11 with d/r: 82 and last sent: 8 (2025-08-12) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-21728743: id 3753 for customer: John Roux End date is 2025-07-26 with d/r: 25 and last sent: 2 (2025-08-18) so 27 (25 + 2 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  564-97534193: id 3755 for customer: Utility Consultancy & Engineer End date is 2025-10-30 with d/r: 70 and last sent: 20 (2025-07-31) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  573-25204511: id 3764 for customer: Shadbolt Ltd End date is 2025-08-17 with d/r: 3 and last sent: 2 (2025-08-18) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-52388292: id 3777 for customer: FURNESS GREEN PARTNERSHIP End date is 2025-10-06 with d/r: 46 and last sent: 14 (2025-08-06) so 60 (46 + 14 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-32031304: id 3789 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-09 with d/r: 49 and last sent: 11 (2025-08-09) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-35848309: id 3798 for customer: Silverfox Surveys Ltd End date is 2025-07-26 with d/r: 25 and last sent: 2 (2025-08-18) so 27 (25 + 2 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-62544390: id 3807 for customer: STORTFORD HOLDINGS Ltd End date is 2025-07-11 with d/r: 40 and last sent: 10 (2025-08-10) so 50 (40 + 10 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-57006680: id 3809 for customer: ARCHITECTURAL METALWORK SERVICES Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  567-51281740: id 3810 for customer: Erith Business Systems End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  574-99555081: id 3811 for customer: R3nder Ltd End date is 2025-08-15 with d/r: 5 and last sent: 2 (2025-08-18) so 7 (5 + 2 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  777-13092772: id 3823 for customer: EDM-London End date is 2025-06-27 with d/r: 54 and last sent: 8 (2025-08-12) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-37144192: id 3827 for customer: Dean Whitbrook End date is 2025-11-08 with d/r: 79 and last sent: 11 (2025-08-09) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  573-17118867: id 3828 for customer: KERRY JANE INTERIORS End date is 2025-07-05 with d/r: 46 and last sent: 16 (2025-08-04) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  572-55764783: id 3835 for customer: Scomac Services End date is 2025-10-20 with d/r: 60 and last sent: 30 (2025-07-21) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  571-92211472: id 3837 for customer: INTELECT MECHICAL End date is 2025-05-24 with d/r: 88 and last sent: 4 (2025-08-16) so 92 (88 + 4 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-36646353: id 3857 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 24 and last sent: 6 (2025-08-14) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-53707888: id 3858 for customer: Utility Consultancy & Engineer End date is 2025-10-12 with d/r: 52 and last sent: 8 (2025-08-12) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-32890248: id 3859 for customer: Utility Consultancy & Engineer End date is 2025-11-05 with d/r: 76 and last sent: 14 (2025-08-06) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-40391345: id 3860 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 71 and last sent: 19 (2025-08-01) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-33205130: id 3872 for customer: Promack Ltd End date is 2025-09-26 with d/r: 36 and last sent: 24 (2025-07-27) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  574-14338009: id 3891 for customer: ATELIERS DE FRANCE End date is 2025-07-26 with d/r: 25 and last sent: 2 (2025-08-18) so 27 (25 + 2 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-44893063: id 3892 for customer: LONDONSTRUCTURALDESIGN Ltd End date is 2025-09-01 with d/r: 11 and last sent: 4 (2025-08-16) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-37972778: id 3904 for customer: McAuliffe Group End date is 2025-10-19 with d/r: 59 and last sent: 1 (2025-08-19) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  573-43386665: id 3955 for customer: STAND INNOVATIONS Ltd End date is 2025-11-15 with d/r: 86 and last sent: 4 (2025-08-16) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  572-59532046: id 3956 for customer: HAMPTON DOORS End date is 2025-11-01 with d/r: 72 and last sent: 18 (2025-08-02) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  570-75625390: id 3959 for customer: Aughton Automation Ltd End date is 2025-10-17 with d/r: 57 and last sent: 3 (2025-08-17) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 4034 for customer: EPROD Software UK Ltd End date is 2025-05-23 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 4104 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 53 and last sent: 7 (2025-08-13) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 12536 for customer: JONATHAN LEES ARCHITECTS LLP End date is 2025-07-16 with d/r: 35 and last sent: 5 (2025-08-15) so 40 (35 + 5 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 12543 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 12554 for customer: Denis Welch Motorsport End date is 2025-06-29 with d/r: 52 and last sent: 6 (2025-08-14) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:95]  : id 12588 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  : id 30765 for customer: STORTFORD HOLDINGS Ltd End date is 2025-08-10 with d/r: 10 and last sent: 2 (2025-08-18) so 12 (10 + 2 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-20 11:00:07] [send_email.php:87]  : id 62691 for customer: POTTER COWAN End date is 2025-08-24 with d/r: 3 and last sent: 2 (2025-08-18) so 5 (3 + 2 is above rule 3: sending email to 
[email_sender] [2025-08-20 11:00:07] [autodesk_subscriptions.class.php:232] Sending email to  from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-20 11:00:08] [send_email.php:95]  : id 62717 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:08] [send_email.php:95]  : id 62758 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 65 and last sent: 25 (2025-07-26) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:08] [send_email.php:95]  : id 62776 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 89 and last sent: 1 (2025-08-19) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-20 11:00:08] [send_email.php:95]  : id 62789 for customer: GRAY & DICK Ltd End date is 2025-07-30 with d/r: 21 and last sent: 6 (2025-08-14) so 27 (21 + 6 is below rule 30: not sending email
[email_sender] [2025-08-20 11:00:08] [send_email.php:95]  : id 63030 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 40 and last sent: 9 () so 49 (40 + 9 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:08] [send_email.php:95]  : id 63034 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 40 and last sent: 9 () so 49 (40 + 9 is below rule 60: not sending email
[email_sender] [2025-08-20 11:00:08] [send_email.php:104] Email sending process completed. 52 emails sent.
[email_sender] [2025-08-21 11:00:02] [send_email.php:11]  autodesk_api initialized.
[email_sender] [2025-08-21 11:00:02] [send_email.php:18]  Settings retrieved from database. Rules: -20,-15,-10,0,1,3,5,10,15,30,60,90, Settings days: ,true,true,true,true,true and time: 11
[email_sender] [2025-08-21 11:00:02] [send_email.php:38]  Settings retrieved from database.
[email_sender] [2025-08-21 11:00:02] [send_email.php:39]  Valid send time detected. Proceeding with email processing.
[email_sender] [2025-08-21 11:00:02] [send_email.php:51]  3767 renewable subscriptions retrieved.
[email_sender] [2025-08-21 11:00:02] [send_email.php:58]  Email template loaded successfully.
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-36033345: id 5 for customer: RICHARDSON ARCHITECTURE Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  570-48836366: id 20 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-01 with d/r: 40 and last sent: 20 (2025-08-01) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  570-67307146: id 28 for customer: THORNE RAINWATER SYSTEMS End date is 2025-11-02 with d/r: 72 and last sent: 18 (2025-08-03) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  564-89454291: id 29 for customer: IRD DESIGN Ltd End date is 2025-10-14 with d/r: 53 and last sent: 7 (2025-08-14) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  575-00642075: id 43 for customer: IAN TITLEY DESIGN End date is 2025-08-26 with d/r: 4 and last sent: 1 (2025-08-20) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-90800535: id 53 for customer: BARRIER ARCHITECTURAL SERVICES End date is 2025-06-25 with d/r: 57 and last sent: 3 (2025-08-18) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-21267569: id 75 for customer: apdesign End date is 2025-09-03 with d/r: 12 and last sent: 3 (2025-08-18) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  572-48896886: id 101 for customer: DSB PROPERTY DESIGNS End date is 2025-09-20 with d/r: 29 and last sent: 1 (2025-08-20) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-37654136: id 103 for customer: Barden Chapman End date is 2025-11-13 with d/r: 83 and last sent: 7 (2025-08-14) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  567-63524427: id 104 for customer: CT GLASS End date is 2025-11-05 with d/r: 75 and last sent: 15 (2025-08-06) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-32890446: id 106 for customer: VKE Contractors End date is 2025-10-15 with d/r: 54 and last sent: 6 (2025-08-15) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-94161683: id 107 for customer: ADR Consulting End date is 2025-07-10 with d/r: 42 and last sent: 12 (2025-08-09) so 54 (42 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  574-97991797: id 117 for customer: ATELIERS DE FRANCE End date is 2025-07-31 with d/r: 21 and last sent: 6 (2025-08-15) so 27 (21 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  569-59178474: id 125 for customer: 360 Prism Ltd End date is 2025-07-07 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  564-87864877: id 127 for customer: Robert Brown Smith End date is 2025-10-08 with d/r: 47 and last sent: 13 (2025-08-08) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  570-41940359: id 135 for customer: ADAMS DESIGN ASSOCIATES Ltd End date is 2025-09-20 with d/r: 29 and last sent: 1 (2025-08-20) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  573-40188932: id 137 for customer: EDM London End date is 2025-10-30 with d/r: 69 and last sent: 21 (2025-07-31) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:87]  574-96087431: id 152 for customer: Civils & Construction Solutions End date is 2025-07-23 with d/r: 29 and last sent: 2 (2025-08-19) so 31 (29 + 2 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:02] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  573-25195702: id 181 for customer: VKE Contractors End date is 2025-08-17 with d/r: 4 and last sent: 1 (2025-08-20) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  572-48568175: id 185 for customer: LITAC ENGINEERING End date is 2025-09-19 with d/r: 28 and last sent: 2 (2025-08-19) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  572-47469602: id 187 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  570-72954823: id 198 for customer: CISTEC Ltd End date is 2025-11-11 with d/r: 81 and last sent: 9 (2025-08-12) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:87]  574-18644809: id 201 for customer: GASTECH ENGINEERING Ltd End date is 2025-08-16 with d/r: 5 and last sent: 2 (2025-08-19) so 7 (5 + 2 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:02] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:02] [send_email.php:95]  563-47081948: id 207 for customer: Tristan Plant End date is 2025-11-09 with d/r: 79 and last sent: 11 (2025-08-10) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:02] [send_email.php:87]  570-60126176: id 210 for customer: CROSSOVER AV Ltd End date is 2025-10-21 with d/r: 60 and last sent: 30 (2025-07-22) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:02] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  575-01683044: id 222 for customer: JCP Consulting Ltd End date is 2025-09-06 with d/r: 15 and last sent: 15 (2025-08-06) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  569-55575321: id 227 for customer: JEL Renewables Ltd End date is 2025-06-30 with d/r: 52 and last sent: 6 (2025-08-15) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  573-33202457: id 242 for customer: Promack Ltd End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-54042242: id 243 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  569-99919365: id 244 for customer: REALISATION BY DESIGN Ltd End date is 2025-09-03 with d/r: 12 and last sent: 3 (2025-08-18) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-58045471: id 261 for customer: BOWER EDLESTON End date is 2025-10-27 with d/r: 66 and last sent: 24 (2025-07-28) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  574-93279082: id 263 for customer: Blue Aardvark Joinery End date is 2025-07-04 with d/r: 48 and last sent: 2 (2025-08-19) so 50 (48 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  569-91563509: id 295 for customer: Concept Eight Architects End date is 2025-08-23 with d/r: 1 and last sent: 2 (2025-08-19) so 3 (1 + 2 is above rule 1: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  : id 308 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-09-19 with d/r: 28 and last sent: 2 (2025-08-19) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  570-60446078: id 325 for customer: Leech Mechanical Services Ltd End date is 2025-10-21 with d/r: 60 and last sent: 30 (2025-07-22) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  : id 332 for customer: K BAVA ARCHITECTS Ltd End date is 2025-10-02 with d/r: 41 and last sent: 19 (2025-08-02) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  561-95896659: id 343 for customer: Davies Mr End date is 2025-10-24 with d/r: 63 and last sent: 27 (2025-07-25) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  572-12556332: id 352 for customer: Whitaker Lianne End date is 2025-06-24 with d/r: 58 and last sent: 4 (2025-08-17) so 62 (58 + 4 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  573-27481734: id 353 for customer: Glent Engineering Ltd End date is 2025-06-10 with d/r: 72 and last sent: 12 (2025-08-09) so 84 (72 + 12 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-41393046: id 367 for customer: TUFCOT ENGINEERING Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  563-47765403: id 368 for customer: AMALGAM MODELMAKERS Ltd End date is 2025-11-12 with d/r: 82 and last sent: 8 (2025-08-13) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  572-45667281: id 369 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-09-06 with d/r: 15 and last sent: 15 (2025-08-06) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  574-98994263: id 380 for customer: NGP Architecture Ltd End date is 2025-08-11 with d/r: 10 and last sent: 2 (2025-08-19) so 12 (10 + 2 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  : id 399 for customer: STRENGER End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-51541127: id 419 for customer: O'MAC CONSTRUCTION End date is 2025-10-03 with d/r: 42 and last sent: 18 (2025-08-03) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  574-31780884: id 426 for customer: J W CONTRACT SERVICES Ltd End date is 2025-10-08 with d/r: 47 and last sent: 13 (2025-08-08) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  567-64336356: id 448 for customer: Bay Building Services End date is 2025-11-06 with d/r: 76 and last sent: 14 (2025-08-07) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  573-40116973: id 472 for customer: Aecor Marine Ltd End date is 2025-10-30 with d/r: 69 and last sent: 21 (2025-07-31) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  : id 476 for customer: Blue Aardvark Joinery End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-59689919: id 477 for customer: NIC Ltd End date is 2025-11-02 with d/r: 72 and last sent: 18 (2025-08-03) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  567-56252692: id 478 for customer: Cornerstone Projects Ltd End date is 2025-10-24 with d/r: 63 and last sent: 27 (2025-07-25) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-31811921: id 481 for customer: CAMB MACHINE KNIVES End date is 2025-07-19 with d/r: 33 and last sent: 3 (2025-08-18) so 36 (33 + 3 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-95952873: id 492 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  574-12406719: id 502 for customer: Project Marble Ltd End date is 2025-07-16 with d/r: 36 and last sent: 6 (2025-08-15) so 42 (36 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  570-00229171: id 503 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-09-03 with d/r: 12 and last sent: 3 (2025-08-18) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  574-85203338: id 511 for customer: ATELIERS DE FRANCE End date is 2025-06-02 with d/r: 80 and last sent: 4 (2025-08-17) so 84 (80 + 4 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  574-27210206: id 522 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 37 and last sent: 23 (2025-07-29) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  : id 523 for customer: HEWITSON Ltd End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  566-96027273: id 525 for customer: Frame Development End date is 2025-07-30 with d/r: 22 and last sent: 7 (2025-08-14) so 29 (22 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  567-64333386: id 534 for customer: Bay Building Services End date is 2025-11-06 with d/r: 76 and last sent: 14 (2025-08-07) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  570-40524951: id 537 for customer: Manor Construction (South Yorkshire) Ltd End date is 2025-09-16 with d/r: 25 and last sent: 5 (2025-08-16) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  570-65006068: id 556 for customer: McAuliffe Group End date is 2025-10-28 with d/r: 67 and last sent: 23 (2025-07-29) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  574-25454704: id 557 for customer: EVOLVE INTEGRATED SOLUTIONS End date is 2025-09-20 with d/r: 29 and last sent: 1 (2025-08-20) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:95]  572-55764387: id 559 for customer: Scomac Services End date is 2025-10-20 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:03] [send_email.php:87]  574-04847152: id 566 for customer: ALLAN MCGOVERN End date is 2025-06-06 with d/r: 76 and last sent: 16 (2025-08-05) so 92 (76 + 16 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  563-44078413: id 586 for customer: Ground Condition Consultants L End date is 2025-11-01 with d/r: 71 and last sent: 19 (2025-08-02) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-98116688: id 595 for customer: Peter Cogill End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 622 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-34812927: id 623 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-27543694: id 634 for customer: BRU Fabrication End date is 2025-08-31 with d/r: 9 and last sent: 1 (2025-08-20) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-36183822: id 636 for customer: Murwell Consulting Engineers Ltd End date is 2025-10-11 with d/r: 50 and last sent: 10 (2025-08-11) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-94161782: id 660 for customer: ADR Consulting End date is 2025-07-10 with d/r: 42 and last sent: 12 (2025-08-09) so 54 (42 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-35853527: id 662 for customer: Utility Consultancy & Engineer End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-79249198: id 685 for customer: TURNBULL SURVEYING End date is 2025-09-16 with d/r: 25 and last sent: 5 (2025-08-16) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-55812392: id 687 for customer: Hewer FM Ltd End date is 2025-10-20 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-31816149: id 697 for customer: All Design (Scotland) Ltd End date is 2025-09-20 with d/r: 29 and last sent: 1 (2025-08-20) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-24998408: id 698 for customer: ZinCo Green Roof Systems End date is 2025-09-18 with d/r: 27 and last sent: 3 (2025-08-18) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-95439412: id 705 for customer: ADF Paris End date is 2025-07-18 with d/r: 34 and last sent: 4 (2025-08-17) so 38 (34 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  569-67099416: id 716 for customer: COVENTRY CONSTRUCTION Ltd End date is 2025-07-20 with d/r: 32 and last sent: 2 (2025-08-19) so 34 (32 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-27784286: id 719 for customer: Clive Martin End date is 2025-10-02 with d/r: 41 and last sent: 19 (2025-08-02) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  777-11777898: id 737 for customer: NARRACOTT S End date is 2025-10-15 with d/r: 54 and last sent: 6 (2025-08-15) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-37459245: id 742 for customer: Southern Aluminium Upvc End date is 2025-11-12 with d/r: 82 and last sent: 8 (2025-08-13) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-16408490: id 762 for customer: One Design Architectural Services End date is 2025-06-30 with d/r: 52 and last sent: 6 (2025-08-15) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-13128704: id 785 for customer: Workbox UK Ltd End date is 2025-06-15 with d/r: 67 and last sent: 7 (2025-08-14) so 74 (67 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-19751230: id 789 for customer: DLG Architects Leeds End date is 2025-07-17 with d/r: 35 and last sent: 5 (2025-08-16) so 40 (35 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-23764132: id 795 for customer: Geoffrey Robinson Ltd End date is 2025-09-12 with d/r: 21 and last sent: 9 (2025-08-12) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-38101832: id 796 for customer: STS STORAGE SYSTEMS End date is 2025-09-13 with d/r: 22 and last sent: 8 (2025-08-13) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-46205357: id 802 for customer: JAMES MACKINTOSH ARCHITECTS End date is 2025-06-13 with d/r: 69 and last sent: 9 (2025-08-12) so 78 (69 + 9 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  569-82407105: id 823 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-38111433: id 840 for customer: TIM HB Ltd End date is 2025-09-13 with d/r: 22 and last sent: 8 (2025-08-13) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-39779536: id 841 for customer: PLACEFIRST CONSTRUCTION End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-37574551: id 842 for customer: AMW DESIGN Ltd End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-23630636: id 846 for customer: GRK CIVILS Ltd End date is 2025-08-07 with d/r: 14 and last sent: 1 (2025-08-20) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 859 for customer: SeAH Wind Ltd End date is 2025-10-22 with d/r: 61 and last sent: 29 (2025-07-23) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-27210404: id 860 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 37 and last sent: 23 (2025-07-29) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  571-92575619: id 872 for customer: IAIN MACRAE End date is 2025-05-25 with d/r: 88 and last sent: 4 (2025-08-17) so 92 (88 + 4 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-49274889: id 875 for customer: NIC Ltd End date is 2025-09-22 with d/r: 31 and last sent: 29 (2025-07-23) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  569-57473650: id 876 for customer: Surrey Tech Services Ltd End date is 2025-07-05 with d/r: 47 and last sent: 1 (2025-08-20) so 48 (47 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-55701536: id 891 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-10-20 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-06550095: id 892 for customer: ark architecture + design End date is 2025-06-15 with d/r: 67 and last sent: 7 (2025-08-14) so 74 (67 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-38296113: id 917 for customer: Wensley & Lawz Ltd End date is 2025-09-25 with d/r: 34 and last sent: 26 (2025-07-26) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-51548253: id 922 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-10-03 with d/r: 42 and last sent: 18 (2025-08-03) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-40455681: id 938 for customer: GREENCOLD End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-60486446: id 956 for customer: NARRACOTT S End date is 2025-10-30 with d/r: 69 and last sent: 21 (2025-07-31) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-30772311: id 957 for customer: TCS CAD & BIM Solutions Ltd End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  571-76923876: id 959 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 23 and last sent: 7 (2025-08-14) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-46238294: id 968 for customer: Furness Green Partnership End date is 2025-09-08 with d/r: 17 and last sent: 13 (2025-08-08) so 30 (17 + 13 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 971 for customer: Cladtech Systems Industrial Roofing & Cladding End date is 2025-09-22 with d/r: 31 and last sent: 29 (2025-07-23) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  569-38279528: id 984 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-04 with d/r: 78 and last sent: 2 (2025-08-19) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  569-33468427: id 988 for customer: CUTLER & MACLEAN Ltd End date is 2025-05-28 with d/r: 85 and last sent: 1 (2025-08-20) so 86 (85 + 1 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-36040471: id 991 for customer: Truck-Lite Europe Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  569-71004061: id 993 for customer: John Woodvine End date is 2025-08-05 with d/r: 16 and last sent: 1 (2025-08-20) so 17 (16 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  555-79670111: id 1006 for customer: Mark Thornton End date is 2025-10-01 with d/r: 40 and last sent: 20 (2025-08-01) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-61955320: id 1010 for customer: EMILY ESTATE UK Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-27277512: id 1022 for customer: Broad Planning and Architecture End date is 2025-09-28 with d/r: 37 and last sent: 23 (2025-07-29) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-38345113: id 1040 for customer: Spirotech SRD Group Ltd End date is 2025-11-16 with d/r: 86 and last sent: 4 (2025-08-17) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-34733842: id 1051 for customer: SeAH Wind Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-79253653: id 1070 for customer: Philip Bingham Associates End date is 2025-09-16 with d/r: 25 and last sent: 5 (2025-08-16) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1075 for customer: MQM Ltd End date is 2025-10-28 with d/r: 67 and last sent: 23 (2025-07-29) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-13383998: id 1082 for customer: EDM-London End date is 2025-06-27 with d/r: 55 and last sent: 1 (2025-08-20) so 56 (55 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  777-13196806: id 1083 for customer: BLACKFRIARS STAGING Ltd End date is 2025-09-06 with d/r: 15 and last sent: 15 (2025-08-06) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-31115373: id 1085 for customer: Syntegon Telstar UK Ltd End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-38587739: id 1092 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-23 with d/r: 62 and last sent: 28 (2025-07-24) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-59618709: id 1120 for customer: JOHN FOWKES ARCHITECTS End date is 2025-10-20 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-84641410: id 1121 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 39 and last sent: 21 (2025-07-31) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-37286526: id 1125 for customer: AIR HANDLING SYSTEMS End date is 2025-11-10 with d/r: 80 and last sent: 10 (2025-08-11) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  563-39096670: id 1126 for customer: COOLSPOT Ltd End date is 2025-10-19 with d/r: 58 and last sent: 2 (2025-08-19) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-57898459: id 1144 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  571-61094664: id 1146 for customer: FRANK H DALE Ltd End date is 2025-10-14 with d/r: 53 and last sent: 7 (2025-08-14) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-36056534: id 1147 for customer: Bauer Consult End date is 2025-10-11 with d/r: 50 and last sent: 10 (2025-08-11) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-50153433: id 1158 for customer: IMPC Ltd End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-92526838: id 1161 for customer: Chris Hinchliff End date is 2025-06-30 with d/r: 52 and last sent: 6 (2025-08-15) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-46580467: id 1162 for customer: Architectural Designs Derby End date is 2025-09-09 with d/r: 18 and last sent: 12 (2025-08-09) so 30 (18 + 12 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-58754007: id 1176 for customer: Electrical Automation Solution End date is 2025-10-28 with d/r: 67 and last sent: 23 (2025-07-29) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1178 for customer: HERMANTES STUDIO End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-36391678: id 1181 for customer: Daniela Favero End date is 2025-10-12 with d/r: 51 and last sent: 9 (2025-08-12) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-37910746: id 1183 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-65874005: id 1184 for customer: Anchorpoint Interiors End date is 2025-11-10 with d/r: 80 and last sent: 10 (2025-08-11) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-36350405: id 1197 for customer: DOWEN FARMER ARCHITECTS Ltd End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  563-24896167: id 1211 for customer: INTELECT MECHICAL End date is 2025-09-17 with d/r: 26 and last sent: 4 (2025-08-17) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1216 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-51912398: id 1246 for customer: C BARLEY SERVICES End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-64821075: id 1251 for customer: ALEXANDER WATERWORTH End date is 2025-10-28 with d/r: 67 and last sent: 23 (2025-07-29) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-97316361: id 1278 for customer: Arkitectonic End date is 2025-07-30 with d/r: 22 and last sent: 7 (2025-08-14) so 29 (22 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-32020515: id 1292 for customer: Abbey Joinery Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-85170872: id 1294 for customer: Igloo Design and Consultancy Ltd End date is 2025-06-09 with d/r: 73 and last sent: 13 (2025-08-08) so 86 (73 + 13 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-21464638: id 1296 for customer: HGCE Ltd End date is 2025-09-04 with d/r: 13 and last sent: 2 (2025-08-19) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-62802431: id 1297 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  565-04736147: id 1298 for customer: B-12 Development Ltd End date is 2025-11-14 with d/r: 84 and last sent: 6 (2025-08-15) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-60416726: id 1312 for customer: Nuckey James End date is 2025-11-07 with d/r: 77 and last sent: 13 (2025-08-08) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-74943521: id 1313 for customer: COLSEC Ltd End date is 2025-11-17 with d/r: 87 and last sent: 3 (2025-08-18) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-87293072: id 1359 for customer: TANDEM ARCHITECTS Ltd End date is 2025-10-07 with d/r: 46 and last sent: 14 (2025-08-07) so 60 (46 + 14 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-93532668: id 1363 for customer: HGCE Ltd End date is 2025-07-07 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  566-87318554: id 1364 for customer: JAMESDODDRELL:ARCHITECT Ltd End date is 2025-07-17 with d/r: 35 and last sent: 5 (2025-08-16) so 40 (35 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  575-01066402: id 1365 for customer: southeast design solutions Ltd End date is 2025-08-28 with d/r: 6 and last sent: 4 (2025-08-17) so 10 (6 + 4 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1402 for customer: Simon Farr End date is 2025-10-27 with d/r: 66 and last sent: 24 (2025-07-28) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-94018361: id 1404 for customer: SP Joinery Design solutions End date is 2025-07-09 with d/r: 43 and last sent: 13 (2025-08-08) so 56 (43 + 13 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-36255356: id 1419 for customer: HYDROMAX Inc. Ltd End date is 2025-11-01 with d/r: 71 and last sent: 19 (2025-08-02) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-21993480: id 1431 for customer: ALMA SHEET METAL LTD End date is 2025-09-03 with d/r: 12 and last sent: 3 (2025-08-18) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-94376298: id 1434 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-34367222: id 1435 for customer: Design Coalition End date is 2025-10-23 with d/r: 62 and last sent: 28 (2025-07-24) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  574-38816849: id 1448 for customer: Mactech Europe Ltd End date is 2025-11-20 with d/r: 90 and last sent: 639 () so 729 (90 + 639 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1456 for customer: Cladtech Systems Industrial Roofing & Cladding End date is 2025-09-22 with d/r: 31 and last sent: 29 (2025-07-23) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  575-01669285: id 1467 for customer: SeAH Wind Ltd End date is 2025-09-02 with d/r: 11 and last sent: 4 (2025-08-17) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-32724853: id 1470 for customer: HMA Ventilation Ltd End date is 2025-10-12 with d/r: 51 and last sent: 9 (2025-08-12) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-38071148: id 1474 for customer: BRODIE PLANNING ASSOCIATES Ltd End date is 2025-09-13 with d/r: 22 and last sent: 8 (2025-08-13) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1491 for customer: Abbey Joinery Ltd End date is 2025-10-03 with d/r: 42 and last sent: 18 (2025-08-03) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-05573266: id 1496 for customer: ATELIERS DE FRANCE End date is 2025-06-11 with d/r: 71 and last sent: 11 (2025-08-10) so 82 (71 + 11 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  573-14186398: id 1498 for customer: RUSHMON Ltd End date is 2025-06-22 with d/r: 60 and last sent: 2 (2025-08-19) so 62 (60 + 2 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-19399186: id 1508 for customer: Roger Betts End date is 2025-07-07 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-33205526: id 1512 for customer: JDW Architects End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-32125658: id 1520 for customer: DLG Architects Leeds End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  569-93809751: id 1527 for customer: CPI Group End date is 2025-08-25 with d/r: 3 and last sent: 2 (2025-08-19) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-25275550: id 1542 for customer: Kevin Judson End date is 2025-09-19 with d/r: 28 and last sent: 2 (2025-08-19) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-36941115: id 1545 for customer: James M Brown Ltd End date is 2025-10-16 with d/r: 55 and last sent: 5 (2025-08-16) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-94152181: id 1553 for customer: Artium Construction Ltd End date is 2025-07-10 with d/r: 42 and last sent: 12 (2025-08-09) so 54 (42 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1554 for customer: ATELIERS DE FRANCE End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  567-47838438: id 1571 for customer: FURNESS GREEN PARTNERSHIP End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  574-98883704: id 1587 for customer: STORTFORD HOLDINGS Ltd End date is 2025-08-08 with d/r: 13 and last sent: 3 (2025-08-18) so 16 (13 + 3 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  570-48504685: id 1607 for customer: IRELAND ALBRECHT LANDSCAPE Ltd End date is 2025-09-30 with d/r: 39 and last sent: 21 (2025-07-31) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-06066877: id 1611 for customer: VKE Contractors End date is 2025-06-13 with d/r: 69 and last sent: 9 (2025-08-12) so 78 (69 + 9 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  564-89630277: id 1650 for customer: KOHA ARCHITECTS Ltd End date is 2025-10-14 with d/r: 53 and last sent: 7 (2025-08-14) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  575-00807173: id 1653 for customer: ARDERN & DRUGGAN Ltd End date is 2025-08-27 with d/r: 5 and last sent: 5 (2025-08-16) so 10 (5 + 5 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  563-37468456: id 1673 for customer: GM Steel Newark Ltd End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-98881031: id 1676 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-32016852: id 1707 for customer: J W CONTRACT SERVICES Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1708 for customer: JAWDESIGN End date is 2025-10-16 with d/r: 55 and last sent: 5 (2025-08-16) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-29853680: id 1709 for customer: ECS BATH Ltd End date is 2025-09-12 with d/r: 21 and last sent: 9 (2025-08-12) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-54600981: id 1710 for customer: CONSULTANCY 26 SERVICES LTD End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-61569683: id 1717 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-33673007: id 1725 for customer: Kevin Judson End date is 2025-09-28 with d/r: 37 and last sent: 23 (2025-07-29) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-04208338: id 1739 for customer: L & Architects End date is 2025-06-04 with d/r: 78 and last sent: 2 (2025-08-19) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  573-14195902: id 1776 for customer: Thermal Earth Ltd End date is 2025-06-22 with d/r: 60 and last sent: 2 (2025-08-19) so 62 (60 + 2 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1777 for customer: PCC CONSULTANTS Ltd End date is 2025-11-01 with d/r: 71 and last sent: 19 (2025-08-02) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-35824229: id 1782 for customer: VKE Contractors End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-27569896: id 1788 for customer: AP4 LLP End date is 2025-10-01 with d/r: 40 and last sent: 20 (2025-08-01) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  572-11448156: id 1804 for customer: ADF Paris End date is 2025-06-23 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1814 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-24035957: id 1816 for customer: PREMIER CAD Ltd End date is 2025-08-09 with d/r: 12 and last sent: 2 (2025-08-19) so 14 (12 + 2 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  : id 1829 for customer: ARTFORM ARCHITECTS End date is 2025-10-06 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  574-96089708: id 1874 for customer: V4 ARCHITECTS Ltd End date is 2025-07-23 with d/r: 29 and last sent: 2 (2025-08-19) so 31 (29 + 2 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  574-94938079: id 1889 for customer: Wiveliscombe Joinery Ltd End date is 2025-07-21 with d/r: 31 and last sent: 1 (2025-08-20) so 32 (31 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:95]  573-35472356: id 1893 for customer: LIFESTYLE INTERIORS Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:04] [send_email.php:87]  573-04006645: id 1904 for customer: Jameson Builders End date is 2025-10-21 with d/r: 60 and last sent: 30 (2025-07-22) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-48342359: id 1907 for customer: DAVID HALLAM Ltd End date is 2025-09-30 with d/r: 39 and last sent: 21 (2025-07-31) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-82289555: id 1908 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-59716681: id 1922 for customer: McAuliffe Group End date is 2025-10-29 with d/r: 68 and last sent: 22 (2025-07-30) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-88436286: id 1926 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-12577755: id 1942 for customer: ATELIERS DE FRANCE End date is 2025-07-17 with d/r: 35 and last sent: 5 (2025-08-16) so 40 (35 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-30137158: id 1944 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-93155733: id 1955 for customer: MRM ELECTRICAL SOLUTIONS Ltd End date is 2025-10-23 with d/r: 62 and last sent: 28 (2025-07-24) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  563-46766006: id 1972 for customer: BSBA TEES Ltd End date is 2025-11-10 with d/r: 80 and last sent: 10 (2025-08-11) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  575-01512206: id 1982 for customer: Ramsay McMichael Consulting End date is 2025-09-01 with d/r: 10 and last sent: 5 (2025-08-16) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-50418836: id 1989 for customer: Elevation One Building Design Ltd End date is 2025-10-16 with d/r: 55 and last sent: 5 (2025-08-16) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  569-91701485: id 1991 for customer: Eyeking Ltd End date is 2025-08-23 with d/r: 1 and last sent: 2 (2025-08-19) so 3 (1 + 2 is above rule 1: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 1992 for customer: VKE Contractors End date is 2025-10-01 with d/r: 40 and last sent: 20 (2025-08-01) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-33245480: id 2001 for customer: MAX BUSTON Ltd End date is 2025-09-16 with d/r: 25 and last sent: 5 (2025-08-16) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-59027997: id 2003 for customer: RUSSELL JONES Ltd End date is 2025-10-19 with d/r: 58 and last sent: 2 (2025-08-19) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2007 for customer: Shields (Driffield) Ltd End date is 2025-09-30 with d/r: 39 and last sent: 21 (2025-07-31) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2008 for customer: George Everett End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-64043212: id 2020 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-85627465: id 2040 for customer: COHANIM ARCHITECTURE End date is 2025-06-04 with d/r: 78 and last sent: 2 (2025-08-19) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  574-00538372: id 2054 for customer: JEL Renewables Ltd End date is 2025-05-25 with d/r: 88 and last sent: 4 (2025-08-17) so 92 (88 + 4 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-58721541: id 2055 for customer: Dendra Consulting Ltd End date is 2025-10-28 with d/r: 67 and last sent: 23 (2025-07-29) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-37196454: id 2069 for customer: COWAL DESIGN CONSULTANTS End date is 2025-11-08 with d/r: 78 and last sent: 12 (2025-08-09) so 90 (78 + 12 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-96315974: id 2074 for customer: Stair Formwork End date is 2025-07-24 with d/r: 28 and last sent: 1 (2025-08-20) so 29 (28 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-27391729: id 2078 for customer: CUBIC BUILDING SURVEYING End date is 2025-09-05 with d/r: 14 and last sent: 1 (2025-08-20) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2094 for customer: AGW ELECTRICAL SERVICES Ltd End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-40734292: id 2115 for customer: John Farah End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-86383747: id 2120 for customer: KT Fabrications Ltd End date is 2025-10-03 with d/r: 42 and last sent: 18 (2025-08-03) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-33329619: id 2123 for customer: Aughton Automation Ltd End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2136 for customer: Washington Waterjet Ltd End date is 2025-10-02 with d/r: 41 and last sent: 19 (2025-08-02) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  571-91785365: id 2138 for customer: HIGHALL DEVELOPMENTS End date is 2025-05-23 with d/r: 90 and last sent: 2 (2025-08-19) so 92 (90 + 2 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-83903340: id 2140 for customer: CHESTERFORD SURVEYS Ltd End date is 2025-06-10 with d/r: 72 and last sent: 12 (2025-08-09) so 84 (72 + 12 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  563-35958127: id 2142 for customer: IRD DESIGN Ltd End date is 2025-10-15 with d/r: 54 and last sent: 6 (2025-08-15) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-15237562: id 2143 for customer: TFG STAGE TECHNOLOGY Ltd End date is 2025-06-27 with d/r: 55 and last sent: 1 (2025-08-20) so 56 (55 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-89015021: id 2151 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-19349768: id 2153 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 23 and last sent: 7 (2025-08-14) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-36391381: id 2158 for customer: Aecor Marine Ltd End date is 2025-10-12 with d/r: 51 and last sent: 9 (2025-08-12) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2164 for customer: JAWDESIGN End date is 2025-10-16 with d/r: 55 and last sent: 5 (2025-08-16) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  574-96427129: id 2165 for customer: GREENMAN ENVIRONMENTAL MANAGEMENT End date is 2025-07-25 with d/r: 27 and last sent: 4 (2025-08-17) so 31 (27 + 4 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  563-37300685: id 2180 for customer: RC DONKIN & PARTNER LTD End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-38046292: id 2187 for customer: Claire Raw End date is 2025-11-15 with d/r: 85 and last sent: 5 (2025-08-16) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-92722818: id 2198 for customer: Roadcraft Crane and Plant Hire End date is 2025-07-01 with d/r: 51 and last sent: 5 (2025-08-16) so 56 (51 + 5 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-35106697: id 2202 for customer: ARA Architects End date is 2025-10-26 with d/r: 65 and last sent: 25 (2025-07-27) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-37286823: id 2204 for customer: Keir Townsend Ltd End date is 2025-11-09 with d/r: 79 and last sent: 11 (2025-08-10) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-34540958: id 2209 for customer: Stefan Martin End date is 2025-10-03 with d/r: 42 and last sent: 18 (2025-08-03) so 60 (42 + 18 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-32477432: id 2223 for customer: R NUTTALL & Co. End date is 2025-09-22 with d/r: 31 and last sent: 29 (2025-07-23) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-43813746: id 2224 for customer: Designer Metals Ltd End date is 2025-09-22 with d/r: 31 and last sent: 29 (2025-07-23) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-35831355: id 2225 for customer: VKE Contractors End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2226 for customer: JMC Packaging End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-13762865: id 2227 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 23 and last sent: 7 (2025-08-14) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2242 for customer: PAMTAR Ltd End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-32027641: id 2264 for customer: Abbey Joinery Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-20988838: id 2268 for customer: BEAVERDENT Ltd End date is 2025-09-02 with d/r: 11 and last sent: 4 (2025-08-17) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-10547316: id 2269 for customer: HANNAH LAWSON STUDIO End date is 2025-06-05 with d/r: 77 and last sent: 1 (2025-08-20) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2284 for customer: BIMSynergy End date is 2025-11-02 with d/r: 72 and last sent: 18 (2025-08-03) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  575-02157156: id 2286 for customer: Coast Consulting Engineers End date is 2025-09-04 with d/r: 13 and last sent: 2 (2025-08-19) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-42465462: id 2289 for customer: Utility Consultancy & Engineer End date is 2025-11-10 with d/r: 80 and last sent: 10 (2025-08-11) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  572-54671851: id 2290 for customer: Carson Powell Construction Ltd End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2301 for customer: lydia rowe End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2315 for customer: YORKSHIRE CHOICE HOMES CONSTRUCTION Ltd End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-38076789: id 2319 for customer: THUNDERBOLT & MAINTENANCE End date is 2025-09-13 with d/r: 22 and last sent: 8 (2025-08-13) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  563-35400673: id 2329 for customer: DMC ARCHITECTURE DESIGN Ltd End date is 2025-10-12 with d/r: 51 and last sent: 9 (2025-08-12) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2330 for customer: FRANK H DALE Ltd End date is 2025-10-14 with d/r: 53 and last sent: 7 (2025-08-14) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-86477996: id 2331 for customer: ALU-FIX UK Ltd End date is 2025-06-10 with d/r: 72 and last sent: 12 (2025-08-09) so 84 (72 + 12 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-93544051: id 2332 for customer: Beardmax Limitet End date is 2025-07-07 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-47135979: id 2336 for customer: Karen Gardner Architects Ltd End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2350 for customer: Anthony Stuchberry End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-60486347: id 2352 for customer: NARRACOTT S End date is 2025-10-30 with d/r: 69 and last sent: 21 (2025-07-31) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  572-42802219: id 2413 for customer: VKE Contractors End date is 2025-08-22 with d/r: 0 and last sent: 1 (2025-08-20) so 1 (0 + 1 is above rule 0: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-51983637: id 2428 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-33205625: id 2460 for customer: JDW Architects End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-66746724: id 2476 for customer: ADS End date is 2025-11-01 with d/r: 71 and last sent: 19 (2025-08-02) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  562-94469544: id 2480 for customer: Solus Homes End date is 2025-06-27 with d/r: 55 and last sent: 1 (2025-08-20) so 56 (55 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-10577998: id 2497 for customer: EDM London End date is 2025-06-05 with d/r: 77 and last sent: 1 (2025-08-20) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  570-50497145: id 2500 for customer: Peak Circuit Ltd End date is 2025-10-05 with d/r: 44 and last sent: 16 (2025-08-05) so 60 (44 + 16 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  572-43806564: id 2514 for customer: APPROVED ELECTRICAL INSTALLATI End date is 2025-08-26 with d/r: 4 and last sent: 1 (2025-08-20) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-27265931: id 2521 for customer: ORIGIN DESIGN STUDIO Ltd End date is 2025-09-28 with d/r: 37 and last sent: 23 (2025-07-29) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2524 for customer: LINROC Ltd End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-28774572: id 2527 for customer: INTELECT MECHICAL End date is 2025-08-03 with d/r: 18 and last sent: 3 (2025-08-18) so 21 (18 + 3 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2539 for customer: SeAH Wind Ltd End date is 2025-09-23 with d/r: 32 and last sent: 28 (2025-07-24) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2558 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-41179223: id 2608 for customer: DAVID SALISBURY JOINERY Ltd End date is 2025-11-03 with d/r: 73 and last sent: 17 (2025-08-04) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-40383526: id 2609 for customer: Treveth Holdings LLP End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-05983438: id 2625 for customer: BARRIER ARCHITECTURAL SERVICES End date is 2025-06-13 with d/r: 69 and last sent: 9 (2025-08-12) so 78 (69 + 9 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  563-43882136: id 2626 for customer: Adam Langsbury Chartered Build End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2646 for customer: BIMSynergy End date is 2025-11-02 with d/r: 72 and last sent: 18 (2025-08-03) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  574-17937204: id 2670 for customer: Kevin Mitchell Consulting Engineers Ltd End date is 2025-08-13 with d/r: 8 and last sent: 3 (2025-08-18) so 11 (8 + 3 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-11224705: id 2671 for customer: MELIUS HOMES Ltd End date is 2025-07-10 with d/r: 42 and last sent: 12 (2025-08-09) so 54 (42 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-64915024: id 2683 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-04 with d/r: 78 and last sent: 2 (2025-08-19) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-84641509: id 2688 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 39 and last sent: 21 (2025-07-31) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  566-56786617: id 2708 for customer: A & M Architectural Partnership End date is 2025-05-30 with d/r: 83 and last sent: 7 (2025-08-14) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  572-13359749: id 2709 for customer: Castle Masonry Products Ltd End date is 2025-06-27 with d/r: 55 and last sent: 1 (2025-08-20) so 56 (55 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-95603322: id 2712 for customer: Wiveliscombe Joinery Ltd End date is 2025-07-21 with d/r: 31 and last sent: 1 (2025-08-20) so 32 (31 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  560-07789113: id 2768 for customer: Surveying Solutions Ltd End date is 2025-10-22 with d/r: 61 and last sent: 29 (2025-07-23) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  567-63567482: id 2769 for customer: Bay Building Services End date is 2025-11-05 with d/r: 75 and last sent: 15 (2025-08-06) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  563-48124598: id 2783 for customer: Mark Architecture Ltd End date is 2025-11-13 with d/r: 83 and last sent: 7 (2025-08-14) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  572-75412629: id 2799 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-07 with d/r: 16 and last sent: 14 (2025-08-07) so 30 (16 + 14 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  573-13068029: id 2803 for customer: EDM London End date is 2025-06-15 with d/r: 67 and last sent: 7 (2025-08-14) so 74 (67 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  : id 2804 for customer: BWM End date is 2025-09-24 with d/r: 33 and last sent: 27 (2025-07-25) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-85174040: id 2809 for customer: ATELIERS DE FRANCE End date is 2025-06-02 with d/r: 80 and last sent: 4 (2025-08-17) so 84 (80 + 4 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  564-81177817: id 2814 for customer: E & M Design Partnership End date is 2025-09-20 with d/r: 29 and last sent: 1 (2025-08-20) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-09838098: id 2841 for customer: JAMES MACKINTOSH ARCHITECTS End date is 2025-07-02 with d/r: 50 and last sent: 4 (2025-08-17) so 54 (50 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  573-26001394: id 2869 for customer: MPR Architectural Designs Ltd End date is 2025-08-23 with d/r: 1 and last sent: 2 (2025-08-19) so 3 (1 + 2 is above rule 1: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-33568852: id 2871 for customer: ORIGIN DESIGN STUDIO Ltd End date is 2025-10-18 with d/r: 57 and last sent: 3 (2025-08-18) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-38325416: id 2879 for customer: Kevin Judson End date is 2025-11-16 with d/r: 86 and last sent: 4 (2025-08-17) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  572-55743009: id 2884 for customer: AIR HANDLING SYSTEMS End date is 2025-11-11 with d/r: 81 and last sent: 9 (2025-08-12) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-36239916: id 2898 for customer: PCC CONSULTANTS Ltd End date is 2025-11-01 with d/r: 71 and last sent: 19 (2025-08-02) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  572-43389365: id 2942 for customer: LOWRY LIGHTING SOLUTIONS Ltd End date is 2025-08-24 with d/r: 2 and last sent: 1 (2025-08-20) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  560-14529128: id 2987 for customer: HATTRELL DS ONE End date is 2025-11-15 with d/r: 85 and last sent: 5 (2025-08-16) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:95]  574-18378058: id 2990 for customer: Designteam End date is 2025-08-15 with d/r: 6 and last sent: 1 (2025-08-20) so 7 (6 + 1 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  : id 3004 for customer: WE Marson & Co. End date is 2025-11-20 with d/r: 90 and last sent: 273 () so 363 (90 + 273 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:05] [send_email.php:87]  575-01538336: id 3005 for customer: MO Construction Ltd End date is 2025-09-01 with d/r: 10 and last sent: 5 (2025-08-16) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-59716582: id 3008 for customer: McAuliffe Group End date is 2025-10-29 with d/r: 68 and last sent: 22 (2025-07-30) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-39310653: id 3013 for customer: Squire Associates (Aberdeen) Ltd End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-55398361: id 3021 for customer: Adib Nouri Zina End date is 2025-10-19 with d/r: 58 and last sent: 2 (2025-08-19) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3032 for customer: VKE Contractors End date is 2025-10-20 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  570-38101733: id 3035 for customer: STS STORAGE SYSTEMS End date is 2025-09-13 with d/r: 22 and last sent: 8 (2025-08-13) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3041 for customer: Alex Grey End date is 2025-09-17 with d/r: 26 and last sent: 4 (2025-08-17) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:87]  573-32179603: id 3070 for customer: Kevin Judson End date is 2025-09-21 with d/r: 30 and last sent: 30 (2025-07-22) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-32239155: id 3107 for customer: S2CARCHITECTS End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-35549263: id 3124 for customer: Byjc Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  570-40726176: id 3127 for customer: IGUANA DEVELOPMENTS Ltd End date is 2025-09-16 with d/r: 25 and last sent: 5 (2025-08-16) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3129 for customer: Truck-Lite Europe Ltd End date is 2025-10-22 with d/r: 61 and last sent: 29 (2025-07-23) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:87]  574-25997508: id 3138 for customer: Epoch Architecture End date is 2025-09-21 with d/r: 30 and last sent: 30 (2025-07-22) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  570-44772660: id 3139 for customer: Constantine Design Ltd End date is 2025-09-23 with d/r: 32 and last sent: 28 (2025-07-24) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-17141007: id 3140 for customer: GIGANT Ltd End date is 2025-08-29 with d/r: 7 and last sent: 3 (2025-08-18) so 10 (7 + 3 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-36191146: id 3141 for customer: CSL Associates Ltd End date is 2025-10-11 with d/r: 50 and last sent: 10 (2025-08-11) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-84381313: id 3146 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-30188430: id 3164 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 23 and last sent: 7 (2025-08-14) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  565-00640173: id 3171 for customer: POWERCOM SYSTEMS End date is 2025-11-07 with d/r: 77 and last sent: 13 (2025-08-08) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-34398097: id 3185 for customer: Premo Fabrications Ltd End date is 2025-09-18 with d/r: 27 and last sent: 3 (2025-08-18) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  566-59603476: id 3208 for customer: NIGHTINGALE JOINERY Ltd End date is 2025-06-03 with d/r: 79 and last sent: 3 (2025-08-18) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3239 for customer: M & C ROOFING CONTRACTORS Ltd End date is 2025-11-03 with d/r: 73 and last sent: 17 (2025-08-04) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-55456804: id 3243 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  569-73908915: id 3252 for customer: PDR GROUP SERVICES End date is 2025-08-02 with d/r: 19 and last sent: 4 (2025-08-17) so 23 (19 + 4 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-88124028: id 3257 for customer: JMC Packaging End date is 2025-06-23 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-38596120: id 3277 for customer: ORANGE KEY Ltd End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-06801505: id 3283 for customer: EDM London End date is 2025-06-18 with d/r: 64 and last sent: 4 (2025-08-17) so 68 (64 + 4 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-43598976: id 3327 for customer: Shear Stress Ltd End date is 2025-11-16 with d/r: 86 and last sent: 4 (2025-08-17) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  570-38455782: id 3340 for customer: MACHINES AND CONTROLS Ltd End date is 2025-09-13 with d/r: 22 and last sent: 8 (2025-08-13) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  565-04538979: id 3351 for customer: D & G UTILITIES Ltd End date is 2025-11-14 with d/r: 84 and last sent: 6 (2025-08-15) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-19330566: id 3356 for customer: EDM London End date is 2025-07-14 with d/r: 38 and last sent: 8 (2025-08-13) so 46 (38 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-25816572: id 3366 for customer: JB Surveying Ltd End date is 2025-09-20 with d/r: 29 and last sent: 1 (2025-08-20) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-36777968: id 3367 for customer: RD NAIRN Ltd End date is 2025-11-06 with d/r: 76 and last sent: 14 (2025-08-07) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-60577508: id 3376 for customer: ECS BATH Ltd End date is 2025-10-30 with d/r: 69 and last sent: 21 (2025-07-31) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  570-81465285: id 3394 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-13069019: id 3396 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-15 with d/r: 67 and last sent: 7 (2025-08-14) so 74 (67 + 7 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-04439554: id 3410 for customer: ECODEV GROUP End date is 2025-06-05 with d/r: 77 and last sent: 1 (2025-08-20) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-49530691: id 3412 for customer: TYLER PARKES End date is 2025-10-15 with d/r: 54 and last sent: 6 (2025-08-15) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-26160653: id 3419 for customer: POTTER COWAN End date is 2025-08-24 with d/r: 2 and last sent: 1 (2025-08-20) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-17611758: id 3430 for customer: METRICAB End date is 2025-08-09 with d/r: 12 and last sent: 2 (2025-08-19) so 14 (12 + 2 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:87]  567-53479680: id 3442 for customer: McAuliffe Group End date is 2025-10-21 with d/r: 60 and last sent: 30 (2025-07-22) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3448 for customer: SHERRINGTON LIFTING SERVICES Ltd End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-01043658: id 3451 for customer: Elevation One Building Design Ltd End date is 2025-08-12 with d/r: 9 and last sent: 1 (2025-08-20) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-36777671: id 3452 for customer: V4 ARCHITECTS Ltd End date is 2025-11-06 with d/r: 76 and last sent: 14 (2025-08-07) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  575-01297717: id 3453 for customer: Advanced Water Treatment UK Ltd End date is 2025-08-29 with d/r: 7 and last sent: 3 (2025-08-18) so 10 (7 + 3 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  565-05805127: id 3472 for customer: SL PLASTICS End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-41227921: id 3477 for customer: Verdi Systems Ltd End date is 2025-11-03 with d/r: 73 and last sent: 17 (2025-08-04) so 90 (73 + 17 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3487 for customer: SALT & WHITE ARCHITECTS End date is 2025-10-10 with d/r: 49 and last sent: 11 (2025-08-10) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-27766965: id 3491 for customer: STRUCTURE WORKSHOP End date is 2025-10-02 with d/r: 41 and last sent: 19 (2025-08-02) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3493 for customer: Martyn Lowther End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-61258943: id 3500 for customer: STRUCTURAL DESIGN SERVICES Ltd End date is 2025-11-10 with d/r: 80 and last sent: 10 (2025-08-11) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  561-85235864: id 3522 for customer: LOCKWOODS CONSTRUCTION End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-98419488: id 3544 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-41993197: id 3556 for customer: Lazzeri Creative Interiors End date is 2025-10-01 with d/r: 40 and last sent: 20 (2025-08-01) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-33212652: id 3558 for customer: M & E Design Ltd End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-21063077: id 3564 for customer: KOHA ARCHITECTS Ltd End date is 2025-08-31 with d/r: 9 and last sent: 1 (2025-08-20) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:87]  567-53543424: id 3572 for customer: Jameson Builders End date is 2025-10-21 with d/r: 60 and last sent: 30 (2025-07-22) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-06243754: id 3582 for customer: ALEXANDER WATERWORTH End date is 2025-06-14 with d/r: 68 and last sent: 8 (2025-08-13) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-09401796: id 3598 for customer: ATELIERS DE FRANCE End date is 2025-06-29 with d/r: 53 and last sent: 7 (2025-08-14) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-93530292: id 3602 for customer: HGCE Ltd End date is 2025-07-07 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-32215203: id 3617 for customer: MICHAEL VAUGHAN RACKING SERVIC End date is 2025-09-15 with d/r: 24 and last sent: 6 (2025-08-15) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-34734040: id 3618 for customer: HEATING DESIGN SOLUTIONS End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-27210503: id 3631 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 37 and last sent: 23 (2025-07-29) so 60 (37 + 23 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  566-63440025: id 3649 for customer: Hammond Design End date is 2025-06-04 with d/r: 78 and last sent: 2 (2025-08-19) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-92527234: id 3650 for customer: STRENGER End date is 2025-06-30 with d/r: 52 and last sent: 6 (2025-08-15) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-22571554: id 3693 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3695 for customer: Truck-Lite Europe Ltd End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-91270489: id 3699 for customer: DURATA End date is 2025-06-26 with d/r: 56 and last sent: 2 (2025-08-19) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  560-02732245: id 3718 for customer: FERNBALLOT Ltd End date is 2025-10-11 with d/r: 50 and last sent: 10 (2025-08-11) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-33477126: id 3724 for customer: Glent Engineering Ltd End date is 2025-09-27 with d/r: 36 and last sent: 24 (2025-07-28) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-36759459: id 3729 for customer: OVNI Consulting Engineers Ltd End date is 2025-11-06 with d/r: 76 and last sent: 14 (2025-08-07) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  569-97731818: id 3734 for customer: NORTHALLERTON DRAUGHTING End date is 2025-08-31 with d/r: 9 and last sent: 1 (2025-08-20) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-20718161: id 3736 for customer: KITCHEN ARCHITECTURE Ltd End date is 2025-09-05 with d/r: 14 and last sent: 1 (2025-08-20) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3747 for customer: KMD CONSULTING End date is 2025-10-06 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-57622669: id 3748 for customer: CUMBRIA WASTE GROUP End date is 2025-10-27 with d/r: 66 and last sent: 24 (2025-07-28) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 3751 for customer: Sarah Darlow Darlow End date is 2025-11-11 with d/r: 81 and last sent: 9 (2025-08-12) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-21728743: id 3753 for customer: John Roux End date is 2025-07-26 with d/r: 26 and last sent: 3 (2025-08-18) so 29 (26 + 3 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  564-97534193: id 3755 for customer: Utility Consultancy & Engineer End date is 2025-10-30 with d/r: 69 and last sent: 21 (2025-07-31) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-25204511: id 3764 for customer: Shadbolt Ltd End date is 2025-08-17 with d/r: 4 and last sent: 1 (2025-08-20) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-52388292: id 3777 for customer: FURNESS GREEN PARTNERSHIP End date is 2025-10-06 with d/r: 45 and last sent: 15 (2025-08-06) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-32031304: id 3789 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-09 with d/r: 48 and last sent: 12 (2025-08-09) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-35848309: id 3798 for customer: Silverfox Surveys Ltd End date is 2025-07-26 with d/r: 26 and last sent: 3 (2025-08-18) so 29 (26 + 3 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-62544390: id 3807 for customer: STORTFORD HOLDINGS Ltd End date is 2025-07-11 with d/r: 41 and last sent: 11 (2025-08-10) so 52 (41 + 11 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-57006680: id 3809 for customer: ARCHITECTURAL METALWORK SERVICES Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  567-51281740: id 3810 for customer: Erith Business Systems End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-99555081: id 3811 for customer: R3nder Ltd End date is 2025-08-15 with d/r: 6 and last sent: 1 (2025-08-20) so 7 (6 + 1 is below rule 10: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  777-13092772: id 3823 for customer: EDM-London End date is 2025-06-27 with d/r: 55 and last sent: 1 (2025-08-20) so 56 (55 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-37144192: id 3827 for customer: Dean Whitbrook End date is 2025-11-08 with d/r: 78 and last sent: 12 (2025-08-09) so 90 (78 + 12 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-17118867: id 3828 for customer: KERRY JANE INTERIORS End date is 2025-07-05 with d/r: 47 and last sent: 1 (2025-08-20) so 48 (47 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-55764783: id 3835 for customer: Scomac Services End date is 2025-10-20 with d/r: 59 and last sent: 1 (2025-08-20) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  571-92211472: id 3837 for customer: INTELECT MECHICAL End date is 2025-05-24 with d/r: 89 and last sent: 1 (2025-08-20) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-36646353: id 3857 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 23 and last sent: 7 (2025-08-14) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-53707888: id 3858 for customer: Utility Consultancy & Engineer End date is 2025-10-12 with d/r: 51 and last sent: 9 (2025-08-12) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-32890248: id 3859 for customer: Utility Consultancy & Engineer End date is 2025-11-05 with d/r: 75 and last sent: 15 (2025-08-06) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-40391345: id 3860 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 70 and last sent: 20 (2025-08-01) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-33205130: id 3872 for customer: Promack Ltd End date is 2025-09-26 with d/r: 35 and last sent: 25 (2025-07-27) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  574-14338009: id 3891 for customer: ATELIERS DE FRANCE End date is 2025-07-26 with d/r: 26 and last sent: 3 (2025-08-18) so 29 (26 + 3 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:87]  572-44893063: id 3892 for customer: LONDONSTRUCTURALDESIGN Ltd End date is 2025-09-01 with d/r: 10 and last sent: 5 (2025-08-16) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-37972778: id 3904 for customer: McAuliffe Group End date is 2025-10-19 with d/r: 58 and last sent: 2 (2025-08-19) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  573-43386665: id 3955 for customer: STAND INNOVATIONS Ltd End date is 2025-11-15 with d/r: 85 and last sent: 5 (2025-08-16) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  572-59532046: id 3956 for customer: HAMPTON DOORS End date is 2025-11-01 with d/r: 71 and last sent: 19 (2025-08-02) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  570-75625390: id 3959 for customer: Aughton Automation Ltd End date is 2025-10-17 with d/r: 56 and last sent: 4 (2025-08-17) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:87]  : id 4034 for customer: EPROD Software UK Ltd End date is 2025-05-23 with d/r: 90 and last sent: 2 (2025-08-19) so 92 (90 + 2 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-21 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription {{subs_status,"active":"is ending soon", "expired": "has expired"}}
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 4104 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 52 and last sent: 8 (2025-08-13) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 12536 for customer: JONATHAN LEES ARCHITECTS LLP End date is 2025-07-16 with d/r: 36 and last sent: 6 (2025-08-15) so 42 (36 + 6 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 12543 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 12554 for customer: Denis Welch Motorsport End date is 2025-06-29 with d/r: 53 and last sent: 7 (2025-08-14) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 12588 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 30765 for customer: STORTFORD HOLDINGS Ltd End date is 2025-08-10 with d/r: 11 and last sent: 1 (2025-08-20) so 12 (11 + 1 is below rule 15: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 62691 for customer: POTTER COWAN End date is 2025-08-24 with d/r: 2 and last sent: 1 (2025-08-20) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 62717 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 62758 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 64 and last sent: 26 (2025-07-26) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 62776 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 88 and last sent: 2 (2025-08-19) so 90 (88 + 2 is below rule 90: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 62789 for customer: GRAY & DICK Ltd End date is 2025-07-30 with d/r: 22 and last sent: 7 (2025-08-14) so 29 (22 + 7 is below rule 30: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 63030 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 39 and last sent: 10 () so 49 (39 + 10 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:95]  : id 63034 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 39 and last sent: 10 () so 49 (39 + 10 is below rule 60: not sending email
[email_sender] [2025-08-21 11:00:06] [send_email.php:104] Email sending process completed. 36 emails sent.
[email_sender] [2025-08-22 11:00:03] [send_email.php:11]  autodesk_api initialized.
[email_sender] [2025-08-22 11:00:03] [send_email.php:18]  Settings retrieved from database. Rules: -20,-15,-10,0,1,3,5,10,15,30,60,90, Settings days: ,true,true,true,true,true and time: 11
[email_sender] [2025-08-22 11:00:03] [send_email.php:38]  Settings retrieved from database.
[email_sender] [2025-08-22 11:00:03] [send_email.php:39]  Valid send time detected. Proceeding with email processing.
[email_sender] [2025-08-22 11:00:03] [send_email.php:51]  3767 renewable subscriptions retrieved.
[email_sender] [2025-08-22 11:00:03] [send_email.php:58]  Email template loaded successfully.
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  574-36033345: id 5 for customer: RICHARDSON ARCHITECTURE Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  570-48836366: id 20 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-01 with d/r: 39 and last sent: 21 (2025-08-01) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  570-67307146: id 28 for customer: THORNE RAINWATER SYSTEMS End date is 2025-11-02 with d/r: 71 and last sent: 19 (2025-08-03) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  564-89454291: id 29 for customer: IRD DESIGN Ltd End date is 2025-10-14 with d/r: 52 and last sent: 8 (2025-08-14) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:87]  575-00642075: id 43 for customer: IAN TITLEY DESIGN End date is 2025-08-26 with d/r: 3 and last sent: 2 (2025-08-20) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:03] [send_email.php:87]  574-90800535: id 53 for customer: BARRIER ARCHITECTURAL SERVICES End date is 2025-06-25 with d/r: 58 and last sent: 4 (2025-08-18) so 62 (58 + 4 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  574-21267569: id 75 for customer: apdesign End date is 2025-09-03 with d/r: 11 and last sent: 4 (2025-08-18) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  572-48896886: id 101 for customer: DSB PROPERTY DESIGNS End date is 2025-09-20 with d/r: 28 and last sent: 2 (2025-08-20) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  574-37654136: id 103 for customer: Barden Chapman End date is 2025-11-13 with d/r: 82 and last sent: 8 (2025-08-14) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  567-63524427: id 104 for customer: CT GLASS End date is 2025-11-05 with d/r: 74 and last sent: 16 (2025-08-06) so 90 (74 + 16 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  574-32890446: id 106 for customer: VKE Contractors End date is 2025-10-15 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  574-94161683: id 107 for customer: ADR Consulting End date is 2025-07-10 with d/r: 43 and last sent: 13 (2025-08-09) so 56 (43 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  574-97991797: id 117 for customer: ATELIERS DE FRANCE End date is 2025-07-31 with d/r: 22 and last sent: 7 (2025-08-15) so 29 (22 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:87]  569-59178474: id 125 for customer: 360 Prism Ltd End date is 2025-07-07 with d/r: 46 and last sent: 16 (2025-08-06) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  564-87864877: id 127 for customer: Robert Brown Smith End date is 2025-10-08 with d/r: 46 and last sent: 14 (2025-08-08) so 60 (46 + 14 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  570-41940359: id 135 for customer: ADAMS DESIGN ASSOCIATES Ltd End date is 2025-09-20 with d/r: 28 and last sent: 2 (2025-08-20) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:95]  573-40188932: id 137 for customer: EDM London End date is 2025-10-30 with d/r: 68 and last sent: 22 (2025-07-31) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:03] [send_email.php:87]  574-96087431: id 152 for customer: Civils & Construction Solutions End date is 2025-07-23 with d/r: 30 and last sent: 1 (2025-08-21) so 31 (30 + 1 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:03] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:04] [send_email.php:87]  573-25195702: id 181 for customer: VKE Contractors End date is 2025-08-17 with d/r: 5 and last sent: 2 (2025-08-20) so 7 (5 + 2 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-48568175: id 185 for customer: LITAC ENGINEERING End date is 2025-09-19 with d/r: 27 and last sent: 3 (2025-08-19) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-47469602: id 187 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-72954823: id 198 for customer: CISTEC Ltd End date is 2025-11-11 with d/r: 80 and last sent: 10 (2025-08-12) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-18644809: id 201 for customer: GASTECH ENGINEERING Ltd End date is 2025-08-16 with d/r: 6 and last sent: 1 (2025-08-21) so 7 (6 + 1 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  563-47081948: id 207 for customer: Tristan Plant End date is 2025-11-09 with d/r: 78 and last sent: 12 (2025-08-10) so 90 (78 + 12 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-60126176: id 210 for customer: CROSSOVER AV Ltd End date is 2025-10-21 with d/r: 59 and last sent: 1 (2025-08-21) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  575-01683044: id 222 for customer: JCP Consulting Ltd End date is 2025-09-06 with d/r: 14 and last sent: 1 (2025-08-21) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  569-55575321: id 227 for customer: JEL Renewables Ltd End date is 2025-06-30 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-33202457: id 242 for customer: Promack Ltd End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-54042242: id 243 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  569-99919365: id 244 for customer: REALISATION BY DESIGN Ltd End date is 2025-09-03 with d/r: 11 and last sent: 4 (2025-08-18) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-58045471: id 261 for customer: BOWER EDLESTON End date is 2025-10-27 with d/r: 65 and last sent: 25 (2025-07-28) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-93279082: id 263 for customer: Blue Aardvark Joinery End date is 2025-07-04 with d/r: 49 and last sent: 3 (2025-08-19) so 52 (49 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:87]  569-91563509: id 295 for customer: Concept Eight Architects End date is 2025-08-23 with d/r: 0 and last sent: 1 (2025-08-21) so 1 (0 + 1 is above rule 0: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  : id 308 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-09-19 with d/r: 27 and last sent: 3 (2025-08-19) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-60446078: id 325 for customer: Leech Mechanical Services Ltd End date is 2025-10-21 with d/r: 59 and last sent: 1 (2025-08-21) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  : id 332 for customer: K BAVA ARCHITECTS Ltd End date is 2025-10-02 with d/r: 40 and last sent: 20 (2025-08-02) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  561-95896659: id 343 for customer: Davies Mr End date is 2025-10-24 with d/r: 62 and last sent: 28 (2025-07-25) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-12556332: id 352 for customer: Whitaker Lianne End date is 2025-06-24 with d/r: 59 and last sent: 1 (2025-08-21) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-27481734: id 353 for customer: Glent Engineering Ltd End date is 2025-06-10 with d/r: 73 and last sent: 13 (2025-08-09) so 86 (73 + 13 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-41393046: id 367 for customer: TUFCOT ENGINEERING Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  563-47765403: id 368 for customer: AMALGAM MODELMAKERS Ltd End date is 2025-11-12 with d/r: 81 and last sent: 9 (2025-08-13) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-45667281: id 369 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-09-06 with d/r: 14 and last sent: 1 (2025-08-21) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-98994263: id 380 for customer: NGP Architecture Ltd End date is 2025-08-11 with d/r: 11 and last sent: 1 (2025-08-21) so 12 (11 + 1 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  : id 399 for customer: STRENGER End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-51541127: id 419 for customer: O'MAC CONSTRUCTION End date is 2025-10-03 with d/r: 41 and last sent: 19 (2025-08-03) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-31780884: id 426 for customer: J W CONTRACT SERVICES Ltd End date is 2025-10-08 with d/r: 46 and last sent: 14 (2025-08-08) so 60 (46 + 14 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  567-64336356: id 448 for customer: Bay Building Services End date is 2025-11-06 with d/r: 75 and last sent: 15 (2025-08-07) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-40116973: id 472 for customer: Aecor Marine Ltd End date is 2025-10-30 with d/r: 68 and last sent: 22 (2025-07-31) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  : id 476 for customer: Blue Aardvark Joinery End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-59689919: id 477 for customer: NIC Ltd End date is 2025-11-02 with d/r: 71 and last sent: 19 (2025-08-03) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  567-56252692: id 478 for customer: Cornerstone Projects Ltd End date is 2025-10-24 with d/r: 62 and last sent: 28 (2025-07-25) so 90 (62 + 28 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-31811921: id 481 for customer: CAMB MACHINE KNIVES End date is 2025-07-19 with d/r: 34 and last sent: 4 (2025-08-18) so 38 (34 + 4 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-95952873: id 492 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-12406719: id 502 for customer: Project Marble Ltd End date is 2025-07-16 with d/r: 37 and last sent: 7 (2025-08-15) so 44 (37 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-00229171: id 503 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-09-03 with d/r: 11 and last sent: 4 (2025-08-18) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-85203338: id 511 for customer: ATELIERS DE FRANCE End date is 2025-06-02 with d/r: 81 and last sent: 5 (2025-08-17) so 86 (81 + 5 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-27210206: id 522 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 36 and last sent: 24 (2025-07-29) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  : id 523 for customer: HEWITSON Ltd End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:87]  566-96027273: id 525 for customer: Frame Development End date is 2025-07-30 with d/r: 23 and last sent: 8 (2025-08-14) so 31 (23 + 8 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  567-64333386: id 534 for customer: Bay Building Services End date is 2025-11-06 with d/r: 75 and last sent: 15 (2025-08-07) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-40524951: id 537 for customer: Manor Construction (South Yorkshire) Ltd End date is 2025-09-16 with d/r: 24 and last sent: 6 (2025-08-16) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-65006068: id 556 for customer: McAuliffe Group End date is 2025-10-28 with d/r: 66 and last sent: 24 (2025-07-29) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-25454704: id 557 for customer: EVOLVE INTEGRATED SOLUTIONS End date is 2025-09-20 with d/r: 28 and last sent: 2 (2025-08-20) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-55764387: id 559 for customer: Scomac Services End date is 2025-10-20 with d/r: 58 and last sent: 2 (2025-08-20) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-04847152: id 566 for customer: ALLAN MCGOVERN End date is 2025-06-06 with d/r: 77 and last sent: 1 (2025-08-21) so 78 (77 + 1 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  563-44078413: id 586 for customer: Ground Condition Consultants L End date is 2025-11-01 with d/r: 70 and last sent: 20 (2025-08-02) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  564-98116688: id 595 for customer: Peter Cogill End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  : id 622 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-34812927: id 623 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-27543694: id 634 for customer: BRU Fabrication End date is 2025-08-31 with d/r: 8 and last sent: 2 (2025-08-20) so 10 (8 + 2 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-36183822: id 636 for customer: Murwell Consulting Engineers Ltd End date is 2025-10-11 with d/r: 49 and last sent: 11 (2025-08-11) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-94161782: id 660 for customer: ADR Consulting End date is 2025-07-10 with d/r: 43 and last sent: 13 (2025-08-09) so 56 (43 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-35853527: id 662 for customer: Utility Consultancy & Engineer End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  564-79249198: id 685 for customer: TURNBULL SURVEYING End date is 2025-09-16 with d/r: 24 and last sent: 6 (2025-08-16) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  572-55812392: id 687 for customer: Hewer FM Ltd End date is 2025-10-20 with d/r: 58 and last sent: 2 (2025-08-20) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-31816149: id 697 for customer: All Design (Scotland) Ltd End date is 2025-09-20 with d/r: 28 and last sent: 2 (2025-08-20) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-24998408: id 698 for customer: ZinCo Green Roof Systems End date is 2025-09-18 with d/r: 26 and last sent: 4 (2025-08-18) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-95439412: id 705 for customer: ADF Paris End date is 2025-07-18 with d/r: 35 and last sent: 5 (2025-08-17) so 40 (35 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  569-67099416: id 716 for customer: COVENTRY CONSTRUCTION Ltd End date is 2025-07-20 with d/r: 33 and last sent: 3 (2025-08-19) so 36 (33 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-27784286: id 719 for customer: Clive Martin End date is 2025-10-02 with d/r: 40 and last sent: 20 (2025-08-02) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  777-11777898: id 737 for customer: NARRACOTT S End date is 2025-10-15 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-37459245: id 742 for customer: Southern Aluminium Upvc End date is 2025-11-12 with d/r: 81 and last sent: 9 (2025-08-13) so 90 (81 + 9 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-16408490: id 762 for customer: One Design Architectural Services End date is 2025-06-30 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-13128704: id 785 for customer: Workbox UK Ltd End date is 2025-06-15 with d/r: 68 and last sent: 8 (2025-08-14) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  573-19751230: id 789 for customer: DLG Architects Leeds End date is 2025-07-17 with d/r: 36 and last sent: 6 (2025-08-16) so 42 (36 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-23764132: id 795 for customer: Geoffrey Robinson Ltd End date is 2025-09-12 with d/r: 20 and last sent: 10 (2025-08-12) so 30 (20 + 10 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-38101832: id 796 for customer: STS STORAGE SYSTEMS End date is 2025-09-13 with d/r: 21 and last sent: 9 (2025-08-13) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  564-46205357: id 802 for customer: JAMES MACKINTOSH ARCHITECTS End date is 2025-06-13 with d/r: 70 and last sent: 10 (2025-08-12) so 80 (70 + 10 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  569-82407105: id 823 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-38111433: id 840 for customer: TIM HB Ltd End date is 2025-09-13 with d/r: 21 and last sent: 9 (2025-08-13) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  570-39779536: id 841 for customer: PLACEFIRST CONSTRUCTION End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  567-37574551: id 842 for customer: AMW DESIGN Ltd End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:87]  573-23630636: id 846 for customer: GRK CIVILS Ltd End date is 2025-08-07 with d/r: 15 and last sent: 2 (2025-08-20) so 17 (15 + 2 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:04] [send_email.php:87]  : id 859 for customer: SeAH Wind Ltd End date is 2025-10-22 with d/r: 60 and last sent: 30 (2025-07-23) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:04] [send_email.php:95]  574-27210404: id 860 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 36 and last sent: 24 (2025-07-29) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:04] [send_email.php:87]  572-49274889: id 875 for customer: NIC Ltd End date is 2025-09-22 with d/r: 30 and last sent: 30 (2025-07-23) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:04] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  569-57473650: id 876 for customer: Surrey Tech Services Ltd End date is 2025-07-05 with d/r: 48 and last sent: 2 (2025-08-20) so 50 (48 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-55701536: id 891 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-10-20 with d/r: 58 and last sent: 2 (2025-08-20) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-06550095: id 892 for customer: ark architecture + design End date is 2025-06-15 with d/r: 68 and last sent: 8 (2025-08-14) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:87]  574-39106958: id 911 for customer: 2B Engineering Ltd End date is 2025-11-21 with d/r: 90 and last sent: 639 () so 729 (90 + 639 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  567-38296113: id 917 for customer: Wensley & Lawz Ltd End date is 2025-09-25 with d/r: 33 and last sent: 27 (2025-07-26) so 60 (33 + 27 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-51548253: id 922 for customer: THE BUSH CONSULTANCY Ltd End date is 2025-10-03 with d/r: 41 and last sent: 19 (2025-08-03) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-40455681: id 938 for customer: GREENCOLD End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:87]  567-72535826: id 952 for customer: McAuliffe Group End date is 2025-11-21 with d/r: 90 and last sent: 2100 () so 2190 (90 + 2100 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  567-60486446: id 956 for customer: NARRACOTT S End date is 2025-10-30 with d/r: 68 and last sent: 22 (2025-07-31) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-30772311: id 957 for customer: TCS CAD & BIM Solutions Ltd End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  571-76923876: id 959 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 22 and last sent: 8 (2025-08-14) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-46238294: id 968 for customer: Furness Green Partnership End date is 2025-09-08 with d/r: 16 and last sent: 14 (2025-08-08) so 30 (16 + 14 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:87]  : id 971 for customer: Cladtech Systems Industrial Roofing & Cladding End date is 2025-09-22 with d/r: 30 and last sent: 30 (2025-07-23) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  569-38279528: id 984 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-04 with d/r: 79 and last sent: 3 (2025-08-19) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  569-33468427: id 988 for customer: CUTLER & MACLEAN Ltd End date is 2025-05-28 with d/r: 86 and last sent: 2 (2025-08-20) so 88 (86 + 2 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-36040471: id 991 for customer: Truck-Lite Europe Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  569-71004061: id 993 for customer: John Woodvine End date is 2025-08-05 with d/r: 17 and last sent: 2 (2025-08-20) so 19 (17 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  555-79670111: id 1006 for customer: Mark Thornton End date is 2025-10-01 with d/r: 39 and last sent: 21 (2025-08-01) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  570-61955320: id 1010 for customer: EMILY ESTATE UK Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-27277512: id 1022 for customer: Broad Planning and Architecture End date is 2025-09-28 with d/r: 36 and last sent: 24 (2025-07-29) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-38345113: id 1040 for customer: Spirotech SRD Group Ltd End date is 2025-11-16 with d/r: 85 and last sent: 5 (2025-08-17) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-34733842: id 1051 for customer: SeAH Wind Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  564-79253653: id 1070 for customer: Philip Bingham Associates End date is 2025-09-16 with d/r: 24 and last sent: 6 (2025-08-16) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  : id 1075 for customer: MQM Ltd End date is 2025-10-28 with d/r: 66 and last sent: 24 (2025-07-29) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-13383998: id 1082 for customer: EDM-London End date is 2025-06-27 with d/r: 56 and last sent: 2 (2025-08-20) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  777-13196806: id 1083 for customer: BLACKFRIARS STAGING Ltd End date is 2025-09-06 with d/r: 14 and last sent: 1 (2025-08-21) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-31115373: id 1085 for customer: Syntegon Telstar UK Ltd End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-38587739: id 1092 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-23 with d/r: 61 and last sent: 29 (2025-07-24) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  570-59618709: id 1120 for customer: JOHN FOWKES ARCHITECTS End date is 2025-10-20 with d/r: 58 and last sent: 2 (2025-08-20) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  564-84641410: id 1121 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 38 and last sent: 22 (2025-07-31) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-37286526: id 1125 for customer: AIR HANDLING SYSTEMS End date is 2025-11-10 with d/r: 79 and last sent: 11 (2025-08-11) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  563-39096670: id 1126 for customer: COOLSPOT Ltd End date is 2025-10-19 with d/r: 57 and last sent: 3 (2025-08-19) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-57898459: id 1144 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  571-61094664: id 1146 for customer: FRANK H DALE Ltd End date is 2025-10-14 with d/r: 52 and last sent: 8 (2025-08-14) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-36056534: id 1147 for customer: Bauer Consult End date is 2025-10-11 with d/r: 49 and last sent: 11 (2025-08-11) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-50153433: id 1158 for customer: IMPC Ltd End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-92526838: id 1161 for customer: Chris Hinchliff End date is 2025-06-30 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-46580467: id 1162 for customer: Architectural Designs Derby End date is 2025-09-09 with d/r: 17 and last sent: 13 (2025-08-09) so 30 (17 + 13 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  567-58754007: id 1176 for customer: Electrical Automation Solution End date is 2025-10-28 with d/r: 66 and last sent: 24 (2025-07-29) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  : id 1178 for customer: HERMANTES STUDIO End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-36391678: id 1181 for customer: Daniela Favero End date is 2025-10-12 with d/r: 50 and last sent: 10 (2025-08-12) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-37910746: id 1183 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  567-65874005: id 1184 for customer: Anchorpoint Interiors End date is 2025-11-10 with d/r: 79 and last sent: 11 (2025-08-11) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  573-36350405: id 1197 for customer: DOWEN FARMER ARCHITECTS Ltd End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  563-24896167: id 1211 for customer: INTELECT MECHICAL End date is 2025-09-17 with d/r: 25 and last sent: 5 (2025-08-17) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  : id 1216 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-51912398: id 1246 for customer: C BARLEY SERVICES End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  570-64821075: id 1251 for customer: ALEXANDER WATERWORTH End date is 2025-10-28 with d/r: 66 and last sent: 24 (2025-07-29) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:87]  574-97316361: id 1278 for customer: Arkitectonic End date is 2025-07-30 with d/r: 23 and last sent: 8 (2025-08-14) so 31 (23 + 8 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-32020515: id 1292 for customer: Abbey Joinery Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-85170872: id 1294 for customer: Igloo Design and Consultancy Ltd End date is 2025-06-09 with d/r: 74 and last sent: 14 (2025-08-08) so 88 (74 + 14 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  574-21464638: id 1296 for customer: HGCE Ltd End date is 2025-09-04 with d/r: 12 and last sent: 3 (2025-08-19) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-62802431: id 1297 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  565-04736147: id 1298 for customer: B-12 Development Ltd End date is 2025-11-14 with d/r: 83 and last sent: 7 (2025-08-15) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  572-60416726: id 1312 for customer: Nuckey James End date is 2025-11-07 with d/r: 76 and last sent: 14 (2025-08-08) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  570-74943521: id 1313 for customer: COLSEC Ltd End date is 2025-11-17 with d/r: 86 and last sent: 4 (2025-08-18) so 90 (86 + 4 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  564-87293072: id 1359 for customer: TANDEM ARCHITECTS Ltd End date is 2025-10-07 with d/r: 45 and last sent: 15 (2025-08-07) so 60 (45 + 15 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:87]  574-93532668: id 1363 for customer: HGCE Ltd End date is 2025-07-07 with d/r: 46 and last sent: 16 (2025-08-06) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:05] [send_email.php:95]  566-87318554: id 1364 for customer: JAMESDODDRELL:ARCHITECT Ltd End date is 2025-07-17 with d/r: 36 and last sent: 6 (2025-08-16) so 42 (36 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:05] [send_email.php:87]  575-01066402: id 1365 for customer: southeast design solutions Ltd End date is 2025-08-28 with d/r: 5 and last sent: 5 (2025-08-17) so 10 (5 + 5 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:05] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  : id 1402 for customer: Simon Farr End date is 2025-10-27 with d/r: 65 and last sent: 25 (2025-07-28) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-94018361: id 1404 for customer: SP Joinery Design solutions End date is 2025-07-09 with d/r: 44 and last sent: 14 (2025-08-08) so 58 (44 + 14 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-36255356: id 1419 for customer: HYDROMAX Inc. Ltd End date is 2025-11-01 with d/r: 70 and last sent: 20 (2025-08-02) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  567-21993480: id 1431 for customer: ALMA SHEET METAL LTD End date is 2025-09-03 with d/r: 11 and last sent: 4 (2025-08-18) so 15 (11 + 4 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-94376298: id 1434 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-34367222: id 1435 for customer: Design Coalition End date is 2025-10-23 with d/r: 61 and last sent: 29 (2025-07-24) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-38816849: id 1448 for customer: Mactech Europe Ltd End date is 2025-11-20 with d/r: 89 and last sent: 1 (2025-08-21) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:87]  : id 1456 for customer: Cladtech Systems Industrial Roofing & Cladding End date is 2025-09-22 with d/r: 30 and last sent: 30 (2025-07-23) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:06] [send_email.php:87]  575-01669285: id 1467 for customer: SeAH Wind Ltd End date is 2025-09-02 with d/r: 10 and last sent: 5 (2025-08-17) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-32724853: id 1470 for customer: HMA Ventilation Ltd End date is 2025-10-12 with d/r: 50 and last sent: 10 (2025-08-12) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  570-38071148: id 1474 for customer: BRODIE PLANNING ASSOCIATES Ltd End date is 2025-09-13 with d/r: 21 and last sent: 9 (2025-08-13) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  : id 1491 for customer: Abbey Joinery Ltd End date is 2025-10-03 with d/r: 41 and last sent: 19 (2025-08-03) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-05573266: id 1496 for customer: ATELIERS DE FRANCE End date is 2025-06-11 with d/r: 72 and last sent: 12 (2025-08-10) so 84 (72 + 12 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-14186398: id 1498 for customer: RUSHMON Ltd End date is 2025-06-22 with d/r: 61 and last sent: 1 (2025-08-21) so 62 (61 + 1 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:87]  572-19399186: id 1508 for customer: Roger Betts End date is 2025-07-07 with d/r: 46 and last sent: 16 (2025-08-06) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-33205526: id 1512 for customer: JDW Architects End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-32125658: id 1520 for customer: DLG Architects Leeds End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  569-93809751: id 1527 for customer: CPI Group End date is 2025-08-25 with d/r: 2 and last sent: 1 (2025-08-21) so 3 (2 + 1 is below rule 3: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-25275550: id 1542 for customer: Kevin Judson End date is 2025-09-19 with d/r: 27 and last sent: 3 (2025-08-19) so 30 (27 + 3 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-36941115: id 1545 for customer: James M Brown Ltd End date is 2025-10-16 with d/r: 54 and last sent: 6 (2025-08-16) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-94152181: id 1553 for customer: Artium Construction Ltd End date is 2025-07-10 with d/r: 43 and last sent: 13 (2025-08-09) so 56 (43 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  : id 1554 for customer: ATELIERS DE FRANCE End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  567-47838438: id 1571 for customer: FURNESS GREEN PARTNERSHIP End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-98883704: id 1587 for customer: STORTFORD HOLDINGS Ltd End date is 2025-08-08 with d/r: 14 and last sent: 1 (2025-08-21) so 15 (14 + 1 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  570-48504685: id 1607 for customer: IRELAND ALBRECHT LANDSCAPE Ltd End date is 2025-09-30 with d/r: 38 and last sent: 22 (2025-07-31) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-06066877: id 1611 for customer: VKE Contractors End date is 2025-06-13 with d/r: 70 and last sent: 10 (2025-08-12) so 80 (70 + 10 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  564-89630277: id 1650 for customer: KOHA ARCHITECTS Ltd End date is 2025-10-14 with d/r: 52 and last sent: 8 (2025-08-14) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  575-00807173: id 1653 for customer: ARDERN & DRUGGAN Ltd End date is 2025-08-27 with d/r: 4 and last sent: 1 (2025-08-21) so 5 (4 + 1 is below rule 5: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  563-37468456: id 1673 for customer: GM Steel Newark Ltd End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-98881031: id 1676 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-32016852: id 1707 for customer: J W CONTRACT SERVICES Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  : id 1708 for customer: JAWDESIGN End date is 2025-10-16 with d/r: 54 and last sent: 6 (2025-08-16) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-29853680: id 1709 for customer: ECS BATH Ltd End date is 2025-09-12 with d/r: 20 and last sent: 10 (2025-08-12) so 30 (20 + 10 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  572-54600981: id 1710 for customer: CONSULTANCY 26 SERVICES LTD End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-61569683: id 1717 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-33673007: id 1725 for customer: Kevin Judson End date is 2025-09-28 with d/r: 36 and last sent: 24 (2025-07-29) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-04208338: id 1739 for customer: L & Architects End date is 2025-06-04 with d/r: 79 and last sent: 3 (2025-08-19) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-14195902: id 1776 for customer: Thermal Earth Ltd End date is 2025-06-22 with d/r: 61 and last sent: 1 (2025-08-21) so 62 (61 + 1 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  : id 1777 for customer: PCC CONSULTANTS Ltd End date is 2025-11-01 with d/r: 70 and last sent: 20 (2025-08-02) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:87]  567-72621938: id 1778 for customer: Designmap End date is 2025-11-21 with d/r: 90 and last sent: 2100 () so 2190 (90 + 2100 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  573-35824229: id 1782 for customer: VKE Contractors End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  574-27569896: id 1788 for customer: AP4 LLP End date is 2025-10-01 with d/r: 39 and last sent: 21 (2025-08-01) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:87]  572-11448156: id 1804 for customer: ADF Paris End date is 2025-06-23 with d/r: 60 and last sent: 2 (2025-08-20) so 62 (60 + 2 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:06] [send_email.php:95]  : id 1814 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:06] [send_email.php:87]  573-24035957: id 1816 for customer: PREMIER CAD Ltd End date is 2025-08-09 with d/r: 13 and last sent: 3 (2025-08-19) so 16 (13 + 3 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:06] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 1829 for customer: ARTFORM ARCHITECTS End date is 2025-10-06 with d/r: 44 and last sent: 16 (2025-08-06) so 60 (44 + 16 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  574-96089708: id 1874 for customer: V4 ARCHITECTS Ltd End date is 2025-07-23 with d/r: 30 and last sent: 1 (2025-08-21) so 31 (30 + 1 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-94938079: id 1889 for customer: Wiveliscombe Joinery Ltd End date is 2025-07-21 with d/r: 32 and last sent: 2 (2025-08-20) so 34 (32 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-35472356: id 1893 for customer: LIFESTYLE INTERIORS Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-04006645: id 1904 for customer: Jameson Builders End date is 2025-10-21 with d/r: 59 and last sent: 1 (2025-08-21) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  570-48342359: id 1907 for customer: DAVID HALLAM Ltd End date is 2025-09-30 with d/r: 38 and last sent: 22 (2025-07-31) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  564-82289555: id 1908 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  567-59716681: id 1922 for customer: McAuliffe Group End date is 2025-10-29 with d/r: 67 and last sent: 23 (2025-07-30) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  564-88436286: id 1926 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-12577755: id 1942 for customer: ATELIERS DE FRANCE End date is 2025-07-17 with d/r: 36 and last sent: 6 (2025-08-16) so 42 (36 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-30137158: id 1944 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  564-93155733: id 1955 for customer: MRM ELECTRICAL SOLUTIONS Ltd End date is 2025-10-23 with d/r: 61 and last sent: 29 (2025-07-24) so 90 (61 + 29 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  563-46766006: id 1972 for customer: BSBA TEES Ltd End date is 2025-11-10 with d/r: 79 and last sent: 11 (2025-08-11) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  575-01512206: id 1982 for customer: Ramsay McMichael Consulting End date is 2025-09-01 with d/r: 9 and last sent: 1 (2025-08-21) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  567-50418836: id 1989 for customer: Elevation One Building Design Ltd End date is 2025-10-16 with d/r: 54 and last sent: 6 (2025-08-16) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  569-91701485: id 1991 for customer: Eyeking Ltd End date is 2025-08-23 with d/r: 0 and last sent: 1 (2025-08-21) so 1 (0 + 1 is above rule 0: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 1992 for customer: VKE Contractors End date is 2025-10-01 with d/r: 39 and last sent: 21 (2025-08-01) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  567-33245480: id 2001 for customer: MAX BUSTON Ltd End date is 2025-09-16 with d/r: 24 and last sent: 6 (2025-08-16) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  570-59027997: id 2003 for customer: RUSSELL JONES Ltd End date is 2025-10-19 with d/r: 57 and last sent: 3 (2025-08-19) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2007 for customer: Shields (Driffield) Ltd End date is 2025-09-30 with d/r: 38 and last sent: 22 (2025-07-31) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2008 for customer: George Everett End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-64043212: id 2020 for customer: ALEXANDER WATERWORTH End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-85627465: id 2040 for customer: COHANIM ARCHITECTURE End date is 2025-06-04 with d/r: 79 and last sent: 3 (2025-08-19) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-00538372: id 2054 for customer: JEL Renewables Ltd End date is 2025-05-25 with d/r: 89 and last sent: 1 (2025-08-21) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  567-58721541: id 2055 for customer: Dendra Consulting Ltd End date is 2025-10-28 with d/r: 66 and last sent: 24 (2025-07-29) so 90 (66 + 24 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-37196454: id 2069 for customer: COWAL DESIGN CONSULTANTS End date is 2025-11-08 with d/r: 77 and last sent: 13 (2025-08-09) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  574-96315974: id 2074 for customer: Stair Formwork End date is 2025-07-24 with d/r: 29 and last sent: 2 (2025-08-20) so 31 (29 + 2 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  567-27391729: id 2078 for customer: CUBIC BUILDING SURVEYING End date is 2025-09-05 with d/r: 13 and last sent: 2 (2025-08-20) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2094 for customer: AGW ELECTRICAL SERVICES Ltd End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  570-40734292: id 2115 for customer: John Farah End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  564-86383747: id 2120 for customer: KT Fabrications Ltd End date is 2025-10-03 with d/r: 41 and last sent: 19 (2025-08-03) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-33329619: id 2123 for customer: Aughton Automation Ltd End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2136 for customer: Washington Waterjet Ltd End date is 2025-10-02 with d/r: 40 and last sent: 20 (2025-08-02) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-83903340: id 2140 for customer: CHESTERFORD SURVEYS Ltd End date is 2025-06-10 with d/r: 73 and last sent: 13 (2025-08-09) so 86 (73 + 13 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  563-35958127: id 2142 for customer: IRD DESIGN Ltd End date is 2025-10-15 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-15237562: id 2143 for customer: TFG STAGE TECHNOLOGY Ltd End date is 2025-06-27 with d/r: 56 and last sent: 2 (2025-08-20) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  564-89015021: id 2151 for customer: SPECTRUM WORKPLACE Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-19349768: id 2153 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 22 and last sent: 8 (2025-08-14) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-36391381: id 2158 for customer: Aecor Marine Ltd End date is 2025-10-12 with d/r: 50 and last sent: 10 (2025-08-12) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2164 for customer: JAWDESIGN End date is 2025-10-16 with d/r: 54 and last sent: 6 (2025-08-16) so 60 (54 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-96427129: id 2165 for customer: GREENMAN ENVIRONMENTAL MANAGEMENT End date is 2025-07-25 with d/r: 28 and last sent: 1 (2025-08-21) so 29 (28 + 1 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  563-37300685: id 2180 for customer: RC DONKIN & PARTNER LTD End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-38046292: id 2187 for customer: Claire Raw End date is 2025-11-15 with d/r: 84 and last sent: 6 (2025-08-16) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-92722818: id 2198 for customer: Roadcraft Crane and Plant Hire End date is 2025-07-01 with d/r: 52 and last sent: 6 (2025-08-16) so 58 (52 + 6 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-35106697: id 2202 for customer: ARA Architects End date is 2025-10-26 with d/r: 64 and last sent: 26 (2025-07-27) so 90 (64 + 26 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-37286823: id 2204 for customer: Keir Townsend Ltd End date is 2025-11-09 with d/r: 78 and last sent: 12 (2025-08-10) so 90 (78 + 12 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-34540958: id 2209 for customer: Stefan Martin End date is 2025-10-03 with d/r: 41 and last sent: 19 (2025-08-03) so 60 (41 + 19 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  573-32477432: id 2223 for customer: R NUTTALL & Co. End date is 2025-09-22 with d/r: 30 and last sent: 30 (2025-07-23) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  570-43813746: id 2224 for customer: Designer Metals Ltd End date is 2025-09-22 with d/r: 30 and last sent: 30 (2025-07-23) so 60 (30 + 30 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-35831355: id 2225 for customer: VKE Contractors End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2226 for customer: JMC Packaging End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-13762865: id 2227 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 22 and last sent: 8 (2025-08-14) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2242 for customer: PAMTAR Ltd End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-32027641: id 2264 for customer: Abbey Joinery Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  567-20988838: id 2268 for customer: BEAVERDENT Ltd End date is 2025-09-02 with d/r: 10 and last sent: 5 (2025-08-17) so 15 (10 + 5 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-10547316: id 2269 for customer: HANNAH LAWSON STUDIO End date is 2025-06-05 with d/r: 78 and last sent: 2 (2025-08-20) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2284 for customer: BIMSynergy End date is 2025-11-02 with d/r: 71 and last sent: 19 (2025-08-03) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  575-02157156: id 2286 for customer: Coast Consulting Engineers End date is 2025-09-04 with d/r: 12 and last sent: 3 (2025-08-19) so 15 (12 + 3 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  573-42465462: id 2289 for customer: Utility Consultancy & Engineer End date is 2025-11-10 with d/r: 79 and last sent: 11 (2025-08-11) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  572-54671851: id 2290 for customer: Carson Powell Construction Ltd End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2301 for customer: lydia rowe End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2315 for customer: YORKSHIRE CHOICE HOMES CONSTRUCTION Ltd End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  570-38076789: id 2319 for customer: THUNDERBOLT & MAINTENANCE End date is 2025-09-13 with d/r: 21 and last sent: 9 (2025-08-13) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  563-35400673: id 2329 for customer: DMC ARCHITECTURE DESIGN Ltd End date is 2025-10-12 with d/r: 50 and last sent: 10 (2025-08-12) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  : id 2330 for customer: FRANK H DALE Ltd End date is 2025-10-14 with d/r: 52 and last sent: 8 (2025-08-14) so 60 (52 + 8 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:95]  574-86477996: id 2331 for customer: ALU-FIX UK Ltd End date is 2025-06-10 with d/r: 73 and last sent: 13 (2025-08-09) so 86 (73 + 13 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:07] [send_email.php:87]  574-93544051: id 2332 for customer: Beardmax Limitet End date is 2025-07-07 with d/r: 46 and last sent: 16 (2025-08-06) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:07] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  567-47135979: id 2336 for customer: Karen Gardner Architects Ltd End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  : id 2350 for customer: Anthony Stuchberry End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  567-60486347: id 2352 for customer: NARRACOTT S End date is 2025-10-30 with d/r: 68 and last sent: 22 (2025-07-31) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:87]  572-42802219: id 2413 for customer: VKE Contractors End date is 2025-08-22 with d/r: 0 and last sent: 1 (2025-08-21) so 1 (0 + 1 is above rule 0: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:08] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  573-51983637: id 2428 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  573-33205625: id 2460 for customer: JDW Architects End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  570-66746724: id 2476 for customer: ADS End date is 2025-11-01 with d/r: 70 and last sent: 20 (2025-08-02) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  562-94469544: id 2480 for customer: Solus Homes End date is 2025-06-27 with d/r: 56 and last sent: 2 (2025-08-20) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  573-10577998: id 2497 for customer: EDM London End date is 2025-06-05 with d/r: 78 and last sent: 2 (2025-08-20) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  570-50497145: id 2500 for customer: Peak Circuit Ltd End date is 2025-10-05 with d/r: 43 and last sent: 17 (2025-08-05) so 60 (43 + 17 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:87]  572-43806564: id 2514 for customer: APPROVED ELECTRICAL INSTALLATI End date is 2025-08-26 with d/r: 3 and last sent: 2 (2025-08-20) so 5 (3 + 2 is above rule 3: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:08] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  574-27265931: id 2521 for customer: ORIGIN DESIGN STUDIO Ltd End date is 2025-09-28 with d/r: 36 and last sent: 24 (2025-07-29) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  : id 2524 for customer: LINROC Ltd End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  567-28774572: id 2527 for customer: INTELECT MECHICAL End date is 2025-08-03 with d/r: 19 and last sent: 4 (2025-08-18) so 23 (19 + 4 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  : id 2539 for customer: SeAH Wind Ltd End date is 2025-09-23 with d/r: 31 and last sent: 29 (2025-07-24) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  : id 2558 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  573-41179223: id 2608 for customer: DAVID SALISBURY JOINERY Ltd End date is 2025-11-03 with d/r: 72 and last sent: 18 (2025-08-04) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  573-40383526: id 2609 for customer: Treveth Holdings LLP End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  574-05983438: id 2625 for customer: BARRIER ARCHITECTURAL SERVICES End date is 2025-06-13 with d/r: 70 and last sent: 10 (2025-08-12) so 80 (70 + 10 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  563-43882136: id 2626 for customer: Adam Langsbury Chartered Build End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  : id 2646 for customer: BIMSynergy End date is 2025-11-02 with d/r: 71 and last sent: 19 (2025-08-03) so 90 (71 + 19 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  574-17937204: id 2670 for customer: Kevin Mitchell Consulting Engineers Ltd End date is 2025-08-13 with d/r: 9 and last sent: 1 (2025-08-21) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  574-11224705: id 2671 for customer: MELIUS HOMES Ltd End date is 2025-07-10 with d/r: 43 and last sent: 13 (2025-08-09) so 56 (43 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  573-64915024: id 2683 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-04 with d/r: 79 and last sent: 3 (2025-08-19) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  564-84641509: id 2688 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 38 and last sent: 22 (2025-07-31) so 60 (38 + 22 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:87]  566-56786617: id 2708 for customer: A & M Architectural Partnership End date is 2025-05-30 with d/r: 84 and last sent: 8 (2025-08-14) so 92 (84 + 8 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:08] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  572-13359749: id 2709 for customer: Castle Masonry Products Ltd End date is 2025-06-27 with d/r: 56 and last sent: 2 (2025-08-20) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:95]  574-95603322: id 2712 for customer: Wiveliscombe Joinery Ltd End date is 2025-07-21 with d/r: 32 and last sent: 2 (2025-08-20) so 34 (32 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:08] [send_email.php:87]  572-63144208: id 2766 for customer: O & D CONSTRUCTION Ltd End date is 2025-11-21 with d/r: 90 and last sent: 1369 () so 1459 (90 + 1369 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:08] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  560-07789113: id 2768 for customer: Surveying Solutions Ltd End date is 2025-10-22 with d/r: 60 and last sent: 30 (2025-07-23) so 90 (60 + 30 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-63567482: id 2769 for customer: Bay Building Services End date is 2025-11-05 with d/r: 74 and last sent: 16 (2025-08-06) so 90 (74 + 16 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  563-48124598: id 2783 for customer: Mark Architecture Ltd End date is 2025-11-13 with d/r: 82 and last sent: 8 (2025-08-14) so 90 (82 + 8 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  572-75412629: id 2799 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-07 with d/r: 15 and last sent: 15 (2025-08-07) so 30 (15 + 15 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-13068029: id 2803 for customer: EDM London End date is 2025-06-15 with d/r: 68 and last sent: 8 (2025-08-14) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  : id 2804 for customer: BWM End date is 2025-09-24 with d/r: 32 and last sent: 28 (2025-07-25) so 60 (32 + 28 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-85174040: id 2809 for customer: ATELIERS DE FRANCE End date is 2025-06-02 with d/r: 81 and last sent: 5 (2025-08-17) so 86 (81 + 5 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  564-81177817: id 2814 for customer: E & M Design Partnership End date is 2025-09-20 with d/r: 28 and last sent: 2 (2025-08-20) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-09838098: id 2841 for customer: JAMES MACKINTOSH ARCHITECTS End date is 2025-07-02 with d/r: 51 and last sent: 5 (2025-08-17) so 56 (51 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  573-26001394: id 2869 for customer: MPR Architectural Designs Ltd End date is 2025-08-23 with d/r: 0 and last sent: 1 (2025-08-21) so 1 (0 + 1 is above rule 0: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-33568852: id 2871 for customer: ORIGIN DESIGN STUDIO Ltd End date is 2025-10-18 with d/r: 56 and last sent: 4 (2025-08-18) so 60 (56 + 4 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-38325416: id 2879 for customer: Kevin Judson End date is 2025-11-16 with d/r: 85 and last sent: 5 (2025-08-17) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  572-55743009: id 2884 for customer: AIR HANDLING SYSTEMS End date is 2025-11-11 with d/r: 80 and last sent: 10 (2025-08-12) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-36239916: id 2898 for customer: PCC CONSULTANTS Ltd End date is 2025-11-01 with d/r: 70 and last sent: 20 (2025-08-02) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  572-43389365: id 2942 for customer: LOWRY LIGHTING SOLUTIONS Ltd End date is 2025-08-24 with d/r: 1 and last sent: 2 (2025-08-20) so 3 (1 + 2 is above rule 1: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  560-14529128: id 2987 for customer: HATTRELL DS ONE End date is 2025-11-15 with d/r: 84 and last sent: 6 (2025-08-16) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-18378058: id 2990 for customer: Designteam End date is 2025-08-15 with d/r: 7 and last sent: 2 (2025-08-20) so 9 (7 + 2 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  : id 3004 for customer: WE Marson & Co. End date is 2025-11-20 with d/r: 89 and last sent: 1 (2025-08-21) so 90 (89 + 1 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  575-01538336: id 3005 for customer: MO Construction Ltd End date is 2025-09-01 with d/r: 9 and last sent: 1 (2025-08-21) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-59716582: id 3008 for customer: McAuliffe Group End date is 2025-10-29 with d/r: 67 and last sent: 23 (2025-07-30) so 90 (67 + 23 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-39310653: id 3013 for customer: Squire Associates (Aberdeen) Ltd End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  572-55398361: id 3021 for customer: Adib Nouri Zina End date is 2025-10-19 with d/r: 57 and last sent: 3 (2025-08-19) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  : id 3032 for customer: VKE Contractors End date is 2025-10-20 with d/r: 58 and last sent: 2 (2025-08-20) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  570-38101733: id 3035 for customer: STS STORAGE SYSTEMS End date is 2025-09-13 with d/r: 21 and last sent: 9 (2025-08-13) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  : id 3041 for customer: Alex Grey End date is 2025-09-17 with d/r: 25 and last sent: 5 (2025-08-17) so 30 (25 + 5 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-32179603: id 3070 for customer: Kevin Judson End date is 2025-09-21 with d/r: 29 and last sent: 1 (2025-08-21) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-32239155: id 3107 for customer: S2CARCHITECTS End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-35549263: id 3124 for customer: Byjc Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  570-40726176: id 3127 for customer: IGUANA DEVELOPMENTS Ltd End date is 2025-09-16 with d/r: 24 and last sent: 6 (2025-08-16) so 30 (24 + 6 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  : id 3129 for customer: Truck-Lite Europe Ltd End date is 2025-10-22 with d/r: 60 and last sent: 30 (2025-07-23) so 90 (60 + 30 is above rule 60: sending email to 
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending email to  from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription {{subs_status,"active":"is ending soon", "expired": "has expired"}}
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-25997508: id 3138 for customer: Epoch Architecture End date is 2025-09-21 with d/r: 29 and last sent: 1 (2025-08-21) so 30 (29 + 1 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  570-44772660: id 3139 for customer: Constantine Design Ltd End date is 2025-09-23 with d/r: 31 and last sent: 29 (2025-07-24) so 60 (31 + 29 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-17141007: id 3140 for customer: GIGANT Ltd End date is 2025-08-29 with d/r: 6 and last sent: 4 (2025-08-18) so 10 (6 + 4 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-36191146: id 3141 for customer: CSL Associates Ltd End date is 2025-10-11 with d/r: 49 and last sent: 11 (2025-08-11) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-84381313: id 3146 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-30188430: id 3164 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 22 and last sent: 8 (2025-08-14) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  565-00640173: id 3171 for customer: POWERCOM SYSTEMS End date is 2025-11-07 with d/r: 76 and last sent: 14 (2025-08-08) so 90 (76 + 14 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-34398097: id 3185 for customer: Premo Fabrications Ltd End date is 2025-09-18 with d/r: 26 and last sent: 4 (2025-08-18) so 30 (26 + 4 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  566-59603476: id 3208 for customer: NIGHTINGALE JOINERY Ltd End date is 2025-06-03 with d/r: 80 and last sent: 4 (2025-08-18) so 84 (80 + 4 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  : id 3239 for customer: M & C ROOFING CONTRACTORS Ltd End date is 2025-11-03 with d/r: 72 and last sent: 18 (2025-08-04) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-55456804: id 3243 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  569-73908915: id 3252 for customer: PDR GROUP SERVICES End date is 2025-08-02 with d/r: 20 and last sent: 5 (2025-08-17) so 25 (20 + 5 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  574-88124028: id 3257 for customer: JMC Packaging End date is 2025-06-23 with d/r: 60 and last sent: 2 (2025-08-20) so 62 (60 + 2 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-38596120: id 3277 for customer: ORANGE KEY Ltd End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-06801505: id 3283 for customer: EDM London End date is 2025-06-18 with d/r: 65 and last sent: 5 (2025-08-17) so 70 (65 + 5 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-43598976: id 3327 for customer: Shear Stress Ltd End date is 2025-11-16 with d/r: 85 and last sent: 5 (2025-08-17) so 90 (85 + 5 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  570-38455782: id 3340 for customer: MACHINES AND CONTROLS Ltd End date is 2025-09-13 with d/r: 21 and last sent: 9 (2025-08-13) so 30 (21 + 9 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  565-04538979: id 3351 for customer: D & G UTILITIES Ltd End date is 2025-11-14 with d/r: 83 and last sent: 7 (2025-08-15) so 90 (83 + 7 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-19330566: id 3356 for customer: EDM London End date is 2025-07-14 with d/r: 39 and last sent: 9 (2025-08-13) so 48 (39 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-25816572: id 3366 for customer: JB Surveying Ltd End date is 2025-09-20 with d/r: 28 and last sent: 2 (2025-08-20) so 30 (28 + 2 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-36777968: id 3367 for customer: RD NAIRN Ltd End date is 2025-11-06 with d/r: 75 and last sent: 15 (2025-08-07) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-60577508: id 3376 for customer: ECS BATH Ltd End date is 2025-10-30 with d/r: 68 and last sent: 22 (2025-07-31) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  570-81465285: id 3394 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  573-13069019: id 3396 for customer: PEPPERS CABLE GLANDS Ltd End date is 2025-06-15 with d/r: 68 and last sent: 8 (2025-08-14) so 76 (68 + 8 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  574-04439554: id 3410 for customer: ECODEV GROUP End date is 2025-06-05 with d/r: 78 and last sent: 2 (2025-08-20) so 80 (78 + 2 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:95]  567-49530691: id 3412 for customer: TYLER PARKES End date is 2025-10-15 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:09] [send_email.php:87]  573-26160653: id 3419 for customer: POTTER COWAN End date is 2025-08-24 with d/r: 1 and last sent: 2 (2025-08-20) so 3 (1 + 2 is above rule 1: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:09] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  574-17611758: id 3430 for customer: METRICAB End date is 2025-08-09 with d/r: 13 and last sent: 3 (2025-08-19) so 16 (13 + 3 is above rule 15: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  567-53479680: id 3442 for customer: McAuliffe Group End date is 2025-10-21 with d/r: 59 and last sent: 1 (2025-08-21) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  : id 3448 for customer: SHERRINGTON LIFTING SERVICES Ltd End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  567-01043658: id 3451 for customer: Elevation One Building Design Ltd End date is 2025-08-12 with d/r: 10 and last sent: 2 (2025-08-20) so 12 (10 + 2 is above rule 10: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-36777671: id 3452 for customer: V4 ARCHITECTS Ltd End date is 2025-11-06 with d/r: 75 and last sent: 15 (2025-08-07) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  575-01297717: id 3453 for customer: Advanced Water Treatment UK Ltd End date is 2025-08-29 with d/r: 6 and last sent: 4 (2025-08-18) so 10 (6 + 4 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  565-05805127: id 3472 for customer: SL PLASTICS End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  573-41227921: id 3477 for customer: Verdi Systems Ltd End date is 2025-11-03 with d/r: 72 and last sent: 18 (2025-08-04) so 90 (72 + 18 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  : id 3487 for customer: SALT & WHITE ARCHITECTS End date is 2025-10-10 with d/r: 48 and last sent: 12 (2025-08-10) so 60 (48 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-27766965: id 3491 for customer: STRUCTURE WORKSHOP End date is 2025-10-02 with d/r: 40 and last sent: 20 (2025-08-02) so 60 (40 + 20 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  : id 3493 for customer: Martyn Lowther End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  572-61258943: id 3500 for customer: STRUCTURAL DESIGN SERVICES Ltd End date is 2025-11-10 with d/r: 79 and last sent: 11 (2025-08-11) so 90 (79 + 11 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  561-85235864: id 3522 for customer: LOCKWOODS CONSTRUCTION End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-98419488: id 3544 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  567-41993197: id 3556 for customer: Lazzeri Creative Interiors End date is 2025-10-01 with d/r: 39 and last sent: 21 (2025-08-01) so 60 (39 + 21 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  573-33212652: id 3558 for customer: M & E Design Ltd End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-21063077: id 3564 for customer: KOHA ARCHITECTS Ltd End date is 2025-08-31 with d/r: 8 and last sent: 2 (2025-08-20) so 10 (8 + 2 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  567-53543424: id 3572 for customer: Jameson Builders End date is 2025-10-21 with d/r: 59 and last sent: 1 (2025-08-21) so 60 (59 + 1 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-06243754: id 3582 for customer: ALEXANDER WATERWORTH End date is 2025-06-14 with d/r: 69 and last sent: 9 (2025-08-13) so 78 (69 + 9 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  574-09401796: id 3598 for customer: ATELIERS DE FRANCE End date is 2025-06-29 with d/r: 54 and last sent: 8 (2025-08-14) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  574-93530292: id 3602 for customer: HGCE Ltd End date is 2025-07-07 with d/r: 46 and last sent: 16 (2025-08-06) so 62 (46 + 16 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  567-32215203: id 3617 for customer: MICHAEL VAUGHAN RACKING SERVIC End date is 2025-09-15 with d/r: 23 and last sent: 7 (2025-08-15) so 30 (23 + 7 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-34734040: id 3618 for customer: HEATING DESIGN SOLUTIONS End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-27210503: id 3631 for customer: SeAH Wind Ltd End date is 2025-09-28 with d/r: 36 and last sent: 24 (2025-07-29) so 60 (36 + 24 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  566-63440025: id 3649 for customer: Hammond Design End date is 2025-06-04 with d/r: 79 and last sent: 3 (2025-08-19) so 82 (79 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-92527234: id 3650 for customer: STRENGER End date is 2025-06-30 with d/r: 53 and last sent: 7 (2025-08-15) so 60 (53 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  573-22571554: id 3693 for customer: ANSIBLE MOTION Ltd End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  : id 3695 for customer: Truck-Lite Europe Ltd End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-91270489: id 3699 for customer: DURATA End date is 2025-06-26 with d/r: 57 and last sent: 3 (2025-08-19) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  560-02732245: id 3718 for customer: FERNBALLOT Ltd End date is 2025-10-11 with d/r: 49 and last sent: 11 (2025-08-11) so 60 (49 + 11 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  573-33477126: id 3724 for customer: Glent Engineering Ltd End date is 2025-09-27 with d/r: 35 and last sent: 25 (2025-07-28) so 60 (35 + 25 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-36759459: id 3729 for customer: OVNI Consulting Engineers Ltd End date is 2025-11-06 with d/r: 75 and last sent: 15 (2025-08-07) so 90 (75 + 15 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  569-97731818: id 3734 for customer: NORTHALLERTON DRAUGHTING End date is 2025-08-31 with d/r: 8 and last sent: 2 (2025-08-20) so 10 (8 + 2 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  573-20718161: id 3736 for customer: KITCHEN ARCHITECTURE Ltd End date is 2025-09-05 with d/r: 13 and last sent: 2 (2025-08-20) so 15 (13 + 2 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  : id 3747 for customer: KMD CONSULTING End date is 2025-10-06 with d/r: 44 and last sent: 16 (2025-08-06) so 60 (44 + 16 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  567-57622669: id 3748 for customer: CUMBRIA WASTE GROUP End date is 2025-10-27 with d/r: 65 and last sent: 25 (2025-07-28) so 90 (65 + 25 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  : id 3751 for customer: Sarah Darlow Darlow End date is 2025-11-11 with d/r: 80 and last sent: 10 (2025-08-12) so 90 (80 + 10 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  573-21728743: id 3753 for customer: John Roux End date is 2025-07-26 with d/r: 27 and last sent: 4 (2025-08-18) so 31 (27 + 4 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  564-97534193: id 3755 for customer: Utility Consultancy & Engineer End date is 2025-10-30 with d/r: 68 and last sent: 22 (2025-07-31) so 90 (68 + 22 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  573-25204511: id 3764 for customer: Shadbolt Ltd End date is 2025-08-17 with d/r: 5 and last sent: 2 (2025-08-20) so 7 (5 + 2 is above rule 5: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  572-52388292: id 3777 for customer: FURNESS GREEN PARTNERSHIP End date is 2025-10-06 with d/r: 44 and last sent: 16 (2025-08-06) so 60 (44 + 16 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:95]  574-32031304: id 3789 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-09 with d/r: 47 and last sent: 13 (2025-08-09) so 60 (47 + 13 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:10] [send_email.php:87]  572-35848309: id 3798 for customer: Silverfox Surveys Ltd End date is 2025-07-26 with d/r: 27 and last sent: 4 (2025-08-18) so 31 (27 + 4 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:10] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  572-62544390: id 3807 for customer: STORTFORD HOLDINGS Ltd End date is 2025-07-11 with d/r: 42 and last sent: 12 (2025-08-10) so 54 (42 + 12 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  572-57006680: id 3809 for customer: ARCHITECTURAL METALWORK SERVICES Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  567-51281740: id 3810 for customer: Erith Business Systems End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  574-99555081: id 3811 for customer: R3nder Ltd End date is 2025-08-15 with d/r: 7 and last sent: 2 (2025-08-20) so 9 (7 + 2 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  777-13092772: id 3823 for customer: EDM-London End date is 2025-06-27 with d/r: 56 and last sent: 2 (2025-08-20) so 58 (56 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  574-37144192: id 3827 for customer: Dean Whitbrook End date is 2025-11-08 with d/r: 77 and last sent: 13 (2025-08-09) so 90 (77 + 13 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  573-17118867: id 3828 for customer: KERRY JANE INTERIORS End date is 2025-07-05 with d/r: 48 and last sent: 2 (2025-08-20) so 50 (48 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  572-55764783: id 3835 for customer: Scomac Services End date is 2025-10-20 with d/r: 58 and last sent: 2 (2025-08-20) so 60 (58 + 2 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:87]  571-92211472: id 3837 for customer: INTELECT MECHICAL End date is 2025-05-24 with d/r: 90 and last sent: 2 (2025-08-20) so 92 (90 + 2 is above rule 90: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:11] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription has expired
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  573-36646353: id 3857 for customer: CYCLIFE UK Ltd End date is 2025-09-14 with d/r: 22 and last sent: 8 (2025-08-14) so 30 (22 + 8 is below rule 30: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  572-53707888: id 3858 for customer: Utility Consultancy & Engineer End date is 2025-10-12 with d/r: 50 and last sent: 10 (2025-08-12) so 60 (50 + 10 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  574-32890248: id 3859 for customer: Utility Consultancy & Engineer End date is 2025-11-05 with d/r: 74 and last sent: 16 (2025-08-06) so 90 (74 + 16 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  573-40391345: id 3860 for customer: MOSEDALE GILLATT ARCHITECTS End date is 2025-10-31 with d/r: 69 and last sent: 21 (2025-08-01) so 90 (69 + 21 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  573-33205130: id 3872 for customer: Promack Ltd End date is 2025-09-26 with d/r: 34 and last sent: 26 (2025-07-27) so 60 (34 + 26 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:87]  574-14338009: id 3891 for customer: ATELIERS DE FRANCE End date is 2025-07-26 with d/r: 27 and last sent: 4 (2025-08-18) so 31 (27 + 4 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:11] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  572-44893063: id 3892 for customer: LONDONSTRUCTURALDESIGN Ltd End date is 2025-09-01 with d/r: 9 and last sent: 1 (2025-08-21) so 10 (9 + 1 is below rule 10: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  573-37972778: id 3904 for customer: McAuliffe Group End date is 2025-10-19 with d/r: 57 and last sent: 3 (2025-08-19) so 60 (57 + 3 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  573-43386665: id 3955 for customer: STAND INNOVATIONS Ltd End date is 2025-11-15 with d/r: 84 and last sent: 6 (2025-08-16) so 90 (84 + 6 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  572-59532046: id 3956 for customer: HAMPTON DOORS End date is 2025-11-01 with d/r: 70 and last sent: 20 (2025-08-02) so 90 (70 + 20 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  570-75625390: id 3959 for customer: Aughton Automation Ltd End date is 2025-10-17 with d/r: 55 and last sent: 5 (2025-08-17) so 60 (55 + 5 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 4104 for customer: AKELA CONSTRUCTION Ltd End date is 2025-10-13 with d/r: 51 and last sent: 9 (2025-08-13) so 60 (51 + 9 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 12536 for customer: JONATHAN LEES ARCHITECTS LLP End date is 2025-07-16 with d/r: 37 and last sent: 7 (2025-08-15) so 44 (37 + 7 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 12543 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:87]  : id 12554 for customer: Denis Welch Motorsport End date is 2025-06-29 with d/r: 54 and last sent: 8 (2025-08-14) so 62 (54 + 8 is above rule 60: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:11] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription {{subs_status,"active":"is ending soon", "expired": "has expired"}}
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 12588 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 30765 for customer: STORTFORD HOLDINGS Ltd End date is 2025-08-10 with d/r: 12 and last sent: 2 (2025-08-20) so 14 (12 + 2 is below rule 15: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:87]  : id 62691 for customer: POTTER COWAN End date is 2025-08-24 with d/r: 1 and last sent: 2 (2025-08-20) so 3 (1 + 2 is above rule 1: sending email to 
[email_sender] [2025-08-22 11:00:11] [autodesk_subscriptions.class.php:232] Sending email to  from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription is ending soon
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 62717 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 62758 for customer: Vida Design Ltd End date is 2025-10-25 with d/r: 63 and last sent: 27 (2025-07-26) so 90 (63 + 27 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 62776 for customer: PAUL MOLINEUX ASSOCIATES End date is 2025-11-18 with d/r: 87 and last sent: 3 (2025-08-19) so 90 (87 + 3 is below rule 90: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:87]  : id 62789 for customer: GRAY & DICK Ltd End date is 2025-07-30 with d/r: 23 and last sent: 8 (2025-08-14) so 31 (23 + 8 is above rule 30: sending <NAME_EMAIL>
[email_sender] [2025-08-22 11:00:11] [autodesk_subscriptions.class.php:232] Sending <NAME_EMAIL> from "TCS CAD & BIM Solutions Limited" <<EMAIL>> with subject Your  subscription {{subs_status,"active":"is ending soon", "expired": "has expired"}}
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 63030 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 38 and last sent: 11 () so 49 (38 + 11 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:95]  : id 63034 for customer: STORTFORD HOLDINGS Ltd End date is 2025-09-30 with d/r: 38 and last sent: 11 () so 49 (38 + 11 is below rule 60: not sending email
[email_sender] [2025-08-22 11:00:11] [send_email.php:104] Email sending process completed. 52 emails sent.
