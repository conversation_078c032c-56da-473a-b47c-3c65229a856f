[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 10:10:19
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69477960142838\n            [quantity] => 1\n            [endDate] => 2026-09-14\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-20T09:45:10.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T10:10:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14' ON DUPLICATE KEY UPDATE subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 10:10:19
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69477960142838\n            [quantity] => 1\n            [endDate] => 2026-09-14\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-20T09:45:10.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T10:10:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14' ON DUPLICATE KEY UPDATE subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 11:11:19
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d531a53a-540c-4f9b-8c7c-63793b4769e6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73832204736884\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-20T10:56:15.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T11:11:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73832204736884', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '73832204736884', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 11:11:19
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d531a53a-540c-4f9b-8c7c-63793b4769e6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73832204736884\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-20T10:56:15.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T11:11:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 11:11:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73832204736884', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '73832204736884', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 11:36:36
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c3f40a1c-d5a2-4107-8561-7ee8f58d415d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69400848050282\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-09-05\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-20T11:21:31.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T11:36:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 11:36:36
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c3f40a1c-d5a2-4107-8561-7ee8f58d415d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69400848050282\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-09-05\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-20T11:21:31.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T11:36:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69400848050282', status = 'Active', quantity = 2, endDate = '2026-09-05', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69400848050282', status = 'Active', quantity = 2, endDate = '2026-09-05', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 11:36:36] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69400848050282', status = 'Active', quantity = 2, endDate = '2026-09-05', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69400848050282', status = 'Active', quantity = 2, endDate = '2026-09-05', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 11:38:49
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => de95a763-5881-4f50-8c25-724758a470d3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69477960142838\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-20T11:18:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T11:38:47.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 11:38:49] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69477960142838', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69477960142838', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 15:39:16
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 88a977c3-59da-4ba3-987d-816568c3d34c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69400848050282\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-20T15:09:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T15:39:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69400848050282', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '69400848050282', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 15:39:16
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 88a977c3-59da-4ba3-987d-816568c3d34c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69400848050282\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-20T15:09:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T15:39:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 15:39:16] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69400848050282', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '69400848050282', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:09:50
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c42ff49d-4d11-4ec6-bda0-44e82ea96f74\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71079825161867\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:54:45.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:09:47.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71079825161867', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '71079825161867', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:09:50
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c42ff49d-4d11-4ec6-bda0-44e82ea96f74\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71079825161867\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:54:45.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:09:47.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:09:50] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71079825161867', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '71079825161867', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:10:13
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 27b1ff77-87f1-45a3-a1e6-1e00d2fc7387\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71079825162968\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:55:07.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:10:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71079825162968', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '71079825162968', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:10:13
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 27b1ff77-87f1-45a3-a1e6-1e00d2fc7387\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71079825162968\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:55:07.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:10:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:10:13] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71079825162968', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '71079825162968', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:10:34
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e267193c-d448-4240-87aa-01de4cf68003\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73314215486158\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:55:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:10:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73314215486158', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73314215486158', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:10:34
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e267193c-d448-4240-87aa-01de4cf68003\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73314215486158\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:55:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:10:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:10:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73314215486158', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73314215486158', autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:10:49
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1eb84ba8-08e3-4b48-9635-f67477307519\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73712837059216\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:55:44.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:10:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73712837059216', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73712837059216', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:10:49
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1eb84ba8-08e3-4b48-9635-f67477307519\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73712837059216\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:55:44.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:10:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:10:49] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73712837059216', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73712837059216', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:11:09
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 408ead5e-ce07-444a-82d2-7d88dd943aa5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73858227484216\n            [quantity] => 4\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:56:04.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:11:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73858227484216', quantity = 4, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73858227484216', quantity = 4, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:11:09
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 408ead5e-ce07-444a-82d2-7d88dd943aa5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73858227484216\n            [quantity] => 4\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:56:04.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:11:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:11:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73858227484216', quantity = 4, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73858227484216', quantity = 4, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:11:31
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 470285ff-1c69-438f-b640-fe09dcb003a7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73982345172995\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:56:26.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:11:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73982345172995', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73982345172995', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 16:11:31
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 470285ff-1c69-438f-b640-fe09dcb003a7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73982345172995\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-20T15:56:26.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T16:11:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 16:11:31] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73982345172995', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73982345172995', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-21 07:40:09
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f452a53d-bf30-4634-a545-e5eb59e1f3fe\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69270257497660\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-08-21\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-21T07:25:04.000+0000\n        )\n\n    [publishedAt] => 2025-08-21T07:40:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69270257497660', status = 'Active', quantity = 2, endDate = '2026-08-21', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69270257497660', status = 'Active', quantity = 2, endDate = '2026-08-21', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-21 07:40:09
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f452a53d-bf30-4634-a545-e5eb59e1f3fe\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69270257497660\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-08-21\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-21T07:25:04.000+0000\n        )\n\n    [publishedAt] => 2025-08-21T07:40:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-21 07:40:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69270257497660', status = 'Active', quantity = 2, endDate = '2026-08-21', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69270257497660', status = 'Active', quantity = 2, endDate = '2026-08-21', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-21 11:39:58
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 38a91e2e-a41d-4b6b-97ab-c218b2c7d4b2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69270257497660\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-21T11:19:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-21T11:39:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69270257497660', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '69270257497660', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-21 11:39:58
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 38a91e2e-a41d-4b6b-97ab-c218b2c7d4b2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69270257497660\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-21T11:19:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-21T11:39:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-21 11:39:58] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69270257497660', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '69270257497660', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 07:07:32
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 07:07:32
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 55b335a6-e3d1-4902-8776-0555ddfcc900\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59680488697967\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-22T06:42:27.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T07:07:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 55b335a6-e3d1-4902-8776-0555ddfcc900\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59680488697967\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-22T06:42:27.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T07:07:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59680488697967', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '59680488697967', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-22 07:07:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59680488697967', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '59680488697967', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:08:37
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2b7c16f1-b790-4742-bc46-d722095a96d1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57122874418375\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-10-15\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-22T10:53:31.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:08:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57122874418375', status = 'Active', quantity = 1, endDate = '2028-10-15', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '57122874418375', status = 'Active', quantity = 1, endDate = '2028-10-15', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:08:37
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2b7c16f1-b790-4742-bc46-d722095a96d1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57122874418375\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-10-15\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-22T10:53:31.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:08:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:08:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57122874418375', status = 'Active', quantity = 1, endDate = '2028-10-15', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '57122874418375', status = 'Active', quantity = 1, endDate = '2028-10-15', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:10:32
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 04908ece-2e0f-4c73-b420-8a49b0b44da0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59843928037733\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-25\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-22T10:35:28.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:10:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:10:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59843928037733', status = 'Active', quantity = 1, endDate = '2026-08-25', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59843928037733', status = 'Active', quantity = 1, endDate = '2026-08-25', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:10:33
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 04908ece-2e0f-4c73-b420-8a49b0b44da0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59843928037733\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-25\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-22T10:35:28.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:10:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:10:33] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59843928037733', status = 'Active', quantity = 1, endDate = '2026-08-25', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59843928037733', status = 'Active', quantity = 1, endDate = '2026-08-25', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:37:08
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 84b15514-bddc-480b-980e-882ace41582f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57122874418375\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-22T11:16:55.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:37:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57122874418375', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '57122874418375', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:37:08
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 84b15514-bddc-480b-980e-882ace41582f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57122874418375\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-22T11:16:55.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:37:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:37:08] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57122874418375', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '57122874418375', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:37:17
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c6764620-cc34-421e-ae4c-d24d8e0640ab\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59843928037733\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-22T11:17:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:37:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59843928037733', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59843928037733', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 11:37:17
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c6764620-cc34-421e-ae4c-d24d8e0640ab\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59843928037733\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-22T11:17:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T11:37:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 11:37:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59843928037733', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59843928037733', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 15:43:48
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8367a4d5-c9ef-4604-942f-4a26f06ce103\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57199375151109\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2028-10-24\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-22T15:28:43.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T15:43:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57199375151109', status = 'Active', quantity = 2, endDate = '2028-10-24', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '57199375151109', status = 'Active', quantity = 2, endDate = '2028-10-24', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 15:43:48
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8367a4d5-c9ef-4604-942f-4a26f06ce103\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57199375151109\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2028-10-24\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-22T15:28:43.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T15:43:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 15:43:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57199375151109', status = 'Active', quantity = 2, endDate = '2028-10-24', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '57199375151109', status = 'Active', quantity = 2, endDate = '2028-10-24', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 19:40:27
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 058afd33-8cd0-4552-9721-eda087be46d8\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57199375151109\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-22T19:10:15.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T19:40:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57199375151109', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '57199375151109', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-22 19:40:27
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 058afd33-8cd0-4552-9721-eda087be46d8\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57199375151109\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-22T19:10:15.000+0000\n        )\n\n    [publishedAt] => 2025-08-22T19:40:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-22 19:40:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57199375151109', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '57199375151109', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-23 14:37:52
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => abf942da-b04c-458e-9521-d90c42584f41\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59921156018881\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-03\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-23T14:02:46.000+0000\n        )\n\n    [publishedAt] => 2025-08-23T14:37:49.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59921156018881', status = 'Active', quantity = 1, endDate = '2026-09-03', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59921156018881', status = 'Active', quantity = 1, endDate = '2026-09-03', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-23 14:37:52
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => abf942da-b04c-458e-9521-d90c42584f41\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59921156018881\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-03\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-23T14:02:46.000+0000\n        )\n\n    [publishedAt] => 2025-08-23T14:37:49.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-23 14:37:52] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59921156018881', status = 'Active', quantity = 1, endDate = '2026-09-03', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59921156018881', status = 'Active', quantity = 1, endDate = '2026-09-03', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-23 15:39:47
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 903860b0-4192-4a80-84d9-59beecf7f397\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59921156018881\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-23T15:04:28.000+0000\n        )\n\n    [publishedAt] => 2025-08-23T15:39:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59921156018881', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59921156018881', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-23 15:39:47
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 903860b0-4192-4a80-84d9-59beecf7f397\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59921156018881\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-23T15:04:28.000+0000\n        )\n\n    [publishedAt] => 2025-08-23T15:39:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-23 15:39:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59921156018881', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59921156018881', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-24 23:40:17
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6ffba838-92db-46fb-bb86-4572d1bffe2d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75266597996116\n            [quantity] => 1\n            [endDate] => 2026-08-24\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-24T23:05:13.000+0000\n        )\n\n    [publishedAt] => 2025-08-24T23:40:15.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75266597996116', quantity = 1, endDate = '2026-08-24' ON DUPLICATE KEY UPDATE subscriptionId = '75266597996116', quantity = 1, endDate = '2026-08-24';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-24 23:40:17
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6ffba838-92db-46fb-bb86-4572d1bffe2d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75266597996116\n            [quantity] => 1\n            [endDate] => 2026-08-24\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-24T23:05:13.000+0000\n        )\n\n    [publishedAt] => 2025-08-24T23:40:15.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-24 23:40:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75266597996116', quantity = 1, endDate = '2026-08-24' ON DUPLICATE KEY UPDATE subscriptionId = '75266597996116', quantity = 1, endDate = '2026-08-24';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-25 03:37:42
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 91120408-0ddc-48b1-a389-c519976d7758\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75266597996116\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-25T03:07:35.000+0000\n        )\n\n    [publishedAt] => 2025-08-25T03:37:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75266597996116', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75266597996116', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-25 03:37:42
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 91120408-0ddc-48b1-a389-c519976d7758\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75266597996116\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-25T03:07:35.000+0000\n        )\n\n    [publishedAt] => 2025-08-25T03:37:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-25 03:37:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75266597996116', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75266597996116', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-25 16:12:11
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b29a3300-3660-4d5b-90c9-c72147c1fd75\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 64811964452952\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-25T15:57:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-25T16:12:09.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '64811964452952', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '64811964452952', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-25 16:12:11
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b29a3300-3660-4d5b-90c9-c72147c1fd75\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 64811964452952\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-25T15:57:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-25T16:12:09.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-25 16:12:11] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '64811964452952', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '64811964452952', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 00:06:06
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 27aa3ac0-2e30-4c15-a748-0ad4502289d3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74700029815254\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-25T23:41:01.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T00:06:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74700029815254', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '74700029815254', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 00:06:06
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 27aa3ac0-2e30-4c15-a748-0ad4502289d3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74700029815254\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-25T23:41:01.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T00:06:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 00:06:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74700029815254', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '74700029815254', status = 'Suspended';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 07:11:05
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9ce724be-4c12-45e5-97f4-bb9d5a4f98c6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69107177208594\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-26T06:41:01.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T07:11:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69107177208594', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69107177208594', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 07:11:05
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9ce724be-4c12-45e5-97f4-bb9d5a4f98c6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69107177208594\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-26T06:41:01.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T07:11:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 07:11:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69107177208594', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69107177208594', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 08:38:41
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e042ca48-b3d4-4580-9e58-59afb107f7cc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63275261587012\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-26\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T08:08:37.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T08:38:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63275261587012', status = 'Active', quantity = 1, endDate = '2026-09-26', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '63275261587012', status = 'Active', quantity = 1, endDate = '2026-09-26', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 08:38:41
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e042ca48-b3d4-4580-9e58-59afb107f7cc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63275261587012\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-26\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T08:08:37.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T08:38:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 08:38:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63275261587012', status = 'Active', quantity = 1, endDate = '2026-09-26', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '63275261587012', status = 'Active', quantity = 1, endDate = '2026-09-26', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 09:09:47
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2d689897-0db8-4985-8ae2-9648a00cfaa2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65331798033507\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-26T08:44:42.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T09:09:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65331798033507', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '65331798033507', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 09:09:47
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2d689897-0db8-4985-8ae2-9648a00cfaa2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65331798033507\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-26T08:44:42.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T09:09:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 09:09:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65331798033507', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '65331798033507', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 09:41:19
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8e154c79-dd82-4d40-8091-e00da0307355\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527130055219\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-01\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T09:26:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T09:41:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527130055219', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72527130055219', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 09:41:19
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8e154c79-dd82-4d40-8091-e00da0307355\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527130055219\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-01\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T09:26:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T09:41:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 09:41:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527130055219', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72527130055219', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 11:36:26
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 282fa9e3-e036-4480-abb0-cd40a024e363\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63275261587012\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-26T11:21:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T11:36:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63275261587012', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63275261587012', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 11:36:26
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 282fa9e3-e036-4480-abb0-cd40a024e363\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63275261587012\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-26T11:21:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T11:36:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 11:36:26] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63275261587012', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63275261587012', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 11:39:00
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3a406d77-f958-43a8-aec1-bba3a80fd1d5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527130055219\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-26T11:23:52.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T11:38:58.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527130055219', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72527130055219', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 11:39:00
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3a406d77-f958-43a8-aec1-bba3a80fd1d5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527130055219\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-26T11:23:52.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T11:38:58.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 11:39:00] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527130055219', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72527130055219', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 12:08:28
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b77b04d1-8cb0-406f-9209-8b87d2921411\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 60008181759135\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-09-13\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T11:33:23.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T12:08:25.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '60008181759135', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '60008181759135', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 12:08:28
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b77b04d1-8cb0-406f-9209-8b87d2921411\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 60008181759135\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-09-13\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T11:33:23.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T12:08:25.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 12:08:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '60008181759135', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '60008181759135', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 12:08:31
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b17e1e12-b679-4383-a3ca-9ec97b4d162d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 60008181733933\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-09-13\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T11:33:26.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T12:08:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '60008181733933', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '60008181733933', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 12:08:31
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b17e1e12-b679-4383-a3ca-9ec97b4d162d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 60008181733933\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-09-13\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T11:33:26.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T12:08:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 12:08:31] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '60008181733933', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '60008181733933', status = 'Active', quantity = 1, endDate = '2028-09-13', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 12:09:43
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 44c4bf96-0539-419a-a3ba-f01bfcfbe9e5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75620796179516\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-26T11:39:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T12:09:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 12:09:43
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 44c4bf96-0539-419a-a3ba-f01bfcfbe9e5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75620796179516\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-26T11:39:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T12:09:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-26 12:09:43] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-26 12:09:47] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75620796179516\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-26\n            [endDate] => 2026-08-25\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => OFF\n            [offeringId] => OD-000280\n            [offeringCode] => RVTLTS\n            [offeringName] => AutoCAD Revit LT Suite\n            [marketingName] => AutoCAD Revit LT Suite\n            [currency] => \n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => \n                    [description] => \n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => GJR ARCHITECTS Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => GJR ARCHITECTS Ltd\n                            [type] => End Customer\n                            [address1] => Axehayes Farm 1 Park 7 The Studio\n                            [address2] => Clyst St. Mary\n                            [address3] => \n                            [city] => Exeter\n                            [stateProvince] => DEVON\n                            [postalCode] => EX5 1DP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => Helen\n                            [primaryAdminLastName] => Barlow\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8318025\n                            [teamName] => Helen\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Helen\n                            [last] => Barlow\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-26 12:09:47] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-26 12:09:53] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75620796179516\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-26\n            [endDate] => 2026-08-25\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => OFF\n            [offeringId] => OD-000280\n            [offeringCode] => RVTLTS\n            [offeringName] => AutoCAD Revit LT Suite\n            [marketingName] => AutoCAD Revit LT Suite\n            [currency] => \n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => \n                    [description] => \n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => GJR ARCHITECTS Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => GJR ARCHITECTS Ltd\n                            [type] => End Customer\n                            [address1] => Axehayes Farm 1 Park 7 The Studio\n                            [address2] => Clyst St. Mary\n                            [address3] => \n                            [city] => Exeter\n                            [stateProvince] => DEVON\n                            [postalCode] => EX5 1DP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => Helen\n                            [primaryAdminLastName] => Barlow\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8318025\n                            [teamName] => Helen\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Helen\n                            [last] => Barlow\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-26 12:09:53] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 13:11:53
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 87945ffd-f3e7-498b-9ca4-f23ea6299af1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56717693784427\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-29\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T12:56:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T13:11:51.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56717693784427', status = 'Active', quantity = 1, endDate = '2026-08-29', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56717693784427', status = 'Active', quantity = 1, endDate = '2026-08-29', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-26 13:11:53
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 87945ffd-f3e7-498b-9ca4-f23ea6299af1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56717693784427\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-29\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-26T12:56:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-26T13:11:51.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-26 13:11:53] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56717693784427', status = 'Active', quantity = 1, endDate = '2026-08-29', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56717693784427', status = 'Active', quantity = 1, endDate = '2026-08-29', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
