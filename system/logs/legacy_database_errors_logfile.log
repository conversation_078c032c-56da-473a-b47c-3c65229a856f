[legacy_database_errors] [2025-08-20 13:56:03] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'pv.products_variations_name' in 'field list'\n    [query] => SELECT pv.products_variations_id, pv.products_variations_name, pv.price, pv.products_id, pac.* FROM products_autodesk_catalog pac JOIN products_variations pv ON pv.autodesk_catalog_unique_hash = pac.unique_hash WHERE pac.srp > 0\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/system/update_prices\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-20 13:56:03\n)\n
[legacy_database_errors] [2025-08-20 15:05:16] [database.php:64]  Array\n(\n    [sql_state] => 23000\n    [driver_error_code] => 1062\n    [driver_error_message] => Duplicate entry '43f23069' for key 'unique_hash'\n    [query] => UPDATE products_autodesk_catalog \n                                   SET hash_string = :new_hash_string, unique_hash = :new_unique_hash \n                                   WHERE id = :id\n    [parameters] => Array\n        (\n            [:new_hash_string] => OD-000002COMSSTNDC100A01New\n            [:new_unique_hash] => 43f23069\n            [:id] => 8527\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/system/fix_autodesk_hashes\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-20 15:05:16\n)\n
