[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 67.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 63.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 54.5) -> reseller_name\n, partner_name\n, account_primary_reseller_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name\n (score: 54.5) loses to existing reseller_number (score: 54.5)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name\n (score: 54.5) loses to existing reseller_number (score: 54.5)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 54.5) loses to existing reseller_number (score: 54.5)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 54.5) -> reseller_name\n, partner_name\n, account_primary_reseller_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: end_customer_name -> company_name\n (score: 67.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: end_customer_name -> endcust_name\n (score: 67.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: end_customer_name -> end_customer_name (score: 67.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 67.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> address (priority: 1, score: 70) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_city -> city (priority: 4, score: 61) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_state -> state (priority: 4, score: 61) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> postal_code (priority: 4, score: 61) -> postal_code\n, end_customer_zip_code
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_country -> country (priority: 4, score: 61) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 63.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 70) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name\n (score: 70) loses to existing agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName\n (score: 70) loses to existing agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 70) loses to existing agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 70) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:115] Conflict: product_family -> product_name\n (score: 79) replaces agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:115] Conflict: product_family -> subs_offeringName\n (score: 79) replaces agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:115] Conflict: product_family -> agreement_program_name (score: 79) replaces agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 14:48:09] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 67.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 63.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 54.5) -> reseller_name\n, partner_name\n, account_primary_reseller_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name\n (score: 54.5) loses to existing reseller_number (score: 54.5)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name\n (score: 54.5) loses to existing reseller_number (score: 54.5)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 54.5) loses to existing reseller_number (score: 54.5)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 54.5) -> reseller_name\n, partner_name\n, account_primary_reseller_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: end_customer_name -> company_name\n (score: 67.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: end_customer_name -> endcust_name\n (score: 67.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: end_customer_name -> end_customer_name (score: 67.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 67.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> address (priority: 1, score: 70) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_city -> city (priority: 4, score: 61) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_state -> state (priority: 4, score: 61) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> postal_code (priority: 4, score: 61) -> postal_code\n, end_customer_zip_code
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_country -> country (priority: 4, score: 61) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 63.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 70) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name\n (score: 70) loses to existing agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName\n (score: 70) loses to existing agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 70) loses to existing agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 70) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:115] Conflict: product_family -> product_name\n (score: 79) replaces agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:115] Conflict: product_family -> subs_offeringName\n (score: 79) replaces agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:115] Conflict: product_family -> agreement_program_name (score: 79) replaces agreement_program_name (score: 70)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName\n (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 62) loses to existing product_family (score: 79)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 62) -> product_name\n, subs_offeringName\n, agreement_program_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name\n (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing sold_to_name (score: 67.4)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name\n, endcust_name\n, end_customer_name
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-20 22:16:24] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped sold_to_name -> sold_to_name (priority: 2, score: 77) -> sold_to_name, sold_to
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped sold_to_number -> sold_to_number (priority: 8, score: 67.33) -> sold_to_number, sold_to_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped vendor_name -> vendor_name (priority: 6, score: 68.29) -> vendor_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> vendor_id (priority: 2, score: 67.4) -> vendor_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> vendor_id (score: 67.4) loses to existing reseller_vendor_id (score: 67.4)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> vendor_id (priority: 2, score: 67.4) -> vendor_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> address (priority: 1, score: 81.4) -> address, end_customer_address_1
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> end_customer_address_2 (priority: 8, score: 70.33) -> end_customer_address_2, address_2
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> end_customer_address_3 (priority: 8, score: 70.33) -> end_customer_address_3, address_3
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_city -> city (priority: 4, score: 70) -> city, end_customer_city
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_state -> state (priority: 4, score: 70) -> state, end_customer_state
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> postal_code (priority: 4, score: 71.8) -> postal_code, end_customer_zip_code
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_country -> country (priority: 4, score: 70) -> country, end_customer_country
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> end_customer_account_type (priority: 7, score: 70.75) -> end_customer_account_type, account_type
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> contact_name (priority: 4, score: 76) -> contact_name, end_customer_contact_name, subscription_contact_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email, subscription_contact_email
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> end_customer_contact_phone (priority: 6, score: 71.29) -> end_customer_contact_phone, contact_phone, phone
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> end_customer_industry_segment (priority: 8, score: 70.33) -> end_customer_industry_segment, industry
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped agreement_terms -> agreement_terms (priority: 7, score: 67.75) -> agreement_terms, terms
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped agreement_type -> agreement_type (priority: 7, score: 67.75) -> agreement_type, contract_type
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped agreement_status -> agreement_status (priority: 6, score: 68.29) -> agreement_status, contract_status
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped agreement_support_level -> agreement_support_level (priority: 8, score: 70.33) -> agreement_support_level, support_level
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_market_segment (priority: 8, score: 70.33) -> product_market_segment, market_segment
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_sku_description (priority: 8, score: 70.33) -> product_sku_description, product_description
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_list_price (priority: 6, score: 71.29) -> product_list_price, list_price, price
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference (score: 94) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber (score: 94) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 94) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference (score: 88) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber (score: 88) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 88) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 88) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 7, score: 67.75) -> status, subs_status, subscription_status
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 6, score: 68.29) -> quantity, subs_quantity, subscription_quantity
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> contact_name (score: 73) loses to existing end_customer_contact_name (score: 76)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_contact_name (score: 73) loses to existing end_customer_contact_name (score: 76)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> subscription_contact_name (score: 73) loses to existing end_customer_contact_name (score: 76)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> contact_name (priority: 4, score: 73) -> contact_name, end_customer_contact_name, subscription_contact_name
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> subscription_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email, subscription_contact_email
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped subscription_level -> subscription_level (priority: 7, score: 67.75) -> subscription_level, service_level
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped quotation_id -> quotation_id (priority: 5, score: 69) -> quotation_id, quote_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped quotation_type -> quotation_type (priority: 8, score: 67.33) -> quotation_type, quote_type
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:113] Conflict: quotation_vendor_id -> vendor_id (score: 67.4) loses to existing reseller_vendor_id (score: 67.4)
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped quotation_vendor_id -> vendor_id (priority: 2, score: 67.4) -> vendor_id
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped quotation_status -> quotation_status (priority: 7, score: 67.75) -> quotation_status, quote_status
[unified_field_mapper] [2025-08-24 01:45:08] [unified_field_mapper.class.php:137] Mapped flaer_phase -> flaer_phase (priority: 8, score: 67.33) -> flaer_phase, phase
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped sold_to_name -> sold_to_name (priority: 2, score: 77) -> sold_to_name, sold_to
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped sold_to_number -> sold_to_number (priority: 8, score: 67.33) -> sold_to_number, sold_to_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped vendor_name -> vendor_name (priority: 6, score: 68.29) -> vendor_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> vendor_id (priority: 2, score: 67.4) -> vendor_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> vendor_id (score: 67.4) loses to existing reseller_vendor_id (score: 67.4)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> vendor_id (priority: 2, score: 67.4) -> vendor_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> address (priority: 1, score: 81.4) -> address, end_customer_address_1
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> end_customer_address_2 (priority: 8, score: 70.33) -> end_customer_address_2, address_2
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> end_customer_address_3 (priority: 8, score: 70.33) -> end_customer_address_3, address_3
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_city -> city (priority: 4, score: 70) -> city, end_customer_city
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_state -> state (priority: 4, score: 70) -> state, end_customer_state
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> postal_code (priority: 4, score: 71.8) -> postal_code, end_customer_zip_code
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_country -> country (priority: 4, score: 70) -> country, end_customer_country
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> end_customer_account_type (priority: 7, score: 70.75) -> end_customer_account_type, account_type
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> contact_name (priority: 4, score: 76) -> contact_name, end_customer_contact_name, subscription_contact_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email, subscription_contact_email
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> end_customer_contact_phone (priority: 6, score: 71.29) -> end_customer_contact_phone, contact_phone, phone
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> end_customer_industry_segment (priority: 8, score: 70.33) -> end_customer_industry_segment, industry
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped agreement_terms -> agreement_terms (priority: 7, score: 67.75) -> agreement_terms, terms
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped agreement_type -> agreement_type (priority: 7, score: 67.75) -> agreement_type, contract_type
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped agreement_status -> agreement_status (priority: 6, score: 68.29) -> agreement_status, contract_status
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped agreement_support_level -> agreement_support_level (priority: 8, score: 70.33) -> agreement_support_level, support_level
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_market_segment (priority: 8, score: 70.33) -> product_market_segment, market_segment
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_sku_description (priority: 8, score: 70.33) -> product_sku_description, product_description
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_list_price (priority: 6, score: 71.29) -> product_list_price, list_price, price
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference (score: 94) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber (score: 94) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 94) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference (score: 88) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber (score: 88) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 88) loses to existing agreement_number (score: 94)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 88) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 7, score: 67.75) -> status, subs_status, subscription_status
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 6, score: 68.29) -> quantity, subs_quantity, subscription_quantity
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> contact_name (score: 73) loses to existing end_customer_contact_name (score: 76)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_contact_name (score: 73) loses to existing end_customer_contact_name (score: 76)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> subscription_contact_name (score: 73) loses to existing end_customer_contact_name (score: 76)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> contact_name (priority: 4, score: 73) -> contact_name, end_customer_contact_name, subscription_contact_name
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> subscription_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email, subscription_contact_email
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped subscription_level -> subscription_level (priority: 7, score: 67.75) -> subscription_level, service_level
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped quotation_id -> quotation_id (priority: 5, score: 69) -> quotation_id, quote_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped quotation_type -> quotation_type (priority: 8, score: 67.33) -> quotation_type, quote_type
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:113] Conflict: quotation_vendor_id -> vendor_id (score: 67.4) loses to existing reseller_vendor_id (score: 67.4)
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped quotation_vendor_id -> vendor_id (priority: 2, score: 67.4) -> vendor_id
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped quotation_status -> quotation_status (priority: 7, score: 67.75) -> quotation_status, quote_status
[unified_field_mapper] [2025-08-24 21:17:49] [unified_field_mapper.class.php:137] Mapped flaer_phase -> flaer_phase (priority: 8, score: 67.33) -> flaer_phase, phase
