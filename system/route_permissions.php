<?php
return [
    'default' => 'user', // Default minimum role requirement
    'routes' => [
        'dashboard' => 'user',
        'users' => 'admin',
        'settings' => 'admin',
        'admin' => 'admin', // Admin section requires admin role
        'system' => 'user', // Temporarily changed from 'dev' to 'user'
        'logs' => 'admin', // Only admin users can access logs
        // Add more routes and their required roles here
    ]
];