<?php

use system\database;

/**
 * Build application paths based on schema
 * 
 * @param array $path Initial path data
 * @return array Complete path data
 */
function build_paths(array $path = [], $schema = []): array {
    // Initialize base paths


    $path['input_params'] = array_merge($_GET, $_POST);

    if (isset($schema['system_views'])){
        $path['system_views'] = $schema['system_views'];
    }

    if (!isset($path['fs_app_root'])) {
        $path['fs_app_root'] = str_replace("system", "", __DIR__);
    }

    // Ensure the path ends with a slash for proper concatenation
    if (!str_ends_with($path['fs_app_root'], '/')) {
        $path['fs_app_root'] .= '/';
    }

    // Load path utilities first (needed for fs_path and normalize_path functions)
    require_once $path['fs_app_root'] . 'system/functions/path_utils.php';

    // Now we can safely normalize the path, preserving leading slash for absolute paths
    $is_absolute = str_starts_with($path['fs_app_root'], '/');
    $normalized = normalize_path($path['fs_app_root']);
    $path['fs_app_root'] = ($is_absolute ? '/' : '') . $normalized . '/';

    // Load request information
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
    $path['request_uri'] = $_SERVER['REQUEST_URI'] ?? '';
    $path['domain'] = $_SERVER['SERVER_NAME'] ?? '';
    $path['script_name'] = $_SERVER['SCRIPT_NAME'];

    // Clean request URI
    $has_params = strpos($path['request_uri'], '?');
    $path['request_uri'] = $has_params ? substr($_SERVER['REQUEST_URI'], 0, $has_params) : $_SERVER['REQUEST_URI'] ?? '';

    // Set document root paths
    if (!isset($path['fs_doc_root'])) $path['fs_doc_root'] = fs_path($_SERVER['DOCUMENT_ROOT']);
    if (!isset($path['doc_root'])) {
        $doc_root = fs_path($_SERVER['DOCUMENT_ROOT']);
        // Ensure doc_root has trailing slash for proper path concatenation
        $path['doc_root'] = rtrim($doc_root, '/');
    }

    $path['fs_app'] = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/';
    // Process resource paths
    if (isset($schema['resources']) && is_array($schema['resources'])) {
        foreach ($schema['resources'] as $dir => $location) {
            $key = $dir === 'root' ? 'fs_resources' : 'fs_' . $dir;
            $path[$key] = join_paths($path['fs_app_root'], $location);
        }
    }

    // Process system paths
    if (isset($schema['system']) && is_array($schema['system'])) {
        foreach ($schema['system'] as $dir => $location) {
            $key = $dir === 'root' ? 'fs_system' : 'fs_sys_' . $dir;
            $path[$key] = join_paths($path['fs_app_root'], $location);

            // Add trailing slash for directories
//            if ($dir !== 'db_class') {
//                $path[$key] .= '/';
//            }
        }
    }
    // Process other paths
    if (isset($schema['other']) && is_array($schema['other'])) {
        foreach ($schema['other'] as $dir => $location) {
            $path['fs_' . $dir] = join_paths($path['fs_app_root'], $location) . '/';
        }
    }
    // Set external paths
    if (isset($schema['external']) && is_array($schema['external'])) {
        foreach ($schema['external'] as $key => $location) {
            $path['fs_' . $key] = $location;
        }
    }
    if ($_SERVER['SERVER_NAME'] == "localhost" && isset($schema['local']) && is_array($schema['local'])) {
        if (isset($schema['local']['external']) && is_array($schema['external'])) {
            foreach ($schema['local']['external'] as $key => $location) {
                $path['fs_' . $key] = $location;
            }
        }
    }
    
    // Set app paths
    // Normalize both paths to use forward slashes for consistent comparison
    $normalized_doc_root = str_replace('\\', '/', $path['doc_root']);
    $normalized_fs_app_root = str_replace('\\', '/', $path['fs_app_root']);

    // Handle case where doc_root is empty or not properly set
    if (empty($normalized_doc_root) || $normalized_doc_root === '/' || $normalized_doc_root === '//') {
        // Fallback: extract web path from filesystem path
        // For development, assume the web path is the last few segments of the filesystem path
        $path_segments = explode('/', trim($normalized_fs_app_root, '/'));

        // Look for common web path patterns and extract the relevant part
        // For paths like E:/Build/test.cadservices.co.uk/baffletrain/autocadlt/autobooks
        // We want /baffletrain/autocadlt/autobooks
        $web_segments = [];
        $found_web_root = false;

        foreach ($path_segments as $segment) {
            // Skip until we find a segment that looks like a web path component
            if (!$found_web_root && (strpos($segment, '.') !== false || $segment === 'httpdocs' || $segment === 'www')) {
                $found_web_root = true;
                continue; // Skip the domain/httpdocs segment itself
            }

            if ($found_web_root) {
                $web_segments[] = $segment;
            }
        }

        // If we couldn't find a clear web root, fall back to last 3 segments
        if (empty($web_segments)) {
            $web_segments = array_slice($path_segments, -3);
        }

        $path['app_root'] = implode('/', $web_segments);
    } else {
        $path['app_root'] = '/' . web_path(str_replace($normalized_doc_root, '', $normalized_fs_app_root));
    }

    // Ensure app_root ends with a slash
//    if (!str_ends_with($path['app_root'], '/')) {
//        $path['app_root'] .= '/';
//    }
    $path['app_path'] = normalize_path(str_replace($path['app_root'], '', $path['request_uri']));
    $path['path_parts'] = explode(DIRECTORY_SEPARATOR, $path['app_path']);
    $path['top_level'] = $path['path_parts'][0] ?? '';
    // Set current page
    if (count($path['path_parts']) > 1) {
        $last_element = count($path['path_parts']) - 1;
        $path['current_page'] = $path['path_parts'][$last_element];
    } else {
        $path['current_page'] = $path['top_level'];
    }
    //$pat = '/\/' . $path['current_page'] . '$/';
   // echo 'papt' . $pat;
    $path['app_path'] = preg_replace('/\/' . str_replace('\\','\\\\',$path['current_page']) . '$/', '', $path['app_path'] );

    $path['current_page'] = explode('?', $path['current_page'])[0];
    // Only set fs_app_path if the required view paths exist
    if ($path['top_level'] === 'system' && isset($path['fs_sys_views'])) {
        $path['fs_app_path'] = fs_path(join_paths($path['fs_sys_views'], $path['app_path']));
    } elseif ($path['top_level'] !== 'system' && isset($path['fs_views'])) {
        $path['fs_app_path'] = fs_path(join_paths($path['fs_views'], $path['app_path']));
    } else {
        // Fallback if view paths are not available
        $path['fs_app_path'] = fs_path(join_paths($path['fs_app_root'], 'resources/views', $path['app_path']));
    }

    $path['app_path'] = str_replace('get_view/', '/', $path['app_path']);
    
    // combos
    $path['fs_full_path'] = fs_path(join_paths($path['fs_app_root'], $path['app_path']));
    $path['full_path'] = web_path(join_paths($path['app_root'], $path['app_path']));

    // combos with current page
    $path['fs_full_page'] = fs_path(join_paths($path['fs_app_path'], $path['current_page']));
    $path['full_page'] = web_path(join_paths($path['full_path'], $path['current_page']));

    // Handle HTMX source paths
    $path['set_by'] = 'default';
    $path['source_path'] = '';
    $path['source_page'] = '';
    $path['source_path_parts'] = '';
    $path['source_app_path'] = '';
    
    if (isset($_SERVER['HTTP_HX_CURRENT_URL'])) {
        $path['set_by'] = 'HTTP_HX_CURRENT_URL';
        $path['hx_current_url'] = $_SERVER['HTTP_HX_CURRENT_URL'];
        $path['hx_current_url_parts'] = parse_url($_SERVER['HTTP_HX_CURRENT_URL']);
        $path['source_path_parts'] = explode('/', normalize_path($path['hx_current_url_parts']['path']));
        $path['source_page'] = array_pop($path['source_path_parts']);
        $path['source_path'] = '/' . web_path(implode('/', $path['source_path_parts']));
        $path['source_app_path'] = normalize_path(str_replace($path['app_root'], '', $path['source_path']));
        $path['source_app_path_parts'] = explode('/', $path['source_app_path']);
        $path['source_fs_path'] = fs_path(join_paths($path['fs_app_root'], 'resources/views', $path['source_app_path']));
    }
    
    // Set database class path and load it
    if (isset($path['fs_system'])) {
        $path['fs_sys_db_class'] = fs_path(join_paths($path['fs_system'], 'classes/database.class.php'));
        require_once $path['fs_sys_db_class'];
    } else {
        // Fallback if fs_system is not available
        $path['fs_sys_db_class'] = fs_path(join_paths($path['fs_app_root'], 'system/classes/database.class.php'));
        require_once $path['fs_sys_db_class'];
    }
    
    // Set action parameter if not set
    global $input_params;
    if (!isset($input_params['action'])) {
        $temp = explode('/', $path['request_uri']);
        $temp2 = array_pop($temp);
        $input_params['action'] = $temp2;
    }
    
    return $path;
}

/**
 * Define constants from path array
 *
 * @param array $path Path data
 * @param bool $generate_ide_helper Whether to generate/update IDE helper file
 * @return void
 */
function build_constants($path, $generate_ide_helper = false): void {
    $constants_defined = [];

    foreach ($path as $key => $value) {
        $constant_name = strtoupper($key);
        if (defined($constant_name)) continue;

        if (is_string($value)) {
            // Preserve absolute paths and trailing slashes when normalizing
            $is_absolute = str_starts_with($value, '/');
            $has_trailing_slash = str_ends_with($value, '/');
            $normalized = normalize_path($value);

            // Reconstruct the path preserving leading and trailing slashes
            $value = ($is_absolute ? '/' : '') . $normalized . ($has_trailing_slash ? '/' : '');
        }

        define($constant_name, $value);

        // Track constants for IDE helper generation
        if ($generate_ide_helper) {
            $constants_defined[$constant_name] = [
                'value' => $value,
                'type' => gettype($value),
                'source_key' => $key
            ];
        }
    }

    // Generate IDE helper file if requested and function is available
    if ($generate_ide_helper && !empty($constants_defined) && function_exists('generate_ide_helper_constants')) {
        generate_ide_helper_constants($constants_defined);
    }
}







/**
 * Build routes from database
 * 
 * @param array $path Path data
 * @return array Route data
 */
function build_routes($path): array {
    $route_tree = [];
    $route_list = [];
    if (!defined('API_RUN') || !API_RUN) {
        $routes = database::table('autobooks_navigation as nav')
            ->select(['id', 'parent_path', 'route_key', 'name', 'icon', 'required_roles', 'show_navbar', 'file_path', 'can_delete', 'is_system'])
            ->cast([
                'required_roles' => 'array',
                'show_navbar' => 'bool',
                'is_system' => 'bool',
                'can_delete' => 'bool'
            ])
            ->orderBy('sort_order', 'asc')
            ->orderBy('name', 'asc')
            ->get();
        foreach ($routes as $key => $route) {
            if (in_array($route, ['icon', 'name', 'sub_folder'])) continue;
            $route_list[$route['route_key']] = $route;
            // Process parent path to build tree
            if ($route['parent_path'] == 'root') {
                // Top-level route
                $route_tree[$route['route_key']] = array_merge($route_tree[$route['route_key']] ?? [],[
                    'name' => $route['name'],
                    'icon' => $route['icon'],
                    'required_roles' => $route['required_roles'] ?: [],
                    'show_navbar' => $route['show_navbar'],
                    'file_path' => $route['file_path'],
                    'can_delete' => $route['can_delete'],
                    'is_system' => $route['is_system']
                ]);
            } else {
                // Nested route - find the correct parent
                $parent_path = normalize_path($route['parent_path']);
                $path_parts = array_filter(explode('/', $parent_path));

                // Navigate to the correct position in the tree
                $branch_ref = &$route_tree;
                foreach ($path_parts as $part) {
                    if (!isset($branch_ref[$part]['sub_folder'])) {
                        $branch_ref[$part]['sub_folder'] = [];
                    }
                    $branch_ref = &$branch_ref[$part]['sub_folder'];
                }

                // Add the route to its parent's sub_folder
                $branch_ref[$route['route_key']] = [
                    'name' => $route['name'],
                    'icon' => $route['icon'],
                    'required_roles' => $route['required_roles'] ?: [],
                    'show_navbar' => $route['show_navbar'],
                    'file_path' => $route['file_path'],
                    'can_delete' => $route['can_delete'],
                    'is_system' => $route['is_system']
                ];
            }
        };

        define('ROUTE_TREE', $route_tree);
        define('ROUTE_LIST', $route_list);
        define('ROUTES', $routes);
        $path['route_tree'] = $route_tree;
        $path['route_list'] = $route_list;
        $path['routes'] = $routes;
    }
    return $path ?? [];
}

/**
 * Remove keywords from path
 * 
 * @param string $path Path to process
 * @return string Processed path
 */
function tcs_remove_keywords($path): string {
    return str_replace('get_view/', '/', $path);
}

/**
 * Create a path from provided segments
 * similar to normalize_path but without trimming slashes
 *
 */
function tcs_path(...$segments): string {
    $path = '';
    // Join segments

    foreach ($segments as $segment) {
        if (is_array($segment)) $segment = implode('/', $segment);
        $path .= '/' . $segment;
    }
    // Replace multiple slashes with a single slash
    while (strpos($path, '//') !== false) {
        $path = str_replace('//', '/', $path);
    }
    $os_convert = str_replace(['\\','/'], DIRECTORY_SEPARATOR, $path);
    return PHP_OS_FAMILY === 'Windows' ? trim($os_convert, DIRECTORY_SEPARATOR) : $os_convert;

}

/**
 * Build API path
 * 
 * @param string $path API endpoint
 * @return string Full API path
 */
function tcs_api($path): string {
    return '/' . APP_ROOT . '/api/' . $path;
}
?>