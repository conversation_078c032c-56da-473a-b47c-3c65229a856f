<?php
/**
 * Minimal Startup Sequence
 *
 * This is a minimal version of the startup sequence that doesn't include
 * authentication checks. It's used by webhook handlers that need to bypass
 * the normal authentication flow.
 */
// Define constants BEFORE including any files that might use them
define('DEBUG_MODE', true);
define('API_RUN', true);
if (!defined('DS')) define('DS', DIRECTORY_SEPARATOR);

// Include necessary files
$path = ['fs_app_root' => "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"];

// Load the path schema
$schema = require_once $path['fs_app_root'] . 'system/config/path_schema.php';

require_once $path['fs_app_root'] . 'system/paths.php';
require_once $path['fs_app_root'] . 'system/functions/functions.php';
$path['doc_root'] = "/var/www/vhosts/cadservices.co.uk/httpdocs/";
$path['fs_doc_root'] = "/var/www/vhosts/cadservices.co.uk/httpdocs/";
$path = build_paths($path, $schema);

build_constants($path);

// Register custom error handlers to capture PHP errors
tcs_register_error_handlers();

require_once $path['fs_app_root'] . 'system/functions/database.php';

require_once($path['fs_doc_root'] . 'vendor/autoload.php');

// Load custom autoloader
require_once($path['fs_app_root'] . "system/autoloader.php");

// Set up error reporting
error_reporting(E_ALL & ~E_NOTICE);

define('POOFED', true);

    $db_server = 'localhost';
    $db_username = 'wwwcadservicescouk';
    $db_password = 'S96#1kvYuCGE';
    $db_database = 'wwwcadservicescouk';

$db = tep_db_connect($db_server, $db_username, $db_password, $db_database) or die('Unable to connect to database server!');


