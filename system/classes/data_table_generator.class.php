<?php
namespace system;

use system\database;
use system\Schema;
use system\column_analyzer;
use Edge\Edge;

/**
 * Data Table Generator Class
 *
 * Generates Edge-compatible data table configurations and views from database tables.
 * Replaces the old template system with a modern Edge-based approach.
 */
class data_table_generator {
    public static $log_target = "data_table_generator";

    /**
     * Generate Edge data table configuration from database table
     *
     * @param string $table_name Database table name
     * @param array $criteria Query criteria for data retrieval
     * @param array $options Display options
     * @return array Edge data table configuration
     */
    public static function generate_table_config(string $table_name, array $criteria = [], array $options = []): array {
        try {
            // Get table schema information
            $schema_info = self::get_table_schema($table_name);
            if (isset($schema_info['error'])) {
                return $schema_info;
            }

            // Add table name to options for intelligent naming
            $options['table_name'] = $table_name;

            // Apply intelligent column selection if enabled
            if ($options['use_intelligent_column_selection'] ?? true) {
                $options = self::apply_intelligent_column_selection($schema_info['columns'], $options);
            }

            // Generate column definitions
            $columns = self::generate_column_definitions($schema_info['columns'], $options);

            // Retrieve data based on criteria
            $data_result = self::retrieve_table_data($table_name, $criteria);
            if (isset($data_result['error'])) {
                return $data_result;
            }

            // Generate available_fields from schema and intelligent column selection
            $available_fields = [];
            if (isset($schema_info['columns'])) {
                // Get all column names
                $all_column_names = array_column($schema_info['columns'], 'name');

                // If intelligent column selection was applied, use hidden columns as available fields
                if (isset($options['available_columns_info'])) {
                    // Use the detailed available columns info from unified field matching
                    foreach ($options['available_columns_info'] as $column_info) {
                        $available_fields[] = $column_info['name'];
                    }
                    tcs_log("Using available fields from unified field matching: " . count($available_fields) . " fields", self::$log_target);
                } else {
                    // Fall back to all columns as available fields
                    $available_fields = $all_column_names;
                    tcs_log("Using all columns as available fields: " . count($available_fields) . " fields", self::$log_target);
                }
            }

            // Build Edge configuration
            $config = [
                'title' => $options['title'] ?? ucfirst(str_replace('_', ' ', $table_name)),
                'description' => $options['description'] ?? "Data table for {$table_name}",
                'items' => $data_result['data'],
                'columns' => $columns,
                'available_fields' => $available_fields,
                'rows' => [
                    'id_prefix' => $options['id_prefix'] ?? 'row_',
                    'id_field' => $schema_info['primary_key'] ?? 'id',
                    'class_postfix' => $options['class_postfix'] ?? '',
                    'extra_parameters' => $options['extra_parameters'] ?? ''
                ],
                'just_body' => $options['just_body'] ?? false,
                'just_rows' => $options['just_rows'] ?? false,
                'items_per_page' => $options['items_per_page'] ?? 30,
                'current_page_num' => $options['current_page_num'] ?? 1,
                'sort_column' => $criteria['order_by'] ?? '',
                'sort_direction' => $criteria['order_direction'] ?? 'asc',
                'callback' => $options['callback'] ?? '',
                'class' => $options['class'] ?? ''
            ];

            return ['success' => true, 'config' => $config];

        } catch (\Exception $e) {
            tcs_log("Error generating table config: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to generate table config: " . $e->getMessage()];
        }
    }

    /**
     * Get database table schema information
     *
     * @param string $table_name Database table name
     * @return array Schema information
     */
    private static function get_table_schema(string $table_name): array {
        try {
            // Check if table exists
            if (!database::tableExists($table_name)) {
                return ['error' => "Table {$table_name} does not exist"];
            }

            // Get column information using DESCRIBE
            $columns_query = "DESCRIBE `{$table_name}`";
            $columns_result = database::rawQuery($columns_query);
            $columns = $columns_result->fetchAll(\PDO::FETCH_ASSOC);

            $schema = [
                'table_name' => $table_name,
                'columns' => [],
                'primary_key' => null
            ];

            foreach ($columns as $column) {
                $column_info = [
                    'name' => $column['Field'],
                    'type' => $column['Type'],
                    'nullable' => $column['Null'] === 'YES',
                    'default' => $column['Default'],
                    'extra' => $column['Extra']
                ];

                // Detect primary key
                if ($column['Key'] === 'PRI') {
                    $schema['primary_key'] = $column['Field'];
                }

                $schema['columns'][] = $column_info;
            }

            return ['success' => true] + $schema;

        } catch (\Exception $e) {
            tcs_log("Error getting table schema: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to get table schema: " . $e->getMessage()];
        }
    }

    /**
     * Apply intelligent column selection using unified field matching system
     *
     * @param array $columns Database column information
     * @param array $options Display options
     * @return array Updated options with intelligent column selection
     */
    private static function apply_intelligent_column_selection(array $columns, array $options = []): array {
        try {
            $hidden_columns = $options['hidden_columns'] ?? [];
            $table_name = $options['table_name'] ?? '';

            // Skip if unified field mapper is not available
            if (!class_exists('system\unified_field_mapper')) {
                tcs_log("Unified field mapper not available, falling back to basic column selection", self::$log_target);
                return self::apply_basic_column_selection($columns, $options);
            }

            tcs_log("Applying unified field-based intelligent column selection for table: {$table_name}", self::$log_target);

            // Get all column names for unified field matching
            $all_column_names = array_column($columns, 'name');

            // Filter out system columns from matching
            $matchable_columns = array_filter($all_column_names, function($column_name) {
                return !in_array($column_name, ['id', 'created_at', 'updated_at', 'data_hash']);
            });

            // Get unified field suggestions
            $suggestions = \system\unified_field_mapper::suggest_field_mappings($matchable_columns);

            tcs_log("Unified field suggestions for " . count($matchable_columns) . " columns: " . count($suggestions) . " matches found", self::$log_target);

            // Categorize columns based on unified field matching and display settings
            $visible_columns = [];
            $available_columns = [];

            foreach ($all_column_names as $column_name) {
                // Skip already hidden columns
                if (in_array($column_name, $hidden_columns)) {
                    continue;
                }

                // Always hide system columns unless explicitly requested
                if (in_array($column_name, ['created_at', 'updated_at', 'data_hash']) && !($options['show_system_columns'] ?? false)) {
                    $hidden_columns[] = $column_name;
                    continue;
                }

                $should_show = false; // Default to hiding unmatched columns
                $unified_field_name = null;
                $confidence = 0;

                // Check if column has unified field match
                if (isset($suggestions[$column_name])) {
                    $suggestion = $suggestions[$column_name];
                    $unified_field_name = $suggestion['field_name'];
                    $confidence = $suggestion['confidence'];

                    // Check if this unified field should be shown by default
                    $should_show = \system\unified_field_definitions::should_show_by_default($unified_field_name);

                    tcs_log("Column '{$column_name}' -> '{$unified_field_name}' (confidence: {$confidence}, show_by_default: " .
                           ($should_show ? 'true' : 'false') . ")", self::$log_target);
                } else {
                    // For unmatched columns, only show essential system columns
                    $essential_columns = ['id']; // Add other essential columns as needed
                    $should_show = in_array($column_name, $essential_columns);

                    tcs_log("Column '{$column_name}' has no unified field match, " .
                           ($should_show ? 'showing as essential' : 'hiding by default'), self::$log_target);
                }

                if ($should_show) {
                    $visible_columns[] = [
                        'name' => $column_name,
                        'unified_field' => $unified_field_name,
                        'confidence' => $confidence
                    ];
                } else {
                    $hidden_columns[] = $column_name;
                    $available_columns[] = [
                        'name' => $column_name,
                        'unified_field' => $unified_field_name,
                        'confidence' => $confidence
                    ];
                }
            }

            // Sort visible columns by unified field priority and confidence
            usort($visible_columns, function($a, $b) {
                // Matched fields come before unmatched
                if ($a['unified_field'] && !$b['unified_field']) return -1;
                if (!$a['unified_field'] && $b['unified_field']) return 1;

                // Sort by confidence if both are matched or both unmatched
                return $b['confidence'] <=> $a['confidence'];
            });

            // Apply max visible columns limit if specified
            $max_visible_columns = $options['max_visible_columns'] ?? null;
            if ($max_visible_columns && count($visible_columns) > $max_visible_columns) {
                $excess_columns = array_slice($visible_columns, $max_visible_columns);
                $visible_columns = array_slice($visible_columns, 0, $max_visible_columns);

                // Move excess columns to available list
                foreach ($excess_columns as $column_info) {
                    $hidden_columns[] = $column_info['name'];
                    $available_columns[] = $column_info;
                }

                tcs_log("Applied max visible columns limit: showing " . count($visible_columns) . ", moved " . count($excess_columns) . " to available", self::$log_target);
            }

            $options['hidden_columns'] = array_unique($hidden_columns);
            $options['available_columns_info'] = $available_columns; // Store for later use
            $options['visible_columns_info'] = $visible_columns; // Store visible column info with unified field data

            tcs_log("Unified field-based column selection complete. Showing " . count($visible_columns) . " columns, hiding " . count($hidden_columns), self::$log_target);

            return $options;

        } catch (\Exception $e) {
            tcs_log("Error in unified field-based column selection: " . $e->getMessage(), self::$log_target);
            // Fall back to basic column selection
            return self::apply_basic_column_selection($columns, $options);
        }
    }

    /**
     * Apply basic column selection as fallback when unified field matching is not available
     *
     * @param array $columns Database column information
     * @param array $options Display options
     * @return array Updated options with basic column selection
     */
    private static function apply_basic_column_selection(array $columns, array $options = []): array {
        try {
            $hidden_columns = $options['hidden_columns'] ?? [];
            $table_name = $options['table_name'] ?? '';

            tcs_log("Applying basic column selection for table: {$table_name}", self::$log_target);

            // Get all column names for context
            $all_column_names = array_column($columns, 'name');

            // Score each column for relevance using the original method
            $column_scores = [];
            foreach ($columns as $column) {
                $column_name = $column['name'];

                // Skip already hidden columns
                if (in_array($column_name, $hidden_columns)) {
                    continue;
                }

                // Calculate relevance score using original method
                $score = self::calculate_column_relevance($table_name, $column_name, $all_column_names, $column);
                $column_scores[$column_name] = $score;

                tcs_log("Column relevance score: {$column_name} = {$score}", self::$log_target);
            }

            // Sort columns by relevance score (highest first)
            arsort($column_scores);

            // Determine how many columns to show by default
            $max_visible_columns = $options['max_visible_columns'] ?? 8;
            $high_relevance_threshold = $options['high_relevance_threshold'] ?? 60;

            // Hide columns that are below threshold or beyond max count
            $visible_count = 0;
            foreach ($column_scores as $column_name => $score) {
                if ($score < $high_relevance_threshold || $visible_count >= $max_visible_columns) {
                    $hidden_columns[] = $column_name;
                    tcs_log("Hiding column: {$column_name} (score: {$score}, visible_count: {$visible_count})", self::$log_target);
                } else {
                    $visible_count++;
                    tcs_log("Showing column: {$column_name} (score: {$score})", self::$log_target);
                }
            }

            $options['hidden_columns'] = array_unique($hidden_columns);

            tcs_log("Basic column selection complete. Showing {$visible_count} columns, hiding " . count($hidden_columns), self::$log_target);

            return $options;

        } catch (\Exception $e) {
            tcs_log("Error in basic column selection: " . $e->getMessage(), self::$log_target);
            return $options; // Return original options on error
        }
    }

    /**
     * Calculate relevance score for a column
     *
     * @param string $table_name Database table name
     * @param string $column_name Column name
     * @param array $all_column_names All column names for context
     * @param array $column_info Column information
     * @return int Relevance score (0-100)
     */
    private static function calculate_column_relevance(string $table_name, string $column_name, array $all_column_names, array $column_info): int {
        $score = 50; // Base score

        // Factor 1: Unified field mapper recognition (40% weight)
        try {
            $suggestions = \system\unified_field_mapper::suggest_field_mappings([$column_name]);
            if (isset($suggestions[$column_name])) {
                $mapper_score = ($suggestions[$column_name]['confidence'] ?? 0) * 0.4;
                $score += $mapper_score;
                tcs_log("Column {$column_name} mapped to {$suggestions[$column_name]['category']} with confidence {$suggestions[$column_name]['confidence']}", self::$log_target);
            }
        } catch (\Exception $e) {
            // Continue without mapper score
        }

        // Factor 2: Column type importance (30% weight)
        $type_score = self::get_column_type_score($column_info) * 0.3;
        $score += $type_score;

        // Factor 3: Column name patterns (20% weight)
        $name_score = self::get_column_name_score($column_name) * 0.2;
        $score += $name_score;

        // Factor 4: Data uniqueness/density (10% weight)
        $uniqueness_score = self::get_column_uniqueness_score($table_name, $column_name) * 0.1;
        $score += $uniqueness_score;

        return min(100, max(0, (int)$score));
    }

    /**
     * Get relevance score based on column type
     */
    private static function get_column_type_score(array $column_info): int {
        $type = strtolower($column_info['type'] ?? '');

        // High importance types
        if (strpos($type, 'varchar') !== false && strpos($type, '255') !== false) {
            return 80; // Standard text fields
        }

        if (strpos($type, 'int') !== false) {
            return 70; // Integer fields (IDs, counts, etc.)
        }

        if (strpos($type, 'decimal') !== false || strpos($type, 'float') !== false) {
            return 75; // Numeric data
        }

        if (strpos($type, 'date') !== false || strpos($type, 'timestamp') !== false) {
            return 65; // Date fields
        }

        // Medium importance
        if (strpos($type, 'text') !== false) {
            return 40; // Large text fields (often less important for overview)
        }

        if (strpos($type, 'enum') !== false) {
            return 85; // Enum fields are usually important
        }

        // Low importance
        if (strpos($type, 'blob') !== false) {
            return 10; // Binary data
        }

        return 50; // Default score
    }

    /**
     * Get relevance score based on column name patterns
     */
    private static function get_column_name_score(string $column_name): int {
        $name = strtolower($column_name);

        // High importance patterns
        $high_importance = ['name', 'title', 'email', 'id', 'status', 'type', 'company', 'customer', 'user', 'account', 'subscription', 'product', 'service'];
        foreach ($high_importance as $pattern) {
            if (strpos($name, $pattern) !== false) {
                return 90;
            }
        }

        // Medium importance patterns
        $medium_importance = ['description', 'address', 'phone', 'date', 'time', 'amount', 'price', 'quantity', 'number'];
        foreach ($medium_importance as $pattern) {
            if (strpos($name, $pattern) !== false) {
                return 60;
            }
        }

        // Low importance patterns
        $low_importance = ['created_at', 'updated_at', 'deleted_at', 'hash', 'token', 'uuid', 'guid', 'internal', 'system'];
        foreach ($low_importance as $pattern) {
            if (strpos($name, $pattern) !== false) {
                return 20;
            }
        }

        return 50; // Default score
    }

    /**
     * Get uniqueness score for a column based on data variance
     *
     * @param string $table_name Database table name
     * @param string $column_name Column name
     * @return int Uniqueness score (0-100)
     */
    private static function get_column_uniqueness_score(string $table_name, string $column_name): int {
        try {
            // Check if table exists
            if (!database::tableExists($table_name)) {
                return 50; // Default score if table doesn't exist
            }

            // Use the database class's built-in column statistics method
            $stats = database::getColumnStats($table_name, $column_name);

            if (isset($stats['error'])) {
                tcs_log("Error getting column stats for {$column_name}: " . $stats['error'], self::$log_target);
                return 50; // Default score on error
            }

            // Get uniqueness ratio from stats
            $uniqueness_ratio = $stats['uniqueness_ratio'] / 100; // Convert percentage to ratio

            // Convert to score based on uniqueness
            if ($uniqueness_ratio >= 0.9) {
                return 90; // Highly unique data (like IDs, emails)
            } elseif ($uniqueness_ratio >= 0.5) {
                return 70; // Moderately unique data
            } elseif ($uniqueness_ratio >= 0.1) {
                return 50; // Some variation
            } elseif ($uniqueness_ratio > 0.01) {
                return 30; // Low variation
            } else {
                return 10; // Almost all identical (should be hidden)
            }

        } catch (\Exception $e) {
            tcs_log("Error calculating uniqueness score for {$column_name}: " . $e->getMessage(), self::$log_target);
            return 50; // Default score on error
        }
    }

    /**
     * Generate column definitions for Edge data table
     *
     * @param array $columns Database column information
     * @param array $options Display options
     * @return array Edge column definitions
     */
    private static function generate_column_definitions(array $columns, array $options = []): array {
        $edge_columns = [];
        $hidden_columns = $options['hidden_columns'] ?? [];
        $column_labels = $options['column_labels'] ?? [];
        $table_name = $options['table_name'] ?? '';
        $use_intelligent_naming = $options['use_intelligent_naming'] ?? true;

        // Get all column names for context analysis
        $all_column_names = array_column($columns, 'name');

        foreach ($columns as $column) {
            $column_name = $column['name'];

            // Skip hidden columns
            if (in_array($column_name, $hidden_columns)) {
                continue;
            }

            // Skip system columns by default
            if (in_array($column_name, ['created_at', 'updated_at', 'data_hash']) && !isset($options['show_system_columns'])) {
                continue;
            }

            // Determine column label
            $column_label = $column_labels[$column_name] ?? null;

            tcs_log("Generator debug - column: $column_name, predefined_label: " . ($column_label ?? 'null') . ", use_intelligent: $use_intelligent_naming, table: $table_name", 'column_analyzer');

            if (!$column_label) {
                if ($use_intelligent_naming && !empty($table_name)) {
                    tcs_log("Generator debug - calling intelligent naming for column: $column_name", 'column_analyzer');
                    $column_label = self::get_intelligent_column_label($table_name, $column_name, $all_column_names);
                } else {
                    tcs_log("Generator debug - using standard formatting for column: $column_name (intelligent: $use_intelligent_naming, table: $table_name)", 'column_analyzer');
                    $column_label = self::format_column_label($column_name);
                }
            } else {
                tcs_log("Generator debug - using predefined label for column: $column_name -> $column_label", 'column_analyzer');
            }

            $edge_column = [
                'label' => $column_label,
                'field' => $column_name,
                'filter' => self::should_enable_filter($column),
                'sortable' => self::should_enable_sort($column),
                'extra_parameters' => ''
            ];

            // Add unified field information if available
            if (isset($options['visible_columns_info'])) {
                foreach ($options['visible_columns_info'] as $visible_col_info) {
                    if ($visible_col_info['name'] === $column_name) {
                        $edge_column['unified_field'] = $visible_col_info['unified_field'];
                        $edge_column['confidence'] = $visible_col_info['confidence'];
                        break;
                    }
                }
            }

            $edge_columns[] = $edge_column;
        }

        return $edge_columns;
    }

    /**
     * Format column name into human-readable label
     *
     * @param string $column_name Database column name
     * @return string Formatted label
     */
    private static function format_column_label(string $column_name): string {
        // Convert snake_case to Title Case
        $label = str_replace('_', ' ', $column_name);
        $label = ucwords($label);
        
        // Handle common abbreviations
        $abbreviations = [
            'Id' => 'ID',
            'Url' => 'URL',
            'Api' => 'API',
            'Csv' => 'CSV',
            'Json' => 'JSON'
        ];
        
        foreach ($abbreviations as $search => $replace) {
            $label = str_replace($search, $replace, $label);
        }
        
        return $label;
    }

    /**
     * Get intelligent column label using column analyzer
     *
     * @param string $table_name Database table name
     * @param string $column_name Column name to analyze
     * @param array $all_columns All column names in the table
     * @return string Intelligent column label
     */
    private static function get_intelligent_column_label(string $table_name, string $column_name, array $all_columns): string {
        try {
            tcs_log("get_intelligent_column_label called for: $column_name", 'column_analyzer');

            // Use column analyzer if available
            if (class_exists('system\column_analyzer')) {
                tcs_log("column_analyzer class exists, calling analyze_column", 'column_analyzer');
                $analysis = column_analyzer::analyze_column($table_name, $column_name, $all_columns);

                tcs_log("Analysis result for $column_name: " . json_encode($analysis), 'column_analyzer');

                // If we have a confident suggestion, use it (use same threshold as column analyzer)
                if ($analysis['confidence'] >= 30 && $analysis['suggested_name'] !== $column_name) {
                    $suggested_name = $analysis['suggested_name'];

                    // Format the suggested name as a label
                    $label = self::format_column_label($suggested_name);

                    // Log the intelligent naming for debugging
                    tcs_log("Intelligent naming applied: {$column_name} -> {$suggested_name} (confidence: {$analysis['confidence']})", 'column_analyzer');

                    return $label;
                } else {
                    tcs_log("No intelligent naming applied for $column_name (confidence: {$analysis['confidence']}%, suggested: {$analysis['suggested_name']})", 'column_analyzer');
                }
            } else {
                tcs_log("column_analyzer class not found", 'column_analyzer');
            }
        } catch (\Exception $e) {
            // Fall back to standard formatting if analysis fails
            tcs_log("Column analysis failed for {$column_name}: " . $e->getMessage(), 'column_analyzer');
        }

        // Fall back to standard column label formatting
        tcs_log("Using standard formatting for $column_name", 'column_analyzer');
        return self::format_column_label($column_name);
    }

    /**
     * Determine if column should have filtering enabled
     *
     * @param array $column Column information
     * @return bool True if filter should be enabled
     */
    private static function should_enable_filter(array $column): bool {
        $type = strtolower($column['type']);
        
        // Enable filters for text and enum types
        if (strpos($type, 'varchar') !== false || 
            strpos($type, 'text') !== false || 
            strpos($type, 'enum') !== false ||
            strpos($type, 'char') !== false) {
            return true;
        }
        
        return false;
    }

    /**
     * Determine if column should have sorting enabled
     *
     * @param array $column Column information
     * @return bool True if sorting should be enabled
     */
    private static function should_enable_sort(array $column): bool {
        $type = strtolower($column['type']);
        
        // Disable sorting for large text fields
        if (strpos($type, 'text') !== false || strpos($type, 'blob') !== false) {
            return false;
        }
        
        return true;
    }

    /**
     * Retrieve data from database table based on criteria
     *
     * @param string $table_name Database table name
     * @param array $criteria Query criteria
     * @return array Data retrieval result
     */
    private static function retrieve_table_data(string $table_name, array $criteria = []): array {
        try {
            // If we have search criteria, use raw SQL for better control
            if (isset($criteria['search']) && !empty($criteria['search'])) {
                return self::retrieve_table_data_with_search($table_name, $criteria);
            }

            $query = database::table($table_name);

            // Apply WHERE conditions
            if (isset($criteria['where']) && is_array($criteria['where'])) {
                foreach ($criteria['where'] as $column => $condition) {
                    if (is_array($condition) && count($condition) >= 2) {
                        $operator = $condition[0];
                        $value = $condition[1];
                        $query->where($column, $operator, $value);
                    } else {
                        $query->where($column, $condition);
                    }
                }
            }

            // Apply ordering
            if (isset($criteria['order_by'])) {
                $direction = $criteria['order_direction'] ?? 'asc';
                $query->orderBy($criteria['order_by'], $direction);
            }

            // Apply pagination
            if (isset($criteria['limit'])) {
                $query->limit($criteria['limit']);

                if (isset($criteria['offset'])) {
                    $query->offset($criteria['offset']);
                }
            }

            $data = $query->get();

            return [
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ];

        } catch (\Exception $e) {
            tcs_log("Error retrieving table data: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to retrieve data: " . $e->getMessage()];
        }
    }

    /**
     * Retrieve table data with search functionality using raw SQL
     */
    private static function retrieve_table_data_with_search(string $table_name, array $criteria = []): array {
        try {
            $search_term = $criteria['search'];
            $search_columns = $criteria['search_columns'] ?? [];

            // Add debugging for search execution
            tcs_log([
                'action' => 'retrieve_table_data_with_search',
                'table_name' => $table_name,
                'search_term' => $search_term,
                'search_columns' => $search_columns,
                'search_columns_count' => count($search_columns)
            ], 'enhanced_data_search_debug');

            // Build base query
            $sql = "SELECT * FROM `{$table_name}`";
            $params = [];
            $where_conditions = [];

            // Add search conditions - use unique parameter names for each column
            if (!empty($search_columns)) {
                $search_conditions = [];
                foreach ($search_columns as $index => $column) {
                    $param_name = "search_term_{$index}";
                    $search_conditions[] = "`{$column}` LIKE :{$param_name}";
                    $params[$param_name] = "%{$search_term}%";
                }
                $where_conditions[] = '(' . implode(' OR ', $search_conditions) . ')';
            } else {
                // Log when no search columns are available
                tcs_log([
                    'action' => 'no_search_columns_available',
                    'table_name' => $table_name,
                    'search_term' => $search_term,
                    'message' => 'No searchable columns found - search will return no results'
                ], 'enhanced_data_search_debug');
            }

            // Add other WHERE conditions
            if (isset($criteria['where']) && is_array($criteria['where'])) {
                $param_counter = 0;
                foreach ($criteria['where'] as $column => $condition) {
                    if (is_array($condition) && count($condition) >= 2) {
                        $operator = $condition[0];
                        $value = $condition[1];
                        $param_name = "where_param_{$param_counter}";
                        $where_conditions[] = "`{$column}` {$operator} :{$param_name}";
                        $params[$param_name] = $value;
                        $param_counter++;
                    } else {
                        $param_name = "where_param_{$param_counter}";
                        $where_conditions[] = "`{$column}` = :{$param_name}";
                        $params[$param_name] = $condition;
                        $param_counter++;
                    }
                }
            }

            // Add WHERE clause if we have conditions
            if (!empty($where_conditions)) {
                $sql .= " WHERE " . implode(' AND ', $where_conditions);
            }

            // Add ordering
            if (isset($criteria['order_by'])) {
                $direction = strtoupper($criteria['order_direction'] ?? 'asc');
                $sql .= " ORDER BY `{$criteria['order_by']}` {$direction}";
            }

            // Add pagination
            if (isset($criteria['limit'])) {
                $sql .= " LIMIT {$criteria['limit']}";

                if (isset($criteria['offset'])) {
                    $sql .= " OFFSET {$criteria['offset']}";
                }
            }

            // Log the final query for debugging
            tcs_log([
                'action' => 'execute_search_query',
                'table_name' => $table_name,
                'sql' => $sql,
                'params' => $params,
                'where_conditions_count' => count($where_conditions)
            ], 'enhanced_data_search_debug');

            // Execute query
            $stmt = database::rawQuery($sql, $params);
            $data = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            // Log the results
            tcs_log([
                'action' => 'search_query_results',
                'table_name' => $table_name,
                'search_term' => $search_term,
                'results_count' => count($data),
                'sql_executed' => $sql
            ], 'enhanced_data_search_debug');

            return [
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ];

        } catch (\Exception $e) {
            tcs_log("Error retrieving table data with search: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to retrieve data with search: " . $e->getMessage()];
        }
    }

    /**
     * Render Edge data table view
     *
     * @param array $config Edge data table configuration
     * @param string $template Template name (default: 'data-table')
     * @return string Rendered HTML
     */
    public static function render_data_table(array $config, string $template = 'data-table'): string {
        try {
            return Edge::render($template, $config);
        } catch (\Exception $e) {
            tcs_log("Error rendering data table: " . $e->getMessage(), self::$log_target);
            return '<div class="error">Failed to render data table: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }

    /**
     * Generate complete data table from database table
     *
     * @param string $table_name Database table name
     * @param array $criteria Query criteria
     * @param array $options Display options
     * @param string $template Template name
     * @return string Rendered HTML or error message
     */
    public static function generate_complete_table(string $table_name, array $criteria = [], array $options = [], string $template = 'data-table'): string {
        $config_result = self::generate_table_config($table_name, $criteria, $options);
        
        if (isset($config_result['error'])) {
            return '<div class="error">Error: ' . htmlspecialchars($config_result['error']) . '</div>';
        }
        
        return self::render_data_table($config_result['config'], $template);
    }
}
