<?php
namespace system;

use system\database;
use PDO;
use Exception;

/**
 * Column Preferences Manager Class
 * 
 * Manages column preferences for data tables including data source configurations
 */
class column_preferences_manager {
    
    private static $log_target = "column_preferences_manager";
    
    /**
     * Ensure the column preferences table exists
     */
    public static function ensure_table(): void {
        try {
            $table_exists = database::rawQuery("SHOW TABLES LIKE 'autobooks_column_preferences'")->rowCount() > 0;
            
            if (!$table_exists) {
                $create_sql = "
                CREATE TABLE `autobooks_column_preferences` (
                    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                    `table_name` varchar(255) NOT NULL COMMENT 'Data table identifier',
                    `user_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'User ID (NULL for global preferences)',
                    `data_source_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'Associated data source ID',
                    `hidden_columns` longtext DEFAULT NULL COMMENT 'JSON array of hidden column IDs',
                    `column_structure` longtext DEFAULT NULL COMMENT 'JSON array of column structure configuration',
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_table_user` (`table_name`, `user_id`),
                    KEY `idx_table_name` (`table_name`),
                    KEY `idx_user_id` (`user_id`),
                    KEY `idx_data_source_id` (`data_source_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Column preferences for data tables'
                ";
                
                database::rawQuery($create_sql);
                tcs_log("Created autobooks_column_preferences table", self::$log_target);
            }
            
        } catch (Exception $e) {
            tcs_log("Error ensuring column preferences table: " . $e->getMessage(), self::$log_target);
            throw $e;
        }
    }
    
    /**
     * Get column preferences for a table
     * 
     * @param string $table_name Table identifier
     * @param int|null $user_id User ID (null for current user)
     * @return array|null Column preferences
     */
    public static function get_preferences(string $table_name, ?int $user_id = null): ?array {
        try {
            self::ensure_table();
            
            if ($user_id === null) {
                $user_id = $_SESSION['user_id'] ?? null;
            }
            
            $db = database::table('autobooks_column_preferences');
            $preferences = $db->where('table_name', $table_name)
                            ->where('user_id', $user_id)
                            ->first();
            
            if ($preferences) {
                // Decode JSON fields
                if (!empty($preferences['hidden_columns'])) {
                    $preferences['hidden_columns'] = json_decode($preferences['hidden_columns'], true) ?: [];
                }
                if (!empty($preferences['column_structure'])) {
                    $preferences['column_structure'] = json_decode($preferences['column_structure'], true) ?: [];
                }
                
                return $preferences;
            }
            
            return null;
            
        } catch (Exception $e) {
            tcs_log("Error getting column preferences: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }
    
    /**
     * Save column preferences for a table
     * 
     * @param string $table_name Table identifier
     * @param array $preferences Preferences data
     * @param int|null $user_id User ID (null for current user)
     * @return bool Success status
     */
    public static function save_preferences(string $table_name, array $preferences, ?int $user_id = null): bool {
        try {
            self::ensure_table();
            
            if ($user_id === null) {
                $user_id = $_SESSION['user_id'] ?? null;
            }
            
            // Prepare data for database
            $data = [
                'table_name' => $table_name,
                'user_id' => $user_id,
                'data_source_id' => $preferences['data_source_id'] ?? null,
                'hidden_columns' => json_encode($preferences['hidden_columns'] ?? []),
                'column_structure' => json_encode($preferences['column_structure'] ?? []),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db = database::table('autobooks_column_preferences');
            
            // Check if preferences already exist
            $existing = $db->where('table_name', $table_name)
                          ->where('user_id', $user_id)
                          ->first();
            
            if ($existing) {
                // Update existing preferences
                $result = $db->where('table_name', $table_name)
                            ->where('user_id', $user_id)
                            ->update($data);
            } else {
                // Create new preferences
                $data['created_at'] = date('Y-m-d H:i:s');
                $result = $db->insert($data);
            }
            
            tcs_log("Saved column preferences for table: $table_name", self::$log_target);
            return $result !== false;
            
        } catch (Exception $e) {
            tcs_log("Error saving column preferences: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
    
    /**
     * Delete column preferences for a table
     * 
     * @param string $table_name Table identifier
     * @param int|null $user_id User ID (null for current user)
     * @return bool Success status
     */
    public static function delete_preferences(string $table_name, ?int $user_id = null): bool {
        try {
            self::ensure_table();
            
            if ($user_id === null) {
                $user_id = $_SESSION['user_id'] ?? null;
            }
            
            $db = database::table('autobooks_column_preferences');
            $result = $db->where('table_name', $table_name)
                        ->where('user_id', $user_id)
                        ->delete();
            
            tcs_log("Deleted column preferences for table: $table_name", self::$log_target);
            return $result !== false;
            
        } catch (Exception $e) {
            tcs_log("Error deleting column preferences: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
    
    /**
     * Get available columns from a data source
     * 
     * @param int $data_source_id Data source ID
     * @return array Available columns
     */
    public static function get_data_source_columns(int $data_source_id): array {
        try {
            $data_source = \system\data_source_manager::get_data_source($data_source_id);
            if (!$data_source) {
                return [];
            }
            
            // Get sample data to determine actual column structure
            $sample_result = \system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);
            if (!$sample_result['success'] || empty($sample_result['data'])) {
                return [];
            }
            
            $sample_data = $sample_result['data'][0];
            $columns = [];
            
            foreach (array_keys($sample_data) as $column_key) {
                $columns[] = [
                    'key' => $column_key,
                    'label' => ucwords(str_replace(['_', '-'], ' ', $column_key)),
                    'visible' => true,
                    'filter' => true
                ];
            }
            
            return $columns;
            
        } catch (Exception $e) {
            tcs_log("Error getting data source columns: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
}
?>
