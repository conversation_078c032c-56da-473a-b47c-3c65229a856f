<?php

/**
 * Modal Tab Management Class
 * 
 * Handles server-side tab management for the system-wide modal component.
 * Provides functionality for detecting modal requests, managing tab state,
 * and generating appropriate HTMX responses with OOB swapping.
 */
class modal_tabs {
    
    /**
     * Check if the current request is targeting the modal
     */
    public static function is_modal_request(): bool {
        return isset($_SERVER['HTTP_HX_TARGET']) && $_SERVER['HTTP_HX_TARGET'] === '#modal_body';
    }
    
    /**
     * Check if modal is currently open (has existing tabs)
     */
    public static function is_modal_open(): bool {
        // We can detect this by checking if there's an HX-Current-URL header
        // or by checking session state if we implement that
        return isset($_SERVER['HTTP_HX_CURRENT_URL']);
    }
    
    /**
     * Get tab title from request data
     */
    public static function get_tab_title($default = 'Content'): string {
        // Check for data-tab-title in the request
        if (isset($_POST['tab_title'])) {
            return $_POST['tab_title'];
        }
        
        // Check for common patterns in request data to generate titles
        if (isset($_POST['csn'])) {
            return 'Customer ' . $_POST['csn'];
        }
        
        if (isset($_POST['subscription_number'])) {
            return 'Subscription ' . $_POST['subscription_number'];
        }
        
        if (isset($_POST['quote_number'])) {
            return 'Quote ' . $_POST['quote_number'];
        }
        
        if (isset($_POST['user_id'])) {
            return 'User Details';
        }
        
        return $default;
    }
    
    /**
     * Generate a unique tab ID based on request data
     */
    public static function generate_tab_id(): string {
        $components = [];
        
        // Use request data to create a unique ID
        if (isset($_POST['csn'])) {
            $components[] = 'customer_' . $_POST['csn'];
        } elseif (isset($_POST['subscription_number'])) {
            $components[] = 'subscription_' . $_POST['subscription_number'];
        } elseif (isset($_POST['quote_number'])) {
            $components[] = 'quote_' . $_POST['quote_number'];
        } elseif (isset($_POST['user_id'])) {
            $components[] = 'user_' . $_POST['user_id'];
        } else {
            // Fallback to timestamp-based ID
            $components[] = 'tab_' . time() . '_' . rand(1000, 9999);
        }
        
        return implode('_', $components);
    }
    
    /**
     * Check if there are any pinned tabs (from session or other storage)
     */
    public static function has_pinned_tabs(): bool {
        // For now, we'll assume no pinned tabs initially
        // This could be enhanced to check session storage or database
        return false;
    }
    
    /**
     * Generate tab bar HTML for OOB swap
     */
    public static function generate_tab_bar_oob($tab_id, $tab_title, $is_new_tab = true): string {
        $tab_bar_html = '<div id="modal_tab_bar" hx-swap-oob="true" class="border-b border-gray-200 mb-4">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <template x-for="tab in tabs" :key="tab.id">
                    <div class="flex items-center group">
                        <button
                            @click="switchTab(tab.id)"
                            :class="tab.active ? 
                                \'border-indigo-500 text-indigo-600\' : 
                                \'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\'"
                            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center"
                            x-text="tab.title">
                        </button>
                        
                        <!-- Pin button -->
                        <button
                            @click="togglePin(tab.id)"
                            :class="tab.pinned ? \'text-indigo-600\' : \'text-gray-400 hover:text-gray-600\'"
                            class="ml-2 p-1 rounded-full hover:bg-gray-100"
                            :title="tab.pinned ? \'Unpin tab\' : \'Pin tab\'">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        
                        <!-- Close button (only show if more than one tab) -->
                        <button
                            x-show="tabs.length > 1"
                            @click="closeTab(tab.id)"
                            class="ml-1 p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </template>
            </nav>
        </div>';
        
        return $tab_bar_html;
    }
    
    /**
     * Set appropriate HTMX response headers for tab management
     */
    public static function set_tab_headers($tab_id, $tab_title, $is_new_tab = true): void {
        if (self::is_modal_request()) {
            if ($is_new_tab) {
                // Trigger Alpine.js to add a new tab
                $tab_data = json_encode([
                    'id' => $tab_id,
                    'title' => $tab_title,
                    'active' => true,
                    'pinned' => false
                ]);
                
                header('HX-Trigger: {"addModalTab": ' . $tab_data . '}');
            } else {
                // Trigger Alpine.js to switch to existing tab
                header('HX-Trigger: {"switchModalTab": "' . $tab_id . '"}');
            }
        }
    }
    
    /**
     * Wrap content with tab management logic
     */
    public static function wrap_content($content, $tab_title = null): string {
        if (!self::is_modal_request()) {
            return $content;
        }
        
        $tab_id = self::generate_tab_id();
        $tab_title = $tab_title ?: self::get_tab_title();
        
        // Determine if this should be a new tab or replace existing
        $modal_open = self::is_modal_open();
        $has_pinned = self::has_pinned_tabs();
        $is_new_tab = $modal_open || $has_pinned;
        
        // Set appropriate headers
        self::set_tab_headers($tab_id, $tab_title, $is_new_tab);
        
        return $content;
    }
}
