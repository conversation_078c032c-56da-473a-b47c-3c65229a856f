<?php

/**
 * Server-side modal tab state management
 * Handles all tab operations on the server side following HTMX principles
 */
class ModalTabState {
    
    private static function getSessionKey() {
        return 'modal_tabs_' . (session_id() ?: 'default');
    }
    
    /**
     * Get current tab state from session
     */
    public static function getState() {
        $key = self::getSessionKey();
        return $_SESSION[$key] ?? [
            'tabs' => [],
            'currentTab' => null,
            'nextTabId' => 1,
            'modalOpen' => false
        ];
    }
    
    /**
     * Save tab state to session
     */
    private static function setState($state) {
        $key = self::getSessionKey();
        $_SESSION[$key] = $state;
    }
    
    /**
     * Add a new tab
     */
    public static function addTab($title, $content, $url = '') {
        $state = self::getState();
        
        $tabId = $state['nextTabId']++;
        $newTab = [
            'id' => $tabId,
            'title' => $title ?: 'New Tab',
            'content' => $content,
            'url' => $url,
            'pinned' => false
        ];
        
        // If modal is closed, replace all tabs; if open, add new tab
        if (!$state['modalOpen']) {
            $state['tabs'] = [$newTab];
        } else {
            $state['tabs'][] = $newTab;
        }
        
        $state['currentTab'] = $tabId;
        $state['modalOpen'] = true;
        
        self::setState($state);
        return $tabId;
    }
    
    /**
     * Switch to a specific tab
     */
    public static function switchTab($tabId) {
        $state = self::getState();
        
        // Verify tab exists
        foreach ($state['tabs'] as $tab) {
            if ($tab['id'] == $tabId) {
                $state['currentTab'] = $tabId;
                self::setState($state);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Close a specific tab
     */
    public static function closeTab($tabId) {
        $state = self::getState();
        
        // Find and remove the tab
        $tabIndex = -1;
        foreach ($state['tabs'] as $index => $tab) {
            if ($tab['id'] == $tabId) {
                $tabIndex = $index;
                break;
            }
        }
        
        if ($tabIndex === -1) {
            return false;
        }
        
        array_splice($state['tabs'], $tabIndex, 1);
        
        // If we closed the current tab, switch to another
        if ($state['currentTab'] == $tabId) {
            if (count($state['tabs']) > 0) {
                $state['currentTab'] = end($state['tabs'])['id'];
            } else {
                $state['currentTab'] = null;
                $state['modalOpen'] = false;
            }
        }
        
        self::setState($state);
        return true;
    }
    
    /**
     * Toggle pin status of a tab
     */
    public static function togglePin($tabId) {
        $state = self::getState();
        
        foreach ($state['tabs'] as &$tab) {
            if ($tab['id'] == $tabId) {
                $tab['pinned'] = !$tab['pinned'];
                self::setState($state);
                return $tab['pinned'];
            }
        }
        
        return false;
    }
    
    /**
     * Close all tabs and modal
     */
    public static function closeModal() {
        $state = [
            'tabs' => [],
            'currentTab' => null,
            'nextTabId' => 1,
            'modalOpen' => false
        ];
        
        self::setState($state);
    }
    
    /**
     * Get current tab content
     */
    public static function getCurrentTabContent() {
        $state = self::getState();
        
        if (!$state['currentTab']) {
            return '';
        }
        
        foreach ($state['tabs'] as $tab) {
            if ($tab['id'] == $state['currentTab']) {
                return $tab['content'];
            }
        }
        
        return '';
    }
    
    /**
     * Update current tab content
     */
    public static function updateCurrentTab($content, $title = null) {
        $state = self::getState();
        
        if (!$state['currentTab']) {
            return false;
        }
        
        foreach ($state['tabs'] as &$tab) {
            if ($tab['id'] == $state['currentTab']) {
                $tab['content'] = $content;
                if ($title !== null) {
                    $tab['title'] = $title;
                }
                self::setState($state);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Extract title from HTML content
     */
    public static function extractTitle($content, $defaultTitle = 'Content') {
        // Try to extract title from h1-h6 tags
        if (preg_match('/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i', $content, $matches)) {
            $title = strip_tags($matches[1]);
            return strlen($title) > 30 ? substr($title, 0, 27) . '...' : $title;
        }
        
        // Try to extract from title attribute or data-title
        if (preg_match('/(?:title|data-title)=["\']([^"\']*)["\']/', $content, $matches)) {
            $title = $matches[1];
            return strlen($title) > 30 ? substr($title, 0, 27) . '...' : $title;
        }
        
        return $defaultTitle;
    }
    
    /**
     * Render the complete modal with tabs
     */
    public static function renderModal() {
        $state = self::getState();
        
        if (!$state['modalOpen'] || empty($state['tabs'])) {
            return '';
        }
        
        return \edge\Edge::render('modal-with-tabs', [
            'tabs' => $state['tabs'],
            'currentTab' => $state['currentTab'],
            'modalOpen' => $state['modalOpen']
        ]);
    }
}
