<?php

namespace system;

use system\unified_field_definitions;

/**
 * Unified Field Mapper
 *
 * Provides intelligent field mapping and normalization for CSV imports and subscription matching.
 * Now uses unified_field_definitions for centralized field management.
 */
class unified_field_mapper {

    private static $log_target = 'unified_field_mapper';

    // Temporary backward compatibility - will be removed once cache clears
    private static $field_mappings = null;

    /**
     * Clear the field mappings cache to force reload from database
     */
    public static function clear_cache(): void {
        self::$field_mappings = null;
    }

    /**
     * Normalize field name for matching - creates variations with spaces and underscores
     */
    private static function normalize_field_name(string $field_name): array {
        $normalized = strtolower(trim($field_name));

        // Create variations: original, with spaces, with underscores
        $variations = [
            $normalized,
            str_replace('_', ' ', $normalized),  // underscores to spaces
            str_replace(' ', '_', $normalized)   // spaces to underscores
        ];

        // Remove duplicates and return
        return array_unique($variations);
    }
    
    /**
     * Get field mappings from unified field definitions with full metadata
     */
    public static function get_field_mappings(): array {
        // Initialize the static property if it's null (backward compatibility)
        if (self::$field_mappings === null) {
            $all_fields = unified_field_definitions::get_all_fields();
            $mappings = [];

            foreach ($all_fields as $field_name => $definition) {
                $mappings[$field_name] = [
                    'patterns' => $definition['patterns'] ?? [],
                    'normalized_fields' => $definition['normalized_fields'] ?? [],
                    'priority' => $definition['matching']['priority'] ?? 10,
                    'enabled' => $definition['matching']['enabled'] ?? false,
                    'confidence_threshold' => $definition['matching']['confidence_threshold'] ?? 70,
                    'exact_match_only' => $definition['matching']['exact_match_only'] ?? false,
                    'fuzzy_matching' => $definition['matching']['fuzzy_matching'] ?? false,
                    'similarity_threshold' => $definition['matching']['similarity_threshold'] ?? 70,
                    'category' => $definition['category'] ?? 'other',
                    'label' => $definition['label'] ?? ucwords(str_replace('_', ' ', $field_name))
                ];
            }

            self::$field_mappings = $mappings;
        }

        return self::$field_mappings;
    }
    
    /**
     * Normalize a CSV entry using the unified field mapping
     * 
     * @param array $entry Original CSV row data
     * @param string $source_table Table name for context
     * @return array Normalized entry with standardized field names
     */
    public static function normalize_entry(array $entry, string $source_table = ''): array {
        $normalized = [
            'id' => $entry['id'] ?? uniqid(),
            'source_table' => $source_table,
            'data_source' => 'csv_table'
        ];
        
        tcs_log("Normalizing entry from {$source_table} with " . count($entry) . " fields", self::$log_target);
        
        // Apply field mappings using priority-based matching with conflict resolution
        $field_mappings = self::get_field_mappings();
        $field_mapping_info = []; // Track mapping information for aliases
        $normalized_field_assignments = []; // Track which original column is assigned to each normalized field

        foreach ($entry as $key => $value) {
            $lower_key = strtolower(trim($key));

            // Find all possible matches and select the best one
            $best_match = self::find_best_field_match($lower_key, $field_mappings, array_keys($entry));

            if ($best_match) {
                // Handle conflicts: if multiple columns map to the same normalized field, keep the best one
                foreach ($best_match['mapping']['normalized_fields'] as $normalized_field) {
                    $should_assign = true;

                    // Check if this normalized field is already assigned
                    if (isset($normalized_field_assignments[$normalized_field])) {
                        $existing_assignment = $normalized_field_assignments[$normalized_field];

                        // Compare scores - keep the higher scoring assignment
                        if ($best_match['score'] <= $existing_assignment['score']) {
                            $should_assign = false;
                            tcs_log("Conflict: {$key} -> {$normalized_field} (score: {$best_match['score']}) loses to existing {$existing_assignment['original_column']} (score: {$existing_assignment['score']})", self::$log_target);
                        } else {
                            tcs_log("Conflict: {$key} -> {$normalized_field} (score: {$best_match['score']}) replaces {$existing_assignment['original_column']} (score: {$existing_assignment['score']})", self::$log_target);
                        }
                    }

                    if ($should_assign) {
                        $normalized[$normalized_field] = $value;
                        $normalized_field_assignments[$normalized_field] = [
                            'original_column' => $key,
                            'score' => $best_match['score'],
                            'field_name' => $best_match['field_name']
                        ];
                    }
                }

                // Store mapping info for potential alias creation
                $field_mapping_info[$key] = [
                    'selected_field' => $best_match['field_name'],
                    'confidence' => $best_match['confidence'],
                    'score' => $best_match['score'],
                    'priority' => $best_match['mapping']['priority']
                ];

                tcs_log("Mapped {$key} -> {$best_match['field_name']} (priority: {$best_match['mapping']['priority']}, score: {$best_match['score']}) -> " . implode(', ', $best_match['mapping']['normalized_fields']), self::$log_target);
            }

            // Always keep original field name as well
            $normalized[$key] = $value;
        }

        // Store field mapping information for potential use in SQL generation
        $normalized['_field_mappings'] = $field_mapping_info;
        
        // Apply post-processing
        $normalized = self::apply_post_processing($normalized);
        
        return $normalized;
    }

    /**
     * Find the best field match using priority-based scoring (public wrapper)
     *
     * @param string $column_name The column name to match
     * @param array $field_mappings All available field mappings
     * @param array $all_columns All column names in the dataset for context
     * @return array|null Best match with field_name, mapping, and score
     */
    public static function find_best_field_match_public(string $column_name, array $field_mappings, array $all_columns = []): ?array {
        return self::find_best_field_match($column_name, $field_mappings, $all_columns);
    }

    /**
     * Find the best field match using priority-based scoring
     *
     * @param string $column_name The column name to match
     * @param array $field_mappings All available field mappings
     * @param array $all_columns All column names in the dataset for context
     * @return array|null Best match with field_name, mapping, and score
     */
    private static function find_best_field_match(string $column_name, array $field_mappings, array $all_columns = []): ?array {
        $possible_matches = [];

        // Find all possible matches
        foreach ($field_mappings as $field_name => $mapping) {
            // Skip disabled fields
            if (!($mapping['enabled'] ?? false)) {
                continue;
            }

            $patterns = array_map('strtolower', $mapping['patterns']);

            // Check for exact match (including space/underscore variations)
            $column_variations = self::normalize_field_name($column_name);
            $exact_match = false;

            foreach ($patterns as $pattern) {
                $pattern_variations = self::normalize_field_name($pattern);
                if (array_intersect($column_variations, $pattern_variations)) {
                    $exact_match = true;
                    break;
                }
            }

            if ($exact_match) {
                $confidence = 100;
            } else {
                // Calculate pattern confidence
                $confidence = self::calculate_pattern_confidence($column_name, $patterns, $mapping);

                // Skip if below threshold
                if ($confidence < ($mapping['confidence_threshold'] ?? 70)) {
                    continue;
                }
            }

            // Calculate contextual score
            $contextual_score = self::calculate_contextual_score($column_name, $field_name, $mapping, $all_columns);

            // Calculate final score combining confidence, priority, and context
            $final_score = self::calculate_final_score($confidence, $mapping['priority'] ?? 10, $contextual_score);

            $possible_matches[] = [
                'field_name' => $field_name,
                'mapping' => $mapping,
                'confidence' => $confidence,
                'contextual_score' => $contextual_score,
                'score' => $final_score,
                'priority' => $mapping['priority'] ?? 10
            ];
        }

        if (empty($possible_matches)) {
            return null;
        }

        // Sort by score (highest first), then by priority (lowest number = highest priority)
        usort($possible_matches, function($a, $b) {
            if ($a['score'] == $b['score']) {
                return $a['priority'] <=> $b['priority'];
            }
            return $b['score'] <=> $a['score'];
        });

        return $possible_matches[0];
    }

    /**
     * Calculate pattern confidence for a column against field patterns
     */
    private static function calculate_pattern_confidence(string $column_name, array $patterns, array $mapping): int {
        $best_confidence = 0;

        foreach ($patterns as $pattern) {
            $confidence = self::calculate_single_pattern_confidence($column_name, $pattern);
            $best_confidence = max($best_confidence, $confidence);
        }

        return $best_confidence;
    }

    /**
     * Calculate confidence for a single pattern match
     */
    private static function calculate_single_pattern_confidence(string $column_name, string $pattern): int {
        // Exact match gets 100%
        if ($column_name === $pattern) {
            return 100;
        }

        // Check for exact match with space/underscore variations (95% confidence)
        $column_variations = self::normalize_field_name($column_name);
        $pattern_variations = self::normalize_field_name($pattern);
        if (array_intersect($column_variations, $pattern_variations)) {
            return 95;
        }

        // Check if column contains the pattern as a whole word (including space/underscore variations)
        foreach ($pattern_variations as $pattern_var) {
            if (preg_match('/\b' . preg_quote($pattern_var, '/') . '\b/', $column_name)) {
                return 90;
            }
        }

        // Also check if any column variation contains the original pattern as whole word
        foreach ($column_variations as $column_var) {
            if (preg_match('/\b' . preg_quote($pattern, '/') . '\b/', $column_var)) {
                return 90;
            }
        }

        // Check if column ends with the pattern (including variations)
        foreach ($pattern_variations as $pattern_var) {
            if (substr($column_name, -strlen($pattern_var)) === $pattern_var) {
                return 85;
            }
        }
        foreach ($column_variations as $column_var) {
            if (substr($column_var, -strlen($pattern)) === $pattern) {
                return 85;
            }
        }

        // Check if column starts with the pattern (including variations)
        foreach ($pattern_variations as $pattern_var) {
            if (substr($column_name, 0, strlen($pattern_var)) === $pattern_var) {
                return 80;
            }
        }
        foreach ($column_variations as $column_var) {
            if (substr($column_var, 0, strlen($pattern)) === $pattern) {
                return 80;
            }
        }

        // Check if pattern is contained in column (but avoid short patterns that could cause false matches)
        // Include space/underscore variations
        foreach ($pattern_variations as $pattern_var) {
            if (strlen($pattern_var) >= 4 && strpos($column_name, $pattern_var) !== false) {
                $pattern_coverage = strlen($pattern_var) / strlen($column_name);
                return max(50, min(75, (int)(50 + ($pattern_coverage * 25))));
            }
        }
        foreach ($column_variations as $column_var) {
            if (strlen($pattern) >= 4 && strpos($column_var, $pattern) !== false) {
                $pattern_coverage = strlen($pattern) / strlen($column_var);
                return max(50, min(75, (int)(50 + ($pattern_coverage * 25))));
            }
        }

        // Check if column is contained in pattern (for compound patterns)
        // Include space/underscore variations
        foreach ($pattern_variations as $pattern_var) {
            if (strlen($column_name) >= 4 && strpos($pattern_var, $column_name) !== false) {
                $column_coverage = strlen($column_name) / strlen($pattern_var);
                return max(50, min(70, (int)(50 + ($column_coverage * 20))));
            }
        }
        foreach ($column_variations as $column_var) {
            if (strlen($column_var) >= 4 && strpos($pattern, $column_var) !== false) {
                $column_coverage = strlen($column_var) / strlen($pattern);
                return max(50, min(70, (int)(50 + ($column_coverage * 20))));
            }
        }

        // No meaningful match
        return 0;
    }

    /**
     * Calculate contextual score based on other columns in the dataset
     */
    private static function calculate_contextual_score(string $column_name, string $field_name, array $mapping, array $all_columns): int {
        $base_score = 50;

        // Boost score for more specific patterns
        $pattern_specificity = self::calculate_pattern_specificity($mapping['patterns'], $column_name);
        $base_score += $pattern_specificity;

        // Reduce score if other more specific fields are already matched
        $competition_penalty = self::calculate_competition_penalty($field_name, $mapping, $all_columns);
        $base_score -= $competition_penalty;

        // Boost score based on field category relevance
        $category_boost = self::calculate_category_relevance($mapping['category'], $all_columns);
        $base_score += $category_boost;

        return max(0, min(100, $base_score));
    }

    /**
     * Calculate how specific a pattern is (more specific = higher score)
     */
    private static function calculate_pattern_specificity(array $patterns, string $column_name): int {
        $specificity = 0;

        foreach ($patterns as $pattern) {
            $pattern_lower = strtolower($pattern);

            // Longer, more specific patterns get higher scores
            if (strpos(strtolower($column_name), $pattern_lower) !== false) {
                $length_bonus = min(20, strlen($pattern) * 2);
                $specificity = max($specificity, $length_bonus);

                // Compound patterns (with underscores) are more specific
                if (strpos($pattern, '_') !== false) {
                    $specificity += 10;
                }
            }
        }

        return $specificity;
    }

    /**
     * Calculate penalty if more specific fields are competing
     */
    private static function calculate_competition_penalty(string $field_name, array $mapping, array $all_columns): int {
        $penalty = 0;

        // Check if there are more specific alternatives in the dataset
        $field_patterns = array_map('strtolower', $mapping['patterns']);

        foreach ($all_columns as $column) {
            $column_lower = strtolower($column);

            // If this is a generic pattern and there's a more specific column available
            if (in_array('name', $field_patterns) && $field_name === 'contact_name') {
                if (strpos($column_lower, 'company') !== false || strpos($column_lower, 'business') !== false) {
                    $penalty += 20; // Reduce likelihood of matching 'name' to contact_name if company_name exists
                }
            }

            if (in_array('name', $field_patterns) && $field_name === 'product_name') {
                if (strpos($column_lower, 'contact') !== false || strpos($column_lower, 'person') !== false) {
                    $penalty += 20; // Reduce likelihood of matching 'name' to product_name if contact fields exist
                }
            }
        }

        return $penalty;
    }

    /**
     * Calculate category relevance boost based on other columns
     */
    private static function calculate_category_relevance(string $category, array $all_columns): int {
        $boost = 0;
        $columns_lower = array_map('strtolower', $all_columns);

        switch ($category) {
            case 'contact':
                if (array_intersect($columns_lower, ['email', 'phone', 'contact_email', 'admin_email'])) {
                    $boost += 10;
                }
                break;

            case 'organization':
                if (array_intersect($columns_lower, ['company', 'organization', 'business', 'customer'])) {
                    $boost += 10;
                }
                break;

            case 'product':
                if (array_intersect($columns_lower, ['product', 'software', 'license', 'subscription'])) {
                    $boost += 10;
                }
                break;
        }

        return $boost;
    }

    /**
     * Calculate final score combining all factors
     */
    private static function calculate_final_score(int $confidence, int $priority, int $contextual_score): float {
        // Priority weight: lower priority number = higher weight
        $priority_weight = max(0.1, 1.0 / ($priority + 1));

        // Combine scores with weights
        $final_score = ($confidence * 0.4) + ($contextual_score * 0.3) + ($priority_weight * 100 * 0.3);

        return round($final_score, 2);
    }

    /**
     * Apply post-processing logic (date parsing, status calculation, etc.)
     */
    private static function apply_post_processing(array $normalized): array {
        // Calculate expiration status and days remaining
        if (!empty($normalized['end_date']) || !empty($normalized['subs_endDate'])) {
            $end_date = $normalized['end_date'] ?? $normalized['subs_endDate'];
            
            $end_timestamp = self::parse_date($end_date);
            if ($end_timestamp) {
                $now = time();
                $days_diff = ($end_timestamp - $now) / (60 * 60 * 24);
                $normalized['subs_enddatediff'] = round($days_diff);
                
                // Override status if expired
                if ($days_diff < 0) {
                    $normalized['subs_status'] = 'EXPIRED';
                    $normalized['status'] = 'EXPIRED';
                } elseif ($days_diff <= 30) {
                    // Keep original status but note it's expiring soon
                    if (empty($normalized['subs_status']) || $normalized['subs_status'] === 'Unknown') {
                        $normalized['subs_status'] = 'EXPIRING';
                    }
                }
                
                tcs_log("Date processing: {$end_date} -> {$days_diff} days remaining", self::$log_target);
            }
        }
        
        return $normalized;
    }
    
    /**
     * Parse date from various formats
     */
    private static function parse_date(string $date_string): ?int {
        if (empty($date_string)) return null;
        
        $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'Y-m-d H:i:s', 'd-m-Y', 'm-d-Y'];
        
        foreach ($formats as $format) {
            $date_obj = \DateTime::createFromFormat($format, $date_string);
            if ($date_obj !== false) {
                return $date_obj->getTimestamp();
            }
        }
        
        // Try strtotime as fallback
        $timestamp = strtotime($date_string);
        return $timestamp !== false ? $timestamp : null;
    }
    
    /**
     * Check if a table contains subscription-related data based on field analysis
     */
    public static function contains_subscription_data(string $table_name, array $available_fields = []): bool {
        if (empty($available_fields)) {
            // Get fields from table if not provided
            try {
                $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
                $columns = tcs_db_query($columns_query) ?: [];
                $available_fields = array_column($columns, 'Field');
            } catch (\Exception $e) {
                return false;
            }
        }
        
        $field_names = array_map('strtolower', $available_fields);
        
        // Look for subscription-related keywords
        $subscription_indicators = [
            'subscription', 'license', 'agreement', 'contract', 'product', 'software',
            'email', 'company', 'customer', 'client', 'end_date', 'expiry', 'quantity'
        ];
        
        $matches = 0;
        foreach ($subscription_indicators as $indicator) {
            foreach ($field_names as $field_name) {
                if (strpos($field_name, $indicator) !== false) {
                    $matches++;
                    break;
                }
            }
        }
        
        // Consider it subscription data if it has 3+ relevant fields
        $has_subscription_data = $matches >= 3;
        
        tcs_log("Table {$table_name} subscription analysis: {$matches} matches, result: " . ($has_subscription_data ? 'YES' : 'NO'), self::$log_target);
        
        return $has_subscription_data;
    }
    
    /**
     * Suggest field mappings for column names with enhanced priority-based matching
     *
     * @param array $column_names Array of column names to analyze
     * @return array Suggested mappings with confidence scores and alternatives
     */
    public static function suggest_field_mappings(array $column_names): array {
        $suggestions = [];
        $field_mappings = self::get_field_mappings();

        foreach ($column_names as $column) {
            $lower_column = strtolower(trim($column));

            // Find best match using priority-based logic
            $best_match = self::find_best_field_match($lower_column, $field_mappings, $column_names);

            if ($best_match) {
                $suggestions[$column] = [
                    'category' => $best_match['field_name'],
                    'field_name' => $best_match['field_name'],
                    'label' => $best_match['mapping']['label'],
                    'normalized_fields' => $best_match['mapping']['normalized_fields'],
                    'confidence' => $best_match['confidence'],
                    'contextual_score' => $best_match['contextual_score'],
                    'final_score' => $best_match['score'],
                    'priority' => $best_match['priority'],
                    'alternatives' => self::find_alternative_matches($lower_column, $field_mappings, $column_names, $best_match['field_name'])
                ];
            }
        }

        return $suggestions;
    }

    /**
     * Find alternative field matches for a column
     */
    private static function find_alternative_matches(string $column_name, array $field_mappings, array $all_columns, string $exclude_field): array {
        $alternatives = [];

        foreach ($field_mappings as $field_name => $mapping) {
            // Skip the best match and disabled fields
            if ($field_name === $exclude_field || !($mapping['enabled'] ?? false)) {
                continue;
            }

            $patterns = array_map('strtolower', $mapping['patterns']);
            $confidence = 0;

            // Check for exact match
            if (in_array($column_name, $patterns)) {
                $confidence = 100;
            } else {
                // Calculate pattern confidence
                $confidence = self::calculate_pattern_confidence($column_name, $patterns, $mapping);
            }

            // Only include alternatives with reasonable confidence
            if ($confidence >= 30) {
                $contextual_score = self::calculate_contextual_score($column_name, $field_name, $mapping, $all_columns);
                $final_score = self::calculate_final_score($confidence, $mapping['priority'] ?? 10, $contextual_score);

                $alternatives[] = [
                    'field_name' => $field_name,
                    'label' => $mapping['label'],
                    'confidence' => $confidence,
                    'contextual_score' => $contextual_score,
                    'final_score' => $final_score,
                    'priority' => $mapping['priority'] ?? 10,
                    'normalized_fields' => $mapping['normalized_fields']
                ];
            }
        }

        // Sort alternatives by score
        usort($alternatives, function($a, $b) {
            return $b['final_score'] <=> $a['final_score'];
        });

        // Return top 3 alternatives
        return array_slice($alternatives, 0, 3);
    }

    /**
     * Public wrapper for find_alternative_matches to support fallback functionality
     *
     * @param string $column_name Column name to find alternatives for
     * @param array $field_mappings Available field mappings
     * @param array $all_columns All column names for context
     * @param string $exclude_field Field to exclude from alternatives
     * @return array Alternative matches sorted by score
     */
    public static function find_alternative_matches_public(string $column_name, array $field_mappings, array $all_columns, string $exclude_field): array {
        return self::find_alternative_matches($column_name, $field_mappings, $all_columns, $exclude_field);
    }

}
