<?php
namespace system;

use system\database;
use Exception;

/**
 * Data Table Configuration Manager
 * 
 * Manages persistent storage of data table column configurations
 */
class data_table_config {
    
    private static $log_target = "data_table_config";
    
    /**
     * Get configuration for a data table
     * 
     * @param string $table_name Unique table identifier
     * @return array|null Configuration data or null if not found
     */
    public static function get_config(string $table_name): ?array {
        try {
            self::ensure_table_exists();
            
            $db = database::table('autobooks_data_table_configurations');
            $config = $db->where('table_name', $table_name)->first();
            
            if ($config) {
                // Decode JSON fields
                if (!empty($config['column_structure'])) {
                    $config['column_structure'] = json_decode($config['column_structure'], true);
                }
                if (!empty($config['hidden_columns'])) {
                    $config['hidden_columns'] = json_decode($config['hidden_columns'], true);
                }
                if (!empty($config['settings'])) {
                    $config['settings'] = json_decode($config['settings'], true);
                }
            }
            
            return $config;
            
        } catch (Exception $e) {
            tcs_log("Error getting config for table $table_name: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }
    
    /**
     * Save configuration for a data table
     * 
     * @param string $table_name Unique table identifier
     * @param array $config Configuration data
     * @return bool Success status
     */
    public static function save_config(string $table_name, array $config): bool {
        try {
            self::ensure_table_exists();
            
            $data = [
                'table_name' => $table_name,
                'data_source_id' => $config['data_source_id'] ?? null,
                'column_structure' => isset($config['column_structure']) ? json_encode($config['column_structure']) : null,
                'hidden_columns' => isset($config['hidden_columns']) ? json_encode($config['hidden_columns']) : null,
                'settings' => isset($config['settings']) ? json_encode($config['settings']) : null,
                'created_by' => $_SESSION['user_id'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db = database::table('autobooks_data_table_configurations');
            
            // Check if configuration already exists
            $existing = $db->where('table_name', $table_name)->first();
            
            if ($existing) {
                // Update existing configuration
                unset($data['table_name']); // Don't update the key field
                $result = $db->where('table_name', $table_name)->update($data);
            } else {
                // Create new configuration
                $data['created_at'] = date('Y-m-d H:i:s');
                $result = $db->insert($data);
            }
            
            tcs_log("Saved config for table $table_name", self::$log_target);
            return true;
            
        } catch (Exception $e) {
            tcs_log("Error saving config for table $table_name: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
    
    /**
     * Delete configuration for a data table
     * 
     * @param string $table_name Unique table identifier
     * @return bool Success status
     */
    public static function delete_config(string $table_name): bool {
        try {
            self::ensure_table_exists();
            
            $db = database::table('autobooks_data_table_configurations');
            $result = $db->where('table_name', $table_name)->delete();
            
            tcs_log("Deleted config for table $table_name", self::$log_target);
            return true;
            
        } catch (Exception $e) {
            tcs_log("Error deleting config for table $table_name: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
    
    /**
     * Get columns from data source for configuration
     * 
     * @param int $data_source_id Data source ID
     * @return array Available columns
     */
    public static function get_data_source_columns(int $data_source_id): array {
        try {
            $data_source = \system\data_source_manager::get_data_source($data_source_id);
            if (!$data_source) {
                return [];
            }
            
            // Get sample data to determine actual column structure
            $sample_result = \system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);
            if (!$sample_result['success'] || empty($sample_result['data'])) {
                return [];
            }
            
            $sample_data = $sample_result['data'][0];
            $columns = [];
            
            foreach (array_keys($sample_data) as $column_key) {
                $columns[] = [
                    'key' => $column_key,
                    'label' => ucwords(str_replace(['_', '-'], ' ', $column_key)),
                    'type' => 'string' // Could be enhanced to detect types
                ];
            }
            
            return $columns;
            
        } catch (Exception $e) {
            tcs_log("Error getting columns for data source $data_source_id: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Ensure the configuration table exists
     */
    private static function ensure_table_exists(): void {
        // Run the migration if needed
        $migration_file = __DIR__ . '/../migrations/create_data_table_configurations.php';
        if (file_exists($migration_file)) {
            // Check if table exists
            $check_query = "SHOW TABLES LIKE 'autobooks_data_table_configurations'";
            $stmt = database::rawQuery($check_query);
            $exists = $stmt->fetch();
            
            if (!$exists) {
                include_once $migration_file;
            }
        }
    }
}
?>
