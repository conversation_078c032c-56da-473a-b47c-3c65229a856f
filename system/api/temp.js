// Modal tab management event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Store reference to modal Alpine component
    let modalComponent = null;

    // Function to get modal Alpine component
    function getModalComponent() {
        if (!modalComponent) {
            const modalElement = document.querySelector('[x-show="showModal"]');
            if (modalElement && modalElement._x_dataStack && modalElement._x_dataStack.length > 0) {
                modalComponent = modalElement._x_dataStack[0];
            }
        }
        return modalComponent;
    }

    // Listen for add new tab events from HTMX
    document.body.addEventListener('addModalTab', function(event) {
        const tabData = event.detail;
        const component = getModalComponent();

        if (component) {
            // Check if tab already exists
            const existingTab = component.tabs.find(tab => tab.id === tabData.id);

            if (!existingTab) {
                // Deactivate all tabs
                component.tabs.forEach(tab => tab.active = false);

                // Add new tab
                component.tabs.push({
                    id: tabData.id,
                    title: tabData.title,
                    active: true,
                    pinned: tabData.pinned || false
                });

                component.activeTab = tabData.id;
            } else {
                // Switch to existing tab
                component.switchTab(tabData.id);
            }
        }
    });

    // Listen for switch tab events from HTMX
    document.body.addEventListener('switchModalTab', function(event) {
        const tabId = event.detail;
        const component = getModalComponent();

        if (component) {
            component.switchTab(tabId);
        }
    });

    // Handle modal opening - check if we should reset tabs
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        if (event.detail.target && event.detail.target.id === 'modal_body') {
            const component = getModalComponent();

            if (component) {
                // Check if modal is currently closed and no pinned tabs
                const modalElement = document.querySelector('[x-show="showModal"]');
                const isModalVisible = modalElement && modalElement.style.display !== 'none';

                if (!isModalVisible && !component.hasPinnedTabs()) {
                    // Reset to single default tab when opening modal fresh
                    component.tabs = [{
                        id: 'default',
                        title: 'Content',
                        content: '',
                        active: true,
                        pinned: false
                    }];
                    component.activeTab = 'default';
                }
            }
        }
    });

    // Reset modal component reference when modal closes
    document.addEventListener('click', function(event) {
        if (event.target.closest('[x-show="showModal"]') === null) {
            modalComponent = null;
        }
    });
});