<?php

namespace api\data_table_storage;
use system\database;
use system\data_table_storage;

/**
 * List all data table configurations for current user
 */
function list_configurations($p) {
    $user_id = data_table_storage::get_current_user_id();
    $configurations = data_table_storage::list_configurations($user_id);

    return json_encode([
        'success' => true,
        'configurations' => $configurations,
        'user_id' => $user_id
    ]);
}

/**
 * Get specific configuration
 */
function get_configuration($p) {
    $table_name = $p['table_name'] ?? null;
    $data_source_id = $p['data_source_id'] ?? null;

    if (empty($table_name) && empty($data_source_id)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    $user_id = data_table_storage::get_current_user_id();
    $config = data_table_storage::get_configuration($table_name, $user_id, $data_source_id);
    
    if ($config) {
        return json_encode([
            'success' => true,
            'configuration' => $config['configuration'],
            'data_source_id' => $config['data_source_id'],
            'table_name' => $config['table_name'],
            'updated_at' => $config['updated_at']
        ]);
    } else {
        return json_encode([
            'success' => false,
            'message' => 'Configuration not found'
        ]);
    }
}

/**
 * Save configuration
 */
function save_configuration($p) {
    $table_name = $p['table_name'] ?? '';
    $configuration = $p['configuration'] ?? [];
    $data_source = $p['data_source'] ?? null;

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    // Parse configuration if it's JSON string
    if (is_string($configuration)) {
        $configuration = json_decode($configuration, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return json_encode(['error' => 'Invalid configuration JSON']);
        }
    }

    $user_id = data_table_storage::get_current_user_id();
    $result = data_table_storage::save_configuration($table_name, $configuration, $user_id, $data_source);

    if ($result) {
        return json_encode([
            'success' => true,
            'message' => 'Configuration saved successfully'
        ]);
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to save configuration'
        ]);
    }
}

/**
 * Delete configuration
 */
function delete_configuration($p) {
    $table_name = $p['table_name'] ?? null;
    $data_source_id = $p['data_source_id'] ?? null;

    if (empty($table_name) && empty($data_source_id)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    $user_id = data_table_storage::get_current_user_id();
    $result = data_table_storage::delete_configuration($table_name, $user_id, $data_source_id);

    if ($result) {
        return json_encode([
            'success' => true,
            'message' => 'Configuration deleted successfully'
        ]);
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to delete configuration'
        ]);
    }
}

/**
 * Reset configuration to default
 */
function reset_to_default($p) {
    $table_name = $p['table_name'] ?? '';
    $columns = $p['columns'] ?? [];
    $data_source = $p['data_source'] ?? null;

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    // Parse columns if it's JSON string
    if (is_string($columns)) {
        $columns = json_decode($columns, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return json_encode(['error' => 'Invalid columns JSON']);
        }
    }

    $user_id = data_table_storage::get_current_user_id();

    // Delete existing configuration
    data_table_storage::delete_configuration($table_name, $user_id);

    // Create new default configuration
    if (!empty($columns)) {
        $configuration = data_table_storage::initialize_default_configuration($table_name, $columns, $user_id, $data_source);
        return json_encode([
            'success' => true,
            'message' => 'Configuration reset to default',
            'configuration' => $configuration
        ]);
    } else {
        return json_encode([
            'success' => true,
            'message' => 'Configuration deleted (no default columns provided)'
        ]);
    }
}

/**
 * Update data source configuration for a table
 */
function update_data_source($p) {
    $table_name = $p['table_name'] ?? '';
    $data_source_selection = $p['data_source_selection'] ?? 'hardcoded';
    $callback = $p['callback'] ?? '';

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    $user_id = data_table_storage::get_current_user_id();

    // Get existing configuration
    $config = data_table_storage::get_configuration($table_name, $user_id);
    $configuration = $config ? $config['configuration'] : [];

    // Parse the selection value
    if ($data_source_selection === 'hardcoded') {
        $data_source_type = 'hardcoded';
        $data_source_id = null;
    } else {
        // It's a data source ID
        $data_source_type = 'data_source';
        $data_source_id = (int)$data_source_selection;
    }

    // Update data source settings
    $configuration['data_source_type'] = $data_source_type;
    $configuration['data_source_id'] = $data_source_id;
    $configuration['updated_at'] = date('Y-m-d H:i:s');

    // Save updated configuration
    $result = data_table_storage::save_configuration($table_name, $configuration, $user_id);

    if ($result) {
        // Regenerate the table with new data source
        if (!empty($callback)) {
            require_once FS_SYS_API . DS . 'data_table.api.php';

            $regenerate_params = [
                'table_name' => $table_name,
                'callback' => $callback
            ];

            $table_result = \api\data_table\data_table_filter($regenerate_params);
            return $table_result;
        }

        return json_encode([
            'success' => true,
            'message' => 'Data source updated successfully'
        ]);
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to update data source'
        ]);
    }
}

/**
 * Preview data from a data source
 */
function preview_data_source($p) {
    $data_source_id = (int)($p['data_source_id'] ?? 0);
    $limit = (int)($p['limit'] ?? 5);

    if (empty($data_source_id)) {
        return '<p class="text-red-600 text-sm">No data source selected</p>';
    }

    try {
        $result = \system\data_source_manager::get_data_source_data($data_source_id, ['limit' => $limit]);

        if (!$result['success']) {
            return '<p class="text-red-600 text-sm">Error: ' . htmlspecialchars($result['error']) . '</p>';
        }

        $data = $result['data'];
        if (empty($data)) {
            return '<p class="text-gray-600 text-sm">No data available from this data source</p>';
        }

        // Generate preview table
        $html = '<div class="overflow-x-auto">';
        $html .= '<table class="min-w-full divide-y divide-gray-200">';

        // Header
        $html .= '<thead class="bg-gray-50">';
        $html .= '<tr>';
        foreach (array_keys($data[0]) as $column) {
            $html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">';
            $html .= htmlspecialchars($column);
            $html .= '</th>';
        }
        $html .= '</tr>';
        $html .= '</thead>';

        // Body
        $html .= '<tbody class="bg-white divide-y divide-gray-200">';
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $value) {
                $html .= '<td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">';
                $html .= htmlspecialchars($value ?? '');
                $html .= '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        $html .= '<p class="text-xs text-gray-500 mt-2">Showing ' . count($data) . ' sample records</p>';

        return $html;

    } catch (Exception $e) {
        return '<p class="text-red-600 text-sm">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
}

/**
 * Update data source and refresh columns
 */
function update_data_source_and_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $data_source_selection = $p['data_source_selection'] ?? -1;
    $callback = $p['callback'] ?? '';

    if (empty($table_name) && empty($data_source_selection)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    $user_id = data_table_storage::get_current_user_id();

    // Parse the selection value first to determine data source info
    $data_source_id = null;
    $data_source_type = 'hardcoded';

    if ($data_source_selection === -1) {
        $data_source_type = 'hardcoded';
        $data_source_id = null;
    } else {
        // It's a data source ID
        $data_source_type = 'data_source';
        $data_source_id = (int)$data_source_selection;
    }

    // Get existing configuration - try both table_name and data_source_id
    $config = null;
    if (!empty($table_name)) {
        $config = data_table_storage::get_configuration($table_name, $user_id);
    } elseif ($data_source_id) {
        $config = data_table_storage::get_configuration(null, $user_id, $data_source_id);
        // If no table_name provided but we have a data source, generate one
        if (!$table_name) {
            $table_name = 'data_source_' . $data_source_id;
        }
    }

    $configuration = $config ? $config['configuration'] : [
        'structure' => [],
        'hidden' => [],
        'columns' => [],
        'created_at' => date('Y-m-d H:i:s')
    ];

    // Update data source settings
    $configuration['data_source_type'] = $data_source_type;
    $configuration['data_source_id'] = $data_source_id;
    $configuration['updated_at'] = date('Y-m-d H:i:s');

    // Debug output
    tcs_log("Data source update: table=$table_name, type=$data_source_type, id=$data_source_id", 'data_table_saga');

    // If switching to a data source, update available columns
    if ($data_source_type === 'data_source' && $data_source_id) {
        try {
            // Get sample data from the data source to determine columns
            $sample_result = \system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);

            if ($sample_result['success'] && !empty($sample_result['data'])) {
                // Build column structure using unified field matching
                $first_row = reset($sample_result['data']);
                $available_fields = array_keys($first_row);

                // Filter out system columns
                $available_fields = array_filter($available_fields, function($field) {
                    return !in_array($field, ['id', 'data_hash']);
                });

                // Use unified field matching to determine which columns to show by default
                $column_structure = generate_unified_column_structure($available_fields, $data_source_id);

                // Update configuration with new structure
                $configuration['structure'] = $column_structure['visible_columns'];
                $configuration['hidden'] = $column_structure['hidden_column_ids'];
                $configuration['available_fields'] = $column_structure['available_fields'];
                $configuration['columns'] = $column_structure['all_columns']; // Store all for reference

                tcs_log("Generated " . count($column_structure['visible_columns']) . " visible columns and " .
                       count($column_structure['available_fields']) . " available fields from data source $data_source_id", 'data_table_saga');
            } else {
                // Fallback: try to get data source configuration
                $data_source = database::table('autobooks_data_sources')
                    ->where('id', $data_source_id)
                    ->first();

                if ($data_source) {
                    $new_structure = [];
                    $column_index = 0;

                    // Try selected_columns first
                    $selected_columns = json_decode($data_source['selected_columns'], true);
                    $column_aliases = json_decode($data_source['column_aliases'], true) ?? [];

                    if (is_array($selected_columns) && !empty($selected_columns)) {
                        foreach ($selected_columns as $table => $columns) {
                            if (is_array($columns)) {
                                foreach ($columns as $column) {
                                    $column_id = 'col_' . $column_index . '_' . md5($table . '.' . $column);
                                    $display_name = $column_aliases[$column] ?? $column;
                                    $field_name = $table . '_' . $column;

                                    $new_structure[] = [
                                        'id' => $column_id,
                                        'label' => ucwords(str_replace('_', ' ', $display_name)),
                                        'field' => $field_name,
                                        'filter' => true,
                                        'fields' => [$field_name],
                                        'visible' => true
                                    ];
                                    $column_index++;
                                }
                            }
                        }
                    } else {
                        // Final fallback: use table schema
                        $table_name = $data_source['table_name'];
                        if ($table_name) {
                            $table_info = \system\data_source_manager::get_table_info($table_name);
                            if ($table_info && isset($table_info['columns'])) {
                                foreach ($table_info['columns'] as $column) {
                                    $column_name = $column['Field'];

                                    // Skip system columns
                                    if (in_array($column_name, ['id', 'created_at', 'updated_at', 'data_hash'])) {
                                        continue;
                                    }

                                    $column_id = 'col_' . $column_index . '_' . md5($column_name);
                                    $new_structure[] = [
                                        'id' => $column_id,
                                        'label' => ucwords(str_replace('_', ' ', $column_name)),
                                        'field' => $column_name,
                                        'filter' => true,
                                        'fields' => [$column_name],
                                        'visible' => true
                                    ];
                                    $column_index++;
                                }
                            }
                        }
                    }

                    // Update configuration with new structure
                    $configuration['structure'] = $new_structure;
                    $configuration['hidden'] = []; // Reset hidden columns
                    $configuration['columns'] = $new_structure; // Store for reference

                    tcs_log("Generated " . count($new_structure) . " columns from data source configuration $data_source_id", 'data_table_saga');
                }
            }
        } catch (Exception $e) {
            tcs_log("Error generating columns for data source $data_source_id: " . $e->getMessage(), 'data_table_saga');
            // If data source loading fails, continue with existing structure
        }
    }

    // Save updated configuration
    $result = data_table_storage::save_configuration($table_name, $configuration, $user_id, $data_source_id);

    if ($result) {
        // Regenerate the table with new data source and columns
        require_once FS_SYS_API . DS . 'data_table.api.php';

        $regenerate_params = [
            'table_name' => $table_name
        ];

        // Add data source ID if we're using a data source
        if ($data_source_type === 'data_source' && $data_source_id) {
            $regenerate_params['data_source_id'] = $data_source_id;
        }

        // Only use callback if we're staying with hardcoded data
        if ($data_source_type === 'hardcoded' && !empty($callback)) {
            $regenerate_params['callback'] = $callback;
        }

        tcs_log("Regenerating table with params: " . json_encode($regenerate_params), 'data_table_saga');
        $table_result = \api\data_table\data_table_filter($regenerate_params);
        return $table_result;
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to update data source and columns'
        ]);
    }
}

/**
 * Hide all columns for a table
 */
function hide_all_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $callback = $p['callback'] ?? '';

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name or data source ID is required']);
    }

    $user_id = data_table_storage::get_current_user_id();

    // Get existing configuration
    $config = data_table_storage::get_configuration($table_name, $user_id);
    $configuration = $config ? $config['configuration'] : [];

    // Get current structure
    $structure = $configuration['structure'] ?? [];

    if (!empty($structure)) {
        // Hide all columns
        $hidden_columns = [];
        foreach ($structure as &$column) {
            $column['visible'] = false;
            $hidden_columns[] = $column['id'];
        }

        // Update configuration
        $configuration['structure'] = $structure;
        $configuration['hidden'] = $hidden_columns;
        $configuration['updated_at'] = date('Y-m-d H:i:s');

        // Save configuration
        $result = data_table_storage::save_configuration($table_name, $configuration, $user_id);

        if ($result) {
            // Regenerate the table
            if (!empty($callback)) {
                require_once FS_SYS_API . DS . 'data_table.api.php';

                $regenerate_params = [
                    'table_name' => $table_name,
                    'callback' => $callback
                ];

                $table_result = \api\data_table\data_table_filter($regenerate_params);
                return $table_result;
            }

            return json_encode([
                'success' => true,
                'message' => 'All columns hidden successfully'
            ]);
        } else {
            return json_encode([
                'success' => false,
                'error' => 'Failed to hide columns'
            ]);
        }
    } else {
        return json_encode([
            'success' => true,
            'message' => 'No columns to hide'
        ]);
    }
}

/**
 * Generate column structure using unified field matching
 * Only shows matched columns that should be displayed by default
 *
 * @param array $available_fields All available field names from the data source
 * @param int $data_source_id Data source ID for logging
 * @return array Structure with visible_columns, hidden_column_ids, available_fields, all_columns
 */
function generate_unified_column_structure(array $available_fields, int $data_source_id): array {
    // Get field suggestions using unified field mapper
    $suggestions = \system\unified_field_mapper::suggest_field_mappings($available_fields);

    $visible_columns = [];
    $hidden_column_ids = [];
    $available_fields_list = [];
    $all_columns = [];
    $column_index = 0;

    // Process each available field
    foreach ($available_fields as $field) {
        $column_id = 'col_' . $column_index . '_' . md5($field);
        $is_matched = isset($suggestions[$field]);
        $should_show_by_default = true;
        $field_label = ucwords(str_replace('_', ' ', $field));

        if ($is_matched) {
            $suggestion = $suggestions[$field];
            $field_label = $suggestion['label'];

            // Check if this field should be shown by default
            $should_show_by_default = \system\unified_field_definitions::should_show_by_default($suggestion['field_name']);

            tcs_log("Field '{$field}' matched to '{$suggestion['field_name']}' (confidence: {$suggestion['confidence']}, show_by_default: " .
                   ($should_show_by_default ? 'true' : 'false') . ")", 'data_table_saga');
        } else {
            tcs_log("Field '{$field}' not matched by unified field system", 'data_table_saga');
        }

        // Create column structure
        $column = [
            'id' => $column_id,
            'label' => $field_label,
            'field' => $field,
            'filter' => true,
            'fields' => [$field],
            'visible' => $should_show_by_default,
            'unified_field' => $is_matched ? $suggestions[$field]['field_name'] : null,
            'confidence' => $is_matched ? $suggestions[$field]['confidence'] : 0
        ];

        // Add to appropriate arrays
        $all_columns[] = $column;

        if ($should_show_by_default) {
            $visible_columns[] = $column;
        } else {
            $hidden_column_ids[] = $column_id;
            // Add to available fields for user to add manually
            $available_fields_list[] = [
                'field' => $field,
                'label' => $field_label,
                'unified_field' => $is_matched ? $suggestions[$field]['field_name'] : null,
                'confidence' => $is_matched ? $suggestions[$field]['confidence'] : 0
            ];
        }

        $column_index++;
    }

    // Sort visible columns by unified field priority and confidence
    usort($visible_columns, function($a, $b) {
        // First sort by whether they have unified field matches
        $a_matched = !is_null($a['unified_field']);
        $b_matched = !is_null($b['unified_field']);

        if ($a_matched && !$b_matched) return -1;
        if (!$a_matched && $b_matched) return 1;

        // If both matched or both unmatched, sort by confidence
        return $b['confidence'] <=> $a['confidence'];
    });

    tcs_log("Column structure generated: " . count($visible_columns) . " visible, " .
           count($available_fields_list) . " available, " . count($all_columns) . " total", 'data_table_saga');

    return [
        'visible_columns' => $visible_columns,
        'hidden_column_ids' => $hidden_column_ids,
        'available_fields' => $available_fields_list,
        'all_columns' => $all_columns
    ];
}
