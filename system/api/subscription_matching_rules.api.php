<?php
namespace api\subscription_matching_rules;
use system\subscription_matching_rules;
/**
 * Subscription Matching Rules API
 */

/**
 * Flush all delayed print_rr outputs
 */


function list_rules($p){
            // Return rules list HTML
            $rules = subscription_matching_rules::get_rules();
            $stats = subscription_matching_rules::get_rule_statistics();
            
            ob_start();
            ?>
            <div id="rules-container" class="space-y-6">
                <?php foreach ($rules as $rule_name => $rule): ?>
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <h3 class="text-lg font-medium text-gray-900">
                                        <?= ucwords(str_replace('_', ' ', $rule_name)) ?>
                                    </h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $rule['enabled'] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' ?>">
                                        <?= $rule['enabled'] ? 'Enabled' : 'Disabled' ?>
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Priority: <?= $rule['priority'] ?>
                                    </span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button type="button" 
                                            class="text-sm text-indigo-600 hover:text-indigo-900"
                                            hx-get="<?= APP_ROOT ?>/api/admin/subscription_matching_rules?action=edit&rule=<?= urlencode($rule_name) ?>"
                                            hx-target="#rule-editor"
                                            hx-swap="innerHTML">
                                        Edit
                                    </button>
                                    <button type="button" 
                                            class="text-sm <?= $rule['enabled'] ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900' ?>"
                                            hx-post="<?= APP_ROOT ?>/api/admin/subscription_matching_rules"
                                            hx-vals='{"action": "toggle", "rule_name": "<?= $rule_name ?>", "enabled": <?= $rule['enabled'] ? 'false' : 'true' ?>}'
                                            hx-target="#rules-container"
                                            hx-swap="outerHTML">
                                        <?= $rule['enabled'] ? 'Disable' : 'Enable' ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="px-6 py-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Field Patterns</h4>
                                    <div class="flex flex-wrap gap-1">
                                        <?php foreach ($rule['field_patterns'] as $pattern): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                                <?= htmlspecialchars($pattern) ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Configuration</h4>
                                    <dl class="text-sm text-gray-600 space-y-1">
                                        <?php if (isset($rule['confidence_score'])): ?>
                                            <div class="flex justify-between">
                                                <dt>Confidence Score:</dt>
                                                <dd><?= $rule['confidence_score'] ?>%</dd>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (isset($rule['similarity_threshold'])): ?>
                                            <div class="flex justify-between">
                                                <dt>Similarity Threshold:</dt>
                                                <dd><?= $rule['similarity_threshold'] ?>%</dd>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (isset($rule['fuzzy_matching'])): ?>
                                            <div class="flex justify-between">
                                                <dt>Fuzzy Matching:</dt>
                                                <dd><?= $rule['fuzzy_matching'] ? 'Yes' : 'No' ?></dd>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (isset($rule['case_sensitive'])): ?>
                                            <div class="flex justify-between">
                                                <dt>Case Sensitive:</dt>
                                                <dd><?= $rule['case_sensitive'] ? 'Yes' : 'No' ?></dd>
                                            </div>
                                        <?php endif; ?>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php
            echo ob_get_clean();
}
            
function toggle($p){
            $rule_name = $_POST['rule_name'] ?? '';
            $enabled = filter_var($_POST['enabled'] ?? false, FILTER_VALIDATE_BOOLEAN);
            
            if (empty($rule_name)) {
                throw new Exception('Rule name is required');
            }
            
            $success = subscription_matching_rules::toggle_rule($rule_name, $enabled);
            
            if ($success) {
                header('HX-Trigger: {"showNotification": {"type": "success", "message": "Rule ' . ($enabled ? 'enabled' : 'disabled') . ' successfully"}}');
                
                // Return updated rules list
                $rules = subscription_matching_rules::get_rules();
                ob_start();
                include 'subscription_matching_rules.api.php';
                echo ob_get_clean();
            } else {
                throw new Exception('Failed to update rule');
            }
}
            
        function reset_defaults($p){
            $success = subscription_matching_rules::reset_to_defaults();
            
            if ($success) {
                header('HX-Trigger: {"showNotification": {"type": "success", "message": "Rules reset to defaults successfully"}}');
                
                // Return updated rules list
                $_REQUEST['action'] = 'list';
                include 'subscription_matching_rules.api.php';
            } else {
                throw new Exception('Failed to reset rules');
            }
}
            
function edit($p){
            $rule_name = $_GET['rule'] ?? '';
            if (empty($rule_name)) {
                throw new Exception('Rule name is required');
            }
            
            $rule = subscription_matching_rules::get_rule($rule_name);
            if (!$rule) {
                throw new Exception('Rule not found');
            }
            
            // Return edit form
            ob_start();
            ?>
            <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" id="rule-edit-modal">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">
                                Edit Rule: <?= ucwords(str_replace('_', ' ', $rule_name)) ?>
                            </h3>
                            <button type="button" 
                                    class="text-gray-400 hover:text-gray-600"
                                    onclick="document.getElementById('rule-edit-modal').remove()">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <form hx-post="<?= APP_ROOT ?>/api/admin/subscription_matching_rules" 
                              hx-target="#rules-container" 
                              hx-swap="outerHTML"
                              class="space-y-4">
                            <input type="hidden" name="action" value="save">
                            <input type="hidden" name="rule_name" value="<?= htmlspecialchars($rule_name) ?>">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Priority</label>
                                    <input type="number" name="priority" value="<?= $rule['priority'] ?>" 
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </div>
                                
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="enabled" value="1" <?= $rule['enabled'] ? 'checked' : '' ?>
                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-700">Enabled</span>
                                    </label>
                                </div>
                            </div>
                            
                            <?php if (isset($rule['similarity_threshold'])): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Similarity Threshold (%)</label>
                                <input type="number" name="similarity_threshold" value="<?= $rule['similarity_threshold'] ?>" 
                                       min="0" max="100"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>
                            <?php endif; ?>
                            
                            <?php if (isset($rule['confidence_score'])): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Confidence Score (%)</label>
                                <input type="number" name="confidence_score" value="<?= $rule['confidence_score'] ?>" 
                                       min="0" max="100"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>
                            <?php endif; ?>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Field Patterns (one per line)</label>
                                <textarea name="field_patterns" rows="6" 
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"><?= implode("\n", $rule['field_patterns']) ?></textarea>
                            </div>
                            
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" 
                                        onclick="document.getElementById('rule-edit-modal').remove()"
                                        class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button type="submit" 
                                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php
            echo ob_get_clean();
}
            
function save($p){
            $rule_name = $_POST['rule_name'] ?? '';
            if (empty($rule_name)) {
                throw new Exception('Rule name is required');
            }
            
            $current_rule = subscription_matching_rules::get_rule($rule_name);
            if (!$current_rule) {
                throw new Exception('Rule not found');
            }
            
            // Update rule configuration
            $updated_rule = $current_rule;
            $updated_rule['priority'] = intval($_POST['priority'] ?? 99);
            $updated_rule['enabled'] = isset($_POST['enabled']);
            
            if (isset($_POST['similarity_threshold'])) {
                $updated_rule['similarity_threshold'] = intval($_POST['similarity_threshold']);
            }
            
            if (isset($_POST['confidence_score'])) {
                $updated_rule['confidence_score'] = intval($_POST['confidence_score']);
            }
            
            if (isset($_POST['field_patterns'])) {
                $patterns = array_filter(array_map('trim', explode("\n", $_POST['field_patterns'])));
                $updated_rule['field_patterns'] = $patterns;
            }
            
            // Validate rule
            $errors = subscription_matching_rules::validate_rule($updated_rule);
            if (!empty($errors)) {
                throw new Exception('Validation errors: ' . implode(', ', $errors));
            }
            
            $success = subscription_matching_rules::update_rule($rule_name, $updated_rule);
            
            if ($success) {
                header('HX-Trigger: {"showNotification": {"type": "success", "message": "Rule updated successfully"}}');
                
                // Return updated rules list
                $_REQUEST['action'] = 'list';
                include 'subscription_matching_rules.api.php';
            } else {
                throw new Exception('Failed to save rule');
            }
}
            
function test($p){
            // Test the current rules against sample data
            ob_start();
            ?>
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Rule Testing</h3>
                <p class="text-sm text-gray-600 mb-4">Testing current rules against available CSV data...</p>
                
                <div class="space-y-4">
                    <?php
                    // Get sample data from CSV tables for testing
                    $rules = subscription_matching_rules::get_rules();
                    $test_results = [];
                    
                    // This would test the rules against actual data
                    echo '<div class="text-sm text-gray-500">Rule testing functionality would be implemented here.</div>';
                    ?>
                </div>
            </div>
            <?php
            echo ob_get_clean();
}


?>
