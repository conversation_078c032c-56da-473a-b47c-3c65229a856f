<?php
namespace api;
use const DEBUG_MODE;

$source_page = SOURCE_PAGE;
$current_page = CURRENT_PAGE;
$path_parts = PATH_PARTS;

// Remove 'api' from the start of path parts if it exists
if ($path_parts[0] === 'api') {
    array_shift($path_parts);
}

if (file_exists(FS_FUNCTIONS . DS . "{$source_page}.fn.php") || file_exists("{$source_page}.fn.php")) {
    include_once(FS_FUNCTIONS . DS . "{$source_page}.fn.php");
    print_rr(FS_FUNCTIONS . DS . "{$source_page}.fn.php include_onced");
}

// If we have a directory structure in the URL
if (count($path_parts) > 1) {
    $api_directory = $path_parts[0];  // First part is the directory/module name
    $action = $path_parts[1];         // Second part is the action

    // Look for <directory>.api.php in the api folder
    $api_file = FS_API . DS . "{$api_directory}.api.php";


    print_rr("Looking for API file: {$api_file}");

    if (file_exists($api_file)) {
        include_once($api_file);
        print_rr("{$api_file} include_onced");

        // Build the namespace path
        $function = __NAMESPACE__ . "\\{$api_directory}\\{$action}";
        print_rr($function, "function: ", true, true);

        if (function_exists($function)) {
            echo $function(INPUT_PARAMS);
        } else {
            http_response_code(404);
           print_rr("API endpoint not found: {$function}");
        }
    } else {
        // Fall back to the original source page based routing
        handle_source_page_routing($source_page, $current_page);
    }
} else {
    // Handle original source page based routing
    handle_source_page_routing($source_page, $current_page);
}

// Helper function to handle the original routing logic
function handle_source_page_routing($source_page, $current_page) {
    $functions_path = FS_FUNCTIONS . DS . "{$source_page}";
    print_rr("including " . FS_FUNCTIONS . DS . "{$source_page}.fn.php");

    if (file_exists(FS_FUNCTIONS . DS . "{$source_page}.fn.php")) {
        include_once(FS_FUNCTIONS . DS . "{$source_page}.fn.php");
        print_rr(FS_FUNCTIONS . DS . "{$source_page}.fn.php include_onced");
    }
    $file_name = $source_page . '.api.php';
    $api_path =  '/'. tcs_path(FS_VIEWS . SOURCE_APP_PATH . '/' .  $source_page . '/');
    $api_endpoint = $api_path . '/' . $file_name;
    print_rr("looking for {$api_endpoint}");
    if (file_exists($api_endpoint)) {
        print_rr( " including {$api_endpoint}");
        include_once($api_endpoint);
        print_rr("- " . "{$api_endpoint} include_onced");
    }else{
        $api_path =  '/'. tcs_path(FS_VIEWS . SOURCE_APP_PATH);
        $api_endpoint = $api_path . '/' . $file_name;
        print_rr("looking for {$api_endpoint} parts: FS_VIEWS: " . FS_VIEWS . DS . "SOURCE_APP_PATH: " . SOURCE_APP_PATH .  "source_page: "  .  $source_page . ".api.php");
        if (file_exists($api_endpoint)) {
            print_rr( " including {$api_endpoint}");
            include_once($api_endpoint);
            print_rr("- " . "{$api_endpoint} include_onced");
        }
    }
    if (file_exists("{$api_path}/{$source_page}.fn.php")) {
        include_once("{$api_path}/{$source_page}.fn.php");
        print_rr(FS_FUNCTIONS . DS . "{$api_path}/{$source_page}.fn.php include_onced");
    }
    $action = $current_page;
    $source_path = '';
    if (SOURCE_APP_PATH != '') $source_path = '\\' . SOURCE_APP_PATH;
    $function = __NAMESPACE__ . $source_path . '\\' . $source_page . '\\'. $action;
    print_rr( $function,"function: ",true,true);

    if (function_exists($function)) {
        echo $function(INPUT_PARAMS);
    } else {
        echo "Invalid action!";
    }
}
?>
