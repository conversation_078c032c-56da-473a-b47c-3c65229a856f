<?php
namespace api\csv_data_source_migration;

use system\data_source_manager;
use system\unified_field_mapper;
use system\database;
use Exception;

/**
 * CSV Data Source Migration API
 * 
 * Utilities to retroactively create data sources for existing CSV tables
 */

/**
 * Find CSV tables that don't have data sources and create them
 */
function create_missing_data_sources($params = []) {
    try {
        $dry_run = $params['dry_run'] ?? true;
        $table_pattern = $params['table_pattern'] ?? 'autobooks_%_data';
        
        // Find all tables matching the CSV pattern
        $csv_tables = find_csv_tables($table_pattern);
        
        // Find existing data sources
        $existing_sources = get_existing_data_sources();
        
        // Find tables without data sources
        $missing_sources = [];
        foreach ($csv_tables as $table_info) {
            $table_name = $table_info['table_name'];
            
            // Check if this table already has a data source
            $has_source = false;
            foreach ($existing_sources as $source) {
                if ($source['table_name'] === $table_name || 
                    (isset($source['resolved_tables']) && in_array($table_name, json_decode($source['resolved_tables'], true) ?? []))) {
                    $has_source = true;
                    break;
                }
            }
            
            if (!$has_source) {
                $missing_sources[] = $table_info;
            }
        }
        
        $results = [];
        $created_count = 0;
        
        foreach ($missing_sources as $table_info) {
            $table_name = $table_info['table_name'];
            
            if ($dry_run) {
                $results[] = [
                    'table_name' => $table_name,
                    'action' => 'would_create',
                    'row_count' => $table_info['row_count'],
                    'column_count' => count($table_info['columns'])
                ];
            } else {
                // Create data source for this table
                $create_result = create_data_source_for_table($table_info);
                
                if ($create_result['success']) {
                    $created_count++;
                    $results[] = [
                        'table_name' => $table_name,
                        'action' => 'created',
                        'data_source_id' => $create_result['data_source_id'],
                        'applied_mappings' => $create_result['applied_count']
                    ];
                } else {
                    $results[] = [
                        'table_name' => $table_name,
                        'action' => 'failed',
                        'error' => $create_result['error']
                    ];
                }
            }
        }
        
        return [
            'success' => true,
            'dry_run' => $dry_run,
            'total_csv_tables' => count($csv_tables),
            'existing_data_sources' => count($existing_sources),
            'missing_data_sources' => count($missing_sources),
            'created_count' => $created_count,
            'results' => $results
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Find all CSV tables matching the pattern
 */
function find_csv_tables($pattern) {
    $tables = [];
    
    // Get all tables matching the pattern
    $pattern_sql = str_replace('%', '%', $pattern);
    $query = "SHOW TABLES LIKE ?";
    $table_names = database::rawQuery($query, [$pattern_sql]);
    
    foreach ($table_names as $row) {
        $table_name = array_values($row)[0];
        
        // Get table info
        $table_info = data_source_manager::get_table_info($table_name);
        if ($table_info && $table_info['row_count'] > 0) {
            $tables[] = $table_info;
        }
    }
    
    return $tables;
}

/**
 * Get existing data sources
 */
function get_existing_data_sources() {
    try {
        $sources = database::table('autobooks_data_sources')
            ->where('status', 'active')
            ->get();
        
        return $sources;
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Create a data source for a specific table
 */
function create_data_source_for_table($table_info) {
    try {
        $table_name = $table_info['name'];
        
        // Get column names (excluding id)
        $column_names = [];
        foreach ($table_info['columns'] as $column) {
            if ($column['Field'] !== 'id') {
                $column_names[] = $column['Field'];
            }
        }
        
        if (empty($column_names)) {
            return ['error' => 'No columns found for unified field mapping'];
        }
        
        // Generate field mapping suggestions
        $suggestions = unified_field_mapper::suggest_field_mappings($column_names);
        
        // Build unified mappings configuration
        $unified_mappings = [
            'min_confidence' => 75,
            'applied' => [],
            'overrides' => []
        ];
        
        // Add high-confidence mappings to applied
        foreach ($suggestions as $column_name => $suggestion) {
            if ($suggestion['confidence'] >= 75) {
                $unified_mappings['applied'][$column_name] = [
                    'category' => $suggestion['field_name'],
                    'field_name' => $suggestion['field_name'],
                    'confidence' => $suggestion['confidence'],
                    'final_score' => $suggestion['final_score'],
                    'normalized_fields' => $suggestion['normalized_fields']
                ];
            }
        }
        
        // Create data source configuration
        $data_source_config = [
            'name' => 'CSV Import: ' . ucfirst(str_replace(['autobooks_', '_data'], '', $table_name)),
            'table_name' => $table_name,
            'description' => "Retroactively created data source for CSV table ({$table_info['row_count']} rows, " . count($column_names) . " columns)",
            'category' => 'csv_import',
            'data_source_type' => 'multi_table_merger',
            'mapping_method' => 'unified_field_mapper',
            'resolved_tables' => [$table_name],
            'unified_mappings' => $unified_mappings,
            'tables' => [$table_name],
            'status' => 'active'
        ];
        
        // Create the data source
        $data_source_id = data_source_manager::create_data_source($data_source_config);
        
        return [
            'success' => true,
            'data_source_id' => $data_source_id,
            'unified_mappings' => $unified_mappings,
            'suggestions_count' => count($suggestions),
            'applied_count' => count($unified_mappings['applied'])
        ];
        
    } catch (Exception $e) {
        return ['error' => 'Failed to create data source: ' . $e->getMessage()];
    }
}

/**
 * Get summary of CSV tables and their data source status
 */
function get_csv_table_summary($params = []) {
    try {
        $table_pattern = $params['table_pattern'] ?? 'autobooks_%_data';
        
        $csv_tables = find_csv_tables($table_pattern);
        $existing_sources = get_existing_data_sources();
        
        $summary = [
            'total_csv_tables' => count($csv_tables),
            'total_data_sources' => count($existing_sources),
            'tables_with_sources' => 0,
            'tables_without_sources' => 0,
            'table_details' => []
        ];
        
        foreach ($csv_tables as $table_info) {
            $table_name = $table_info['name'];
            
            // Check if this table has a data source
            $data_source = null;
            foreach ($existing_sources as $source) {
                if ($source['table_name'] === $table_name || 
                    (isset($source['resolved_tables']) && in_array($table_name, json_decode($source['resolved_tables'], true) ?? []))) {
                    $data_source = $source;
                    break;
                }
            }
            
            if ($data_source) {
                $summary['tables_with_sources']++;
                $status = 'has_data_source';
                $data_source_info = [
                    'id' => $data_source['id'],
                    'name' => $data_source['name'],
                    'mapping_method' => $data_source['mapping_method'] ?? 'none'
                ];
            } else {
                $summary['tables_without_sources']++;
                $status = 'missing_data_source';
                $data_source_info = null;
            }
            
            $summary['table_details'][] = [
                'table_name' => $table_name,
                'row_count' => $table_info['row_count'],
                'column_count' => count($table_info['columns']),
                'status' => $status,
                'data_source' => $data_source_info
            ];
        }
        
        return [
            'success' => true,
            'summary' => $summary
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
