<?php

namespace api\unified_field_definitions;

use system\unified_field_definitions;
use system\unified_field_mapper;
use edge\Edge;
/**
 * API endpoints for managing unified field definitions
 */

/**
 * Get all field definitions
 */
function index($params) {
    try {
        $fields = unified_field_definitions::get_all_fields();
        $stats = unified_field_definitions::get_field_statistics();

        return json_encode([
            'success' => true,
            'fields' => $fields,
            'statistics' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        return json_encode([
            'success' => false,
            'error' => 'Failed to load field definitions: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get single field definition
 */
function get_field($params) {
    try {
        $field_name = $params['field_name'] ?? '';
        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        $field = unified_field_definitions::get_field($field_name);
        if (!$field) {
            throw new Exception('Field not found');
        }

        return json_encode([
            'success' => true,
            'field' => $field,
            'is_custom' => unified_field_definitions::is_custom_field($field_name),
            'is_modified' => unified_field_definitions::is_modified_field($field_name)
        ]);
    } catch (Exception $e) {
        http_response_code(404);
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Create new field definition
 */
function create($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Check if field already exists
        if (unified_field_definitions::get_field($field_name)) {
            throw new Exception('Field already exists');
        }

        // Build definition from form data
        $definition = [
            'label' => $params['label'] ?? '',
            'type' => $params['type'] ?? 'string',
            'category' => $params['category'] ?? 'custom',
            'description' => $params['description'] ?? '',
            'patterns' => array_filter(explode("\n", $params['patterns'] ?? ''), 'trim'),
            'normalized_fields' => array_filter(explode("\n", $params['normalized_fields'] ?? ''), 'trim'),
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => isset($params['matching_enabled']),
                'priority' => (int)($params['matching_priority'] ?? 10),
                'case_sensitive' => isset($params['case_sensitive']),
                'fuzzy_matching' => isset($params['fuzzy_matching']),
                'similarity_threshold' => (int)($params['similarity_threshold'] ?? 70),
                'exact_match_only' => !isset($params['fuzzy_matching'])
            ],
            'display' => [
                'show_by_default' => isset($params['show_by_default']),
                'category_priority' => (int)($params['category_priority'] ?? 5)
            ]
        ];

        // Validate definition
        $validation = unified_field_definitions::validate_field_definition($definition);
        if (!$validation['valid']) {
            throw new Exception('Invalid field definition: ' . implode(', ', $validation['errors']));
        }

        // Save field definition
        $success = unified_field_definitions::save_field_definition($field_name, $definition);
        if (!$success) {
            throw new Exception('Failed to save field definition');
        }

        // Return success message and close modal with OOB swap to refresh field definitions
        header('HX-Trigger: {"closeModal": true, "showNotification": {"type": "success", "message": "Field definition created successfully"}}');

        // Get the category of the created field to maintain tab selection
        $created_field = unified_field_definitions::get_field($field_name);
        $field_category = $created_field['category'] ?? $definition['category'] ?? 'custom';

        // Return the updated field definitions section as OOB swap with correct category selected
        $field_definitions_html = edge::render('field-definitions-section', ['active_category' => $field_category]);

        return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
               '<div class="p-4 bg-green-50 border border-green-200 rounded-md">' .
               '<p class="text-green-800">Successfully created field definition</p>' .
               '</div>';

    } catch (Exception $e) {
        // Return error message in modal
        $error_html = '<div class="p-4 bg-red-50 border border-red-200 rounded-md mb-4">';
        $error_html .= '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        $error_html .= '</div>';

        // Re-include the form with error
        $field = unified_field_definitions::create_field_template($field_name ?? 'new_field');
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $is_new = true;
        $form_title = 'Create New Field Definition';
        $submit_action = 'create';

        ob_start();
        echo $error_html;
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
        return ob_get_clean();
    }
}

/**
 * Update field definition
 */
function update($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Build definition from form data
        $definition = [
            'label' => $params['label'] ?? '',
            'type' => $params['type'] ?? 'string',
            'category' => $params['category'] ?? 'custom',
            'description' => $params['description'] ?? '',
            'patterns' => array_filter(explode("\n", $params['patterns'] ?? ''), 'trim'),
            'normalized_fields' => array_filter(explode("\n", $params['normalized_fields'] ?? ''), 'trim'),
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => isset($params['matching_enabled']),
                'priority' => (int)($params['matching_priority'] ?? 10),
                'case_sensitive' => isset($params['case_sensitive']),
                'fuzzy_matching' => isset($params['fuzzy_matching']),
                'similarity_threshold' => (int)($params['similarity_threshold'] ?? 70),
                'exact_match_only' => !isset($params['fuzzy_matching'])
            ],
            'display' => [
                'show_by_default' => isset($params['show_by_default']),
                'category_priority' => (int)($params['category_priority'] ?? 5)
            ]
        ];

        // Validate definition
        $validation = unified_field_definitions::validate_field_definition($definition);
        if (!$validation['valid']) {
            throw new Exception('Invalid field definition: ' . implode(', ', $validation['errors']));
        }

        // Save field definition
        $success = unified_field_definitions::save_field_definition($field_name, $definition);
        if (!$success) {
            throw new Exception('Failed to update field definition');
        }

        // Return success message and close modal with OOB swap to refresh field definitions
        header('HX-Trigger: {"closeModal": true, "showNotification": {"type": "success", "message": "Field definition updated successfully"}}');

        // Get the category of the updated field to maintain tab selection
        $updated_field = unified_field_definitions::get_field($field_name);
        $field_category = $updated_field['category'] ?? 'identification';
        print_rr($updated_field,'updated_field');
        // Return the updated field definitions section as OOB swap with correct category selected
        $field_definitions_html = edge::render('field-definitions-section', ['active_category' => $field_category]);

        return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
               '<div class="p-4 bg-green-50 border border-green-200 rounded-md">' .
               '<p class="text-green-800">Successfully updated field definition</p>' .
               '</div>';

    } catch (Exception $e) {
        // Return error message in modal
        $error_html = '<div class="p-4 bg-red-50 border border-red-200 rounded-md mb-4">';
        $error_html .= '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        $error_html .= '</div>';

        // Re-include the form with error
        $field = unified_field_definitions::get_field($field_name);
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $is_new = false;
        $form_title = 'Edit Field Definition: ' . $field_name;
        $submit_action = 'update';

        ob_start();
        echo $error_html;
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
        return ob_get_clean();
    }
}

/**
 * Delete field definition
 */
function delete($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Check if field exists
        if (!unified_field_definitions::get_field($field_name)) {
            throw new Exception('Field not found');
        }

        // Delete field definition
        $success = unified_field_definitions::delete_field_definition($field_name);
        if (!$success) {
            throw new Exception('Failed to delete field definition');
        }

        return json_encode([
            'success' => true,
            'message' => 'Field definition deleted successfully'
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Reset field to default
 */
function reset($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Reset field definition
        $success = unified_field_definitions::reset_field_to_default($field_name);
        if (!$success) {
            throw new Exception('Failed to reset field definition');
        }

        // Return success message and close modal
        return '<div class="p-4 bg-green-50 border border-green-200 rounded-md">' .
            '<p class="text-green-800">Successfully reset field definition</p>' .
            '</div>';

    } catch (Exception $e) {
        // Return error message
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}

/**
 * Get field editor form
 */
function edit_form($params) {
    try {
        $field_name = $params['field_name'] ?? '';
        $is_new = $params['is_new'] ?? false;

        if (!$is_new && empty($field_name)) {
            throw new Exception('Field name is required');
        }

        $field = null;
        if (!$is_new) {
            $field = unified_field_definitions::get_field($field_name);
            if (!$field) {
                throw new Exception('Field not found');
            }
        } else {
            $field = unified_field_definitions::create_field_template('new_field');
        }

        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $form_title = $is_new ? 'Create New Field Definition' : 'Edit Field Definition: ' . $field_name;
        $submit_action = $is_new ? 'create' : 'update';

        return edge::render('field-definition-edit-form', [
            'field' => $field,
            'field_name' => $field_name,
            'categories' => $categories,
            'types' => $types,
            'form_title' => $form_title,
            'submit_action' => $submit_action
        ]);


    } catch (Exception $e) {
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}

/**
 * Update field priority and matching settings
 */
function update_field_priority($params = []) {
    try {
        $field_name = $params['field_name'] ?? '';
        $field_priorities = $params['field_priorities'] ?? [];

        if (empty($field_name) && empty($field_priorities)) {
            throw new Exception("Field name or field priorities data is required");
        }

        $updated_count = 0;
        $errors = [];

        // Handle single field update
        if (!empty($field_name)) {
            $priority = $params['priority'] ?? null;
            $enabled = $params['enabled'] ?? null;
            $confidence_threshold = $params['confidence_threshold'] ?? null;

            $result = update_single_field_priority($field_name, $priority, $enabled, $confidence_threshold);
            if ($result['success']) {
                $updated_count++;
            } else {
                $errors[] = $result['error'];
            }
        }

        // Handle bulk field updates
        if (!empty($field_priorities)) {
            foreach ($field_priorities as $field_name => $settings) {
                $priority = $settings['priority'] ?? null;
                $enabled = isset($settings['enabled']) ? (bool)$settings['enabled'] : null;
                $confidence_threshold = $settings['confidence_threshold'] ?? null;
                $exact_match_only = isset($settings['exact_match_only']) ? (bool)$settings['exact_match_only'] : null;
                $fuzzy_matching = isset($settings['fuzzy_matching']) ? (bool)$settings['fuzzy_matching'] : null;

                $result = update_single_field_priority($field_name, $priority, $enabled, $confidence_threshold, $exact_match_only, $fuzzy_matching);
                if ($result['success']) {
                    $updated_count++;
                } else {
                    $errors[] = "Field {$field_name}: " . $result['error'];
                }
            }
        }

        // Return status message for UI with OOB swap if successful
        if ($updated_count > 0) {
            $message = $updated_count === 1 ?
                "Field priority updated successfully" :
                "{$updated_count} field priorities updated successfully";

            if (!empty($errors)) {
                $message .= " (with " . count($errors) . " errors)";
            }

            // Determine category for tab selection - prioritize single field updates
            $field_category = null;
            if (!empty($params['field_name'])) {
                // Single field update - get the category of that field
                $updated_field = unified_field_definitions::get_field($params['field_name']);
                $field_category = $updated_field['category'] ?? null;
            }

            // Return the updated field definitions section as OOB swap with correct category selected
            $field_definitions_html = edge::render('field-definitions-section',
                $field_category ? ['active_category' => $field_category] : []
            );

            return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
                   '<div class="text-sm text-green-700 bg-green-100 px-3 py-2 rounded">' . $message . '</div>';
        } else {
            $error_message = !empty($errors) ? implode(', ', $errors) : 'No fields were updated';
            return '<div class="text-sm text-red-700 bg-red-100 px-3 py-2 rounded">Error: ' . $error_message . '</div>';
        }

    } catch (Exception $e) {
        return '<div class="text-sm text-red-700 bg-red-100 px-3 py-2 rounded">Error: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Update a single field's priority and settings
 */
function update_single_field_priority($field_name, $priority = null, $enabled = null, $confidence_threshold = null, $exact_match_only = null, $fuzzy_matching = null) {
    try {
        // Get current field definition
        $current_definition = unified_field_definitions::get_field($field_name);
        if (!$current_definition) {
            throw new Exception("Field '{$field_name}' not found");
        }

        // Update matching configuration
        $matching_config = $current_definition['matching'] ?? [];

        if ($priority !== null) {
            $matching_config['priority'] = (int)$priority;
        }
        if ($enabled !== null) {
            $matching_config['enabled'] = (bool)$enabled;
        }
        if ($confidence_threshold !== null) {
            $matching_config['confidence_threshold'] = (int)$confidence_threshold;
        }
        if ($exact_match_only !== null) {
            $matching_config['exact_match_only'] = (bool)$exact_match_only;
        }
        if ($fuzzy_matching !== null) {
            $matching_config['fuzzy_matching'] = (bool)$fuzzy_matching;
        }

        // Update the definition
        $updated_definition = $current_definition;
        $updated_definition['matching'] = $matching_config;

        // Save to database
        $success = unified_field_definitions::save_field_definition($field_name, $updated_definition);

        return [
            'success' => $success,
            'error' => $success ? null : "Failed to save field definition"
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Reset all field priorities to defaults
 */
function reset_all_priorities($params = []) {
    try {
        // Get all custom field definitions
        $all_fields = unified_field_definitions::get_all_fields();
        $reset_count = 0;
        $errors = [];

        foreach ($all_fields as $field_name => $definition) {
            // Check if this field has been customized
            if (unified_field_definitions::is_modified_field($field_name)) {
                $success = unified_field_definitions::reset_field_to_default($field_name);
                if ($success) {
                    $reset_count++;
                } else {
                    $errors[] = "Failed to reset field: {$field_name}";
                }
            }
        }

        if ($reset_count > 0) {
            $message = "{$reset_count} field(s) reset to default priorities";
            if (!empty($errors)) {
                $message .= " (with " . count($errors) . " errors)";
            }

            // Return the updated field definitions section as OOB swap (no specific category since multiple fields reset)
            $field_definitions_html = edge::render('field-definitions-section');

            return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
                   '<div class="text-sm text-green-700 bg-green-100 px-3 py-2 rounded">' . $message . '</div>';
        } else {
            return '<div class="text-sm text-blue-700 bg-blue-100 px-3 py-2 rounded">No custom field priorities found to reset</div>';
        }

    } catch (Exception $e) {
        return '<div class="text-sm text-red-700 bg-red-100 px-3 py-2 rounded">Error: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Test field matching for a given column name
 */
function test_field_matching($params = []) {
    try {
        $column_name = $params['column_name'] ?? '';
        $context_columns = $params['context_columns'] ?? [];

        if (empty($column_name)) {
            throw new Exception("Column name is required");
        }

        // Convert context_columns string to array if needed
        if (is_string($context_columns)) {
            $context_columns = array_filter(array_map('trim', explode(',', $context_columns)));
        }

        $suggestions = \system\unified_field_mapper::suggest_field_mappings([$column_name]);
        $suggestion = $suggestions[$column_name] ?? null;

        return json_encode([
            'success' => true,
            'column_name' => $column_name,
            'context_columns' => $context_columns,
            'suggestion' => $suggestion,
            'has_match' => !empty($suggestion)
        ]);

    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Clear field mapper cache to force reload from database
 */
function clear_cache($params = []) {
    try {
        unified_field_mapper::clear_cache();

        return json_encode([
            'success' => true,
            'message' => 'Field mapper cache cleared successfully'
        ]);

    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Get field definitions section component for OOB swap
 */
function get_field_definitions_section($params = []) {
    try {
        $active_category = $params['active_category'] ?? null;

        return edge::render('field-definitions-section',
            $active_category ? ['active_category' => $active_category] : []
        );

    } catch (Exception $e) {
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error loading field definitions: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}

/**
 * Generate preview table showing default display fields
 */
function preview_table($params = []) {
    try {
        // Get fields that should show by default
        $default_fields = unified_field_definitions::get_default_display_fields();

        if (empty($default_fields)) {
            return '<div class="text-center text-gray-500 py-8">' .
                   '<p>No fields are currently set to "Show by default"</p>' .
                   '<p class="text-sm mt-2">Edit field definitions above to mark fields as default display fields.</p>' .
                   '</div>';
        }

        // Sort fields by category priority and then by priority within category
        uasort($default_fields, function($a, $b) {
            $a_priority = $a['display']['category_priority'] ?? 10;
            $b_priority = $b['display']['category_priority'] ?? 10;
            return $a_priority <=> $b_priority;
        });

        // Generate sample data
        $sample_data = generate_sample_data($default_fields);

        // Build table HTML
        $html = '<div class="overflow-x-auto">';
        $html .= '<table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">';

        // Table header
        $html .= '<thead class="bg-gray-50">';
        $html .= '<tr>';
        foreach ($default_fields as $field_name => $field_def) {
            $html .= '<th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0">';
            $html .= htmlspecialchars($field_def['label']);
            $html .= '<div class="text-xs font-normal text-gray-400 mt-1">Priority: ' . ($field_def['display']['category_priority'] ?? 10) . '</div>';
            $html .= '</th>';
        }
        $html .= '</tr>';
        $html .= '</thead>';

        // Table body with sample data
        $html .= '<tbody class="bg-white divide-y divide-gray-200">';
        foreach ($sample_data as $row_index => $row) {
            $html .= '<tr class="' . ($row_index % 2 === 0 ? 'bg-white' : 'bg-gray-50') . '">';
            foreach ($default_fields as $field_name => $field_def) {
                $html .= '<td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200 last:border-r-0">';
                $html .= htmlspecialchars($row[$field_name] ?? '');
                $html .= '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        // Add configuration info
        $html .= '<div class="mt-4 text-sm text-gray-600">';
        $html .= '<p><strong>Configuration Name:</strong> <code>unified_field_definitions_default</code></p>';
        $html .= '<p><strong>Fields Shown:</strong> ' . count($default_fields) . ' fields marked as "Show by default"</p>';
        $html .= '<p><strong>Usage:</strong> This configuration will be applied to new CSV imports and data source tables when enabled in column manager.</p>';
        $html .= '</div>';

        return $html;

    } catch (Exception $e) {
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error generating preview: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}



/**
 * Save default table configuration
 */
function save_default_config($params = []) {
    try {
        // Get fields that should show by default
        $default_fields = unified_field_definitions::get_default_display_fields();

        if (empty($default_fields)) {
            return '<div class="p-3 bg-yellow-50 border border-yellow-200 rounded-md">' .
                   '<p class="text-yellow-800">No fields are currently set to "Show by default". Please configure field display settings first.</p>' .
                   '</div>';
        }

        // Sort fields by category priority
        uasort($default_fields, function($a, $b) {
            $a_priority = $a['display']['category_priority'] ?? 10;
            $b_priority = $b['display']['category_priority'] ?? 10;
            return $a_priority <=> $b_priority;
        });

        // Create column configuration
        $column_config = [];
        $display_order = 1;

        foreach ($default_fields as $field_name => $field_def) {
            $column_config[] = [
                'field_name' => $field_name,
                'label' => $field_def['label'],
                'visible' => true,
                'display_order' => $display_order++,
                'width' => null, // Auto width
                'category' => $field_def['category'],
                'priority' => $field_def['display']['category_priority'] ?? 10
            ];
        }

        // Save configuration with special name
        $config_name = 'unified_field_definitions_default';
        $config_data = [
            'name' => $config_name,
            'description' => 'Default column configuration based on unified field definitions "Show by default" settings',
            'type' => 'default_template',
            'columns' => $column_config,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_system_config' => true,
            'auto_apply' => false // Will be controlled by checkbox in column manager
        ];

        // Save to column configurations (assuming there's a column_manager class)
        if (class_exists('column_manager')) {
            $success = column_manager::save_configuration($config_name, $config_data);
        } else {
            // Fallback: save to a JSON file or database table
            $config_file = 'system/config/default_table_config.json';
            $success = file_put_contents($config_file, json_encode($config_data, JSON_PRETTY_PRINT));
        }

        if ($success) {
            return '<div class="p-3 bg-green-50 border border-green-200 rounded-md">' .
                   '<div class="flex items-center">' .
                   '<svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">' .
                   '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>' .
                   '</svg>' .
                   '<p class="text-green-800"><strong>Configuration saved successfully!</strong></p>' .
                   '</div>' .
                   '<p class="text-green-700 text-sm mt-2">Configuration name: <code>' . $config_name . '</code></p>' .
                   '<p class="text-green-700 text-sm">Fields included: ' . count($column_config) . '</p>' .
                   '<p class="text-green-700 text-sm">This configuration can now be applied to new data tables via the column manager.</p>' .
                   '</div>';
        } else {
            throw new Exception('Failed to save configuration');
        }

    } catch (Exception $e) {
        return '<div class="p-3 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error saving configuration: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}

/**
 * Apply default configuration to a data table
 */
function apply_default_config($params = []) {
    try {
        $table_name = $params['table_name'] ?? '';
        $callback = $params['callback'] ?? '';

        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }

        // Get the saved default template configuration
        $template_name = 'unified_field_definitions_default_template';
        $template_config = column_preferences_manager::get_preferences($template_name);

        if (!$template_config || empty($template_config['column_structure'])) {
            throw new Exception('No default template configuration found. Please configure and save the default template first using the preview table above.');
        }

        // Get current table configuration
        $current_config = column_preferences_manager::get_preferences($table_name);
        if (!$current_config) {
            $current_config = [
                'hidden_columns' => [],
                'column_structure' => []
            ];
        }

        // Use the template configuration
        $updated_config = [
            'hidden_columns' => $template_config['hidden_columns'] ?? [],
            'column_structure' => $template_config['column_structure'] ?? [],
            'data_source_id' => $current_config['data_source_id'] ?? null,
            'applied_template' => 'unified_field_definitions_default',
            'applied_at' => date('Y-m-d H:i:s')
        ];

        // Save the updated configuration
        $success = column_preferences_manager::save_preferences($table_name, $updated_config);

        if (!$success) {
            throw new Exception('Failed to save column configuration');
        }

        // Return updated data table
        if (!empty($callback) && function_exists($callback)) {
            $items = $callback([]);
        } else {
            $items = [];
        }

        // Build columns for display from template
        $columns = [];
        $available_fields = [];

        foreach ($updated_config['column_structure'] as $col) {
            $field_key = $col['field'] ?? $col['fields'][0] ?? '';
            if (!empty($field_key)) {
                $columns[] = [
                    'key' => $field_key,
                    'label' => $col['label'] ?? $field_key,
                    'filter' => true
                ];
                $available_fields[] = $field_key;
            }
        }

        return edge::render('data-table', [
            'table_name' => $table_name,
            'items' => $items,
            'columns' => $columns,
            'just_table' => true,
            'available_fields' => $available_fields,
            'data_source_type' => 'template_applied',
            'template_name' => 'unified_field_definitions_default'
        ]);

    } catch (Exception $e) {
        // Return error in the data table area
        return '<div class="data_table p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error applying default configuration: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}



/**
 * Save the current data-table configuration as the default template
 */
function save_table_config($params = []) {
    try {
        $table_name = $params['table_name'] ?? '';

        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }

        // Get the current column preferences for the preview table
        $current_config = column_preferences_manager::get_preferences($table_name);

        if (!$current_config || empty($current_config['column_structure'])) {
            throw new Exception('No column configuration found for the preview table. Please configure the table columns first using the column manager.');
        }

        // Extract the column configuration
        $column_structure = $current_config['column_structure'];
        $hidden_columns = $current_config['hidden_columns'] ?? [];

        // Create the default template configuration
        $template_config = [
            'name' => 'unified_field_definitions_default',
            'description' => 'Default column configuration template based on unified field definitions',
            'type' => 'default_template',
            'source_table' => $table_name,
            'columns' => $column_structure,
            'hidden_columns' => $hidden_columns,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_system_config' => true,
            'auto_apply' => false // Controlled by checkbox in column manager
        ];

        // Save the template configuration
        // This could be saved to a dedicated templates table or as a special entry in column preferences
        $template_name = 'unified_field_definitions_default_template';
        $success = column_preferences_manager::save_preferences($template_name, [
            'column_structure' => $column_structure,
            'hidden_columns' => $hidden_columns,
            'data_source_id' => 'template',
            'is_template' => true
        ]);

        if ($success) {
            $visible_columns = array_filter($column_structure, function($col) use ($hidden_columns) {
                return !in_array($col['id'] ?? '', $hidden_columns);
            });

            return '<div class="p-3 bg-green-50 border border-green-200 rounded-md">' .
                   '<div class="flex items-center">' .
                   '<svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">' .
                   '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>' .
                   '</svg>' .
                   '<p class="text-green-800"><strong>Default configuration saved successfully!</strong></p>' .
                   '</div>' .
                   '<div class="text-green-700 text-sm mt-2 space-y-1">' .
                   '<p><strong>Template name:</strong> <code>unified_field_definitions_default</code></p>' .
                   '<p><strong>Visible columns:</strong> ' . count($visible_columns) . '</p>' .
                   '<p><strong>Hidden columns:</strong> ' . count($hidden_columns) . '</p>' .
                   '<p><strong>Column order and nesting:</strong> Preserved from current configuration</p>' .
                   '<p class="pt-2 border-t border-green-300"><strong>Usage:</strong> This configuration can now be applied to new data tables via the "Apply default field configuration" checkbox in any column manager.</p>' .
                   '</div>' .
                   '</div>';
        } else {
            throw new Exception('Failed to save template configuration');
        }

    } catch (Exception $e) {
        return '<div class="p-3 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error saving configuration: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}
