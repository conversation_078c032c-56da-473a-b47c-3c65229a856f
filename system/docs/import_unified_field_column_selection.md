# Import Unified Field Column Selection

The import system now uses unified field matching to intelligently select which columns to display by default when importing CSV files, with hidden fields available through the existing column manager.

## Overview

When importing CSV files, the system now:

1. **Analyzes CSV headers** using the unified field matching system
2. **Automatically shows only matched fields** that have `show_by_default: true`
3. **Hides technical/detail fields** that are matched but marked as `show_by_default: false`
4. **Makes hidden fields available** through the existing column manager for manual addition
5. **Uses unified field labels** instead of raw CSV column names

## How It Works

### 1. CSV Import Process

```php
// During CSV import with auto-schema
$import_result = data_importer::import_csv_with_auto_schema($csv_file, $table_name, true, true);

// The system automatically:
// 1. Analyzes CSV headers
// 2. Matches them to unified fields
// 3. Creates table with intelligent column selection
// 4. Generates data source with unified field mappings
```

### 2. Intelligent Column Selection

The enhanced `data_table_generator` now uses unified field matching:

```php
private static function apply_intelligent_column_selection($columns, $options) {
    // Get unified field suggestions for all columns
    $suggestions = unified_field_mapper::suggest_field_mappings($column_names);
    
    foreach ($columns as $column_name) {
        if (isset($suggestions[$column_name])) {
            $unified_field = $suggestions[$column_name]['field_name'];
            
            // Check display settings
            $should_show = unified_field_definitions::should_show_by_default($unified_field);
            
            if ($should_show) {
                $visible_columns[] = $column_name;
            } else {
                $hidden_columns[] = $column_name;
                $available_fields[] = $column_name;
            }
        }
    }
}
```

### 3. Column Manager Integration

The existing column manager automatically receives:
- **Visible columns** - Initially displayed in the table
- **Available fields** - Hidden fields available for manual addition
- **Field metadata** - Unified field names and confidence scores

## Example Scenarios

### Scenario 1: Customer Data Import

**CSV Headers:**
```
company_name, contact_email, product_name, start_date, end_date, 
quantity, phone_number, notes, created_timestamp, internal_id
```

**Automatic Selection:**
- ✅ **Initially Visible:** company_name, contact_email, product_name, start_date, end_date
- 📋 **Available for Addition:** quantity, phone_number, notes, created_timestamp, internal_id

**Result:** Clean, focused view with 5 core business columns, 5 additional fields available in column manager.

### Scenario 2: Product Subscription Import

**CSV Headers:**
```
customer_company, customer_contact, subscription_reference, 
product_family, license_count, subscription_start, subscription_end,
support_level, renewal_date, account_manager, billing_address
```

**Automatic Selection:**
- ✅ **Initially Visible:** customer_company → company_name, subscription_reference, product_family → product_name, subscription_start → start_date, subscription_end → end_date
- 📋 **Available for Addition:** customer_contact → contact_name, license_count → quantity, support_level, renewal_date, account_manager, billing_address → address

### Scenario 3: Technical Data Import

**CSV Headers:**
```
system_id, created_at, updated_at, data_hash, processing_status,
error_count, last_sync, internal_notes, debug_info
```

**Automatic Selection:**
- ✅ **Initially Visible:** processing_status → status
- 📋 **Available for Addition:** system_id, created_at, updated_at, error_count, last_sync, internal_notes → notes, debug_info

**Result:** Only 1 business-relevant column shown initially, 8 technical fields available for detailed analysis.

## Configuration

### Field Display Settings

Control which fields are shown by default:

```json
{
  "field_name": "quantity",
  "display": {
    "show_by_default": false,    // Hide by default during imports
    "category_priority": 3       // Lower priority for display ordering
  }
}
```

### Import Options

Configure import behavior:

```php
$options = [
    'use_intelligent_column_selection' => true,  // Enable unified field matching
    'use_intelligent_naming' => true,            // Use unified field labels
    'max_visible_columns' => 8,                  // Limit initial columns
    'show_system_columns' => false               // Hide timestamps by default
];
```

### Data Source Configuration

The import creates a data source with unified mappings:

```php
$data_source_config = [
    'mapping_method' => 'unified_field_mapper',
    'unified_mappings' => [
        'min_confidence' => 75,
        'applied' => [
            'company_name' => [
                'field_name' => 'company_name',
                'confidence' => 95,
                'normalized_fields' => ['company_name', 'customer_name']
            ]
        ]
    ]
];
```

## Benefits

### 1. Improved User Experience
- **Focused initial view** with only relevant business columns
- **Reduced cognitive load** by hiding technical details
- **Consistent labeling** using unified field names
- **Easy access** to all fields through column manager

### 2. Better Data Discovery
- **Intelligent field recognition** matches CSV headers to business concepts
- **Confidence scoring** helps identify match quality
- **Contextual grouping** of related fields
- **Unified terminology** across different data sources

### 3. Flexible Customization
- **Manual field addition** through existing column manager
- **Configurable display preferences** per field type
- **Override capabilities** for specific use cases
- **Backward compatibility** with existing imports

## Testing

### Test Scripts

1. **Test Import Column Selection:**
   ```
   http://localhost/autobooks/test_import_column_selection.php
   ```

2. **Update Field Display Settings:**
   ```
   http://localhost/autobooks/update_field_display_settings.php
   ```

### Manual Testing

1. **Create test CSV** with mixed field types:
   ```csv
   company_name,contact_email,product_name,quantity,notes,created_at
   Acme Corp,<EMAIL>,AutoCAD LT,5,Important client,2024-01-01
   ```

2. **Import using enhanced method:**
   ```php
   data_importer::import_csv_with_auto_schema($csv_file, $table_name, true, true);
   ```

3. **Verify results:**
   - Core business fields (company_name, contact_email, product_name) visible initially
   - Technical fields (quantity, notes, created_at) available in column manager
   - Unified field labels used instead of raw CSV headers

### Expected Results

- **Initial columns:** 3-5 core business fields
- **Available fields:** 5-10 additional fields in column manager
- **Field labels:** "Company Name" instead of "company_name"
- **Intelligent ordering:** Matched fields before unmatched fields

## Integration Points

### 1. Data Importer
- `import_csv_with_auto_schema()` - Enhanced with unified field matching
- `create_unified_field_data_source()` - Creates data source with field mappings
- `generate_and_store_table_config()` - Uses intelligent column selection

### 2. Data Table Generator
- `apply_intelligent_column_selection()` - Uses unified field matching
- `generate_table_config()` - Provides available fields to column manager
- `get_intelligent_column_label()` - Uses unified field labels

### 3. Column Manager
- Existing components automatically support available fields
- No changes needed to column manager UI
- Hidden fields appear in "Add Field" dropdowns

### 4. Data Table Storage
- `extract_available_fields_from_config()` - Extracts available fields
- `get_table_data()` - Includes available fields in response
- Configuration stores unified field metadata

## Troubleshooting

### Common Issues

**Too many columns visible initially:**
- Check field display settings: `show_by_default: false`
- Verify unified field matching is working
- Adjust `max_visible_columns` setting

**Important fields hidden:**
- Update field definition: `show_by_default: true`
- Check field patterns include your CSV headers
- Verify confidence thresholds

**Column manager not showing available fields:**
- Check `available_fields` is populated in table config
- Verify `extract_available_fields_from_config()` is working
- Check data table storage integration

### Debug Information

Enable logging to see the selection process:
```php
tcs_log("Unified field-based column selection for: " . $table_name, 'data_table_generator');
```

Check logs in `system/logs/data_table_generator_logfile.log` for detailed field matching and selection information.
