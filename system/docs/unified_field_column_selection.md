# Unified Field Column Selection System

The unified field import system now automatically selects only matched columns when switching to a different data source, with intelligent hiding of fields based on display preferences.

## Overview

When creating a column layout for the first time or switching data sources, the system now:

1. **Analyzes all available fields** using the unified field matching system
2. **Automatically selects only matched fields** that should be shown by default
3. **Hides fields** that are matched but marked as "hide by default"
4. **Leaves unmatched and hidden fields** in the available fields list for manual addition

## Field Display Settings

### New Field Definition Properties

Each field definition now supports display settings:

```json
{
  "field_name": "address",
  "label": "Address",
  "matching": {
    "enabled": true,
    "priority": 1
  },
  "display": {
    "show_by_default": true,     // Show this field when matched
    "category_priority": 1       // Priority within category for ordering
  }
}
```

### Display Setting Categories

**Core Business Fields** (`show_by_default: true`, `category_priority: 1`)
- `company_name` - Always show company information
- `product_name` - Always show product information  
- `email` - Always show contact information
- `address` - Always show address information
- `subscription_reference` - Always show subscription identifiers
- `start_date`, `end_date` - Always show date ranges
- `status` - Always show status information

**Secondary Business Fields** (`show_by_default: true`, `category_priority: 2`)
- `contact_name`, `reseller_name` - Show additional names
- `city`, `state`, `country`, `postal_code` - Show address components

**Technical/Detail Fields** (`show_by_default: false`, `category_priority: 3+`)
- `quantity`, `price` - Hide detailed transaction data by default
- `phone`, `website` - Hide secondary contact information
- `description`, `notes` - Hide verbose text fields
- `created_at`, `updated_at` - Hide system timestamps

## How It Works

### 1. Data Source Switching

When switching to a new data source in the column manager:

```php
// Get available fields from data source
$available_fields = array_keys($first_row);

// Use unified field matching
$column_structure = generate_unified_column_structure($available_fields, $data_source_id);

// Apply to configuration
$configuration['structure'] = $column_structure['visible_columns'];
$configuration['hidden'] = $column_structure['hidden_column_ids'];  
$configuration['available_fields'] = $column_structure['available_fields'];
```

### 2. Column Structure Generation

The `generate_unified_column_structure()` function:

1. **Matches fields** using `unified_field_mapper::suggest_field_mappings()`
2. **Checks display preferences** using `unified_field_definitions::should_show_by_default()`
3. **Creates visible columns** for fields that should be shown by default
4. **Creates available fields list** for hidden and unmatched fields
5. **Sorts columns** by unified field priority and confidence

### 3. Column Manager Integration

The existing column manager automatically receives:
- **Visible columns** - Initially displayed in the table
- **Available fields** - Hidden fields available for manual addition
- **Field metadata** - Unified field names, confidence scores, labels

## Example Scenarios

### Scenario 1: SketchUp Data Import

**Available Fields:**
```
end_customer_name, end_customer_address_1, end_customer_city, 
end_customer_state, product_name, subscription_quantity, 
agreement_start_date, agreement_end_date, quotation_id
```

**Automatic Selection:**
- ✅ **Visible:** `end_customer_name` → company_name, `end_customer_address_1` → address, `product_name` → product_name, `agreement_start_date` → start_date, `agreement_end_date` → end_date
- 📋 **Available:** `end_customer_city` → city, `end_customer_state` → state, `subscription_quantity` → quantity, `quotation_id` (unmatched)

### Scenario 2: Customer Data Import

**Available Fields:**
```
company, contact_person, email_address, phone_number, 
street_address, city, state, zip_code, created_date, notes
```

**Automatic Selection:**
- ✅ **Visible:** `company` → company_name, `email_address` → email, `street_address` → address, `city` → city, `state` → state
- 📋 **Available:** `contact_person` → contact_name, `phone_number` → phone, `zip_code` → postal_code, `created_date` → created_at, `notes` → notes

## Configuration

### Updating Display Settings

Use the provided script to update field display settings:

```bash
http://localhost/autobooks/update_field_display_settings.php
```

### Manual Field Configuration

Fields can be manually configured in the database:

```sql
UPDATE autobooks_unified_field_definitions 
SET field_definition = JSON_SET(
    field_definition, 
    '$.display.show_by_default', false,
    '$.display.category_priority', 3
)
WHERE field_name = 'quantity';
```

### Data Source Configuration

When creating data sources, the unified mappings can specify:

```json
{
  "min_confidence": 75,
  "enable_fallback": true,
  "auto_select_matched": true,        // Enable automatic column selection
  "show_confidence_scores": true,     // Show matching confidence in UI
  "overrides": {
    "custom_field": {
      "field_name": "specific_field",
      "show_by_default": true
    }
  }
}
```

## Benefits

1. **Improved User Experience**
   - Only relevant columns shown initially
   - Reduces cognitive load and clutter
   - Maintains access to all fields via column manager

2. **Intelligent Defaults**
   - Business-critical fields always visible
   - Technical details hidden but accessible
   - Consistent behavior across data sources

3. **Flexible Customization**
   - Users can easily add hidden fields
   - Display preferences configurable per field
   - Manual overrides supported

4. **Better Data Discovery**
   - Unified field names provide consistent labeling
   - Confidence scores help identify match quality
   - Available fields clearly organized

## Testing

### Test Scripts

1. **Update Display Settings:**
   ```
   http://localhost/autobooks/update_field_display_settings.php
   ```

2. **Test Column Selection:**
   ```
   http://localhost/autobooks/test_unified_column_selection.php
   ```

### Manual Testing

1. Create a new data table
2. Switch to a data source with many fields
3. Verify only "show by default" fields appear initially
4. Check column manager shows hidden fields in available list
5. Add hidden fields manually and verify they appear

### Expected Results

- **Initial view:** 5-8 core business columns visible
- **Column manager:** 10-20 additional fields available for selection
- **Field labels:** Unified field names instead of raw column names
- **Sorting:** Matched fields appear before unmatched fields

## Troubleshooting

### Common Issues

**Too many columns visible initially:**
- Check field display settings in database
- Verify `show_by_default` is set correctly
- Update field priorities if needed

**Important fields hidden:**
- Update field definition to `show_by_default: true`
- Check unified field matching is working
- Verify field patterns include your column names

**Available fields not showing:**
- Check `available_fields` is being passed to column manager
- Verify `extract_available_fields_from_config()` is working
- Check configuration structure is correct

### Debug Information

Enable logging to see column selection process:
```php
tcs_log("Column structure generated: " . json_encode($column_structure), 'data_table_saga');
```

Check logs in `system/logs/data_table_saga_logfile.log` for detailed information about field matching and column selection.
