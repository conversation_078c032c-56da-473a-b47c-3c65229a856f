# Unified Field Fallback System

The unified field matching system now supports **fallback matching** when the primary field choice conflicts with existing assignments. This ensures that columns get matched to appropriate fields even when their first choice is already taken by a higher-scoring column.

## How It Works

### Primary Matching
1. Each column is analyzed and matched to the best available field based on:
   - **Pattern matching** (exact matches get 100% confidence)
   - **Priority weighting** (lower priority number = higher priority)
   - **Contextual scoring** (based on other columns present)
   - **Final score calculation** combining all factors

### Conflict Resolution
2. When multiple columns want the same field:
   - The column with the **higher final score** gets the field
   - The losing column(s) trigger the **fallback system**

### Fallback Process
3. For columns that lose their primary choice:
   - System finds **alternative field matches** using the same scoring logic
   - Alternatives are sorted by their final scores
   - System tries each alternative in order until it finds an available field
   - Uses a **lower confidence threshold** for fallbacks (configurable)

## Configuration Options

### In Data Source Unified Mappings
```json
{
  "min_confidence": 75,                    // Primary matching threshold
  "enable_fallback": true,                 // Enable/disable fallback system
  "fallback_min_confidence": 60,           // Lower threshold for fallback matches
  "overrides": {                           // Manual field assignments (bypass fallback)
    "custom_column": {
      "field_name": "specific_field"
    }
  }
}
```

### Configuration Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `min_confidence` | 75 | Minimum confidence required for primary field matching |
| `enable_fallback` | true | Whether to enable fallback matching when conflicts occur |
| `fallback_min_confidence` | 60 | Lower confidence threshold for fallback alternatives |
| `overrides` | {} | Manual field assignments that bypass automatic matching |

## Example Scenarios

### Scenario 1: Company Name Conflicts
```
Columns: company_name, end_customer_name, business_name, customer_company

Primary matching:
- company_name → company_name field (score: 85)
- end_customer_name → company_name field (score: 77) ❌ CONFLICT
- business_name → company_name field (score: 70) ❌ CONFLICT  
- customer_company → company_name field (score: 65) ❌ CONFLICT

Fallback resolution:
- company_name → company_name field ✅ (highest score wins)
- end_customer_name → contact_name field ✅ (fallback alternative)
- business_name → reseller_name field ✅ (fallback alternative)
- customer_company → [no suitable fallback] ❌ (gets default alias)
```

### Scenario 2: Address Field Hierarchy
```
Columns: end_customer_address_1, shipping_address, billing_address

Primary matching:
- end_customer_address_1 → address field (score: 95, priority: 1)
- shipping_address → address field (score: 90, priority: 1) ❌ CONFLICT
- billing_address → address field (score: 85, priority: 1) ❌ CONFLICT

Fallback resolution:
- end_customer_address_1 → address field ✅ (highest score wins)
- shipping_address → [creates default alias] ❌ (no suitable address alternatives)
- billing_address → [creates default alias] ❌ (no suitable address alternatives)
```

## Logging and Debugging

The fallback system provides detailed logging for troubleshooting:

```
[data_source_manager] Alias conflict: end_customer_name -> company_name (score: 77) loses to company_name (score: 85)
[data_source_manager] Trying fallback for end_customer_name: found 3 alternatives
[data_source_manager] Fallback success: end_customer_name -> contact_name (score: 65, confidence: 70)
```

### Log Messages
- **Conflict detection**: Shows which columns lose their primary choice
- **Fallback attempts**: Lists alternatives being considered
- **Fallback success/failure**: Reports the outcome of fallback matching
- **Score comparisons**: Shows why certain alternatives are chosen or rejected

## Benefits

1. **Improved Column Coverage**: More columns get meaningful field assignments instead of default aliases
2. **Intelligent Conflict Resolution**: System automatically resolves field assignment conflicts
3. **Configurable Thresholds**: Fine-tune matching sensitivity for different use cases
4. **Comprehensive Logging**: Full visibility into the matching decision process
5. **Backward Compatibility**: Existing configurations continue to work without changes

## Testing

Use the test script to verify fallback functionality:
```
http://localhost/autobooks/test_fallback_matching.php
```

This will show:
- Primary field matches and their alternatives
- Conflict resolution in action
- Fallback assignments being made
- Detailed logging of the process

## Best Practices

1. **Set appropriate confidence thresholds**: 
   - Primary: 75+ for high-quality matches
   - Fallback: 60+ to allow reasonable alternatives

2. **Monitor logs**: Check for excessive conflicts that might indicate:
   - Missing field definitions
   - Incorrect field priorities
   - Need for manual overrides

3. **Use manual overrides**: For critical columns that must map to specific fields regardless of scoring

4. **Test with real data**: Import actual CSV files to see how the fallback system performs with your data patterns
