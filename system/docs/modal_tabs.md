# Modal Tab Functionality

This document explains how to use the new tab functionality in the system-wide modal component.

## Overview

The modal now supports multiple tabs with the following features:
- **Automatic tab creation** when modal is already open
- **Pin functionality** to keep tabs persistent
- **Tab switching** with Alpine.js
- **Server-side tab management** with HTMX integration
- **Automatic initialization** - no manual setup required!

## Tab Management Rules

1. **When modal is closed/not visible:** Opening content replaces the default tab (current behavior)
2. **When modal is already open:** Opening new content creates a new tab and switches to it
3. **Pin functionality:** Pinned tabs remain open and override rule 1 (always use rule 2 when any pinned tabs exist)

## Usage

### Automatic Initialization

**No code changes required!** The tab functionality is automatically enabled for all modal requests. The system:

- Detects HTMX requests targeting `#modal_body`
- Automatically generates appropriate tab titles
- Handles tab creation and switching logic
- Manages pinned tab behavior

### For Custom Tab Titles

Include the tab title in your `hx-vals` for custom tab names:

```php
'hx-vals' => json_encode([
    'your_data' => 'value',
    'tab_title' => 'Custom Tab Title'
])
```

Or use the `data-tab-title` attribute (legacy support):

```php
'data-tab-title' => 'Custom Tab Title'
```

## Examples

### Basic API Function (No Changes Needed!)
```php
function user_modal($p) {
    // Tab functionality is automatically enabled!
    // Your existing code works as-is...
    echo Edge::render('user-modal', ['user' => $user]);
}
```

### Button with Tab Title
```php
Edge::render('forms-button', [
    'hx-post' => APP_ROOT . '/api/customers/view',
    'hx-target' => '#modal_body',
    'hx-vals' => json_encode([
        'csn' => $customer_csn,
        'tab_title' => 'Customer ' . $customer_csn
    ]),
    '@click' => 'showModal = true'
]);
```

## Auto-Generated Titles

The system automatically generates tab titles based on common request parameters:

- `csn` → "Customer {csn}"
- `subscription_number` → "Subscription {number}"
- `quote_number` → "Quote {number}"
- `user_id` → "User Details"

## Technical Details

### Files Modified
- `system/components/edges/component-modal.edge.php` - Main modal component with tab UI
- `system/classes/modal_tabs.class.php` - Server-side tab management
- Various API files updated to use `modal_tabs::enable_tabs()`

### Alpine.js Data Structure
```javascript
{
    tabs: [
        {
            id: 'unique_tab_id',
            title: 'Tab Title',
            active: true,
            pinned: false
        }
    ],
    activeTab: 'unique_tab_id'
}
```

### HTMX Headers
The system uses custom HTMX triggers:
- `addModalTab` - Adds a new tab
- `switchModalTab` - Switches to existing tab

## Testing

Visit `/modal_test` to test the tab functionality with sample content.

## Migration

**Zero migration required!** Existing modal content will automatically gain tab functionality:

1. ✅ **No code changes needed** - tabs work automatically
2. ✅ **Backward compatible** - existing functionality unchanged
3. ✅ **Optional customization** - add `tab_title` in `hx-vals` for custom titles
4. ✅ **Test immediately** - visit `/modal_test` to see it in action
