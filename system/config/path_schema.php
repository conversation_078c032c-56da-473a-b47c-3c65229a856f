<?php
/**
 * Path schema configuration
 * Defines the structure of application paths
 */
return [
    // Base directories
    'base' => [
        'app_root' => '{fs_app_root}',
        'doc_root' => '{fs_doc_root}'
    ],

    // Resource directories
    'resources' => [
        'root' => 'resources',
        'api' => 'resources/api',
        'classes' => 'resources/classes',
        'functions' => 'resources/functions',
        'views' => 'resources/views',
        'config' => 'resources/config',
        'templates' => 'resources/templates',
        'components' => 'resources/components',
        'logs' => 'resources/logs'
    ],
    // System directories
    'system' => [
        'root' => 'system',
        'api' => 'system/api',
        'classes' => 'system/classes',
        'functions' => 'system/functions',
        'views' => 'system/views',
        'config' => 'system/config',
        'templates' => 'system/templates',
        'components' => 'system/components',
        'logs' => 'system/logs'
    ],

    'system_views' => ['system', 'login', 'logout','reset-password', 'settings', 'database_dump'],
    'other' => [
        'uploads' => 'uploads'
    ],
    // External paths
    'external' => [
        'cache' => '/var/www/vhosts/cadservices.co.uk/temp/autobooks',
        'temp' => '/var/www/vhosts/cadservices.co.uk/temp/autobooks',

    ],
    'local' => [
        'external' => [
            'cache' => 'e:\build\temp',
            'temp' => 'e:\build\temp',
        ]
    ]
];

