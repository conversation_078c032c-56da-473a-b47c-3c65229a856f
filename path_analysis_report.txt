PATH USAGE ANALYSIS REPORT
Generated: 2025-07-04 01:52:21
Total matches found: 101
Files with matches: 35

================================================================================
DETAILED RESULTS
================================================================================

FILE: system\classes\router.class.php
----------------------------------------
Pattern: fs_constant_concat
Line 235: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.view.php",
Match: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.view.php"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: fs_constant_concat
Line 236: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php",
Match: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php"
Suggested fix: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php"

Pattern: fs_constant_concat
Line 237: FS_SYS_VIEWS . "/{$system_view}.view.php",
Match: FS_SYS_VIEWS . "/{$system_view}.view.php"
Suggested fix: FS_SYS_VIEWS . "/{$system_view}.view.php"

Pattern: fs_constant_concat
Line 238: FS_SYS_VIEWS . "/{$system_view}.edge.php"
Match: FS_SYS_VIEWS . "/{$system_view}.edge.php"
Suggested fix: FS_SYS_VIEWS . "/{$system_view}.edge.php"


FILE: system\components\edges\email-send-rules-widget.edge.php
----------------------------------------
Pattern: template_app_root
Line 30: hx-post="{{ APP_ROOT }}/api/system/email_history"
Match: {{ APP_ROOT }}/api/system/email_history"
                hx-target="#rules_buttons"
                hx-vals='
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 51: hx-post="{{ APP_ROOT }}/api/system/email_history"
Match: {{ APP_ROOT }}/api/system/email_history"
        hx-include="#new_rule"
        hx-target="#rules_buttons"
        hx-swap="outerHTML"
        hx-vals='
Suggested fix: {{ APP_ROOT }}/api/system/email_history"
        hx-include="#new_rule"
        hx-target="#rules_buttons"
        hx-swap="outerHTML"
        hx-vals='


FILE: system\components\edges\hilt-settings.edge.php
----------------------------------------
Pattern: template_app_root
Line 46: <form hx-post="{{ APP_ROOT }}/api/hilt_settings/upload_csv"
Match: {{ APP_ROOT }}/api/hilt_settings/upload_csv" 
                      hx-target="#table-summary"
                      hx-encoding="multipart/form-data"
                      class="space-y-4">
                    
                    <input type="hidden" name="route_key" value="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 93: hx-post="{{ APP_ROOT }}/api/hilt_settings/clear_data"
Match: {{ APP_ROOT }}/api/hilt_settings/clear_data"
                                    hx-target="#table-summary"
                                    hx-vals='
Suggested fix: {{ APP_ROOT }}/api/hilt_settings/clear_data"
                                    hx-target="#table-summary"
                                    hx-vals='

Pattern: template_app_root
Line 111: <a href="{{ APP_ROOT }}api/hilt_settings/export_csv?route_key={{ $route_key }}"
Match: {{ APP_ROOT }}api/hilt_settings/export_csv?route_key=
Suggested fix: {{ APP_ROOT }}api/hilt_settings/export_csv?route_key=

Pattern: template_app_root
Line 188: <a href="{{ APP_ROOT }}/navigation"
Match: {{ APP_ROOT }}/navigation" 
                           class="inline-block text-blue-600 hover:text-blue-800 text-sm">
                            → Back to Navigation
                        </a>
                    </div>
                </div>
            </div>

            
Suggested fix: {{ APP_ROOT }}/navigation" 
                           class="inline-block text-blue-600 hover:text-blue-800 text-sm">
                            → Back to Navigation
                        </a>
                    </div>
                </div>
            </div>

            


FILE: system\components\edges\layout-head.edge.php
----------------------------------------
Pattern: template_app_root
Line 6: <link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
Match: {{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<script>
    // Define APP_ROOT for JavaScript files
    var APP_ROOT = '<?= APP_ROOT ?>';

    // Navigation tree initialization function
    function initNavTree() 
Suggested fix: {{ Array }}/Array


FILE: system\components\edges\nav-entry-form.bak.php
----------------------------------------
Pattern: template_app_root
Line 94: <link rel="stylesheet" href="{{ APP_ROOT }}/resources/css/htmx-indicators.css">
Match: {{ APP_ROOT }}/resources/css/htmx-indicators.css">

<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>

    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->
    <div id="nav-entry-progress-container" style="display: none;">
        <!-- Progress bar will be loaded here -->
    </div>

    <form
            hx-post="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 105: hx-post="{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
            hx-target="#nav-entry-progress-container"
            hx-swap="innerHTML"
            hx-on::before-request="document.getElementById('nav-entry-progress-container').style.display = 'block'; this.style.display = 'none';"
            @submit="$dispatch('hide-modal')"
            class="space-y-4"
            x-data="
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
            hx-target="#nav-entry-progress-container"
            hx-swap="innerHTML"
            hx-on::before-request="document.getElementById('nav-entry-progress-container').style.display = 'block'; this.style.display = 'none';"
            @submit="$dispatch('hide-modal')"
            class="space-y-4"
            x-data="


FILE: system\components\edges\nav-entry-form.edge.php
----------------------------------------
Pattern: template_app_root
Line 86: <link rel="stylesheet" href="{{ APP_ROOT }}/resources/css/htmx-indicators.css">
Match: {{ APP_ROOT }}/resources/css/htmx-indicators.css">

<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>

    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->
    <div id="nav-entry-progress-container" style="display: none;">
        <!-- Progress bar will be loaded here -->
    </div>

    <form
        hx-post="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 97: hx-post="{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
        hx-target="#nav-entry-progress-container"
        hx-swap="innerHTML"
        hx-trigger="submit"
        hx-on::before-request="if(event.detail.elt.tagName === 'FORM') 
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
        hx-target="#nav-entry-progress-container"
        hx-swap="innerHTML"
        hx-trigger="submit"
        hx-on::before-request="if(event.detail.elt.tagName === 'FORM') 

Pattern: template_app_root
Line 113: hx-post="{{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
Match: {{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
                    hx-target="#template-specific-fields"
                    hx-swap="innerHTML"
                    hx-trigger="change"
                    hx-include="this"
                    onchange="document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $template)
                    @php
                        $isDefault = $key === 'default_template';
                    @endphp
                    <option value="
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
                    hx-target="#template-specific-fields"
                    hx-swap="innerHTML"
                    hx-trigger="change"
                    hx-include="this"
                    onchange="document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $template)
                    @php
                        $isDefault = $key === 'default_template';
                    @endphp
                    <option value="


FILE: system\components\edges\nav-tree.edge.php
----------------------------------------
Pattern: template_app_root
Line 44: hx-delete="{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry"
                    hx-vals='
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 65: hx-post="{{ APP_ROOT }}/api/system/nav_tree/add_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/add_nav_entry"
                hx-vals='
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/add_nav_entry"
                hx-vals='


FILE: system\components\edges\navbar.edge.php
----------------------------------------
Pattern: template_app_root
Line 42: hx-post="{{ APP_ROOT }}/api/system/toggle_debug_mode"
Match: {{ APP_ROOT }}/api/system/toggle_debug_mode"
                               hx-trigger="change"
                               hx-swap="none"
                               hx-indicator="#debug-mode-indicator"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        <span class="ms-3 text-sm font-medium text-gray-900">Debug Mode</span>
                        <!-- Small loading indicator -->
                        <span id="debug-mode-indicator" class="htmx-indicator ml-2">
                            <svg class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                 fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </span>
                    </label>
                </div>
            @endif

            <div>

                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )
                <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
                    hx-target="#content_wrapper"
                    hx-post="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 68: hx-post="{{ APP_ROOT }}/settings"
Match: {{ APP_ROOT }}/settings"
                    hx-replace-url="
Suggested fix: {{ APP_ROOT }}/settings"
                    hx-replace-url="

Pattern: template_app_root
Line 107: <a href="{{ APP_ROOT }}/profile"
Match: {{ APP_ROOT }}/profile"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="
Suggested fix: {{ APP_ROOT }}/profile"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="

Pattern: template_app_root
Line 111: hx-get="{{ APP_ROOT }}/profile"
Match: {{ APP_ROOT }}/profile"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Your Profile (
Suggested fix: {{ APP_ROOT }}/profile"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Your Profile (

Pattern: template_app_root
Line 119: <a href="{{ APP_ROOT }}/users"
Match: {{ APP_ROOT }}/users"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           role="menuitem"
                           tabindex="-1"
                           hx-get="
Suggested fix: {{ APP_ROOT }}/users"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           role="menuitem"
                           tabindex="-1"
                           hx-get="

Pattern: template_app_root
Line 123: hx-get="{{ APP_ROOT }}/users"
Match: {{ APP_ROOT }}/users"
                           hx-target="#content_wrapper"
                           hx-replace-url="
Suggested fix: {{ APP_ROOT }}/users"
                           hx-target="#content_wrapper"
                           hx-replace-url="

Pattern: template_app_root
Line 125: hx-replace-url="{{ APP_ROOT }}/users"
Match: {{ APP_ROOT }}/users"
                           hx-push-url="true">
                            User Management
                        </a>
                    @endif

                    <div class="border-t border-gray-200 my-1"></div>
                    <a href="
Suggested fix: {{ APP_ROOT }}/users"
                           hx-push-url="true">
                            User Management
                        </a>
                    @endif

                    <div class="border-t border-gray-200 my-1"></div>
                    <a href="

Pattern: template_app_root
Line 132: <a href="{{ APP_ROOT }}/settings"
Match: {{ APP_ROOT }}/settings"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="
Suggested fix: {{ APP_ROOT }}/settings"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="

Pattern: template_app_root
Line 136: hx-get="{{ APP_ROOT }}/settings"
Match: {{ APP_ROOT }}/settings"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Settings
                    </a>
                    <a href="
Suggested fix: {{ APP_ROOT }}/settings"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Settings
                    </a>
                    <a href="

Pattern: template_app_root
Line 141: <a href="{{ APP_ROOT }}/logout"
Match: {{ APP_ROOT }}/logout"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-post="
Suggested fix: {{ APP_ROOT }}/logout"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-post="

Pattern: template_app_root
Line 145: hx-post="{{ APP_ROOT }}/logout"
Match: {{ APP_ROOT }}/logout"
                       hx-target="body"
                       hx-push-url="true"
                       hx-redirect="
Suggested fix: {{ APP_ROOT }}/logout"
                       hx-target="body"
                       hx-push-url="true"
                       hx-redirect="

Pattern: template_app_root
Line 148: hx-redirect="{{ APP_ROOT }}/login">
Match: {{ APP_ROOT }}/login">
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
Suggested fix: {{ APP_ROOT }}/login">
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>


FILE: system\components\edges\notification-dropdown.edge.php
----------------------------------------
Pattern: template_app_root
Line 23: hx-get="{{ APP_ROOT }}/api/system/notifications/get_unread_count"
Match: {{ APP_ROOT }}/api/system/notifications/get_unread_count"
             hx-trigger="load, every 30s, notification-updated from:body"
             hx-swap="outerHTML">
            <!-- Badge will be loaded here -->
        </div>
    </button>

    <!-- Notification dropdown -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1">

        <!-- Notification header -->
        <div class="px-4 py-2 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                <a href="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 47: <a href="{{ APP_ROOT }}/notifications"
Match: {{ APP_ROOT }}/notifications"
                   class="text-xs text-blue-600 hover:text-blue-800"
                   hx-get="
Suggested fix: {{ APP_ROOT }}/notifications"
                   class="text-xs text-blue-600 hover:text-blue-800"
                   hx-get="

Pattern: template_app_root
Line 49: hx-get="{{ APP_ROOT }}/notifications"
Match: {{ APP_ROOT }}/notifications"
                   hx-target="#content_wrapper"
                   hx-push-url="true">
                    View all
                </a>
            </div>
        </div>

        <!-- Notification list -->
        <div id="notification-list-container"
             hx-get="
Suggested fix: {{ APP_ROOT }}/notifications"
                   hx-target="#content_wrapper"
                   hx-push-url="true">
                    View all
                </a>
            </div>
        </div>

        <!-- Notification list -->
        <div id="notification-list-container"
             hx-get="

Pattern: template_app_root
Line 59: hx-get="{{ APP_ROOT }}/api/system/notifications/get_notifications"
Match: {{ APP_ROOT }}/api/system/notifications/get_notifications"
             hx-trigger="load, notification-updated from:body"
             hx-swap="innerHTML">
            <!-- Notification list will be loaded here -->
        </div>
    </div>
</div>

<!-- SSE connection for real-time notifications -->
<div hx-ext="sse"
     sse-connect="
Suggested fix: {{ APP_ROOT }}/api/system/notifications/get_notifications"
             hx-trigger="load, notification-updated from:body"
             hx-swap="innerHTML">
            <!-- Notification list will be loaded here -->
        </div>
    </div>
</div>

<!-- SSE connection for real-time notifications -->
<div hx-ext="sse"
     sse-connect="

Pattern: template_app_root
Line 69: sse-connect="{{ APP_ROOT }}/api/system/notifications/sse"
Match: {{ APP_ROOT }}/api/system/notifications/sse"
     sse-swap="none"
     class="hidden">
    <div sse-swap="count"
         hx-trigger="sse:count"
         @sse-message="
            const data = JSON.parse(event.detail.data);
            if (data.count > 0) 
Suggested fix: {{ APP_ROOT }}/api/system/notifications/sse"
     sse-swap="none"
     class="hidden">
    <div sse-swap="count"
         hx-trigger="sse:count"
         @sse-message="
            const data = JSON.parse(event.detail.data);
            if (data.count > 0) 


FILE: system\components\edges\template-container.edge.php
----------------------------------------
Pattern: fs_constant_concat
Line 11: $template_exists = file_exists(FS_APP_ROOT . "resources/components/templates/{$template}.edge.php");
Match: FS_APP_ROOT . "resources/components/templates/{$template}.edge.php"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 11: $template_exists = file_exists(FS_APP_ROOT . "resources/components/templates/{$template}.edge.php");
Match: APP_ROOT . "resources/components/templates/{$template}.edge.php"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'


FILE: system\functions\components.fn.php
----------------------------------------
Pattern: fs_constant_concat
Line 72: //echo "<br>cached version doesn't exist, compiling... from '" . FS_APP_ROOT . "resources/components/{$component_name}.blade.php'<br>\n";
Match: FS_APP_ROOT . "resources/components/{$component_name}.blade.php'
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 72: //echo "<br>cached version doesn't exist, compiling... from '" . FS_APP_ROOT . "resources/components/{$component_name}.blade.php'<br>\n";
Match: APP_ROOT . "resources/components/{$component_name}.blade.php'
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'


FILE: system\templates\default_template.hilt.php
----------------------------------------
Pattern: template_app_root
Line 109: <a href="{{ APP_ROOT }}/navigation"
Match: {{ APP_ROOT }}/navigation" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Manage Navigation
                        </a>
                        <a href="
Suggested fix: {{ Array }}/Array


FILE: system\templates\file_upload_template.hilt.php
----------------------------------------
Pattern: template_app_root
Line 31: <a href="{{ APP_ROOT }}/navigation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
Match: {{ APP_ROOT }}/navigation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Manage Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="template-info mt-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-800 mb-2">File Upload Template Information</h3>
            <div class="text-sm text-blue-700">
                <p><strong>Route:</strong> 
Suggested fix: {{ Array }}/Array


FILE: resources\components\edges\subscription-table-config.edge.php
----------------------------------------
Pattern: template_app_root
Line 9: <form hx-post="{{ APP_ROOT }}/api/save_subscription_config" hx-target="#config_status" class="space-y-4">
Match: {{ APP_ROOT }}/api/save_subscription_config" hx-target="#config_status" class="space-y-4">
        <!-- Replacements Section -->
        <div x-data="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 30: hx-post="{{ APP_ROOT }}/api/add_replacement_row"
Match: {{ APP_ROOT }}/api/add_replacement_row"
                        hx-target="#replacements_container"
                        hx-swap="beforeend">
                    Add Replacement
                </button>
            </div>
        </div>

        <!-- Columns Configuration -->
        <div x-data="
Suggested fix: {{ APP_ROOT }}/api/add_replacement_row"
                        hx-target="#replacements_container"
                        hx-swap="beforeend">
                    Add Replacement
                </button>
            </div>
        </div>

        <!-- Columns Configuration -->
        <div x-data="

Pattern: template_app_root
Line 63: hx-post="{{ APP_ROOT }}/api/add_column_row"
Match: {{ APP_ROOT }}/api/add_column_row"
                        hx-target="#columns_container"
                        hx-swap="beforeend"
                        hx-vals='
Suggested fix: {{ APP_ROOT }}/api/add_column_row"
                        hx-target="#columns_container"
                        hx-swap="beforeend"
                        hx-vals='


FILE: resources\components\edges\subscriptions-email-settings.edge.php
----------------------------------------
Pattern: template_app_root
Line 85: hx-post="{{ APP_ROOT }}/api/update_days_to_send"
Match: {{ APP_ROOT }}/api/update_days_to_send" 
                 hx-include="#email_send_days" 
                 hx-trigger="click delay:2000ms" 
                 hx-swap="none">
                <input type="hidden" x-model="selectedDays" id="email_send_days" name="days_to_send" value="">
                @php
                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
                @endphp
                @foreach($days as $key => $day)
                    <button type="button"
                            name="day_
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 117: hx-post="{{ APP_ROOT }}/api/update_time_to_send">
Match: {{ APP_ROOT }}/api/update_time_to_send">
                    <span class="ml-2 text-lg font-bold text-neutral-900 text-black" x-text="currentVal < 10 ? '0' + currentVal : currentVal">00</span>
                    <span class="text-lg font-bold text-neutral-900 text-black">:00</span>
                </div>
            </div>
        </div>
        @php
            $input = json_encode([
                    'id' => 'test_email_to',
                    'name' => 'test_email_to',
                    'icon' => 'envelope'
            ]);
            $button = json_encode([
                    'id' => 'test_email_send',
                    'name' => 'test_email_send',
                    'label' => 'Send',
                    'icon' => 'envelope',
                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/send_test_email',
                    'hx-swap' => 'this',
                    'hx-target' => 'this',
                    'hx-include' => '#test_email_to'
            ])
        @endphp
        <div class="col-span-4">
            <x-forms-input-button-group
                label="Send test email"
                :input="$input"
                :button="$button"
            />
        </div>
    </div>
    <script>
        Jodit.defaultOptions.controls.insertPlaceholder = 
Suggested fix: {{ APP_ROOT }}/api/update_time_to_send">
                    <span class="ml-2 text-lg font-bold text-neutral-900 text-black" x-text="currentVal < 10 ? '0' + currentVal : currentVal">00</span>
                    <span class="text-lg font-bold text-neutral-900 text-black">:00</span>
                </div>
            </div>
        </div>
        @php
            $input = json_encode([
                    'id' => 'test_email_to',
                    'name' => 'test_email_to',
                    'icon' => 'envelope'
            ]);
            $button = json_encode([
                    'id' => 'test_email_send',
                    'name' => 'test_email_send',
                    'label' => 'Send',
                    'icon' => 'envelope',
                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/send_test_email',
                    'hx-swap' => 'this',
                    'hx-target' => 'this',
                    'hx-include' => '#test_email_to'
            ])
        @endphp
        <div class="col-span-4">
            <x-forms-input-button-group
                label="Send test email"
                :input="$input"
                :button="$button"
            />
        </div>
    </div>
    <script>
        Jodit.defaultOptions.controls.insertPlaceholder = 


FILE: resources\views\subscriptions\email_history\settings.view.php
----------------------------------------
Pattern: app_root_concat
Line 47: $footer .= "<div><label for='days_to_send' class='block text-sm/6 font-medium text-gray-900'>Days To Send: </label></div><div class='isolate inline-flex rounded-md shadow-sm' hx-post='" . APP_ROOT . "/api/update_days_to_send' hx-include='#email_send_days' hx-trigger='click delay:2000ms' hx-swap='none'>
Match: APP_ROOT . "/api/update_days_to_send'
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 70: <DIV class='flex items-center'><input class='block text-sm/6 font-medium text-gray-900' x-model='currentVal' id='hour_of_day' name='time_to_send' type='range' class='' min='0' max='23' step='1' hx-trigger='change delay 1000ms' hx-post='" . APP_ROOT . "api/update_time_to_send'>
Match: APP_ROOT . "api/update_time_to_send'
Suggested fix: APP_ROOT . "api/update_time_to_send'


FILE: .\index.php
----------------------------------------
Pattern: fs_app_root_interpolation
Line 11: $path['fs_system'] = "{$path['fs_app_root']}{$schema['system']['root']}" ;
Match: {$path['fs_app_root']}{$schema['system']['root']
Suggested fix: Manual review required


FILE: .\resources\components\edges\subscription-table-config.edge.php
----------------------------------------
Pattern: template_app_root
Line 9: <form hx-post="{{ APP_ROOT }}/api/save_subscription_config" hx-target="#config_status" class="space-y-4">
Match: {{ APP_ROOT }}/api/save_subscription_config" hx-target="#config_status" class="space-y-4">
        <!-- Replacements Section -->
        <div x-data="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 30: hx-post="{{ APP_ROOT }}/api/add_replacement_row"
Match: {{ APP_ROOT }}/api/add_replacement_row"
                        hx-target="#replacements_container"
                        hx-swap="beforeend">
                    Add Replacement
                </button>
            </div>
        </div>

        <!-- Columns Configuration -->
        <div x-data="
Suggested fix: {{ APP_ROOT }}/api/add_replacement_row"
                        hx-target="#replacements_container"
                        hx-swap="beforeend">
                    Add Replacement
                </button>
            </div>
        </div>

        <!-- Columns Configuration -->
        <div x-data="

Pattern: template_app_root
Line 63: hx-post="{{ APP_ROOT }}/api/add_column_row"
Match: {{ APP_ROOT }}/api/add_column_row"
                        hx-target="#columns_container"
                        hx-swap="beforeend"
                        hx-vals='
Suggested fix: {{ APP_ROOT }}/api/add_column_row"
                        hx-target="#columns_container"
                        hx-swap="beforeend"
                        hx-vals='


FILE: .\resources\components\edges\subscriptions-email-settings.edge.php
----------------------------------------
Pattern: template_app_root
Line 85: hx-post="{{ APP_ROOT }}/api/update_days_to_send"
Match: {{ APP_ROOT }}/api/update_days_to_send" 
                 hx-include="#email_send_days" 
                 hx-trigger="click delay:2000ms" 
                 hx-swap="none">
                <input type="hidden" x-model="selectedDays" id="email_send_days" name="days_to_send" value="">
                @php
                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
                @endphp
                @foreach($days as $key => $day)
                    <button type="button"
                            name="day_
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 117: hx-post="{{ APP_ROOT }}/api/update_time_to_send">
Match: {{ APP_ROOT }}/api/update_time_to_send">
                    <span class="ml-2 text-lg font-bold text-neutral-900 text-black" x-text="currentVal < 10 ? '0' + currentVal : currentVal">00</span>
                    <span class="text-lg font-bold text-neutral-900 text-black">:00</span>
                </div>
            </div>
        </div>
        @php
            $input = json_encode([
                    'id' => 'test_email_to',
                    'name' => 'test_email_to',
                    'icon' => 'envelope'
            ]);
            $button = json_encode([
                    'id' => 'test_email_send',
                    'name' => 'test_email_send',
                    'label' => 'Send',
                    'icon' => 'envelope',
                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/send_test_email',
                    'hx-swap' => 'this',
                    'hx-target' => 'this',
                    'hx-include' => '#test_email_to'
            ])
        @endphp
        <div class="col-span-4">
            <x-forms-input-button-group
                label="Send test email"
                :input="$input"
                :button="$button"
            />
        </div>
    </div>
    <script>
        Jodit.defaultOptions.controls.insertPlaceholder = 
Suggested fix: {{ APP_ROOT }}/api/update_time_to_send">
                    <span class="ml-2 text-lg font-bold text-neutral-900 text-black" x-text="currentVal < 10 ? '0' + currentVal : currentVal">00</span>
                    <span class="text-lg font-bold text-neutral-900 text-black">:00</span>
                </div>
            </div>
        </div>
        @php
            $input = json_encode([
                    'id' => 'test_email_to',
                    'name' => 'test_email_to',
                    'icon' => 'envelope'
            ]);
            $button = json_encode([
                    'id' => 'test_email_send',
                    'name' => 'test_email_send',
                    'label' => 'Send',
                    'icon' => 'envelope',
                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/send_test_email',
                    'hx-swap' => 'this',
                    'hx-target' => 'this',
                    'hx-include' => '#test_email_to'
            ])
        @endphp
        <div class="col-span-4">
            <x-forms-input-button-group
                label="Send test email"
                :input="$input"
                :button="$button"
            />
        </div>
    </div>
    <script>
        Jodit.defaultOptions.controls.insertPlaceholder = 


FILE: .\resources\views\subscriptions\email_history\settings.view.php
----------------------------------------
Pattern: app_root_concat
Line 47: $footer .= "<div><label for='days_to_send' class='block text-sm/6 font-medium text-gray-900'>Days To Send: </label></div><div class='isolate inline-flex rounded-md shadow-sm' hx-post='" . APP_ROOT . "/api/update_days_to_send' hx-include='#email_send_days' hx-trigger='click delay:2000ms' hx-swap='none'>
Match: APP_ROOT . "/api/update_days_to_send'
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 70: <DIV class='flex items-center'><input class='block text-sm/6 font-medium text-gray-900' x-model='currentVal' id='hour_of_day' name='time_to_send' type='range' class='' min='0' max='23' step='1' hx-trigger='change delay 1000ms' hx-post='" . APP_ROOT . "api/update_time_to_send'>
Match: APP_ROOT . "api/update_time_to_send'
Suggested fix: APP_ROOT . "api/update_time_to_send'


FILE: .\system\classes\router.class.php
----------------------------------------
Pattern: fs_constant_concat
Line 235: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.view.php",
Match: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.view.php"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: fs_constant_concat
Line 236: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php",
Match: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php"
Suggested fix: FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php"

Pattern: fs_constant_concat
Line 237: FS_SYS_VIEWS . "/{$system_view}.view.php",
Match: FS_SYS_VIEWS . "/{$system_view}.view.php"
Suggested fix: FS_SYS_VIEWS . "/{$system_view}.view.php"

Pattern: fs_constant_concat
Line 238: FS_SYS_VIEWS . "/{$system_view}.edge.php"
Match: FS_SYS_VIEWS . "/{$system_view}.edge.php"
Suggested fix: FS_SYS_VIEWS . "/{$system_view}.edge.php"


FILE: .\system\components\edges\email-send-rules-widget.edge.php
----------------------------------------
Pattern: template_app_root
Line 30: hx-post="{{ APP_ROOT }}/api/system/email_history"
Match: {{ APP_ROOT }}/api/system/email_history"
                hx-target="#rules_buttons"
                hx-vals='
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 51: hx-post="{{ APP_ROOT }}/api/system/email_history"
Match: {{ APP_ROOT }}/api/system/email_history"
        hx-include="#new_rule"
        hx-target="#rules_buttons"
        hx-swap="outerHTML"
        hx-vals='
Suggested fix: {{ APP_ROOT }}/api/system/email_history"
        hx-include="#new_rule"
        hx-target="#rules_buttons"
        hx-swap="outerHTML"
        hx-vals='


FILE: .\system\components\edges\hilt-settings.edge.php
----------------------------------------
Pattern: template_app_root
Line 46: <form hx-post="{{ APP_ROOT }}/api/hilt_settings/upload_csv"
Match: {{ APP_ROOT }}/api/hilt_settings/upload_csv" 
                      hx-target="#table-summary"
                      hx-encoding="multipart/form-data"
                      class="space-y-4">
                    
                    <input type="hidden" name="route_key" value="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 93: hx-post="{{ APP_ROOT }}/api/hilt_settings/clear_data"
Match: {{ APP_ROOT }}/api/hilt_settings/clear_data"
                                    hx-target="#table-summary"
                                    hx-vals='
Suggested fix: {{ APP_ROOT }}/api/hilt_settings/clear_data"
                                    hx-target="#table-summary"
                                    hx-vals='

Pattern: template_app_root
Line 111: <a href="{{ APP_ROOT }}api/hilt_settings/export_csv?route_key={{ $route_key }}"
Match: {{ APP_ROOT }}api/hilt_settings/export_csv?route_key=
Suggested fix: {{ APP_ROOT }}api/hilt_settings/export_csv?route_key=

Pattern: template_app_root
Line 188: <a href="{{ APP_ROOT }}/navigation"
Match: {{ APP_ROOT }}/navigation" 
                           class="inline-block text-blue-600 hover:text-blue-800 text-sm">
                            → Back to Navigation
                        </a>
                    </div>
                </div>
            </div>

            
Suggested fix: {{ APP_ROOT }}/navigation" 
                           class="inline-block text-blue-600 hover:text-blue-800 text-sm">
                            → Back to Navigation
                        </a>
                    </div>
                </div>
            </div>

            


FILE: .\system\components\edges\layout-head.edge.php
----------------------------------------
Pattern: template_app_root
Line 6: <link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
Match: {{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<script>
    // Define APP_ROOT for JavaScript files
    var APP_ROOT = '<?= APP_ROOT ?>';

    // Navigation tree initialization function
    function initNavTree() 
Suggested fix: {{ Array }}/Array


FILE: .\system\components\edges\nav-entry-form.bak.php
----------------------------------------
Pattern: template_app_root
Line 94: <link rel="stylesheet" href="{{ APP_ROOT }}/resources/css/htmx-indicators.css">
Match: {{ APP_ROOT }}/resources/css/htmx-indicators.css">

<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>

    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->
    <div id="nav-entry-progress-container" style="display: none;">
        <!-- Progress bar will be loaded here -->
    </div>

    <form
            hx-post="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 105: hx-post="{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
            hx-target="#nav-entry-progress-container"
            hx-swap="innerHTML"
            hx-on::before-request="document.getElementById('nav-entry-progress-container').style.display = 'block'; this.style.display = 'none';"
            @submit="$dispatch('hide-modal')"
            class="space-y-4"
            x-data="
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
            hx-target="#nav-entry-progress-container"
            hx-swap="innerHTML"
            hx-on::before-request="document.getElementById('nav-entry-progress-container').style.display = 'block'; this.style.display = 'none';"
            @submit="$dispatch('hide-modal')"
            class="space-y-4"
            x-data="


FILE: .\system\components\edges\nav-entry-form.edge.php
----------------------------------------
Pattern: template_app_root
Line 86: <link rel="stylesheet" href="{{ APP_ROOT }}/resources/css/htmx-indicators.css">
Match: {{ APP_ROOT }}/resources/css/htmx-indicators.css">

<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>

    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->
    <div id="nav-entry-progress-container" style="display: none;">
        <!-- Progress bar will be loaded here -->
    </div>

    <form
        hx-post="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 97: hx-post="{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
        hx-target="#nav-entry-progress-container"
        hx-swap="innerHTML"
        hx-trigger="submit"
        hx-on::before-request="if(event.detail.elt.tagName === 'FORM') 
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
        hx-target="#nav-entry-progress-container"
        hx-swap="innerHTML"
        hx-trigger="submit"
        hx-on::before-request="if(event.detail.elt.tagName === 'FORM') 

Pattern: template_app_root
Line 113: hx-post="{{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
Match: {{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
                    hx-target="#template-specific-fields"
                    hx-swap="innerHTML"
                    hx-trigger="change"
                    hx-include="this"
                    onchange="document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $template)
                    @php
                        $isDefault = $key === 'default_template';
                    @endphp
                    <option value="
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
                    hx-target="#template-specific-fields"
                    hx-swap="innerHTML"
                    hx-trigger="change"
                    hx-include="this"
                    onchange="document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $template)
                    @php
                        $isDefault = $key === 'default_template';
                    @endphp
                    <option value="


FILE: .\system\components\edges\nav-tree.edge.php
----------------------------------------
Pattern: template_app_root
Line 44: hx-delete="{{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/delete_nav_entry"
                    hx-vals='
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 65: hx-post="{{ APP_ROOT }}/api/system/nav_tree/add_nav_entry"
Match: {{ APP_ROOT }}/api/system/nav_tree/add_nav_entry"
                hx-vals='
Suggested fix: {{ APP_ROOT }}/api/system/nav_tree/add_nav_entry"
                hx-vals='


FILE: .\system\components\edges\navbar.edge.php
----------------------------------------
Pattern: template_app_root
Line 42: hx-post="{{ APP_ROOT }}/api/system/toggle_debug_mode"
Match: {{ APP_ROOT }}/api/system/toggle_debug_mode"
                               hx-trigger="change"
                               hx-swap="none"
                               hx-indicator="#debug-mode-indicator"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        <span class="ms-3 text-sm font-medium text-gray-900">Debug Mode</span>
                        <!-- Small loading indicator -->
                        <span id="debug-mode-indicator" class="htmx-indicator ml-2">
                            <svg class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                 fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </span>
                    </label>
                </div>
            @endif

            <div>

                @if (  file_exists(FS_APP_PATH . DIRECTORY_SEPARATOR . '/' . CURRENT_PAGE . '.settings.php')  )
                <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
                    hx-target="#content_wrapper"
                    hx-post="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 68: hx-post="{{ APP_ROOT }}/settings"
Match: {{ APP_ROOT }}/settings"
                    hx-replace-url="
Suggested fix: {{ APP_ROOT }}/settings"
                    hx-replace-url="

Pattern: template_app_root
Line 107: <a href="{{ APP_ROOT }}/profile"
Match: {{ APP_ROOT }}/profile"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="
Suggested fix: {{ APP_ROOT }}/profile"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="

Pattern: template_app_root
Line 111: hx-get="{{ APP_ROOT }}/profile"
Match: {{ APP_ROOT }}/profile"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Your Profile (
Suggested fix: {{ APP_ROOT }}/profile"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Your Profile (

Pattern: template_app_root
Line 119: <a href="{{ APP_ROOT }}/users"
Match: {{ APP_ROOT }}/users"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           role="menuitem"
                           tabindex="-1"
                           hx-get="
Suggested fix: {{ APP_ROOT }}/users"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           role="menuitem"
                           tabindex="-1"
                           hx-get="

Pattern: template_app_root
Line 123: hx-get="{{ APP_ROOT }}/users"
Match: {{ APP_ROOT }}/users"
                           hx-target="#content_wrapper"
                           hx-replace-url="
Suggested fix: {{ APP_ROOT }}/users"
                           hx-target="#content_wrapper"
                           hx-replace-url="

Pattern: template_app_root
Line 125: hx-replace-url="{{ APP_ROOT }}/users"
Match: {{ APP_ROOT }}/users"
                           hx-push-url="true">
                            User Management
                        </a>
                    @endif

                    <div class="border-t border-gray-200 my-1"></div>
                    <a href="
Suggested fix: {{ APP_ROOT }}/users"
                           hx-push-url="true">
                            User Management
                        </a>
                    @endif

                    <div class="border-t border-gray-200 my-1"></div>
                    <a href="

Pattern: template_app_root
Line 132: <a href="{{ APP_ROOT }}/settings"
Match: {{ APP_ROOT }}/settings"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="
Suggested fix: {{ APP_ROOT }}/settings"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="

Pattern: template_app_root
Line 136: hx-get="{{ APP_ROOT }}/settings"
Match: {{ APP_ROOT }}/settings"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Settings
                    </a>
                    <a href="
Suggested fix: {{ APP_ROOT }}/settings"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Settings
                    </a>
                    <a href="

Pattern: template_app_root
Line 141: <a href="{{ APP_ROOT }}/logout"
Match: {{ APP_ROOT }}/logout"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-post="
Suggested fix: {{ APP_ROOT }}/logout"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-post="

Pattern: template_app_root
Line 145: hx-post="{{ APP_ROOT }}/logout"
Match: {{ APP_ROOT }}/logout"
                       hx-target="body"
                       hx-push-url="true"
                       hx-redirect="
Suggested fix: {{ APP_ROOT }}/logout"
                       hx-target="body"
                       hx-push-url="true"
                       hx-redirect="

Pattern: template_app_root
Line 148: hx-redirect="{{ APP_ROOT }}/login">
Match: {{ APP_ROOT }}/login">
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
Suggested fix: {{ APP_ROOT }}/login">
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>


FILE: .\system\components\edges\notification-dropdown.edge.php
----------------------------------------
Pattern: template_app_root
Line 23: hx-get="{{ APP_ROOT }}/api/system/notifications/get_unread_count"
Match: {{ APP_ROOT }}/api/system/notifications/get_unread_count"
             hx-trigger="load, every 30s, notification-updated from:body"
             hx-swap="outerHTML">
            <!-- Badge will be loaded here -->
        </div>
    </button>

    <!-- Notification dropdown -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1">

        <!-- Notification header -->
        <div class="px-4 py-2 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                <a href="
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 47: <a href="{{ APP_ROOT }}/notifications"
Match: {{ APP_ROOT }}/notifications"
                   class="text-xs text-blue-600 hover:text-blue-800"
                   hx-get="
Suggested fix: {{ APP_ROOT }}/notifications"
                   class="text-xs text-blue-600 hover:text-blue-800"
                   hx-get="

Pattern: template_app_root
Line 49: hx-get="{{ APP_ROOT }}/notifications"
Match: {{ APP_ROOT }}/notifications"
                   hx-target="#content_wrapper"
                   hx-push-url="true">
                    View all
                </a>
            </div>
        </div>

        <!-- Notification list -->
        <div id="notification-list-container"
             hx-get="
Suggested fix: {{ APP_ROOT }}/notifications"
                   hx-target="#content_wrapper"
                   hx-push-url="true">
                    View all
                </a>
            </div>
        </div>

        <!-- Notification list -->
        <div id="notification-list-container"
             hx-get="

Pattern: template_app_root
Line 59: hx-get="{{ APP_ROOT }}/api/system/notifications/get_notifications"
Match: {{ APP_ROOT }}/api/system/notifications/get_notifications"
             hx-trigger="load, notification-updated from:body"
             hx-swap="innerHTML">
            <!-- Notification list will be loaded here -->
        </div>
    </div>
</div>

<!-- SSE connection for real-time notifications -->
<div hx-ext="sse"
     sse-connect="
Suggested fix: {{ APP_ROOT }}/api/system/notifications/get_notifications"
             hx-trigger="load, notification-updated from:body"
             hx-swap="innerHTML">
            <!-- Notification list will be loaded here -->
        </div>
    </div>
</div>

<!-- SSE connection for real-time notifications -->
<div hx-ext="sse"
     sse-connect="

Pattern: template_app_root
Line 69: sse-connect="{{ APP_ROOT }}/api/system/notifications/sse"
Match: {{ APP_ROOT }}/api/system/notifications/sse"
     sse-swap="none"
     class="hidden">
    <div sse-swap="count"
         hx-trigger="sse:count"
         @sse-message="
            const data = JSON.parse(event.detail.data);
            if (data.count > 0) 
Suggested fix: {{ APP_ROOT }}/api/system/notifications/sse"
     sse-swap="none"
     class="hidden">
    <div sse-swap="count"
         hx-trigger="sse:count"
         @sse-message="
            const data = JSON.parse(event.detail.data);
            if (data.count > 0) 


FILE: .\system\components\edges\template-container.edge.php
----------------------------------------
Pattern: fs_constant_concat
Line 11: $template_exists = file_exists(FS_APP_ROOT . "resources/components/templates/{$template}.edge.php");
Match: FS_APP_ROOT . "resources/components/templates/{$template}.edge.php"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 11: $template_exists = file_exists(FS_APP_ROOT . "resources/components/templates/{$template}.edge.php");
Match: APP_ROOT . "resources/components/templates/{$template}.edge.php"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'


FILE: .\system\functions\components.fn.php
----------------------------------------
Pattern: fs_constant_concat
Line 72: //echo "<br>cached version doesn't exist, compiling... from '" . FS_APP_ROOT . "resources/components/{$component_name}.blade.php'<br>\n";
Match: FS_APP_ROOT . "resources/components/{$component_name}.blade.php'
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 72: //echo "<br>cached version doesn't exist, compiling... from '" . FS_APP_ROOT . "resources/components/{$component_name}.blade.php'<br>\n";
Match: APP_ROOT . "resources/components/{$component_name}.blade.php'
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'


FILE: .\system\templates\default_template.hilt.php
----------------------------------------
Pattern: template_app_root
Line 109: <a href="{{ APP_ROOT }}/navigation"
Match: {{ APP_ROOT }}/navigation" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Manage Navigation
                        </a>
                        <a href="
Suggested fix: {{ Array }}/Array


FILE: .\system\templates\file_upload_template.hilt.php
----------------------------------------
Pattern: template_app_root
Line 31: <a href="{{ APP_ROOT }}/navigation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
Match: {{ APP_ROOT }}/navigation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Manage Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="template-info mt-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-800 mb-2">File Upload Template Information</h3>
            <div class="text-sm text-blue-700">
                <p><strong>Route:</strong> 
Suggested fix: {{ Array }}/Array


FILE: .\update_path_usage.php
----------------------------------------
Pattern: template_app_root
Line 115: // Pattern 4: Template patterns {{ APP_ROOT }}/path -> {{ APP_ROOT }}/path
Match: {{ APP_ROOT }}/path -> 
Suggested fix: {{ Array }}/Array

Pattern: template_app_root
Line 115: // Pattern 4: Template patterns {{ APP_ROOT }}/path -> {{ APP_ROOT }}/path
Match: {{ APP_ROOT }}/path
    $pattern4 = '/\
Suggested fix: {{ APP_ROOT }}/path
    $pattern4 = '/\


FILE: .\update_remaining_paths.php
----------------------------------------
Pattern: fs_constant_concat
Line 64: // Pattern 1: FS_CONSTANT . "{$variable}.extension" -> FS_CONSTANT . DIRECTORY_SEPARATOR . "{$variable}.extension"
Match: FS_CONSTANT . "{$variable}.extension"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'

Pattern: app_root_concat
Line 80: // Pattern 2: APP_ROOT . "{$variable}" -> APP_ROOT . DIRECTORY_SEPARATOR . "{$variable}"
Match: APP_ROOT . "{$variable}"
Suggested fix: Array . DIRECTORY_SEPARATOR . 'Array'


