<?php
/**
 * Test script for database dump functionality
 * Run this to test if the database dump system works correctly
 */

// Include the necessary files
require_once __DIR__ . '/system/startup_sequence.php';

echo "🧪 Testing Database Dump Functionality\n";
echo "=====================================\n\n";

try {
    // Test database connection
    echo "1. Testing database connection...\n";
    $pdo = system\database::connection()->getPDO();
    echo "✅ Database connection successful\n\n";
    
    // Test getting table list
    echo "2. Getting table list...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "✅ Found " . count($tables) . " tables\n";
    
    // Show first few tables
    echo "   Sample tables: " . implode(', ', array_slice($tables, 0, 5)) . "\n\n";
    
    // Test SQL folder access
    echo "3. Testing SQL folder access...\n";
    $sql_folder = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'system' . DIRECTORY_SEPARATOR . 'sql';
    echo "   SQL folder path: {$sql_folder}\n";
    
    if (!is_dir($sql_folder)) {
        echo "   Creating SQL folder...\n";
        mkdir($sql_folder, 0755, true);
    }
    
    if (is_writable($sql_folder)) {
        echo "✅ SQL folder is writable\n\n";
    } else {
        echo "❌ SQL folder is not writable\n\n";
        exit(1);
    }
    
    // Test dumping a small table
    echo "4. Testing table dump (autobooks_users)...\n";
    
    if (in_array('autobooks_users', $tables)) {
        // Get table structure
        $create_stmt = $pdo->query("SHOW CREATE TABLE `autobooks_users`");
        $create_result = $create_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get table data (limit to 5 records for testing)
        $data_stmt = $pdo->query("SELECT * FROM `autobooks_users` LIMIT 5");
        $data = $data_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "   Table structure retrieved\n";
        echo "   Found " . count($data) . " records (limited to 5 for testing)\n";
        
        // Generate test SQL content
        $test_filename = "test_dump_autobooks_users.sql";
        $test_file_path = $sql_folder . DIRECTORY_SEPARATOR . $test_filename;
        
        $sql_content = "-- Test dump for autobooks_users\n";
        $sql_content .= "-- Generated: " . date('Y-m-d H:i:s') . "\n\n";
        $sql_content .= "DROP TABLE IF EXISTS `autobooks_users`;\n";
        $sql_content .= $create_result['Create Table'] . ";\n\n";
        
        if (!empty($data)) {
            $columns = array_keys($data[0]);
            $column_list = '`' . implode('`, `', $columns) . '`';
            
            $sql_content .= "INSERT INTO `autobooks_users` ({$column_list}) VALUES\n";
            
            $values = [];
            foreach ($data as $row) {
                $escaped_values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $escaped_values[] = 'NULL';
                    } else {
                        $escaped_values[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $escaped_values) . ')';
            }
            
            $sql_content .= implode(",\n", $values) . ";\n";
        }
        
        // Write test file
        $bytes_written = file_put_contents($test_file_path, $sql_content);
        
        if ($bytes_written !== false) {
            echo "✅ Test dump created successfully\n";
            echo "   File: {$test_filename}\n";
            echo "   Size: " . number_format($bytes_written) . " bytes\n\n";
        } else {
            echo "❌ Failed to create test dump file\n\n";
            exit(1);
        }
    } else {
        echo "⚠️ autobooks_users table not found, skipping dump test\n\n";
    }
    
    echo "🎉 All tests passed! Database dump system is ready to use.\n";
    echo "\n";
    echo "Next steps:\n";
    echo "1. Run the SQL script: system/sql/add_database_dump_navigation.sql\n";
    echo "2. Access the Database Dump Manager at: /system/database_dump\n";
    echo "3. Use FTP to download files from system/sql folder\n";
    
} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
