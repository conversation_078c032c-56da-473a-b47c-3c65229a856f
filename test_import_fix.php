<?php
/**
 * Test script to verify the import fix works correctly
 * Access via browser: http://localhost/autobooks/test_import_fix.php
 */

header('Content-Type: text/plain');

echo "=== Testing Import Fix (Namespace + Blueprint Index Issues) ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence to initialize everything
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    // Create a simple test CSV
    echo "2. Creating test CSV...\n";
    
    $test_csv_data = [
        ['company_name', 'contact_email', 'product_name', 'start_date', 'end_date', 'status'],
        ['Test Corp', '<EMAIL>', 'AutoCAD LT', '2024-01-01', '2024-12-31', 'Active'],
        ['Demo Inc', '<EMAIL>', 'SketchUp Pro', '2024-02-01', '2025-01-31', 'Active']
    ];
    
    // Create temporary CSV file
    $temp_csv_file = tempnam(sys_get_temp_dir(), 'test_fix_') . '.csv';
    $csv_handle = fopen($temp_csv_file, 'w');
    
    foreach ($test_csv_data as $row) {
        fputcsv($csv_handle, $row);
    }
    fclose($csv_handle);
    
    echo "Created test CSV: {$temp_csv_file}\n";
    echo "Headers: " . implode(', ', $test_csv_data[0]) . "\n\n";
    
    // Test the import process
    echo "3. Testing import process with fix...\n";
    $test_table_name = 'autobooks_test_fix_' . time() . '_data';
    
    echo "Importing to table: {$test_table_name}\n";
    
    // Import the CSV using the enhanced import method
    $import_result = system\data_importer::import_csv_with_auto_schema($temp_csv_file, $test_table_name, true, true);
    
    if (isset($import_result['error'])) {
        echo "❌ Import failed: " . $import_result['error'] . "\n";
    } else {
        echo "✅ Import successful!\n";
        echo "- Table created: {$test_table_name}\n";
        echo "- Rows imported: " . ($import_result['import_result']['success_count'] ?? 'unknown') . "\n";
        
        // Check if config was stored
        if (isset($import_result['config_result'])) {
            $config_result = $import_result['config_result'];
            if (isset($config_result['error'])) {
                echo "❌ Config storage failed: " . $config_result['error'] . "\n";
            } else {
                echo "✅ Table configuration stored successfully!\n";
                echo "- Action: " . ($config_result['action'] ?? 'unknown') . "\n";
            }
        } else {
            echo "⚠️ No config result returned\n";
        }
        
        // Check if data source was created
        if (isset($import_result['data_source_result'])) {
            $data_source_result = $import_result['data_source_result'];
            if (isset($data_source_result['error'])) {
                echo "❌ Data source creation failed: " . $data_source_result['error'] . "\n";
            } else {
                echo "✅ Data source created successfully!\n";
                echo "- Data source ID: " . ($data_source_result['data_source_id'] ?? 'unknown') . "\n";
            }
        } else {
            echo "⚠️ No data source result returned\n";
        }
        
        // Test retrieving the configuration
        echo "\n4. Testing configuration retrieval...\n";
        
        try {
            $stored_config = system\table_config_manager::get_table_config($test_table_name);
            
            if ($stored_config) {
                echo "✅ Configuration retrieved successfully!\n";
                echo "- Table name: " . $stored_config['table_name'] . "\n";
                echo "- Route key: " . $stored_config['route_key'] . "\n";
                echo "- Description: " . $stored_config['description'] . "\n";
                echo "- Data source: " . $stored_config['data_source'] . "\n";
                
                // Check if the config has the expected structure
                $table_config = json_decode($stored_config['table_config'], true);
                if ($table_config && isset($table_config['columns'])) {
                    echo "- Visible columns: " . count($table_config['columns']) . "\n";
                    echo "- Available fields: " . count($table_config['available_fields'] ?? []) . "\n";
                    
                    echo "\nVisible columns:\n";
                    foreach ($table_config['columns'] as $i => $column) {
                        echo "  " . ($i + 1) . ". {$column['field']}: {$column['label']}\n";
                    }
                    
                    if (!empty($table_config['available_fields'])) {
                        echo "\nAvailable fields:\n";
                        foreach ($table_config['available_fields'] as $i => $field) {
                            echo "  " . ($i + 1) . ". {$field}\n";
                        }
                    }
                } else {
                    echo "⚠️ Configuration structure is invalid\n";
                }
            } else {
                echo "❌ Configuration not found\n";
            }
        } catch (Exception $e) {
            echo "❌ Error retrieving configuration: " . $e->getMessage() . "\n";
        }
        
        // Test data table storage integration
        echo "\n5. Testing data table storage integration...\n";
        
        try {
            $user_id = 1; // Assuming user ID 1 exists
            $table_data = system\data_table_storage::get_table_data($test_table_name, [], [], $user_id);
            
            if ($table_data['success']) {
                echo "✅ Data table storage integration working!\n";
                echo "- Source: " . $table_data['source'] . "\n";
                echo "- Records: " . $table_data['count'] . "\n";
                echo "- Available fields: " . count($table_data['available_fields'] ?? []) . "\n";
                
                if (!empty($table_data['available_fields'])) {
                    echo "Available fields: " . implode(', ', $table_data['available_fields']) . "\n";
                }
            } else {
                echo "❌ Data table storage integration failed\n";
            }
        } catch (Exception $e) {
            echo "❌ Error testing data table storage: " . $e->getMessage() . "\n";
        }
        
        // Clean up test table
        echo "\n6. Cleaning up test data...\n";
        try {
            system\database::rawQuery("DROP TABLE IF EXISTS `{$test_table_name}`");
            echo "✅ Test table cleaned up\n";
        } catch (Exception $e) {
            echo "⚠️ Could not clean up test table: " . $e->getMessage() . "\n";
        }
    }
    
    // Clean up temporary CSV file
    unlink($temp_csv_file);
    echo "✅ Temporary CSV file cleaned up\n";
    
    echo "\n=== Test Complete ===\n";
    
    if (!isset($import_result['error'])) {
        echo "✅ Both import fixes are working correctly!\n";
        echo "✅ Blueprint index() method fix applied\n";
        echo "✅ Namespace issue fix applied\n";
        echo "✅ Table configuration storage is working\n";
        echo "✅ Data source creation is working\n";
        echo "✅ Unified field column selection is working\n";

        echo "\nThe import process should now complete successfully.\n";
        echo "Try importing your SketchUp CSV again.\n";
    } else {
        echo "❌ Import fix needs further investigation\n";
        echo "Check the logs for more details:\n";
        echo "- system/logs/data_importer_logfile.log\n";
        echo "- system/logs/table_config_manager_logfile.log\n";
        echo "- system/logs/data_table_generator_logfile.log\n";
        echo "\nAlso check Apache error logs for PHP fatal errors.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Test ===\n";
?>
