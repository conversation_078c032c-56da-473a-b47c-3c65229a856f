# Autodesk Catalog Hash Fix Scripts

This directory contains scripts to fix unique hash inconsistencies in the `products_autodesk_catalog` table that can occur when the hash generation algorithm changes, causing duplicate records with different hashes.

## Problem Description

The `products_autodesk_catalog` table uses a unique hash system to identify records based on specific field combinations. When the hash generation logic changes, existing records may have outdated hashes that don't match what would be generated with the current algorithm. This can cause:

- Duplicate records that should be identical
- Broken relationships with other tables
- Import failures due to hash mismatches

## Scripts Overview

### 1. `test_hash_generation.php` (Recommended First Step)

**Purpose**: Test the current hash generation logic against existing records to identify mismatches.

**Usage**:
```bash
cd /path/to/autobooks/resources/scripts
php test_hash_generation.php
```

**What it does**:
- Tests the first 10 records from the catalog
- Compares stored hashes with newly generated hashes
- Shows detailed breakdown of field values used for hash generation
- Provides a summary of matches/mismatches

**Sample Output**:
```
Testing first 10 records:
------------------------------------------------------------------------------------------------------------------------
ID   Old Hash   New Hash   H Match  U Match  Generated Hash String
------------------------------------------------------------------------------------------------------------------------
8527 <USER>   <GROUP>   NO       NO       OD-000002COMSSTNDC100A01New
8528 1ea9d536   e5f6g7h8   NO       NO       OD-000002COMSSTNDC100A01RenewalM2S
...
```

### 2. `fix_autodesk_catalog_hashes.php` (Command Line)

**Purpose**: Fix all hash inconsistencies by regenerating both `hash_string` and `unique_hash` fields.

**Usage**:
```bash
cd /path/to/autobooks/resources/scripts
php fix_autodesk_catalog_hashes.php
```

**What it does**:
- Processes all records in the `products_autodesk_catalog` table
- Regenerates `hash_string` from individual field values
- Generates new `unique_hash` using current algorithm
- Updates both fields in the database
- Provides detailed logging of all changes
- Detects and reports duplicate hashes

**Features**:
- Batch processing (100 records at a time by default)
- Progress reporting
- Error handling and logging
- Duplicate detection

### 3. `fix_autodesk_hashes.view.php` (Web Interface)

**Purpose**: Web-based interface for fixing hashes with preview functionality.

**Access**: Navigate to `/system/views/admin/fix_autodesk_hashes.view.php` in your browser

**Features**:
- **Dry Run Mode**: Preview changes without applying them
- **Batch Size Control**: Adjust processing batch size
- **Real-time Results**: See progress and results in the browser
- **Change Preview**: View sample changes before applying
- **Duplicate Detection**: Identify potential duplicate records

**Recommended Workflow**:
1. Run in "Dry Run" mode first to preview changes
2. Review the results and duplicate warnings
3. Run again without "Dry Run" to apply changes

## Hash Generation Logic

The unique hash is generated from these fields (in order):
1. `offeringId`
2. `intendedUsage_code`
3. `accessModel_code`
4. `servicePlan_code`
5. `connectivity_code`
6. `term_code`
7. `orderAction`
8. `specialProgramDiscount_code`
9. `fromQty`
10. `toQty`

**Process**:
1. Concatenate all field values (NULL values become empty strings)
2. Generate CRC32 hash of the concatenated string
3. Store both the concatenated string (`hash_string`) and hash (`unique_hash`)

**Example**:
- Field values: `OD-000002`, `COM`, `S`, `STND`, `C100`, `A01`, `New`, ``, ``, ``
- Hash string: `OD-000002COMSSTNDC100A01New`
- Unique hash: `d2ba8185` (CRC32 of hash string)

## Recommended Usage Workflow

### Step 1: Test Current State
```bash
php test_hash_generation.php
```
This will show you if there are hash mismatches and how many records are affected.

### Step 2: Preview Changes (Web Interface)
1. Open `/system/views/admin/fix_autodesk_hashes.view.php`
2. Check "Dry Run" option
3. Click "Fix Hashes"
4. Review the results and any duplicate warnings

### Step 3: Apply Changes
Choose one of these options:

**Option A: Web Interface**
1. Uncheck "Dry Run" option
2. Click "Fix Hashes"
3. Monitor progress and results

**Option B: Command Line**
```bash
php fix_autodesk_catalog_hashes.php
```

### Step 4: Verify Results
Run the test script again to confirm all hashes are now consistent:
```bash
php test_hash_generation.php
```

## Important Notes

### Backup Recommendation
Always backup your database before running the fix scripts:
```sql
CREATE TABLE products_autodesk_catalog_backup AS SELECT * FROM products_autodesk_catalog;
```

### Related Tables
The scripts only update the main `products_autodesk_catalog` table. If you have related tables that reference the `unique_hash` field, you may need to update them separately using the existing `data_importer::update_autodesk_catalog_hashes()` function.

### Duplicate Detection
If the scripts detect duplicate hashes after regeneration, this indicates truly duplicate data that may need manual review. The scripts will log these duplicates for investigation.

### Performance
- Default batch size is 100 records
- Processing time depends on table size
- Web interface may timeout on very large tables (use command line for large datasets)

## Troubleshooting

### "Class not found" errors
Ensure you're running the scripts from the correct directory and that the application files are properly included.

### Memory issues
Reduce the batch size if you encounter memory problems.

### Timeout issues (Web interface)
Use the command line script for large datasets or increase PHP execution time limits.

### Permission errors
Ensure the web server has write permissions to the database and log files.

## Files Created/Modified

- `resources/scripts/test_hash_generation.php` - Hash testing script
- `resources/scripts/fix_autodesk_catalog_hashes.php` - Command line fix script  
- `system/views/admin/fix_autodesk_hashes.view.php` - Web interface
- `resources/scripts/README_hash_fix.md` - This documentation
