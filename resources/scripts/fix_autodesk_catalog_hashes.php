<?php
/**
 * Fix Autodesk Catalog Hashes
 *
 * This script regenerates the unique hashes in the products_autodesk_catalog table
 * by rebuilding the hash_string from individual field values and then generating
 * new unique_hash values using the current hash function.
 *
 * This fixes issues where duplicate records exist due to hash generation changes.
 *
 * Usage: php fix_autodesk_catalog_hashes.php
 */

// Set up error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include necessary files
$doc_root = dirname(dirname(__DIR__));
require_once($doc_root . '/system/includes/application_top.php');
require_once($doc_root . '/system/classes/data_importer.class.php');

// Set up logging
function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo $timestamp . " - " . $message . "\n";
    error_log($timestamp . " - " . $message);
}

/**
 * Generate hash string from field values
 * This matches the logic used in the current import process
 */
function generate_hash_string($row) {
    // Fields used to generate the unique hash (in order)
    $hash_fields = [
        'offeringId',
        'intendedUsage_code', 
        'accessModel_code',
        'servicePlan_code',
        'connectivity_code',
        'term_code',
        'orderAction',
        'specialProgramDiscount_code',
        'fromQty',
        'toQty'
    ];
    
    $hash_values = [];
    foreach ($hash_fields as $field) {
        $value = isset($row[$field]) ? $row[$field] : '';
        // Convert NULL values to empty string
        if ($value === null) {
            $value = '';
        }
        $hash_values[] = $value;
    }
    
    return implode('', $hash_values);
}

/**
 * Generate unique hash from hash string
 * This matches the current hash generation logic
 */
function generate_unique_hash($hash_string) {
    return hash('crc32', $hash_string);
}

log_message("Starting Autodesk catalog hash fix process");

// Set batch size (adjust as needed based on server resources)
$batch_size = 100;
$offset = 0;
$total_records = 0;
$updated_records = 0;
$failed_records = 0;
$duplicate_hashes = [];

try {
    // Get total count for progress reporting
    $count_query = "SELECT COUNT(*) as total FROM products_autodesk_catalog";
    $count_result = tep_db_query($count_query);
    $count_row = tep_db_fetch_array($count_result);
    $total_count = $count_row['total'];

    log_message("Total records to process: {$total_count}");

    // Track hash usage to detect duplicates
    $hash_usage = [];

    // Process records in batches
    while (true) {
        // Select a batch of records with all necessary fields
        $query = "SELECT id, unique_hash, hash_string, offeringId, intendedUsage_code, 
                         accessModel_code, servicePlan_code, connectivity_code, term_code, 
                         orderAction, specialProgramDiscount_code, fromQty, toQty 
                  FROM products_autodesk_catalog 
                  LIMIT {$batch_size} OFFSET {$offset}";
        
        $result = tep_db_query($query);
        $batch_count = 0;
        $batch_updates = [];

        // Process each record in the batch
        while ($row = tep_db_fetch_array($result)) {
            $batch_count++;
            $total_records++;

            // Generate new hash string from field values
            $new_hash_string = generate_hash_string($row);
            
            // Generate new unique hash
            $new_unique_hash = generate_unique_hash($new_hash_string);
            
            $old_hash_string = $row['hash_string'];
            $old_unique_hash = $row['unique_hash'];

            // Track hash usage for duplicate detection
            if (!isset($hash_usage[$new_unique_hash])) {
                $hash_usage[$new_unique_hash] = [];
            }
            $hash_usage[$new_unique_hash][] = [
                'id' => $row['id'],
                'hash_string' => $new_hash_string
            ];

            // Check if update is needed
            if ($new_hash_string !== $old_hash_string || $new_unique_hash !== $old_unique_hash) {
                $batch_updates[] = [
                    'id' => $row['id'],
                    'old_hash_string' => $old_hash_string,
                    'new_hash_string' => $new_hash_string,
                    'old_unique_hash' => $old_unique_hash,
                    'new_unique_hash' => $new_unique_hash
                ];
            }
        }

        // If no records were found, we're done
        if ($batch_count === 0) {
            break;
        }

        // Update records in batch
        foreach ($batch_updates as $update) {
            $update_query = "UPDATE products_autodesk_catalog 
                           SET hash_string = :new_hash_string, unique_hash = :new_unique_hash 
                           WHERE id = :id";
            $params = [
                ':new_hash_string' => $update['new_hash_string'],
                ':new_unique_hash' => $update['new_unique_hash'],
                ':id' => $update['id']
            ];

            try {
                $update_result = tep_db_query($update_query, null, $params);
                $affected = tep_db_affected_rows($update_result);
                if ($affected > 0) {
                    $updated_records++;
                    log_message("Updated record ID {$update['id']}: '{$update['old_unique_hash']}' -> '{$update['new_unique_hash']}'");
                }
            } catch (Exception $e) {
                log_message("Error updating record ID {$update['id']}: " . $e->getMessage());
                $failed_records++;
            }
        }

        // Move to next batch
        $offset += $batch_size;

        // Log progress
        $progress = round(($total_records / $total_count) * 100, 2);
        log_message("Processed {$total_records}/{$total_count} records ({$progress}%)");
    }

    // Check for duplicate hashes
    foreach ($hash_usage as $hash => $records) {
        if (count($records) > 1) {
            $duplicate_hashes[$hash] = $records;
            log_message("WARNING: Duplicate hash '{$hash}' found in " . count($records) . " records:");
            foreach ($records as $record) {
                log_message("  - ID {$record['id']}: {$record['hash_string']}");
            }
        }
    }

    // Log final results
    log_message("Hash fix process completed");
    log_message("Total records processed: {$total_records}");
    log_message("Updated records: {$updated_records}");
    log_message("Failed records: {$failed_records}");
    log_message("Duplicate hashes found: " . count($duplicate_hashes));

    if (count($duplicate_hashes) > 0) {
        log_message("WARNING: There are still duplicate hashes after the update. This may indicate truly duplicate data.");
    }

} catch (Exception $e) {
    log_message("Fatal error: " . $e->getMessage());
    exit(1);
}

log_message("Script completed successfully");
?>
