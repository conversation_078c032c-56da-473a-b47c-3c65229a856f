/**
 * Universal Sortable Handler
 * 
 * This script provides a simple, reusable sortable functionality that works with any element
 * that has the '.sortable' class and uses HTMX inline attributes for configuration.
 * 
 * Usage:
 * Add class="sortable" to any container element along with these data attributes:
 * - data-sortable-handle: CSS selector for drag handle (default: '.drag-handle')
 * - data-sortable-group: Group name for cross-container dragging (optional)
 * - data-sortable-animation: Animation duration in ms (default: 150)
 * - data-sortable-ghost-class: CSS class for ghost element (default: 'sortable-ghost')
 * - data-sortable-chosen-class: CSS class for chosen element (default: 'sortable-chosen')
 * - data-sortable-drag-class: CSS class for dragging element (default: 'sortable-drag')
 * - data-sortable-filter: CSS selector for elements to exclude from sorting (default: '.htmx-indicator')
 * - data-sortable-item-selector: CSS selector for valid sortable items (optional)
 * 
 * HTMX Integration:
 * The sortable container should have HTMX attributes for handling the sort end event:
 * - hx-post: URL to send the sort data to
 * - hx-trigger: Should be "sortableEnd" (this script will trigger this event)
 * - hx-target: Target element for the response
 * - hx-swap: How to swap the response
 * - hx-vals: Additional values to send (optional)
 * 
 * The script will automatically include form data and add sort-specific data:
 * - sortedIds: JSON array of sorted item IDs (from data-sort-id attributes)
 * - fromIndex: Original index of moved item
 * - toIndex: New index of moved item
 * - movedId: ID of the moved item
 * - fromContainer: ID of source container (for cross-container moves)
 * - toContainer: ID of target container (for cross-container moves)
 */

(function() {
    'use strict';
    
    let sortableInstances = new Map();
    
    function initializeSortables(container = document) {
        const sortableElements = container.querySelectorAll('.sortable');
        
        sortableElements.forEach(element => {
            // Skip if already initialized
            if (sortableInstances.has(element)) {
                return;
            }
            
            // Get configuration from data attributes
            const config = {
                handle: element.dataset.sortableHandle || '.drag-handle',
                group: element.dataset.sortableGroup || null,
                animation: parseInt(element.dataset.sortableAnimation) || 150,
                ghostClass: element.dataset.sortableGhostClass || 'sortable-ghost',
                chosenClass: element.dataset.sortableChosenClass || 'sortable-chosen',
                dragClass: element.dataset.sortableDragClass || 'sortable-drag',
                filter: element.dataset.sortableFilter || '.htmx-indicator',
                itemSelector: element.dataset.sortableItemSelector || null,
                fallbackOnBody: true,
                swapThreshold: 0.65
            };
            
            // Set up group configuration if specified
            if (config.group) {
                config.group = {
                    name: config.group,
                    pull: true,
                    put: true
                };
            }
            
            // Add onMove handler if item selector is specified
            if (config.itemSelector) {
                config.onMove = function(evt) {
                    return evt.related.className.indexOf('htmx-indicator') === -1 &&
                           evt.related.matches(config.itemSelector);
                };
            } else {
                config.onMove = function(evt) {
                    return evt.related.className.indexOf('htmx-indicator') === -1;
                };
            }
            
            // Add event handlers
            config.onStart = function(evt) {
                document.body.classList.add('sorting-active');
                evt.item.classList.add('dragging');
            };
            
            config.onEnd = function(evt) {
                document.body.classList.remove('sorting-active');
                evt.item.classList.remove('dragging');
                
                // Prepare sort data
                const sortData = {
                    fromIndex: evt.oldIndex,
                    toIndex: evt.newIndex,
                    movedId: evt.item.dataset.sortId || evt.item.id || null,
                    fromContainer: evt.from.id || null,
                    toContainer: evt.to.id || null
                };
                
                // Get sorted IDs from the target container
                const sortedItems = evt.to.querySelectorAll('[data-sort-id]');
                if (sortedItems.length > 0) {
                    sortData.sortedIds = Array.from(sortedItems).map(item => item.dataset.sortId);
                } else {
                    // Fallback to using element IDs if no data-sort-id attributes
                    const itemsWithIds = evt.to.querySelectorAll('[id]');
                    sortData.sortedIds = Array.from(itemsWithIds).map(item => item.id);
                }
                
                // Store sort data in the element for HTMX to pick up
                element.dataset.sortData = JSON.stringify(sortData);
                
                // Trigger HTMX event
                htmx.trigger(element, 'sortableEnd');
                
                // Disable sorting temporarily to prevent conflicts during HTMX update
                this.option("disabled", true);
            };
            
            // Create sortable instance
            const sortableInstance = Sortable.create(element, config);
            sortableInstances.set(element, sortableInstance);
            
            // Re-enable sorting after HTMX updates
            element.addEventListener('htmx:afterSwap', function() {
                if (sortableInstances.has(element)) {
                    sortableInstances.get(element).option("disabled", false);
                }
            });
            
            element.addEventListener('htmx:afterSettle', function() {
                if (sortableInstances.has(element)) {
                    sortableInstances.get(element).option("disabled", false);
                }
            });
        });
    }
    
    function destroySortables(container = document) {
        const sortableElements = container.querySelectorAll('.sortable');
        
        sortableElements.forEach(element => {
            if (sortableInstances.has(element)) {
                const instance = sortableInstances.get(element);
                if (instance && instance.destroy) {
                    instance.destroy();
                }
                sortableInstances.delete(element);
            }
        });
    }
    
    // Initialize on DOM ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeSortables();
    });
    
    // Reinitialize after HTMX loads new content
    if (typeof htmx !== 'undefined') {
        htmx.onLoad(function(content) {
            initializeSortables(content);
        });
        
        // Clean up before HTMX swaps content
        document.body.addEventListener('htmx:beforeSwap', function(evt) {
            destroySortables(evt.detail.target);
        });
    }
    
    // Expose functions globally for manual control if needed
    window.UniversalSortable = {
        initialize: initializeSortables,
        destroy: destroySortables,
        getInstance: function(element) {
            return sortableInstances.get(element);
        }
    };
    
})();
