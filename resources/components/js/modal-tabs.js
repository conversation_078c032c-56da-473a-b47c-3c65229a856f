/**
 * Modal Tab Management System
 * Enhances HTMX integration with tab functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Enhanced HTMX integration for tab management
    document.body.addEventListener('htmx:configRequest', function(event) {
        // Add tab-related headers for requests targeting modal_body
        if (event.detail.target && event.detail.target.id === 'modal_body') {
            const triggerElement = event.detail.elt;
            const tabTitle = triggerElement.getAttribute('data-tab-title');
            
            if (tabTitle) {
                // Get modal state from Alpine.js
                const modalElement = document.querySelector('[x-data*="tabs"]');
                if (modalElement && modalElement._x_dataStack) {
                    const modalData = modalElement._x_dataStack[0];
                    
                    // Add headers to help server determine tab behavior
                    event.detail.headers['X-Tab-Title'] = tabTitle;
                    event.detail.headers['X-Modal-State'] = modalData.showModal ? 'open' : 'closed';
                    event.detail.headers['X-Has-Pinned-Tabs'] = modalData.tabs.some(t => t.pinned) ? 'true' : 'false';
                    event.detail.headers['X-Tab-Count'] = modalData.tabs.length.toString();
                }
            }
        }
    });
    
    // Handle successful responses and create tabs
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target && event.detail.target.id === 'modal_body') {
            const xhr = event.detail.xhr;
            const tabTitle = xhr.getResponseHeader('X-Tab-Title');
            const tabId = xhr.getResponseHeader('X-Tab-Id');
            
            if (tabTitle && tabId) {
                // Get the modal Alpine.js component
                const modalElement = document.querySelector('[x-data*="tabs"]');
                if (modalElement && modalElement._x_dataStack) {
                    const modalData = modalElement._x_dataStack[0];
                    
                    // Handle tab creation based on modal state and rules
                    const shouldCreateNewTab = determineTabBehavior(modalData, tabTitle, tabId);
                    
                    if (shouldCreateNewTab) {
                        modalData.handleTabCreation(tabTitle, tabId, event.detail.target.innerHTML);
                    }
                }
            }
        }
    });
    
    // Utility function to determine tab creation behavior
    function determineTabBehavior(modalData, tabTitle, tabId) {
        // Rule 1: When modal is closed/not visible - create single tab
        if (!modalData.showModal) {
            return true;
        }
        
        // Rule 2: When modal is open - create new tab and switch to it
        if (modalData.showModal) {
            return true;
        }
        
        // Rule 3: Pin functionality overrides rule 1
        if (modalData.tabs.some(t => t.pinned)) {
            return true; // Always use rule 2 when pinned tabs exist
        }
        
        return true; // Default to creating tab
    }
    
    // Enhanced keyboard navigation
    document.addEventListener('keydown', function(event) {
        const modalElement = document.querySelector('[x-data*="tabs"]');
        if (!modalElement || !modalElement._x_dataStack) return;
        
        const modalData = modalElement._x_dataStack[0];
        if (!modalData.showModal) return;
        
        // Arrow key navigation when a tab button is focused
        if (event.target.closest('.tab-button')) {
            if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
                event.preventDefault();
                
                const tabButtons = Array.from(document.querySelectorAll('.tab-button'));
                const currentIndex = tabButtons.indexOf(event.target.closest('.tab-button'));
                
                let newIndex;
                if (event.key === 'ArrowLeft') {
                    newIndex = currentIndex > 0 ? currentIndex - 1 : tabButtons.length - 1;
                } else {
                    newIndex = currentIndex < tabButtons.length - 1 ? currentIndex + 1 : 0;
                }
                
                tabButtons[newIndex].focus();
                
                // Also switch to the tab
                const tabId = tabButtons[newIndex].getAttribute('data-tab-id');
                if (tabId) {
                    modalData.switchToTab(tabId);
                }
            }
        }
    });
    
    // Handle tab creation from external events (for programmatic tab creation)
    document.addEventListener('modal-create-tab', function(event) {
        const modalElement = document.querySelector('[x-data*="tabs"]');
        if (modalElement && modalElement._x_dataStack) {
            const modalData = modalElement._x_dataStack[0];
            modalData.addTab(
                event.detail.title,
                event.detail.content,
                event.detail.url || '',
                event.detail.id || null
            );
            
            // Ensure modal is visible
            if (!modalData.showModal) {
                modalData.showModal = true;
            }
        }
    });
    
    // Utility function to create tabs programmatically
    window.createModalTab = function(title, content, url = '', id = null) {
        const event = new CustomEvent('modal-create-tab', {
            detail: { title, content, url, id }
        });
        document.dispatchEvent(event);
    };
    
    // Enhanced error handling for tab operations
    document.body.addEventListener('htmx:responseError', function(event) {
        if (event.detail.target && event.detail.target.id === 'modal_body') {
            const tabTitle = event.detail.elt.getAttribute('data-tab-title');
            if (tabTitle) {
                // Show error in a tab
                const errorContent = `
                    <div class="p-6">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Error Loading Tab</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>Failed to load content for "${tabTitle}"</p>
                                        <p>Status: ${event.detail.xhr.status} ${event.detail.xhr.statusText}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                window.createModalTab(`Error: ${tabTitle}`, errorContent);
            }
        }
    });
    
    // Debug helper (remove in production)
    if (window.location.search.includes('debug=tabs')) {
        window.debugTabs = function() {
            const modalElement = document.querySelector('[x-data*="tabs"]');
            if (modalElement && modalElement._x_dataStack) {
                const modalData = modalElement._x_dataStack[0];
                console.log('Tab Debug Info:', {
                    tabs: modalData.tabs,
                    activeTabId: modalData.activeTabId,
                    showModal: modalData.showModal,
                    nextTabId: modalData.nextTabId
                });
            }
        };
        
        console.log('Tab debugging enabled. Use debugTabs() to inspect tab state.');
    }
});
