// HTMX Sortable Integration for Column Manager
htmx.onLoad(function(content) {
    // Column sorting
    var columnSortables = content.querySelectorAll(".column-sortable");
    for (var i = 0; i < columnSortables.length; i++) {
        var sortable = columnSortables[i];
        var sortableInstance = new Sortable(sortable, {
            animation: 150,
            handle: '.column-drag-handle',
            filter: '.htmx-indicator',
            ghostClass: 'sortable-ghost',
            onMove: function (evt) {
                return evt.related.className.indexOf('htmx-indicator') === -1;
            },
            onEnd: function (evt) {
                // Get the new column order
                var columnElements = sortable.querySelectorAll('[data-column-id]');
                var columnIds = Array.from(columnElements).map(function(el) {
                    return el.dataset.columnId;
                });

                // Make HTMX call with JSON string format (like temp2.php)
                htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/reorder_columns', {
                    values: {
                        table_name: sortable.dataset.tableName,
                        callback: sortable.dataset.callback,
                        data_source: sortable.dataset.dataSource,
                        column_order: JSON.stringify(columnIds)
                    },
                    target: '.data_table',
                    swap: 'outerHTML'
                });

                this.option("disabled", true);
            }
        });

        // Re-enable sorting on the htmx:afterSwap event
        sortable.addEventListener("htmx:afterSwap", function() {
            sortableInstance.option("disabled", false);
        });
    }

    // Field and action sorting
    var fieldSortables = content.querySelectorAll(".field-container");
    for (var j = 0; j < fieldSortables.length; j++) {
        var fieldContainer = fieldSortables[j];
        var fieldSortableInstance = new Sortable(fieldContainer, {
            group: 'fieldsAndActions',
            animation: 150,
            handle: '.field-drag-handle, .action-drag-handle',
            filter: '.htmx-indicator',
            ghostClass: 'sortable-ghost',
            onMove: function (evt) {
                return evt.related.className.indexOf('htmx-indicator') === -1;
            },
            onEnd: function (evt) {
                // Handle field/action movement between columns
                var targetColumnId = evt.to.closest('[data-column-id]').dataset.columnId;
                var sourceColumnId = evt.from.closest('[data-column-id]').dataset.columnId;

                if (targetColumnId !== sourceColumnId) {
                    var fieldName = evt.item.dataset.fieldName;
                    var actionId = evt.item.dataset.actionId;

                    if (fieldName) {
                        // Move field between columns
                        htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/move_field', {
                            values: {
                                table_name: evt.to.closest('.column-sortable').dataset.tableName,
                                callback: evt.to.closest('.column-sortable').dataset.callback,
                                data_source: evt.to.closest('.column-sortable').dataset.dataSource,
                                field_name: fieldName,
                                source_column_id: sourceColumnId,
                                target_column_id: targetColumnId
                            },
                            target: '.data_table',
                            swap: 'outerHTML'
                        });
                    } else if (actionId) {
                        // Move action button between columns
                        htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/move_action_button', {
                            values: {
                                table_name: evt.to.closest('.column-sortable').dataset.tableName,
                                callback: evt.to.closest('.column-sortable').dataset.callback,
                                data_source: evt.to.closest('.column-sortable').dataset.dataSource,
                                action_id: actionId,
                                source_column_id: sourceColumnId,
                                target_column_id: targetColumnId
                            },
                            target: '.data_table',
                            swap: 'outerHTML'
                        });
                    }
                }

                this.option("disabled", true);
            }
        });

        // Re-enable sorting on the htmx:afterSwap event
        fieldContainer.addEventListener("htmx:afterSwap", function() {
            fieldSortableInstance.option("disabled", false);
        });
    }
});
