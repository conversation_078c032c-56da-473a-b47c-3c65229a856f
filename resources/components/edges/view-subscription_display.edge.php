@props([
    'title' => 'Subscription Display',
    'description' => '',
    'subscription' => [],
    'matched_data' => [],
    'histdata' => [],
    'class' => ''
])

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header Section -->
    <div class="bg-white shadow-sm rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 truncate">
                        {{ $subscription['subs_offeringName'] ?? 'Subscription Details' }}
                    </h1>
                    <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <span class="font-medium">Reference:</span>
                            <span class="ml-1">{{ $subscription['subs_subscriptionReferenceNumber'] ?? 'N/A' }}</span>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <span class="font-medium">Status:</span>
                            <span class="ml-1 px-2 py-1 text-xs font-medium rounded-full
                                {{ ($subscription['subs_status'] ?? '') === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ $subscription['subs_status'] ?? 'Unknown' }}
                            </span>
                        </div>
                        @if(isset($subscription['subs_enddatediff']) && $subscription['subs_enddatediff'] <= 90)
                            <div class="mt-2 flex items-center text-sm">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $subscription['subs_enddatediff'] <= 30 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ $subscription['subs_enddatediff'] > 0 ? 'Expires in ' . $subscription['subs_enddatediff'] . ' days' : 'Expired ' . abs($subscription['subs_enddatediff']) . ' days ago' }}
                                </span>
                            </div>
                        @endif4444
                    </div>
                </div>
                <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-2">
                    @if(isset($subscription['subs_enddatediff']) && $subscription['subs_enddatediff'] <= 90 && $subscription['subs_enddatediff'] >= -30)
                        <button type="button"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                hx-post="{{ APP_ROOT }}/api/subscriptions/SendEmail"
                                hx-swap="innerHTML"
                                hx-target="#modal_body"
                                hx-vals='{{ json_encode([
                                    "subscription_number" => $subscription['subs_subscriptionReferenceNumber'] ?? '',
                                    "subscription_id" => $subscription['subs_subscriptionId'] ?? '',
                                    "user_id" => $_SESSION['user_id'] ?? ''
                                ]) }}'
                                @click="showModal = true">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Send Reminder
                        </button>
                    @endif
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-post="{{ APP_ROOT }}/api/subscriptions/flag_modal"
                            hx-swap="innerHTML"
                            hx-target="#modal_body"
                            hx-vals='{{ json_encode([
                                "target" => "subscription",
                                "target_reference" => $subscription['subs_subscriptionId'] ?? $subscription['subs_subscriptionReferenceNumber'] ?? '',
                                "customer_csn" => $subscription['endcust_account_csn'] ?? '',
                                "endcust_name" => $subscription['endcust_name'] ?? '',
                                "email_address" => $subscription['endcust_primary_admin_email'] ?? '',
                                "user_id" => $_SESSION['user_id'] ?? ''
                            ]) }}'
                            @click="showModal = true">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21a2 2 0 012 2v11a2 2 0 01-2 2H3z"></path>
                        </svg>
                        Add Note
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Primary Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Subscription Details Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Subscription Details</h2>
                </div>
                <div class="px-6 py-4">
                    <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Product</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_offeringName'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Quantity</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_quantity'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Start Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_startDate'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">End Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_endDate'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Term</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_term'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Auto Renew</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ ($subscription['subs_autoRenew'] ?? '') === 'true' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ ($subscription['subs_autoRenew'] ?? '') === 'true' ? 'Yes' : 'No' }}
                                </span>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Customer Information Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Customer Information</h2>
                    @if(!empty($matched_data))
                        <p class="mt-1 text-sm text-gray-500">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Matched with manual entry
                            </span>
                        </p>
                    @endif
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Primary Customer Details -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Primary Contact</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Company</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $subscription['endcust_name'] ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Contact Name</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ trim(($subscription['endcust_primary_admin_first_name'] ?? '') . ' ' . ($subscription['endcust_primary_admin_last_name'] ?? '')) ?: 'N/A' }}
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if(!empty($subscription['endcust_primary_admin_email']))
                                            <a href="mailto:{{ $subscription['endcust_primary_admin_email'] }}"
                                               class="text-indigo-600 hover:text-indigo-500">
                                                {{ $subscription['endcust_primary_admin_email'] }}
                                            </a>
                                        @else
                                            N/A
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">CSN</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $subscription['endcust_account_csn'] ?? 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Address Information -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Address</h3>
                            <div class="text-sm text-gray-900 space-y-1">
                                @if(!empty($subscription['endcust_address1']))
                                    <div>{{ $subscription['endcust_address1'] }}</div>
                                @endif
                                @if(!empty($subscription['endcust_address2']))
                                    <div>{{ $subscription['endcust_address2'] }}</div>
                                @endif
                                @if(!empty($subscription['endcust_address3']))
                                    <div>{{ $subscription['endcust_address3'] }}</div>
                                @endif
                                <div>
                                    {{ $subscription['endcust_city'] ?? '' }}
                                    @if(!empty($subscription['endcust_state_province']) && !empty($subscription['endcust_city']))
                                        , {{ $subscription['endcust_state_province'] }}
                                    @elseif(!empty($subscription['endcust_state_province']))
                                        {{ $subscription['endcust_state_province'] }}
                                    @endif
                                    {{ $subscription['endcust_postal_code'] ?? '' }}
                                </div>
                                @if(!empty($subscription['endcust_country']))
                                    <div>{{ $subscription['endcust_country'] }}</div>
                                @endif
                                @if(empty($subscription['endcust_address1']) && empty($subscription['endcust_city']) && empty($subscription['endcust_country']))
                                    <div class="text-gray-500">No address information available</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Details (Collapsible) -->
            <div x-data="{ expanded: false }" class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <button @click="expanded = !expanded"
                            class="flex items-center justify-between w-full text-left">
                        <h2 class="text-lg font-medium text-gray-900">Additional Details</h2>
                        <svg :class="{'rotate-180': expanded}"
                             class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div x-show="expanded" x-transition class="px-6 py-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Billing Behavior</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_billingBehavior'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Billing Frequency</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_billingFrequency'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Intended Usage</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_intendedUsage'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Connectivity</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_connectivity'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Service Plan</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_servicePlan'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Opportunity Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $subscription['subs_opportunityNumber'] ?? 'N/A' }}</dd>
                        </div>
                    </div>

                    <!-- Partner Information -->
                    @if(!empty($subscription['soldto_name']) || !empty($subscription['solpro_name']) || !empty($subscription['resell_name']))
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h3 class="text-sm font-medium text-gray-900 mb-4">Partner Information</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                @if(!empty($subscription['soldto_name']))
                                    <div>
                                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wide">Sold To</h4>
                                        <div class="mt-2 text-sm text-gray-900">{{ $subscription['soldto_name'] }}</div>
                                        @if(!empty($subscription['soldto_account_csn']))
                                            <div class="text-xs text-gray-500">CSN: {{ $subscription['soldto_account_csn'] }}</div>
                                        @endif
                                    </div>
                                @endif
                                @if(!empty($subscription['solpro_name']))
                                    <div>
                                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wide">Solution Provider</h4>
                                        <div class="mt-2 text-sm text-gray-900">{{ $subscription['solpro_name'] }}</div>
                                        @if(!empty($subscription['solpro_account_csn']))
                                            <div class="text-xs text-gray-500">CSN: {{ $subscription['solpro_account_csn'] }}</div>
                                        @endif
                                    </div>
                                @endif
                                @if(!empty($subscription['resell_name']))
                                    <div>
                                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wide">Nurture Reseller</h4>
                                        <div class="mt-2 text-sm text-gray-900">{{ $subscription['resell_name'] }}</div>
                                        @if(!empty($subscription['resell_account_csn']))
                                            <div class="text-xs text-gray-500">CSN: {{ $subscription['resell_account_csn'] }}</div>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Right Column - Timeline and Actions -->
        <div class="space-y-6">
            <!-- Quick Actions Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Quick Actions</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    @if(isset($subscription['subs_enddatediff']) && $subscription['subs_enddatediff'] <= 90 && $subscription['subs_enddatediff'] >= -30)
                        <button type="button"
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                hx-post="{{ APP_ROOT }}/api/subscriptions/create_quote"
                                hx-swap="innerHTML"
                                hx-target="#modal_body"
                                hx-vals='{{ json_encode([
                                    "sub_num" => $subscription['subs_subscriptionReferenceNumber'] ?? '',
                                    "subs_subscriptionId" => $subscription['subs_subscriptionId'] ?? '',
                                    "sub_id" => $subscription['subs_id'] ?? '',
                                    "user_id" => $_SESSION['user_id'] ?? ''
                                ]) }}'
                                @click="showModal = true">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Create Renewal Quote
                        </button>
                    @endif

                    <button type="button"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="window.print()">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                            </svg>
                        Print Details
                    </button>

                    <button type="button"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="navigator.clipboard.writeText('{{ $subscription['subs_subscriptionReferenceNumber'] ?? '' }}')">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy Reference
                    </button>
                </div>
            </div>

            <!-- Timeline Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Activity Timeline</h2>
                </div>
                <div class="px-6 py-4">
                    @if(!empty($histdata))
                        <x-component-activity-feed :activity="$histdata" />
                    @else
                        <div class="text-center py-6">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No activity yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Activity and communications will appear here.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Matched Data Card (if available) -->
            @if(!empty($matched_data))
                <div class="bg-blue-50 shadow-sm rounded-lg border border-blue-200">
                    <div class="px-6 py-4 border-b border-blue-200">
                        <h2 class="text-lg font-medium text-blue-900">Manual Entry Data</h2>
                        <p class="mt-1 text-sm text-blue-700">Additional information from manual entries</p>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="space-y-3">
                            @foreach($matched_data as $key => $value)
                                @if(!empty($value))
                                    <div>
                                        <dt class="text-sm font-medium text-blue-700">{{ ucwords(str_replace('_', ' ', $key)) }}</dt>
                                        <dd class="mt-1 text-sm text-blue-900">{{ $value }}</dd>
                                    </div>
                                @endif
                            @endforeach
                        </dl>
                    </div>
                </div>
            @endif

            <!-- System Information Card -->
            <div x-data="{ expanded: false }" class="bg-gray-50 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <button @click="expanded = !expanded"
                            class="flex items-center justify-between w-full text-left">
                        <h2 class="text-lg font-medium text-gray-700">System Information</h2>
                        <svg :class="{'rotate-180': expanded}"
                             class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div x-show="expanded" x-transition class="px-6 py-4">
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Subscription ID</dt>
                            <dd class="mt-1 text-sm text-gray-700 font-mono">{{ $subscription['subs_subscriptionId'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Record Type</dt>
                            <dd class="mt-1 text-sm text-gray-700">{{ $subscription['subs_recordType'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Offering ID</dt>
                            <dd class="mt-1 text-sm text-gray-700 font-mono">{{ $subscription['subs_offeringId'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Offering Code</dt>
                            <dd class="mt-1 text-sm text-gray-700 font-mono">{{ $subscription['subs_offeringCode'] ?? 'N/A' }}</dd>
                        </div>
                        @if(!empty($subscription['subs_created_at']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created</dt>
                                <dd class="mt-1 text-sm text-gray-700">{{ $subscription['subs_created_at'] }}</dd>
                            </div>
                        @endif
                        @if(!empty($subscription['subs_last_modified']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Modified</dt>
                                <dd class="mt-1 text-sm text-gray-700">{{ $subscription['subs_last_modified'] }}</dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print { display: none !important; }
    .print-break { page-break-before: always; }
    body { font-size: 12px; }
    .shadow-sm { box-shadow: none !important; }
    .bg-white { background: white !important; }
    .text-indigo-600 { color: #000 !important; }
    .bg-indigo-600 { background: #000 !important; }
    .border-gray-200 { border-color: #ccc !important; }
}
</style>