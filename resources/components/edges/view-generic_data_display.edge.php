@props([
    'title' => 'Data Display',
    'description' => '',
    'data' => [],
    'metadata' => [],
    'class' => '',
    'table_name' => '',
    'source_info' => []
])

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header Section -->
    <div class="bg-white shadow-sm rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 truncate">
                        {{ $title }}
                    </h1>
                    @if(!empty($description))
                        <p class="mt-1 text-sm text-gray-500">{{ $description }}</p>
                    @endif
                    @if(!empty($source_info))
                        <div class="mt-2 flex flex-wrap gap-2">
                            @if(!empty($source_info['table']))
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Table: {{ $source_info['table'] }}
                                </span>
                            @endif
                            @if(!empty($source_info['source']))
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Source: {{ $source_info['source'] }}
                                </span>
                            @endif
                        </div>
                    @endif
                </div>
                <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-2">
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="window.print()">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Print
                    </button>
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="navigator.clipboard.writeText(JSON.stringify({{ json_encode($data) }}, null, 2))">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy Data
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Primary Data -->
        <div class="lg:col-span-2 space-y-6">
            @if(!empty($data))
                @php
                    // Group fields by type for better organization
                    $primary_fields = [];
                    $secondary_fields = [];
                    $system_fields = [];
                    
                    foreach ($data as $key => $value) {
                        // Skip empty values for cleaner display
                        if (is_null($value) || $value === '') continue;
                        
                        // Categorize fields based on naming patterns
                        $lower_key = strtolower($key);
                        if (strpos($lower_key, '_id') !== false || 
                            strpos($lower_key, 'created') !== false || 
                            strpos($lower_key, 'modified') !== false ||
                            strpos($lower_key, 'updated') !== false) {
                            $system_fields[$key] = $value;
                        } elseif (strpos($lower_key, 'address') !== false || 
                                  strpos($lower_key, 'city') !== false || 
                                  strpos($lower_key, 'state') !== false || 
                                  strpos($lower_key, 'country') !== false ||
                                  strpos($lower_key, 'postal') !== false ||
                                  strpos($lower_key, 'phone') !== false ||
                                  strpos($lower_key, 'email') !== false) {
                            $secondary_fields[$key] = $value;
                        } else {
                            $primary_fields[$key] = $value;
                        }
                    }
                @endphp

                <!-- Primary Information Card -->
                @if(!empty($primary_fields))
                    <div class="bg-white shadow-sm rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Primary Information</h2>
                        </div>
                        <div class="px-6 py-4">
                            <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                @foreach($primary_fields as $key => $value)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ ucwords(str_replace(['_', '-'], ' ', $key)) }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if(is_array($value))
                                                <pre class="text-xs bg-gray-50 p-2 rounded">{{ json_encode($value, JSON_PRETTY_PRINT) }}</pre>
                                            @elseif(is_bool($value))
                                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                    {{ $value ? 'Yes' : 'No' }}
                                                </span>
                                            @elseif(filter_var($value, FILTER_VALIDATE_EMAIL))
                                                <a href="mailto:{{ $value }}" class="text-indigo-600 hover:text-indigo-500">{{ $value }}</a>
                                            @elseif(filter_var($value, FILTER_VALIDATE_URL))
                                                <a href="{{ $value }}" target="_blank" class="text-indigo-600 hover:text-indigo-500">{{ $value }}</a>
                                            @else
                                                {{ $value }}
                                            @endif
                                        </dd>
                                    </div>
                                @endforeach
                            </dl>
                        </div>
                    </div>
                @endif

                <!-- Contact/Address Information Card -->
                @if(!empty($secondary_fields))
                    <div class="bg-white shadow-sm rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Contact & Address Information</h2>
                        </div>
                        <div class="px-6 py-4">
                            <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                @foreach($secondary_fields as $key => $value)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ ucwords(str_replace(['_', '-'], ' ', $key)) }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if(filter_var($value, FILTER_VALIDATE_EMAIL))
                                                <a href="mailto:{{ $value }}" class="text-indigo-600 hover:text-indigo-500">{{ $value }}</a>
                                            @elseif(filter_var($value, FILTER_VALIDATE_URL))
                                                <a href="{{ $value }}" target="_blank" class="text-indigo-600 hover:text-indigo-500">{{ $value }}</a>
                                            @else
                                                {{ $value }}
                                            @endif
                                        </dd>
                                    </div>
                                @endforeach
                            </dl>
                        </div>
                    </div>
                @endif

                <!-- Raw Data Card (Collapsible) -->
                <div x-data="{ expanded: false }" class="bg-white shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <button @click="expanded = !expanded"
                                class="flex items-center justify-between w-full text-left">
                            <h2 class="text-lg font-medium text-gray-900">Raw Data</h2>
                            <svg :class="{'rotate-180': expanded}"
                                 class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div x-show="expanded" x-transition class="px-6 py-4">
                        <pre class="text-xs bg-gray-50 p-4 rounded overflow-x-auto">{{ json_encode($data, JSON_PRETTY_PRINT) }}</pre>
                    </div>
                </div>
            @else
                <div class="bg-white shadow-sm rounded-lg">
                    <div class="px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                        <p class="mt-1 text-sm text-gray-500">No data was found for this record.</p>
                    </div>
                </div>
            @endif
        </div>

        <!-- Right Column - Metadata and System Info -->
        <div class="space-y-6">
            <!-- Metadata Card -->
            @if(!empty($metadata))
                <div class="bg-white shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Metadata</h2>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="space-y-3">
                            @foreach($metadata as $key => $value)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ ucwords(str_replace(['_', '-'], ' ', $key)) }}</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $value }}</dd>
                                </div>
                            @endforeach
                        </dl>
                    </div>
                </div>
            @endif

            <!-- System Information Card -->
            @if(!empty($system_fields))
                <div x-data="{ expanded: false }" class="bg-gray-50 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <button @click="expanded = !expanded"
                                class="flex items-center justify-between w-full text-left">
                            <h2 class="text-lg font-medium text-gray-700">System Information</h2>
                            <svg :class="{'rotate-180': expanded}"
                                 class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div x-show="expanded" x-transition class="px-6 py-4">
                        <dl class="space-y-3">
                            @foreach($system_fields as $key => $value)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ ucwords(str_replace(['_', '-'], ' ', $key)) }}</dt>
                                    <dd class="mt-1 text-sm text-gray-700 font-mono">{{ $value }}</dd>
                                </div>
                            @endforeach
                        </dl>
                    </div>
                </div>
            @endif

            <!-- Quick Actions Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Quick Actions</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <button type="button"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="window.print()">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Print Details
                    </button>

                    <button type="button"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="navigator.clipboard.writeText(JSON.stringify({{ json_encode($data) }}, null, 2))">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy JSON
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print { display: none !important; }
    .print-break { page-break-before: always; }
    body { font-size: 12px; }
    .shadow-sm { box-shadow: none !important; }
    .bg-white { background: white !important; }
    .text-indigo-600 { color: #000 !important; }
    .bg-indigo-600 { background: #000 !important; }
    .border-gray-200 { border-color: #ccc !important; }
    button { display: none !important; }
}
</style>
