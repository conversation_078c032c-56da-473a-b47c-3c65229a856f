@props([
    'message' => 'Quote submitted successfully!',
    'quote_id' => null
])

<x-alert-box type="success" title="{{ $message }}">
    @if($quote_id)
        <p class="mt-2">
            Your quote ID is: <span class="font-semibold">{{ $quote_id }}</span>
        </p>
    @endif
    <div class="mt-4">
        <div class="flex">
            <x-forms-button
                label="Create Another Quote"
                variant="secondary"
                type="button"
                extra_attributes="hx-get='{{ APP_ROOT }}/api/quote-v3/new' hx-target='#quote_form_container'"
                class_suffix="bg-green-50 text-green-800 hover:bg-green-100 focus:ring-green-600 focus:ring-offset-green-50"
            />
            <x-forms-button
                label="View All Quotes"
                variant="secondary"
                type="button"
                extra_attributes="hx-get='quotes' hx-target='#main-content'"
                class_suffix="ml-3 bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:ring-indigo-600"
            />
        </div>
    </div>
</x-alert-box>
