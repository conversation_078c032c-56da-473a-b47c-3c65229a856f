<?php
//$subs = $autodesk->subscriptions->get_renewable();
//$email_send_rules = [1,3,5,10,15,30,60,90]; //days remaing to send a reminder email.  if the subscription is within this many days of expiring, send an email.
$path = [];
 $path['fs_app_root'] = __DIR__;
define('FS_APP_ROOT', $path['fs_app_root']);

require_once("resources/functions/functions.php");
require_once("resources/functions/database.php");
require_once("resources/classes/autodesk_api.class.php");
$autodesk = new autodesk_api();

$email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
$settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days'); // Array of booleans [true, false, ...]
$settings_time = (int) autodesk_api::database_get_storage('subscription_renew_email_send_time'); // Integer, hour of the day (0-23)

$now = new DateTime();
$current_day_of_week = (int) $now->format('w'); // 0 for Sunday, 1 for Monday, ..., 6 for Saturday
$current_hour = (int) $now->format('G'); // Current hour in 24-hour format
$current_minute = (int) $now->format('i'); // Current minute

// Check if today is a send day
if (!$settings_days[$current_day_of_week]) {
    die('Not a send day.');
}

// Check if the current time is within ±10 minutes of the send hour
$send_hour = $settings_time;
$send_minute_start = $send_hour * 60 - 10; // Start time in minutes from midnight
$send_minute_end = $send_hour * 60 + 10;   // End time in minutes from midnight
$current_time_in_minutes = $current_hour * 60 + $current_minute;

if ($current_time_in_minutes < $send_minute_start || $current_time_in_minutes > $send_minute_end) {
    die('Not within the send time window.');
}

// If execution reaches here, it is a valid send time
echo 'Email can be sent.';

//$autodesk = new autodesk_api();


$input_params = array_merge($_GET, $_POST);
include($path['fs_app_root'] . DIRECTORY_SEPARATOR . "system/paths.php");

$tcs_database = str_replace('.', '', DOMAIN);

// Database connection details
$db_server = 'localhost';
$db_username = $tcs_database;
$db_password = 'S96#1kvYuCGE';
$db_database = $tcs_database;

$db = tep_db_connect($db_server, $db_username, $db_password, $db_database) or die('Unable to connect to database server!');


$subs = $autodesk->subscriptions->get_renewable();
//shuffle($subs);
$file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/subscriptions/email_history/reminder_email.view.php';
echo "Loading " . $file_path;
$email_template = file_get_contents($file_path);
if ($email_template === false) {
    echo "<br> Failed to load email template from {$file_path}";
}

// Split the content by lines
$lines = explode("\n", $email_template);

$email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
$settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
$settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');
//print_rr(i:$subs,co:false);
// Check if it's time to send an email.
echo "<br>There are " . count($subs) . " subs to process";
$count = 0;
foreach ($subs as $sub) {

    if ($sub['tcs_unsubscribe'] == 1) continue;
    $now = new DateTime();
    $end_date = new DateTime($sub['endDate']);
    $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
    $last_sent = new DateTime($date_last_sent);
    $days_remaining = $now->diff($end_date)->days;
    $days_since_last_sent = $now->diff($last_sent)->days;
    foreach ($email_rules as $key => $rule) {
        if ($days_remaining <= $rule) {

            if ((($days_remaining + $days_since_last_sent) > $rule)) {
                echo "<br>";
                echo "<br> Processing subscription $count" . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $days_since_last_sent . ": ";
                echo "<br>  is below rule $rule: sending email . <br> " . PHP_EOL;
                $count++;
                if ($count > 5) break;
                $autodesk->subscriptions->send_reminder_email($sub, $rule);
                //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
            }
            break;
        }
    }
}
