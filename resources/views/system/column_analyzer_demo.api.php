<?php

/**
 * Column Analyzer Demo API
 * 
 * Provides API endpoints to demonstrate the intelligent column naming system
 */

namespace api\system\column_analyzer_demo;

use system\database;
use Edge\Edge;

/**
 * Analyze a specific table's columns
 */
function analyze_table($p) {
    $table_name = $p['table_name'] ?? '';
    
    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }
    
    try {
        // Check if table exists
        if (!database::tableExists($table_name)) {
            return json_encode(['error' => "Table '{$table_name}' does not exist"]);
        }
        
        // Get column names
        $schema_query = "DESCRIBE `{$table_name}`";
        $schema_result = database::rawQuery($schema_query);
        $columns = $schema_result->fetchAll(\PDO::FETCH_ASSOC);
        $column_names = array_column($columns, 'Field');
        
        // Analyze columns
        $analysis_results = \column_analyzer::analyze_table_columns($table_name, $column_names);
        
        // Format for JSON response
        $response = [
            'success' => true,
            'table_name' => $table_name,
            'summary' => [
                'total_columns' => $analysis_results['total_columns'],
                'analyzed_columns' => $analysis_results['analyzed_columns'],
                'suggestions_count' => count($analysis_results['suggestions'])
            ],
            'suggestions' => [],
            'all_results' => []
        ];
        
        // Format suggestions
        foreach ($analysis_results['suggestions'] as $column => $result) {
            $response['suggestions'][] = [
                'original_name' => $result['original_name'],
                'suggested_name' => $result['suggested_name'],
                'confidence' => round($result['confidence'], 1),
                'reasoning' => $result['reasoning']
            ];
        }
        
        // Format all results for detailed view
        foreach ($analysis_results['all_results'] as $column => $result) {
            $response['all_results'][] = [
                'column_name' => $column,
                'original_name' => $result['original_name'],
                'suggested_name' => $result['suggested_name'],
                'confidence' => round($result['confidence'], 1),
                'reasoning' => $result['reasoning'],
                'analysis_performed' => $result['analysis_performed']
            ];
        }
        
        return json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return json_encode(['error' => 'Analysis failed: ' . $e->getMessage()]);
    }
}

/**
 * Get list of available tables for analysis
 */
function get_tables($p) {
    try {
        $query = "SHOW TABLES";
        $result = database::rawQuery($query);
        $tables = $result->fetchAll(\PDO::FETCH_COLUMN);
        
        // Filter to relevant tables (exclude system tables)
        $relevant_tables = array_filter($tables, function($table) {
            return !in_array($table, ['information_schema', 'mysql', 'performance_schema', 'sys']) &&
                   !str_starts_with($table, 'temp_');
        });
        
        return json_encode([
            'success' => true,
            'tables' => array_values($relevant_tables),
            'count' => count($relevant_tables)
        ], JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return json_encode(['error' => 'Failed to get tables: ' . $e->getMessage()]);
    }
}

/**
 * Analyze a single column
 */
function analyze_column($p) {
    $table_name = $p['table_name'] ?? '';
    $column_name = $p['column_name'] ?? '';
    
    if (empty($table_name) || empty($column_name)) {
        return json_encode(['error' => 'Table name and column name are required']);
    }
    
    try {
        // Get all column names for context
        $schema_query = "DESCRIBE `{$table_name}`";
        $schema_result = database::rawQuery($schema_query);
        $columns = $schema_result->fetchAll(\PDO::FETCH_ASSOC);
        $column_names = array_column($columns, 'Field');
        
        // Check if column exists
        if (!in_array($column_name, $column_names)) {
            return json_encode(['error' => "Column '{$column_name}' does not exist in table '{$table_name}'"]);
        }
        
        // Analyze the specific column
        $analysis = \column_analyzer::analyze_column($table_name, $column_name, $column_names);
        
        // Get column statistics
        $stats = database::getColumnStats($table_name, $column_name);
        
        $response = [
            'success' => true,
            'table_name' => $table_name,
            'column_name' => $column_name,
            'analysis' => [
                'original_name' => $analysis['original_name'],
                'suggested_name' => $analysis['suggested_name'],
                'confidence' => round($analysis['confidence'], 1),
                'reasoning' => $analysis['reasoning'],
                'analysis_performed' => $analysis['analysis_performed']
            ],
            'statistics' => $stats
        ];
        
        // Add detailed analysis if available
        if (isset($analysis['detailed_analysis'])) {
            $response['detailed_analysis'] = $analysis['detailed_analysis'];
        }
        
        return json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return json_encode(['error' => 'Analysis failed: ' . $e->getMessage()]);
    }
}

/**
 * Generate demo data table with intelligent column names
 */
function demo_data_table($p) {
    $table_name = $p['table_name'] ?? 'autodesk_accounts';
    
    try {
        // Use the enhanced data table generator with intelligent naming
        $options = [
            'use_intelligent_naming' => true,
            'title' => "Demo: Intelligent Column Names for {$table_name}",
            'description' => 'This table demonstrates the intelligent column naming system',
            'items_per_page' => 10
        ];
        
        $criteria = [
            'limit' => 10,
            'order_by' => 'id',
            'order_direction' => 'asc'
        ];
        
        // Generate the data table
        $table_config = \data_table_generator::generate_table_config($table_name, $criteria, $options);
        
        if (isset($table_config['error'])) {
            return '<div class="alert alert-danger">Error: ' . htmlspecialchars($table_config['error']) . '</div>';
        }
        
        // Render the table
        $html = \data_table_generator::render_data_table($table_config['config']);
        
        // Add some explanation
        $explanation = '
        <div class="alert alert-info mb-3">
            <h5>🧠 Intelligent Column Naming Demo</h5>
            <p>This table uses the intelligent column naming system to automatically suggest better column labels based on:</p>
            <ul>
                <li><strong>Column Name Analysis (30%)</strong> - Pattern matching with known column types</li>
                <li><strong>Data Pattern Analysis (50%)</strong> - Analysis of actual data content</li>
                <li><strong>Context Analysis (20%)</strong> - Relationship with other columns in the table</li>
            </ul>
            <p>Compare the column headers with the original database column names to see the improvements.</p>
        </div>';
        
        return $explanation . $html;
        
    } catch (\Exception $e) {
        return '<div class="alert alert-danger">Error generating demo table: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Show configuration and patterns
 */
function show_config($p) {
    try {
        $mapping_rules = require(FS_SYSTEM . DS . 'config' . DS . 'column_mapping_rules.php');
        $data_patterns = require(FS_SYSTEM . DS . 'config' . DS . 'data_patterns.php');
        
        $response = [
            'success' => true,
            'configuration' => [
                'standard_column_names' => array_keys($mapping_rules['standard_column_names']),
                'exact_matches' => array_keys($mapping_rules['column_name_patterns']['exact_matches']),
                'partial_matches' => array_keys($mapping_rules['column_name_patterns']['partial_matches']),
                'generic_patterns' => array_keys($mapping_rules['column_name_patterns']['generic_patterns']),
                'regex_patterns' => array_keys($data_patterns['regex_patterns']),
                'keyword_lists' => array_keys($data_patterns['keyword_lists']),
                'analysis_weights' => $data_patterns['analysis_weights'],
                'decision_thresholds' => $data_patterns['decision_thresholds']
            ]
        ];
        
        return json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return json_encode(['error' => 'Failed to load configuration: ' . $e->getMessage()]);
    }
}
