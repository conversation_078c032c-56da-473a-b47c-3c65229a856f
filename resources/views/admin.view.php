<?php
/**
 * Admin Views Router
 * Routes admin-related view requests to appropriate handlers
 */

// Ensure user has admin access using proper role hierarchy
use system\users;
users::requireRole('admin');

// Get the sub-route from the URL
$path_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
$admin_route = '';

// Find the admin route part
$found_admin = false;
foreach ($path_parts as $part) {
    if ($found_admin) {
        $admin_route = $part;
        break;
    }
    if ($part === 'admin') {
        $found_admin = true;
    }
}

// Route to appropriate view
switch ($admin_route) {
    case 'subscription_matching_rules':
        include __DIR__ . '/admin/subscription_matching_rules.view.php';
        break;

    case 'fix_autodesk_hashes.view.php':
    case 'fix_autodesk_hashes':
        include __DIR__ . '/../system/views/admin/fix_autodesk_hashes.view.php';
        break;
        
    case '':
    case 'dashboard':
        // Admin dashboard
        ?>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                <p class="mt-2 text-sm text-gray-600">System administration and configuration</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Subscription Matching Rules -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Subscription Matching</dt>
                                    <dd class="text-lg font-medium text-gray-900">Rules Manager</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-5">
                            <div class="rounded-md shadow">
                                <a href="<?= APP_ROOT ?>/admin/subscription_matching_rules" 
                                   class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                    Configure Rules
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- More admin tools can be added here -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">System</dt>
                                    <dd class="text-lg font-medium text-gray-900">Analytics</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-5">
                            <div class="rounded-md shadow">
                                <a href="#" 
                                   class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                    View Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hash Fix Tool -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 12 2 2 4-4" />
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Database Tools</dt>
                                    <dd class="text-lg font-medium text-gray-900">Hash Fix Tool</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-5">
                            <div class="rounded-md shadow">
                                <a href="<?= APP_ROOT ?>/admin/fix_autodesk_hashes"
                                   class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                                    Fix Autodesk Hashes
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        break;
        
    default:
        http_response_code(404);
        ?>
        <div class="text-center py-12">
            <h2 class="text-xl text-gray-900">Page Not Found</h2>
            <p class="text-gray-600">The admin page "<?= htmlspecialchars($admin_route) ?>" was not found.</p>
            <a href="<?= APP_ROOT ?>/admin" class="mt-4 inline-block text-indigo-600 hover:text-indigo-500">
                Return to Admin Dashboard
            </a>
        </div>
        <?php
        break;
}
?>
