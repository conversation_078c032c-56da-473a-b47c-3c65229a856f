<?php
namespace api;

use autodesk_api\autodesk_api;
use Edge\Edge;

// Include the specific API files
include_once(FS_RESOURCES . '/quotes.api.php');
include_once(FS_VIEWS . '/customers/customers.api.php');
include_once(FS_RESOURCES . '/subscriptions.api.php');
include_once(FS_RESOURCES . '/generic_view.api.php');

/**
 * General view API handler that routes to appropriate view functions
 * based on the context or parameters provided
 */
function view($p) {
    // Check if this is a generic view request
    if (isset($p['table_name']) || isset($p['generic'])) {
        return \api\generic_view($p);
    }

    // Determine which view function to call based on parameters

    // Check for quote-specific parameters
    if (isset($p['quote_number']) || (isset($p['id']) && isset($p['quote_context']))) {
        return \api\autodesk\quotes\view($p);
    }

    // Check for customer-specific parameters
    if (isset($p['csn']) || (isset($p['id']) && isset($p['customer_context']))) {
        return \api\customers\view($p);
    }

    // Check for subscription-specific parameters
    if (isset($p['subscription_number']) || isset($p['subscription_id']) || (isset($p['id']) && isset($p['subscription_context']))) {
        return \api\subscriptions\view($p);
    }

    // Try to determine context from the HTTP referer or source page
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $source_page = defined('SOURCE_PAGE') ? SOURCE_PAGE : '';

    if (strpos($referer, '/quotes') !== false || $source_page === 'quotes') {
        return \api\autodesk\quotes\view($p);
    }

    if (strpos($referer, '/customers') !== false || $source_page === 'customers') {
        return \api\customers\view($p);
    }

    if (strpos($referer, '/subscriptions') !== false || $source_page === 'subscriptions') {
        return \api\subscriptions\view($p);
    }

    // Default fallback - return error message
    echo Edge::render('layout-card', [
        'content' => '<div class="p-4 text-red-600">Unable to determine view context. Please specify the appropriate parameters.</div>'
    ]);
}
