<?php
namespace api;

use Edge\Edge;
use data_table;

/**
 * Generic view API for displaying any datatable row data
 * This function can be used by any datatable action button to display row details
 */
function generic_view($p) {
    // Extract parameters
    $table_name = $p['table_name'] ?? '';
    $record_id = $p['id'] ?? $p['record_id'] ?? '';
    $id_field = $p['id_field'] ?? 'id';
    $data_source = $p['data_source'] ?? null;
    
    // If no table name provided, try to determine from context
    if (empty($table_name)) {
        // Try to get from HTTP referer or session
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        if (preg_match('/\/([^\/]+)$/', $referer, $matches)) {
            $table_name = $matches[1];
        }
    }
    
    if (empty($table_name) || empty($record_id)) {
        echo Edge::render('layout-card', [
            'content' => '<div class="p-4 text-red-600">Missing required parameters: table_name and record_id</div>'
        ]);
        return;
    }
    
    try {
        // Get the data source configuration if available
        $data_source_config = null;
        if ($data_source) {
            $data_source_config = get_data_source_config($data_source);
        }
        
        // Get the record data
        $record_data = get_record_data($table_name, $record_id, $id_field, $data_source_config);
        
        if (empty($record_data)) {
            echo Edge::render('layout-card', [
                'content' => '<div class="p-4 text-yellow-600">No data found for the specified record.</div>'
            ]);
            return;
        }
        
        // Prepare metadata
        $metadata = [
            'table_name' => $table_name,
            'record_id' => $record_id,
            'id_field' => $id_field,
            'retrieved_at' => date('Y-m-d H:i:s')
        ];
        
        if ($data_source_config) {
            $metadata['data_source'] = $data_source_config['name'] ?? $data_source;
            $metadata['data_source_type'] = $data_source_config['type'] ?? 'unknown';
        }
        
        // Prepare source info
        $source_info = [
            'table' => $table_name,
            'source' => $data_source_config['name'] ?? 'Database'
        ];
        
        // Generate a meaningful title
        $title = generate_record_title($record_data, $table_name);
        
        // Render the generic display template
        echo Edge::render('view-generic_data_display', [
            'title' => $title,
            'description' => "Detailed view of record from {$table_name}",
            'data' => $record_data,
            'metadata' => $metadata,
            'source_info' => $source_info,
            'table_name' => $table_name
        ]);
        
    } catch (Exception $e) {
        echo Edge::render('layout-card', [
            'content' => '<div class="p-4 text-red-600">Error retrieving record: ' . htmlspecialchars($e->getMessage()) . '</div>'
        ]);
    }
}

/**
 * Get record data from various sources
 */
function get_record_data($table_name, $record_id, $id_field = 'id', $data_source_config = null) {
    // If we have a data source configuration, use it
    if ($data_source_config) {
        return get_data_source_record($data_source_config, $record_id, $id_field);
    }
    
    // Try to get from database table
    return get_database_record($table_name, $record_id, $id_field);
}

/**
 * Get record from data source (CSV, API, etc.)
 */
function get_data_source_record($config, $record_id, $id_field) {
    switch ($config['type']) {
        case 'csv':
            return get_csv_record($config, $record_id, $id_field);
        case 'api':
            return get_api_record($config, $record_id, $id_field);
        case 'database':
            return get_database_record($config['table'], $record_id, $id_field);
        default:
            throw new Exception("Unsupported data source type: " . $config['type']);
    }
}

/**
 * Get record from CSV data source
 */
function get_csv_record($config, $record_id, $id_field) {
    $csv_file = $config['file_path'] ?? '';
    if (!file_exists($csv_file)) {
        throw new Exception("CSV file not found: " . $csv_file);
    }
    
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        throw new Exception("Cannot open CSV file: " . $csv_file);
    }
    
    // Get headers
    $headers = fgetcsv($handle);
    if (!$headers) {
        fclose($handle);
        throw new Exception("Cannot read CSV headers");
    }
    
    // Find the ID field index
    $id_index = array_search($id_field, $headers);
    if ($id_index === false) {
        fclose($handle);
        throw new Exception("ID field '{$id_field}' not found in CSV");
    }
    
    // Search for the record
    while (($row = fgetcsv($handle)) !== false) {
        if (isset($row[$id_index]) && $row[$id_index] == $record_id) {
            fclose($handle);
            return array_combine($headers, $row);
        }
    }
    
    fclose($handle);
    return null;
}

/**
 * Get record from API data source
 */
function get_api_record($config, $record_id, $id_field) {
    // This would need to be implemented based on specific API requirements
    throw new Exception("API data source not yet implemented");
}

/**
 * Get record from database table
 */
function get_database_record($table_name, $record_id, $id_field) {
    global $db;

    // For test tables or hardcoded data, try to get from session/cache first
    if (strpos($table_name, '_test') !== false || strpos($table_name, 'test_') !== false) {
        return get_test_record($table_name, $record_id, $id_field);
    }

    if (!$db) {
        throw new Exception("Database connection not available");
    }

    // Sanitize table name to prevent SQL injection
    $table_name = preg_replace('/[^a-zA-Z0-9_]/', '', $table_name);
    $id_field = preg_replace('/[^a-zA-Z0-9_]/', '', $id_field);

    try {
        $sql = "SELECT * FROM `{$table_name}` WHERE `{$id_field}` = :record_id LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':record_id', $record_id);
        $stmt->execute();

        return $stmt->fetch(\PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // If table doesn't exist, try to get from test data
        return get_test_record($table_name, $record_id, $id_field);
    }
}

/**
 * Get record from test/hardcoded data
 */
function get_test_record($table_name, $record_id, $id_field) {
    // For the generic view test, return sample data
    if ($table_name === 'employees_test' || $table_name === 'generic_view_test_table') {
        $test_data = [
            [
                'id' => 1,
                'employee_id' => 'EMP001',
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '************',
                'department' => 'Engineering',
                'position' => 'Senior Developer',
                'salary' => 85000,
                'hire_date' => '2020-03-15',
                'is_active' => true,
                'manager_id' => 5,
                'address1' => '123 Main Street',
                'address2' => 'Apt 4B',
                'city' => 'San Francisco',
                'state' => 'CA',
                'postal_code' => '94105',
                'country' => 'USA',
                'emergency_contact_name' => 'Jane Doe',
                'emergency_contact_phone' => '************',
                'created_at' => '2020-03-15 09:00:00',
                'updated_at' => '2024-01-15 14:30:00'
            ],
            [
                'id' => 2,
                'employee_id' => 'EMP002',
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '************',
                'department' => 'Marketing',
                'position' => 'Marketing Manager',
                'salary' => 75000,
                'hire_date' => '2019-08-22',
                'is_active' => true,
                'manager_id' => 3,
                'address1' => '456 Oak Avenue',
                'address2' => '',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'postal_code' => '90210',
                'country' => 'USA',
                'emergency_contact_name' => 'Bob Smith',
                'emergency_contact_phone' => '************',
                'created_at' => '2019-08-22 10:15:00',
                'updated_at' => '2024-01-10 11:20:00'
            ],
            [
                'id' => 3,
                'employee_id' => 'EMP003',
                'name' => 'Bob Johnson',
                'email' => '<EMAIL>',
                'phone' => '************',
                'department' => 'Sales',
                'position' => 'Sales Representative',
                'salary' => 65000,
                'hire_date' => '2021-11-08',
                'is_active' => false,
                'manager_id' => 4,
                'address1' => '789 Pine Street',
                'address2' => 'Suite 200',
                'city' => 'Seattle',
                'state' => 'WA',
                'postal_code' => '98101',
                'country' => 'USA',
                'emergency_contact_name' => 'Alice Johnson',
                'emergency_contact_phone' => '************',
                'created_at' => '2021-11-08 13:45:00',
                'updated_at' => '2023-12-20 16:10:00'
            ],
            [
                'id' => 4,
                'employee_id' => 'EMP004',
                'name' => 'Alice Williams',
                'email' => '<EMAIL>',
                'phone' => '************',
                'department' => 'Human Resources',
                'position' => 'HR Specialist',
                'salary' => 70000,
                'hire_date' => '2018-05-14',
                'is_active' => true,
                'manager_id' => null,
                'address1' => '321 Elm Drive',
                'address2' => '',
                'city' => 'Portland',
                'state' => 'OR',
                'postal_code' => '97201',
                'country' => 'USA',
                'emergency_contact_name' => 'Charlie Williams',
                'emergency_contact_phone' => '************',
                'created_at' => '2018-05-14 08:30:00',
                'updated_at' => '2024-01-05 09:45:00'
            ]
        ];

        // Find the record with matching ID
        foreach ($test_data as $record) {
            if (isset($record[$id_field]) && $record[$id_field] == $record_id) {
                return $record;
            }
        }
    }

    return null;
}

/**
 * Get data source configuration
 */
function get_data_source_config($data_source_id) {
    // This would typically load from a configuration file or database
    // For now, return null to fall back to database lookup
    return null;
}

/**
 * Generate a meaningful title for the record
 */
function generate_record_title($record_data, $table_name) {
    if (empty($record_data)) {
        return "Record Details";
    }
    
    // Common name fields to look for
    $name_fields = ['name', 'title', 'label', 'description', 'subject'];
    
    foreach ($name_fields as $field) {
        if (isset($record_data[$field]) && !empty($record_data[$field])) {
            return $record_data[$field];
        }
    }
    
    // Look for fields containing 'name'
    foreach ($record_data as $key => $value) {
        if (stripos($key, 'name') !== false && !empty($value)) {
            return $value;
        }
    }
    
    // Look for email or identifier
    foreach ($record_data as $key => $value) {
        if ((stripos($key, 'email') !== false || stripos($key, 'id') !== false) && !empty($value)) {
            return $value;
        }
    }
    
    // Fallback to table name
    return ucwords(str_replace(['_', '-'], ' ', $table_name)) . " Details";
}

/**
 * Enhanced view function that can handle both specific and generic contexts
 */
function view($p) {
    // Check if this is a generic view request
    if (isset($p['table_name']) || isset($p['generic'])) {
        return generic_view($p);
    }
    
    // Otherwise, delegate to the existing view API
    return \api\view($p);
}
