<?php
namespace autodesk_api;

use system\data_importer;

class autodesk_products {
    private autodesk_api_interface $api;
    public $debug_log;
    public $products = [];

    function __construct($api) {
        $this->api = $api;
    }

    public function get($id) {
        $criteria = [];
        if (strpos($id, '-'))
            $criteria["filter[productId]"] = $id;
        else
            $criteria["filter[productReferenceNumber]"] = $id;
        return $this->api->search_autodesk_products($criteria);
    }
    public function get_all($db_data, $criteria = ["limit" => 100] )  {
        print_rr($criteria, 'database');
        return $this->database_get_autodesk_products($db_data, $criteria);
    }

    public function get_distinct($db_data, $criteria = ["limit" => 100] )  {
        print_rr($criteria, 'database');
    return $this->database_get_autodesk_products($db_data, $criteria, true);
    }

    private function database_get_autodesk_products($db_data = [], $criteria = [],$get_distinct = false)   {
        $default_criteria = [
            "limit" => 100,
            "order_by" => "offeringName",
            "search_columns" => ['pac.offeringName', 'pac.offeringCode', 'pac.offeringId', 'pac.intendedUsage_code', 'pac.intendedUsage_description', 'pac.accessModel_code', 'pac.accessModel_description', 'pac.servicePlan_code', 'pac.servicePlan_description', 'pac.connectivity_code', 'pac.connectivity_description', 'pac.term_code', 'pac.term_description', 'pac.renewOnlyDate', 'pac.orderAction', 'pac.specialProgramDiscount_code', 'pac.specialProgramDiscount_description']
        ];

        $table_schema = [
            "pac" => [
                'query' => "FROM products_autodesk_catalog pac"
            ],
            "p2a" => [
                'query' => "JOIN products_to_autodesk_catalog p2a ON p2a.unique_hash = pac.unique_hash"
            ],
            "products" => [
                'query' => "JOIN products ON p2a.products_id = products.products_id"
            ]
        ];

        // $q =  "SELECT * FROM products_autodesk_catalog pac " . tcs_db_build_criteria(array_merge($default_criteria, $criteria),$table_schema);
        $criteria_string = "";
        [
            $criteria_string,
            $criteria_cols,
            $criteria_tabs
        ] = tcs_db_build_criteria(array_merge($default_criteria, $criteria),$table_schema);
        if (empty($db_data)){
            $table_data['columns'] = ' * ';
            foreach ($table_schema as $key => $table) {
                $table_data['tables'] .= " " . $table['query'];
            }
        } else{
            $table_data = tcs_db_build_tables($db_data,$table_schema,$criteria_cols,$criteria_tabs);
        }
          $distinct = $get_distinct ? "DISTINCT " : "";
        $query = "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']}  {$criteria_string}";
        print_rr(i:$query,l:'finalq',fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query);
    }



    public function get_catalog() { /* Configuration*/
        $csv_file_path = DOC_ROOT . '/feeds/product_catalog.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_catalog =  $this->api->get_autodesk_product_catalog();
            if ($get_catalog['status'] == 'fail') {
                return $get_catalog;
            } else {
                $response = $get_catalog;
            }
            print_rr($get_catalog);
            $response_headers = $response['headers'];
            if (is_string($response_headers['Location'][0])) {
                $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
                file_put_contents($csv_file_path, $input_file);
            } else {
                return ['status' => 'fail', 'response' => $get_catalog];
            }
        }
        return $this->import_autodesk_catalog_into_database($csv_file_path);
    }


    private function get_product_catalog_header_map() {
        return  ['offeringName' => 'offeringName', 'offeringCode' => 'offeringCode', 'offeringId' => 'offeringId', 'intendedUsage.code' => 'intendedUsage_code', 'intendedUsage.description' => 'intendedUsage_description', 'accessModel.code' => 'accessModel_code', 'accessModel.description' => 'accessModel_description', 'servicePlan.code' => 'servicePlan_code', 'servicePlan.description' => 'servicePlan_description', 'connectivity.code' => 'connectivity_code', 'connectivity.description' => 'connectivity_description', 'term.code' => 'term_code', 'term.description' => 'term_description', 'lifeCycleState' => 'lifeCycleState', 'renewOnlyDate' => 'renewOnlyDate', 'discontinueDate' => 'discontinueDate', 'orderAction' => 'orderAction', 'specialProgramDiscount.code' => 'specialProgramDiscount_code', 'specialProgramDiscount.description' => 'specialProgramDiscount_description', 'fromQty' => 'fromQty', 'toQty' => 'toQty', 'currency' => 'currency', 'SRP' => 'SRP', 'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount', 'renewalDiscountPercent' => 'renewalDiscountPercent', 'renewalDiscountAmount' => 'renewalDiscountAmount', 'costAfterRenewalDiscount' => 'costAfterRenewalDiscount', 'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent', 'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount', 'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount', 'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent', 'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount', 'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount', 'effectiveStartDate' => 'effectiveStartDate', 'effectiveEndDate' => 'effectiveEndDate'];
    }


    public function import_autodesk_catalog_into_database($csv_file_path) {    /* Main script execution*/
        $output = data_importer::import_csv_into_database(
            autodesk_api::get_product_catalog_header_map(),
            $csv_file_path,
            ['offeringId', 'intendedUsage.code', 'accessModel.code', 'servicePlan.code', 'connectivity.code', 'term.code', 'orderAction', 'specialProgramDiscount.code', 'fromQty', 'toQty'],
            true,
            'product_import'
        );
        $this->database_update_autodesk_pricing();
        return $output;
    }





    public function get_promos() { /* Configuration*/
        $csv_file_path = DOC_ROOT . '/feeds/product_promos.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_promos = $this->api->get_autodesk_product_promos();

            if (empty($get_promos) || $get_promos['status'] == 'fail') {
                return ['status' => 'fail 1', 'response' => $get_promos];
            } else {
                $response = $get_promos;
            }
            $response_headers = $response['headers'];
            if (is_string($response_headers['Location'][0])) {
                $input_file = file_get_contents($response_headers['Location'][0]);   /* Download the CSV file and save it to the specified path                */
                file_put_contents($csv_file_path, $input_file);
            } else {
                return ['status' => 'fail 2', 'response' => $response, 'debug' => $get_promos];
            }
        }
        return ['status' => 'success', 'response' => $response]; //$this->import_autodesk_promos_into_database($csv_file_path);
    }


    private function get_product_promos_header_map() {
        return  ['offeringName' => 'offeringName', 'offeringCode' => 'offeringCode', 'offeringId' => 'offeringId', 'intendedUsage.code' => 'intendedUsage_code', 'intendedUsage.description' => 'intendedUsage_description', 'accessModel.code' => 'accessModel_code', 'accessModel.description' => 'accessModel_description', 'servicePlan.code' => 'servicePlan_code', 'servicePlan.description' => 'servicePlan_description', 'connectivity.code' => 'connectivity_code', 'connectivity.description' => 'connectivity_description', 'term.code' => 'term_code', 'term.description' => 'term_description', 'lifeCycleState' => 'lifeCycleState', 'renewOnlyDate' => 'renewOnlyDate', 'discontinueDate' => 'discontinueDate', 'orderAction' => 'orderAction', 'specialProgramDiscount.code' => 'specialProgramDiscount_code', 'specialProgramDiscount.description' => 'specialProgramDiscount_description', 'fromQty' => 'fromQty', 'toQty' => 'toQty', 'currency' => 'currency', 'SRP' => 'SRP', 'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount', 'renewalDiscountPercent' => 'renewalDiscountPercent', 'renewalDiscountAmount' => 'renewalDiscountAmount', 'costAfterRenewalDiscount' => 'costAfterRenewalDiscount', 'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent', 'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount', 'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount', 'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent', 'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount', 'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount', 'effectiveStartDate' => 'effectiveStartDate', 'effectiveEndDate' => 'effectiveEndDate'];
    }


    public function import_autodesk_promos_into_database($csv_file_path) {    /* Main script execution*/
        $output = data_importer::import_csv_into_database(
            $this->get_product_promos_header_map(),
            $csv_file_path,
            ['offeringId', 'intendedUsage.code', 'accessModel.code', 'servicePlan.code', 'connectivity.code', 'term.code', 'orderAction', 'specialProgramDiscount.code', 'fromQty', 'toQty'],
            true
        );
        print_rr($output);
        $this->database_update_autodesk_pricing();
        return $output;
    }




    private function database_get_autodesk_variations() {
        return tcs_db_query("
            SELECT
                pv.products_variations_id,
                pv.model,
                pv.price,
                pv.products_id,
                pv.attributes,
                pv.gtin,
                pac.*
            FROM
                products_autodesk_catalog pac
            JOIN
                products_variations pv
                ON pv.autodesk_catalog_unique_hash = pac.unique_hash
            WHERE
                pac.srp > 0
        ");
    }

    public function database_update_autodesk_pricing($products = null, $variations = null) {
        if ($products == null) $products = $this->database_get_autodesk_products();
        if ($variations == null) $variations = $this->database_get_autodesk_variations();
        $recordTypes = ["products" => $products, "products_variations" => $variations];

        // Initialize return data structure
        $pricing_update_results = [
            'summary' => [
                'products_updated' => 0,
                'variations_updated' => 0,
                'total_updated' => 0
            ],
            'products' => [],
            'variations' => [],
            'errors' => []
        ];

        tcs_log("Starting pricing update " . count($products) . " products and " . count($variations) . " variations", 'pricing_update');
        tcs_log("first record: " . print_r($products[0], true), 'pricing_update');
        tcs_log("recordTypes: " . print_r($recordTypes, true), 'pricing_update');

        foreach ($recordTypes as $key => $recordType) {
            $ids = [];
            $caseStatements = [];
            $pricing_details = [];

            tcs_log("processing: " . print_r($key, true), 'pricing_update');
            if (count($recordType) == 0) continue;

            // Collect all product prices and ids for the bulk update
            foreach ($recordType as $record) {
                $old_price = $key == "products" ?
                    ($record['products_products_price'] ?? $record['products_price'] ?? 0) :
                    ($record['price'] ?? 0);
                $new_price = $this->products_calculate_pricing($record);

                tcs_log("processing {$key}_id: " . $record["{$key}_id"] . " setting price to $new_price", 'pricing_update');

                $ids[] = $record["{$key}_id"];  // Collect the ID
                $caseStatements[] = "WHEN {$record["{$key}_id"]} THEN {$new_price}";

                // Store detailed pricing information
                if ($key == "products") {
                    $pricing_details[] = [
                        'product_id' => $record['products_products_id'] ?? $record['products_id'] ?? '',
                        'product_name' => $record['products_products_name'] ?? $record['products_name'] ?? 'Unknown Product',
                        'autodesk_offering_name' => $record['pac_offeringName'] ?? $record['offeringName'] ?? '',
                        'autodesk_offering_code' => $record['pac_offeringCode'] ?? $record['offeringCode'] ?? '',
                        'autodesk_offering_id' => $record['pac_offeringId'] ?? $record['offeringId'] ?? '',
                        'old_price' => number_format($old_price, 2),
                        'new_price' => number_format($new_price, 2),
                        'price_change' => number_format($new_price - $old_price, 2),
                        'srp' => $record['pac_SRP'] ?? $record['SRP'] ?? 0,
                        'discounts_applied' => [
                            'renewal_discount' => $record['pac_renewalDiscountAmount'] ?? $record['renewalDiscountAmount'] ?? 0,
                            'volume_discount' => $record['pac_transactionVolumeDiscountAmount'] ?? $record['transactionVolumeDiscountAmount'] ?? 0,
                            'duration_discount' => $record['pac_serviceDurationDiscountAmount'] ?? $record['serviceDurationDiscountAmount'] ?? 0
                        ]
                    ];
                } else {
                    // Create variation name from available data
                    $variation_name = $record['model'] ?? 'Variation';
                    if (!empty($record['gtin'])) {
                        $variation_name .= ' (GTIN: ' . $record['gtin'] . ')';
                    }
                    if (!empty($record['attributes'])) {
                        $variation_name .= ' - ' . substr($record['attributes'], 0, 50);
                    }

                    $pricing_details[] = [
                        'variation_id' => $record['products_variations_id'],
                        'variation_name' => $variation_name,
                        'variation_model' => $record['model'] ?? '',
                        'variation_gtin' => $record['gtin'] ?? '',
                        'variation_attributes' => $record['attributes'] ?? '',
                        'product_id' => $record['products_id'] ?? '',
                        'autodesk_offering_name' => $record['offeringName'] ?? '',
                        'autodesk_offering_code' => $record['offeringCode'] ?? '',
                        'autodesk_offering_id' => $record['offeringId'] ?? '',
                        'old_price' => number_format($old_price, 2),
                        'new_price' => number_format($new_price, 2),
                        'price_change' => number_format($new_price - $old_price, 2),
                        'srp' => $record['SRP'] ?? 0,
                        'discounts_applied' => [
                            'renewal_discount' => $record['renewalDiscountAmount'] ?? 0,
                            'volume_discount' => $record['transactionVolumeDiscountAmount'] ?? 0,
                            'duration_discount' => $record['serviceDurationDiscountAmount'] ?? 0
                        ]
                    ];
                }
            }

            // If no records are found, continue to next record type
            if (empty($ids)) continue;

            // Build the bulk update query using CASE
            $idsList = implode(",", $ids);
            $caseQuery = implode(" ", $caseStatements);

            $col = 'products_price';
            if ($key == "products_variations") {
                $col = "price";
            }

            $sql_query = "" .
                "UPDATE `{$key}`
                SET `$col` = CASE `{$key}_id` $caseQuery
                END
                WHERE `{$key}_id` IN ($idsList)";

            print_rr($sql_query);

            try {
                tep_db_query($sql_query);

                // Store results
                if ($key == "products") {
                    $pricing_update_results['products'] = $pricing_details;
                    $pricing_update_results['summary']['products_updated'] = count($pricing_details);
                } else {
                    $pricing_update_results['variations'] = $pricing_details;
                    $pricing_update_results['summary']['variations_updated'] = count($pricing_details);
                }

                tcs_log("Successfully updated " . count($pricing_details) . " {$key} prices", 'pricing_update');

            } catch (Exception $e) {
                $error_msg = "Error updating {$key} prices: " . $e->getMessage();
                $pricing_update_results['errors'][] = $error_msg;
                tcs_log($error_msg, 'pricing_update');
            }
        }

        // Calculate total updated
        $pricing_update_results['summary']['total_updated'] =
            $pricing_update_results['summary']['products_updated'] +
            $pricing_update_results['summary']['variations_updated'];

        tcs_log("Pricing update completed. Total updated: " . $pricing_update_results['summary']['total_updated'], 'pricing_update');

        return $this->format_pricing_update_html($pricing_update_results);
    }
    
    public function get_autodesk_product_from_catalog($products_id, $hash = null) {
        print_rr($autodesk_id,'autodesk_id' ,true,true);
        if ($hash) {
            $query_sql = "SELECT * FROM products_autodesk_catalog WHERE `unique_hash` = :hash";
            $query = tep_db_query($query_sql, null, [":hash" => $hash]);
            print_rr($query_sql);
            if (tep_db_num_rows($query)) return tep_db_fetch_array($query);
        }
        $query_sql = "SELECT * FROM products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac ON p2a.unique_hash = pac.unique_hash WHERE products_id = :products_id";
        $query = tep_db_query($query_sql, null, [":products_id" => $products_id]);
        return tep_db_fetch_array($query);
    }

    public function products_calculate_pricing($product) {
        $price = $product['SRP'];
        $price -= $product['renewalDiscountAmount'] ?? 0;
        $price -= $product['transactionVolumeDiscountAmount'] ?? 0;
        $price -= $product['serviceDurationDiscountAmount'] ?? 0;
        return $price;
    }

    private function format_pricing_update_html($results) {
        $html = '<div class="pricing-update-results p-4">';

        // Summary section
        $html .= '<div class="mb-6">';
        $html .= '<h3 class="text-lg font-semibold text-gray-900 mb-3">Pricing Update Summary</h3>';
        $html .= '<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">';
        $html .= '<div class="bg-blue-50 p-4 rounded-lg">';
        $html .= '<div class="text-2xl font-bold text-blue-600">' . $results['summary']['products_updated'] . '</div>';
        $html .= '<div class="text-sm text-blue-800">Products Updated</div>';
        $html .= '</div>';
        $html .= '<div class="bg-green-50 p-4 rounded-lg">';
        $html .= '<div class="text-2xl font-bold text-green-600">' . $results['summary']['variations_updated'] . '</div>';
        $html .= '<div class="text-sm text-green-800">Variations Updated</div>';
        $html .= '</div>';
        $html .= '<div class="bg-purple-50 p-4 rounded-lg">';
        $html .= '<div class="text-2xl font-bold text-purple-600">' . $results['summary']['total_updated'] . '</div>';
        $html .= '<div class="text-sm text-purple-800">Total Updated</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        // Errors section
        if (!empty($results['errors'])) {
            $html .= '<div class="mb-6">';
            $html .= '<h4 class="text-md font-semibold text-red-600 mb-2">Errors</h4>';
            $html .= '<div class="bg-red-50 border border-red-200 rounded-lg p-3">';
            foreach ($results['errors'] as $error) {
                $html .= '<div class="text-red-700 text-sm">' . htmlspecialchars($error) . '</div>';
            }
            $html .= '</div>';
            $html .= '</div>';
        }

        // Products section
        if (!empty($results['products'])) {
            $html .= '<div class="mb-6">';
            $html .= '<h4 class="text-md font-semibold text-gray-900 mb-3">Product Price Updates</h4>';
            $html .= '<div class="overflow-x-auto">';
            $html .= '<table class="min-w-full bg-white border border-gray-200 rounded-lg">';
            $html .= '<thead class="bg-gray-50">';
            $html .= '<tr>';
            $html .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Product</th>';
            $html .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Autodesk Offering</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Old Price</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">New Price</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Change</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">SRP</th>';
            $html .= '</tr>';
            $html .= '</thead>';
            $html .= '<tbody class="divide-y divide-gray-200">';

            foreach ($results['products'] as $product) {
                $change_class = floatval($product['price_change']) >= 0 ? 'text-green-600' : 'text-red-600';
                $change_symbol = floatval($product['price_change']) >= 0 ? '+' : '';

                $html .= '<tr class="hover:bg-gray-50">';
                $html .= '<td class="px-4 py-2">';
                $html .= '<div class="font-medium text-gray-900">' . htmlspecialchars($product['product_name']) . '</div>';
                $html .= '<div class="text-sm text-gray-500">ID: ' . htmlspecialchars($product['product_id']) . '</div>';
                $html .= '</td>';
                $html .= '<td class="px-4 py-2">';
                $html .= '<div class="text-sm text-gray-900">' . htmlspecialchars($product['autodesk_offering_name']) . '</div>';
                $html .= '<div class="text-xs text-gray-500">' . htmlspecialchars($product['autodesk_offering_code']) . '</div>';
                $html .= '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm text-gray-900">£' . $product['old_price'] . '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm font-medium text-gray-900">£' . $product['new_price'] . '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm font-medium ' . $change_class . '">' . $change_symbol . '£' . $product['price_change'] . '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm text-gray-500">£' . number_format($product['srp'], 2) . '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
            $html .= '</div>';
            $html .= '</div>';
        }

        // Variations section
        if (!empty($results['variations'])) {
            $html .= '<div class="mb-6">';
            $html .= '<h4 class="text-md font-semibold text-gray-900 mb-3">Variation Price Updates</h4>';
            $html .= '<div class="overflow-x-auto">';
            $html .= '<table class="min-w-full bg-white border border-gray-200 rounded-lg">';
            $html .= '<thead class="bg-gray-50">';
            $html .= '<tr>';
            $html .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Variation</th>';
            $html .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Autodesk Offering</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Old Price</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">New Price</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Change</th>';
            $html .= '<th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">SRP</th>';
            $html .= '</tr>';
            $html .= '</thead>';
            $html .= '<tbody class="divide-y divide-gray-200">';

            foreach ($results['variations'] as $variation) {
                $change_class = floatval($variation['price_change']) >= 0 ? 'text-green-600' : 'text-red-600';
                $change_symbol = floatval($variation['price_change']) >= 0 ? '+' : '';

                $html .= '<tr class="hover:bg-gray-50">';
                $html .= '<td class="px-4 py-2">';
                $html .= '<div class="font-medium text-gray-900">' . htmlspecialchars($variation['variation_name']) . '</div>';
                $html .= '<div class="text-sm text-gray-500">ID: ' . htmlspecialchars($variation['variation_id']) . '</div>';
                if (!empty($variation['variation_gtin'])) {
                    $html .= '<div class="text-xs text-gray-400">GTIN: ' . htmlspecialchars($variation['variation_gtin']) . '</div>';
                }
                $html .= '</td>';
                $html .= '<td class="px-4 py-2">';
                $html .= '<div class="text-sm text-gray-900">' . htmlspecialchars($variation['autodesk_offering_name']) . '</div>';
                $html .= '<div class="text-xs text-gray-500">' . htmlspecialchars($variation['autodesk_offering_code']) . '</div>';
                $html .= '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm text-gray-900">£' . $variation['old_price'] . '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm font-medium text-gray-900">£' . $variation['new_price'] . '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm font-medium ' . $change_class . '">' . $change_symbol . '£' . $variation['price_change'] . '</td>';
                $html .= '<td class="px-4 py-2 text-right text-sm text-gray-500">£' . number_format($variation['srp'], 2) . '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}
