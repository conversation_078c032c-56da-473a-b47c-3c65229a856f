<?php
use system\database;
class subscription_matcher {
    
    private $db;
    private $confidence_threshold = 0.7;
    
    public function __construct() {
        // Database connection will be handled by existing TCS functions
    }
    
    /**
     * Match subscription data with manual entries based on email and company name
     * 
     * @param array $subscription_data The main subscription data from Autodesk API
     * @return array Matched data with confidence scores
     */
    public function match_subscription_data($subscription_data) {
        $matches = [];
        
        // Extract key matching fields
        $email = $subscription_data['endcust_primary_admin_email'] ?? '';
        $company_name = $subscription_data['endcust_name'] ?? '';
        $subscription_ref = $subscription_data['subs_subscriptionReferenceNumber'] ?? '';
        
        // Try exact email match first
        if (!empty($email)) {
            $email_matches = $this->find_by_email($email);
            foreach ($email_matches as $match) {
                $match['confidence'] = $this->calculate_confidence($subscription_data, $match, 'email');
                $match['match_type'] = 'email';
                $matches[] = $match;
            }
        }
        
        // Try company name match
        if (!empty($company_name)) {
            $company_matches = $this->find_by_company_name($company_name);
            foreach ($company_matches as $match) {
                // Avoid duplicates from email matching
                if (!$this->is_duplicate_match($matches, $match)) {
                    $match['confidence'] = $this->calculate_confidence($subscription_data, $match, 'company');
                    $match['match_type'] = 'company';
                    $matches[] = $match;
                }
            }
        }
        
//        // Try subscription reference match
//        if (!empty($subscription_ref)) {
//            $ref_matches = $this->find_by_subscription_reference($subscription_ref);
//            foreach ($ref_matches as $match) {
//                if (!$this->is_duplicate_match($matches, $match)) {
//                    $match['confidence'] = 1.0; // Exact reference match = highest confidence
//                    $match['match_type'] = 'reference';
//                    $matches[] = $match;
//                }
//            }
//        }
        
        // Sort by confidence score (highest first)
        usort($matches, function($a, $b) {
            return $b['confidence'] <=> $a['confidence'];
        });
        
        // Return only matches above threshold
        return array_filter($matches, function($match) {
            return $match['confidence'] >= $this->confidence_threshold;
        });
    }
    
    /*
     *
     * Find manual entries by email address
     *
     */
    private function find_by_email($email) {
        try {
            $query = "SELECT * FROM manual_subscription_entries
                          WHERE LOWER(email_address)    =  LOWER(?)
                                OR LOWER(contact_email) =  LOWER(?)
                                OR LOWER(admin_email)   =  LOWER(?)";
            // Using TCS database functions
            $results = tcs_db_query($query, [$email, $email, $email]);
            return $results ?: [];
        } catch (Exception $e) {
            // Table might not exist yet
            return [];
        }
    }
    
    /**
     * Find manual entries by company name using fuzzy matching
     */
    private function find_by_company_name($company_name) {
        try {
            // Tokenize and filter terms
            [$long_terms, $all_terms] = $this->get_company_search_terms($company_name);

            // Debug logging
            tcs_log("Company search for '{$company_name}': long_terms=" . json_encode($long_terms) . ", all_terms=" . json_encode($all_terms), 'subscription_matcher');

            $params = [];
            $where = '';

            if (!empty($long_terms)) {
                // OR over terms >= 4 chars (more permissive for substantial terms)
                $likes = [];
                foreach ($long_terms as $t) {
                    $likes[] = 'LOWER(company_name) LIKE ?';
                    $params[] = '%' . strtolower($t) . '%';
                }
                $where = '(' . implode(' OR ', $likes) . ')';
                tcs_log("Using OR logic for long terms: {$where}", 'subscription_matcher');
            } else {
                // Fallback: AND over the remaining terms (more restrictive for short terms)
                if (empty($all_terms)) {
                    return []; // nothing sensible to search
                }
                $likes = [];
                foreach ($all_terms as $t) {
                    $likes[] = 'LOWER(company_name) LIKE ?';
                    $params[] = '%' . strtolower($t) . '%';
                }
                $where = '(' . implode(' AND ', $likes) . ')';
                tcs_log("Using AND logic for short terms: {$where}", 'subscription_matcher');
            }

            $query = "SELECT * FROM manual_subscription_entries WHERE {$where} ORDER BY created_at DESC LIMIT 200";
            tcs_log("Executing query: {$query} with params: " . json_encode($params), 'subscription_matcher');
            $rows = tcs_db_query($query, $params) ?: [];
            tcs_log("Found " . count($rows) . " raw matches before similarity filtering", 'subscription_matcher');

            // Compute similarity in PHP to preserve confidence behavior
            foreach ($rows as &$row) {
                $row['name_similarity'] = $this->calculate_name_similarity($company_name, $row['company_name'] ?? '');
            }
            unset($row);

            // Filter out very poor matches (less than 30% similarity)
            $rows = array_filter($rows, function($row) {
                return ($row['name_similarity'] ?? 0) >= 0.3;
            });

            tcs_log("After similarity filtering (≥30%): " . count($rows) . " matches remain", 'subscription_matcher');

            // Sort by similarity desc to improve result relevance
            usort($rows, function($a, $b){
                return ($b['name_similarity'] ?? 0) <=> ($a['name_similarity'] ?? 0);
            });

            return $rows;
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Build search terms from a company name: strip non-alphanumerics, remove common company tokens,
     * return [terms >= 4 chars, all remaining terms]
     */
    private function get_company_search_terms(string $name): array {
        $stop = [
            'ltd','limited','inc','incorporated','corp','corporation','llc','plc','co','company','and','&','group','holdings','service','services','solution','solutions','uk','usa','ireland','the','of','studio','architects','architecture'
        ];
        $s = strtolower($name);
        $s = preg_replace('/[^a-z0-9]+/i',' ', $s);
        $parts = array_filter(array_map('trim', explode(' ', $s)), function($p){ return $p !== ''; });
        $parts = array_values(array_unique(array_filter($parts, function($p) use ($stop){
            return !in_array($p, $stop, true);
        })));

        $long = array_values(array_filter($parts, function($p){ return strlen($p) >= 4; }));
        return [$long, $parts];
    }
    
    /**
     * Find manual entries by subscription reference number
     */
    private function find_by_subscription_reference($ref) {
        try {
            $query = "
                SELECT * FROM manual_subscription_entries
                WHERE subscription_reference = ?
                OR subscription_number = ?
                OR reference_number = ?
            ";

            $results = tcs_db_query($query, [$ref, $ref, $ref]);
            return $results ?: [];
        } catch (Exception $e) {
            // Table might not exist yet
            return [];
        }
    }
    
    /**
     * Calculate confidence score for a match
     */
    private function calculate_confidence($subscription_data, $match_data, $match_type) {
        $confidence = 0.0;
        
        switch ($match_type) {
            case 'email':
                $confidence = 0.9; // High confidence for email matches
                
                // Boost confidence if company names also match
                if (!empty($subscription_data['endcust_name']) && !empty($match_data['company_name'])) {
                    $name_similarity = $this->calculate_name_similarity(
                        $subscription_data['endcust_name'], 
                        $match_data['company_name']
                    );
                    $confidence = min(1.0, $confidence + ($name_similarity * 0.1));
                }
                break;
                
            case 'company':
                // Use the name similarity calculated in the query
                $confidence = $match_data['name_similarity'] ?? 0.7;
                
                // Boost if we have any email matches
                if (!empty($subscription_data['endcust_primary_admin_email']) && 
                    !empty($match_data['email_address'])) {
                    if (strtolower($subscription_data['endcust_primary_admin_email']) === 
                        strtolower($match_data['email_address'])) {
                        $confidence = min(1.0, $confidence + 0.2);
                    }
                }
                break;
                
            case 'reference':
                $confidence = 1.0; // Perfect match
                break;
        }
        
        return $confidence;
    }
    
    /**
     * Clean company name for better matching
     */
    private function clean_company_name($name) {
        // Remove common business suffixes and prefixes
        $suffixes = ['ltd', 'limited', 'inc', 'incorporated', 'corp', 'corporation', 
                    'llc', 'plc', 'co', 'company', '&', 'and'];
        
        $clean = strtolower(trim($name));
        
        // Remove punctuation
        $clean = preg_replace('/[^\w\s]/', ' ', $clean);
        
        // Remove common suffixes
        foreach ($suffixes as $suffix) {
            $clean = preg_replace('/\b' . preg_quote($suffix) . '\b/', '', $clean);
        }
        
        // Clean up extra spaces
        $clean = preg_replace('/\s+/', ' ', trim($clean));
        
        return $clean;
    }
    
    /**
     * Calculate similarity between two company names
     */
    private function calculate_name_similarity($name1, $name2) {
        $clean1 = $this->clean_company_name($name1);
        $clean2 = $this->clean_company_name($name2);
        
        // Use Levenshtein distance for similarity
        $max_len = max(strlen($clean1), strlen($clean2));
        if ($max_len === 0) return 0.0;
        
        $distance = levenshtein($clean1, $clean2);
        return 1.0 - ($distance / $max_len);
    }
    
    /**
     * Check if a match is already in the results array
     */
    private function is_duplicate_match($matches, $new_match) {
        foreach ($matches as $existing_match) {
            if (isset($existing_match['id']) && isset($new_match['id']) && 
                $existing_match['id'] === $new_match['id']) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get all manual subscription entries for admin purposes
     * Includes both manual_subscription_entries table and CSV-based tables
     */
    public function get_all_manual_entries($criteria = []) {
        $all_entries = [];

        // Get entries from manual_subscription_entries table
        try {
            $where_conditions = [];
            $params = [];

            if (!empty($criteria['search'])) {
                // Use optimized term-based search
                [$long_terms, $all_terms] = $this->get_company_search_terms($criteria['search']);
                $search_terms = !empty($long_terms) ? $long_terms : $all_terms;
                $term_operator = !empty($long_terms) ? 'OR' : 'AND'; // OR for ≥4 chars, AND for short terms

                if (!empty($search_terms)) {
                    $search_columns = ['company_name', 'email_address', 'subscription_reference'];
                    $column_conditions = [];

                    foreach ($search_columns as $column) {
                        $term_conditions = [];
                        foreach ($search_terms as $term) {
                            $term_conditions[] = "LOWER({$column}) LIKE ?";
                            $params[] = '%' . strtolower($term) . '%';
                        }
                        if (!empty($term_conditions)) {
                            $column_conditions[] = '(' . implode(" {$term_operator} ", $term_conditions) . ')';
                        }
                    }

                    if (!empty($column_conditions)) {
                        $where_conditions[] = '(' . implode(' OR ', $column_conditions) . ')';
                    }
                }
            }

            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            $limit_clause = isset($criteria['limit']) ? 'LIMIT ' . intval($criteria['limit']) : 'LIMIT 100';

            $query = "SELECT *, 'manual_entries' as source_table FROM manual_subscription_entries {$where_clause} ORDER BY created_at DESC {$limit_clause}";

            $manual_entries = tcs_db_query($query, $params) ?: [];
            $all_entries = array_merge($all_entries, $manual_entries);
        } catch (Exception $e) {
            // Table might not exist yet, continue
        }

        // Get entries from CSV-based tables
        $csv_entries = $this->get_csv_table_entries($criteria);
        $all_entries = array_merge($all_entries, $csv_entries);

        // Sort by created date if available
        usort($all_entries, function($a, $b) {
            $a_date = $a['created_at'] ?? $a['date_created'] ?? '1970-01-01';
            $b_date = $b['created_at'] ?? $b['date_created'] ?? '1970-01-01';
            return strcmp($b_date, $a_date); // Newest first
        });

        // Apply limit if specified
        if (isset($criteria['limit'])) {
            $all_entries = array_slice($all_entries, 0, intval($criteria['limit']));
        }

        return $all_entries;
    }


    public function get_all_csv_entries($criteria = []) {
        return $this->get_csv_table_entries($criteria);
    }

    /**
     * Get entries from CSV-based tables that might contain subscription data
     */
    private function get_csv_table_entries($criteria = []) {
        $csv_entries = [];

        try {
            // Get CSV tables from unified configuration system
            $csv_tables = [];

            tcs_log("Starting CSV table search with criteria: " . json_encode($criteria), 'subscription_matcher');

            // Try unified system first (data_table_storage)
            try {
                $unified_tables = database::table('autobooks_data_table_storage')
                    ->select(['table_name', 'configuration'])
                    ->get(); // Get all tables since data_source_id might be null

                foreach ($unified_tables as $table_config) {
                    $config = json_decode($table_config['configuration'], true);
                    tcs_log("Checking table: {$table_config['table_name']}, config: " . json_encode($config), 'subscription_matcher');

                    // Only include CSV data sources
                    if ($config && isset($config['available_fields']) &&
                        isset($config['data_source']) && $config['data_source'] === 'csv') {
                        $csv_tables[] = [
                            'table_name' => $table_config['table_name'],
                            'available_fields' => $config['available_fields'],
                            'description' => $config['description'] ?? ''
                        ];
                        tcs_log("Added CSV table: {$table_config['table_name']}", 'subscription_matcher');
                    }
                }

                tcs_log("Found " . count($csv_tables) . " CSV tables from unified system", 'subscription_matcher');
            } catch (Exception $e) {
                // Fall back to legacy system
            }

            // Fall back to legacy system if no unified tables found
            if (empty($csv_tables)) {
                tcs_log("No unified tables found, trying legacy system", 'subscription_matcher');

                $table_exists_query = "SHOW TABLES LIKE 'autobooks_table_configs'";
                $table_exists = tcs_db_query($table_exists_query);

                if (!empty($table_exists)) {
                    $config_query = "SELECT table_name, column_mappings, description FROM autobooks_table_configs WHERE data_source = 'csv' AND is_active = 1";
                    $legacy_tables = tcs_db_query($config_query) ?: [];

                    tcs_log("Found " . count($legacy_tables) . " legacy CSV tables", 'subscription_matcher');

                    foreach ($legacy_tables as $table_config) {
                        $column_mappings = json_decode($table_config['column_mappings'], true) ?: [];
                        $csv_tables[] = [
                            'table_name' => $table_config['table_name'],
                            'available_fields' => array_keys($column_mappings),
                            'description' => $table_config['description'] ?? ''
                        ];
                        tcs_log("Added legacy CSV table: {$table_config['table_name']}", 'subscription_matcher');
                    }
                }
            }

            // Additional fallback: Look for tables that might be CSV imports if still no tables found
            if (empty($csv_tables)) {
                $all_tables_query = "SHOW TABLES";
                $all_tables = tcs_db_query($all_tables_query) ?: [];

                foreach ($all_tables as $table_row) {
                    $table_name = array_values($table_row)[0]; // Get table name from result

                    // Skip system tables and known non-CSV tables
                    if ($this->is_potential_csv_table($table_name)) {
                        $csv_tables[] = [
                            'table_name' => $table_name,
                            'available_fields' => [], // Will be determined later
                            'description' => "Potential CSV table: {$table_name}"
                        ];
                    }
                }
            }

            tcs_log("Processing " . count($csv_tables) . " CSV tables for subscription data", 'subscription_matcher');

            foreach ($csv_tables as $table_config) {
                $table_name = $table_config['table_name'];
                tcs_log("Processing table: {$table_name}", 'subscription_matcher');

                // Check if table actually exists before processing
                if (!database::tableExists($table_name)) {
                    tcs_log("Table {$table_name} does not exist, skipping", 'subscription_matcher');
                    continue;
                }

                // Get available fields (unified system) or column mappings (legacy system)
                $available_fields = $table_config['available_fields'] ?? [];
                $column_mappings = [];

                if (empty($available_fields) && isset($table_config['column_mappings'])) {
                    // Legacy system - extract from column_mappings
                    $column_mappings = json_decode($table_config['column_mappings'], true) ?: [];
                    $available_fields = array_keys($column_mappings);
                }

                tcs_log("Available fields for {$table_name}: " . json_encode($available_fields), 'subscription_matcher');

                // Check if this table might contain subscription-related data using unified system
                if (\system\unified_field_mapper::contains_subscription_data($table_name, $available_fields)) {
                    tcs_log("Table {$table_name} contains subscription data, searching...", 'subscription_matcher');

                    $table_entries = $this->search_csv_table($table_name, $available_fields, $criteria);

                    tcs_log("Found " . count($table_entries) . " entries in {$table_name}", 'subscription_matcher');

                    // Add metadata to each entry
                    foreach ($table_entries as &$entry) {
                        $entry['source_table'] = $table_name;
                        $entry['table_description'] = $table_config['description'] ?? '';
                        $entry['can_add_to_unity'] = true;
                    }

                    $csv_entries = array_merge($csv_entries, $table_entries);
                } else {
                    tcs_log("Table {$table_name} does not contain subscription data", 'subscription_matcher');
                }
            }

            tcs_log("Total CSV entries found: " . count($csv_entries), 'subscription_matcher');
        } catch (Exception $e) {
            // Continue without CSV entries if there's an error
        }

        return $csv_entries;
    }

    /**
     * Check if a table contains subscription-related data based on available fields
     */
    private function table_contains_subscription_data($table_name, $available_fields) {
        try {
            // Use available fields if provided, otherwise get from table structure
            if (!empty($available_fields)) {
                $column_names = array_map('strtolower', $available_fields);
            } else {
                // Fallback: Get actual column names from the table
                $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
                $columns = tcs_db_query($columns_query) ?: [];
                $column_names = array_map(function($col) {
                    return strtolower($col['Field']);
                }, $columns);
            }

            // Look for subscription-related keywords in column names
            $subscription_keywords = [
                'subscription', 'company', 'email', 'product', 'license',
                'customer', 'client', 'account', 'reference', 'serial',
                'start_date', 'end_date', 'expiry', 'renewal', 'quantity'
            ];

            foreach ($subscription_keywords as $keyword) {
                foreach ($column_names as $column_name) {
                    if (strpos($column_name, $keyword) !== false) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Search a CSV table for subscription-related data
     */
    private function search_csv_table($table_name, $available_fields, $criteria) {
        try {
            // Double-check table exists before querying
            if (!database::tableExists($table_name)) {
                tcs_log("Table {$table_name} does not exist, cannot search", 'subscription_matcher');
                return [];
            }

            $where_conditions = [];
            $params = [];

            // Build search conditions if search term provided
            if (!empty($criteria['search'])) {
                // Use optimized term-based search like company name matching
                [$long_terms, $all_terms] = $this->get_company_search_terms($criteria['search']);

                // Use available fields for searching, or get from table structure
                $searchable_fields = [];
                if (!empty($available_fields)) {
                    $searchable_fields = $available_fields;
                } else {
                    // Fallback: Get searchable columns from table
                    $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
                    $columns = tcs_db_query($columns_query) ?: [];
                    $searchable_fields = array_column($columns, 'Field');
                }

                // Filter to text-based columns only
                $text_columns = [];
                foreach ($searchable_fields as $column_name) {
                    // For available_fields, we assume they're searchable
                    // For columns from SHOW COLUMNS, we need to check type
                    if (!empty($available_fields)) {
                        $text_columns[] = $column_name;
                    } else {
                        // Get column info to check type
                        $column_info_query = "SHOW COLUMNS FROM `{$table_name}` LIKE ?";
                        $column_info = tcs_db_query($column_info_query, [$column_name]);
                        if (!empty($column_info)) {
                            $column_type = strtolower($column_info[0]['Type']);
                            if (strpos($column_type, 'varchar') !== false ||
                                strpos($column_type, 'text') !== false ||
                                strpos($column_type, 'char') !== false) {
                                $text_columns[] = $column_name;
                            }
                        }
                    }
                }

                if (!empty($text_columns)) {
                    $search_terms = !empty($long_terms) ? $long_terms : $all_terms;
                    $term_operator = !empty($long_terms) ? 'OR' : 'AND'; // OR for ≥4 chars, AND for short terms

                    if (!empty($search_terms)) {
                        $column_conditions = [];
                        foreach ($text_columns as $column_name) {
                            $term_conditions = [];
                            foreach ($search_terms as $term) {
                                $term_conditions[] = "LOWER(`{$column_name}`) LIKE ?";
                                $params[] = '%' . strtolower($term) . '%';
                            }
                            if (!empty($term_conditions)) {
                                $column_conditions[] = '(' . implode(" {$term_operator} ", $term_conditions) . ')';
                            }
                        }

                        if (!empty($column_conditions)) {
                            $where_conditions[] = '(' . implode(' OR ', $column_conditions) . ')';
                        }
                    }
                }
            }

            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            $limit_clause = isset($criteria['limit']) ? 'LIMIT ' . intval($criteria['limit']) : 'LIMIT 1000'; // Increased default limit

            $query = "SELECT * FROM `{$table_name}` {$where_clause} ORDER BY id DESC {$limit_clause}";

            $results = tcs_db_query($query, $params) ?: [];

            // Normalize the data to match subscription format
            return array_map(function($row) use ($table_name) {
                return $this->normalize_csv_entry($row, $table_name);
            }, $results);

        } catch (Exception $e) {
            tcs_log("Error searching table {$table_name}: " . $e->getMessage(), 'subscription_matcher');
            return [];
        }
    }

    /**
     * Normalize CSV table entry to match subscription format using unified field mapper
     */
    private function normalize_csv_entry($entry, $table_name) {
        // Use the unified field mapper for consistent normalization
        return \system\unified_field_mapper::normalize_entry($entry, $table_name);
    }

    /**
     * Check if a table name suggests it might be a CSV import table
     */
    private function is_potential_csv_table($table_name) {
        // Skip system tables and known application tables
        if (str_starts_with($table_name, 'autobooks_') && str_ends_with($table_name, '_data') ) return true;
        return false;
    }
}
//
//
//        // If table has typical CSV-like structure, consider it
//        try {
//            $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
//            $columns = tcs_db_query($columns_query) ?: [];
//
//            // Look for common CSV import patterns
//            $has_id = false;
//            $has_timestamps = false;
//            $text_columns = 0;
//
//            foreach ($columns as $column) {
//                $field_name = strtolower($column['Field']);
//                $field_type = strtolower($column['Type']);
//
//                if ($field_name === 'id') $has_id = true;
//                if (in_array($field_name, ['created_at', 'updated_at'])) $has_timestamps = true;
//                if (strpos($field_type, 'varchar') !== false || strpos($field_type, 'text') !== false) {
//                    $text_columns++;
//                }
//            }
//
//            // If it has typical CSV import structure (id, timestamps, multiple text columns)
//            return $has_id && $text_columns >= 3;
//
//        } catch (Exception $e) {
//            return false;
//        }
    //}

