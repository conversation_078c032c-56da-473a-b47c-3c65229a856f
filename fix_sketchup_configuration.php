<?php
/**
 * Fix the SketchUp configuration to use intelligent column selection
 * Access via browser: http://localhost/autobooks/fix_sketchup_configuration.php
 */

header('Content-Type: text/plain');

echo "=== Fixing SketchUp Configuration ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    echo "2. Analyzing current configuration...\n";
    
    // Get the current malformed configuration
    $current_config = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->whereNull('user_id')
        ->first();
    
    if (!$current_config) {
        echo "❌ No configuration found for table '{$table_name}'\n";
        exit(1);
    }
    
    $config_data = json_decode($current_config['configuration'], true);
    
    echo "Current configuration analysis:\n";
    echo "- Structure count: " . count($config_data['structure'] ?? []) . "\n";
    echo "- Available fields count: " . count($config_data['available_fields'] ?? []) . "\n";
    echo "- Hidden fields count: " . count($config_data['hidden'] ?? []) . "\n";
    echo "- Data source ID: " . ($config_data['data_source_id'] ?? 'none') . "\n\n";
    
    // Check if any columns have unified field information
    $columns_with_unified = 0;
    if (!empty($config_data['structure'])) {
        foreach ($config_data['structure'] as $col) {
            if (!empty($col['unified_field'])) {
                $columns_with_unified++;
            }
        }
    }
    
    echo "Columns with unified field info: {$columns_with_unified}\n";
    
    if ($columns_with_unified == 0) {
        echo "❌ Configuration has no unified field information - needs to be regenerated\n\n";
    } else {
        echo "✅ Configuration already has unified field information\n\n";
        exit(0);
    }
    
    echo "3. Getting data source information...\n";
    
    $data_source_id = $config_data['data_source_id'] ?? null;
    if (!$data_source_id) {
        echo "❌ No data source ID found in configuration\n";
        exit(1);
    }
    
    echo "Using data source ID: {$data_source_id}\n";
    
    // Get all columns from the table
    $columns_info = system\database::getColumnInfo($table_name);
    $all_columns = array_column($columns_info, 'Field');
    
    echo "Total columns in table: " . count($all_columns) . "\n\n";
    
    echo "4. Generating proper configuration with unified field matching...\n";
    
    // Generate proper column structure using unified field matching
    $column_structure = generate_unified_column_structure($all_columns, $data_source_id);
    
    echo "Generated structure:\n";
    echo "- Visible columns: " . count($column_structure['visible_columns']) . "\n";
    echo "- Hidden columns: " . count($column_structure['hidden_column_ids']) . "\n";
    echo "- Available fields: " . count($column_structure['available_fields']) . "\n\n";
    
    // Verify the generated structure has unified field information
    $generated_with_unified = 0;
    foreach ($column_structure['visible_columns'] as $col) {
        if (!empty($col['unified_field'])) {
            $generated_with_unified++;
        }
    }
    
    echo "Generated columns with unified field info: {$generated_with_unified}\n";
    
    if ($generated_with_unified == 0) {
        echo "❌ Generated structure still has no unified field information\n";
        echo "This indicates an issue with the generate_unified_column_structure function\n";
        exit(1);
    }
    
    echo "✅ Generated structure has proper unified field information\n\n";
    
    echo "5. Creating proper configuration...\n";
    
    // Create the proper configuration structure
    $proper_config = [
        'structure' => $column_structure['visible_columns'],
        'hidden' => $column_structure['hidden_column_ids'],
        'available_fields' => array_column($column_structure['available_fields'], 'field'),
        'table_schema' => $config_data['table_schema'] ?? null, // Preserve existing schema
        'data_source_type' => 'data_source',
        'data_source_id' => $data_source_id,
        'created_at' => $config_data['created_at'] ?? date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    echo "New configuration structure:\n";
    echo "- Structure: " . count($proper_config['structure']) . " visible columns\n";
    echo "- Hidden: " . count($proper_config['hidden']) . " hidden columns\n";
    echo "- Available fields: " . count($proper_config['available_fields']) . " fields\n\n";
    
    echo "6. Updating the stored configuration...\n";
    
    // Update the configuration in the database
    $update_result = system\database::table('autobooks_data_table_storage')
        ->where('id', $current_config['id'])
        ->update([
            'configuration' => json_encode($proper_config),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    
    if ($update_result) {
        echo "✅ Configuration updated successfully\n\n";
    } else {
        echo "❌ Failed to update configuration\n";
        exit(1);
    }
    
    echo "7. Verifying the updated configuration...\n";
    
    // Retrieve and verify the updated configuration
    $updated_config = system\database::table('autobooks_data_table_storage')
        ->where('table_name', $table_name)
        ->whereNull('user_id')
        ->first();
    
    $updated_data = json_decode($updated_config['configuration'], true);
    
    echo "Updated configuration verification:\n";
    echo "- Structure count: " . count($updated_data['structure'] ?? []) . "\n";
    echo "- Hidden count: " . count($updated_data['hidden'] ?? []) . "\n";
    echo "- Available fields count: " . count($updated_data['available_fields'] ?? []) . "\n";
    
    // Check unified field information
    $updated_with_unified = 0;
    if (!empty($updated_data['structure'])) {
        foreach ($updated_data['structure'] as $col) {
            if (!empty($col['unified_field'])) {
                $updated_with_unified++;
            }
        }
    }
    
    echo "- Columns with unified field info: {$updated_with_unified}\n\n";
    
    if ($updated_with_unified > 0) {
        echo "✅ Configuration now has proper unified field information!\n\n";
        
        echo "Sample visible columns:\n";
        foreach (array_slice($updated_data['structure'], 0, 5) as $i => $col) {
            $unified_info = !empty($col['unified_field']) ? 
                " -> {$col['unified_field']} (confidence: {$col['confidence']})" : 
                " (no unified field)";
            echo "  " . ($i + 1) . ". {$col['field']}: {$col['label']}{$unified_info}\n";
        }
        
        if (count($updated_data['structure']) > 5) {
            echo "  ... and " . (count($updated_data['structure']) - 5) . " more visible columns\n";
        }
        
        echo "\nSample available fields:\n";
        foreach (array_slice($updated_data['available_fields'], 0, 10) as $i => $field) {
            echo "  " . ($i + 1) . ". {$field}\n";
        }
        
        if (count($updated_data['available_fields']) > 10) {
            echo "  ... and " . (count($updated_data['available_fields']) - 10) . " more available fields\n";
        }
    } else {
        echo "❌ Configuration still lacks unified field information\n";
        exit(1);
    }
    
    echo "\n8. Testing data retrieval with new configuration...\n";
    
    try {
        $table_data = system\data_table_storage::get_table_data($table_name, [], [], null);
        
        if ($table_data['success']) {
            echo "✅ Data retrieval successful:\n";
            echo "- Records: " . $table_data['count'] . "\n";
            echo "- Source: " . $table_data['source'] . "\n";
            echo "- Available fields: " . count($table_data['available_fields'] ?? []) . "\n";
        } else {
            echo "❌ Data retrieval failed\n";
        }
    } catch (Exception $e) {
        echo "❌ Error testing data retrieval: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== Fix Complete ===\n";
    echo "✅ SketchUp configuration has been fixed with intelligent column selection\n";
    echo "✅ Only " . count($updated_data['structure']) . " relevant columns will be visible initially\n";
    echo "✅ " . count($updated_data['available_fields']) . " additional fields are available in column manager\n";
    echo "✅ Unified field labels and metadata are properly preserved\n";
    
    echo "\nThe SketchUp view should now display:\n";
    echo "- Clean, focused table with core business columns\n";
    echo "- Unified field labels (e.g., 'Company Name' instead of 'end_customer_name')\n";
    echo "- Column manager with hidden fields available for manual addition\n";
    echo "- No more overwhelming technical field clutter\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Fix ===\n";
?>
